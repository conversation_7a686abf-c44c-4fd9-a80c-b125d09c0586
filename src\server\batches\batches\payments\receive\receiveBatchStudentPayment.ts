import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { runDLocked } from '@/server/common/distributed-lock/runDLocked'
import { ensureProfile } from '@/server/common/error/ensure-profile.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db, Transaction } from '@/server/db/db'
import { batchStudentTable, studentFeePaymentItemTable, studentFeePaymentTable } from '@/server/db/schema/batch-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'

import { updateStudentPaymentReminderType } from '../process/update-student-reminder-type/updateStudentPaymentReminderType'

// Find batch student by ID
const findStudentFeePaymentDaf = (log: pino.Logger, studentFeePaymentId: UUID) =>
  withLog(log, 'findStudentFeePaymentDaf', () =>
    db.query.studentFeePaymentTable.findFirst({
      columns: {
        studentId: true,
        status: true,
      },
      with: {
        items: {
          columns: {
            batchId: true,
            cycle: true,
          },
        },
      },
      where: eq(studentFeePaymentTable.id, studentFeePaymentId),
    }),
  )

const updateStudentFeePaymentDaf = (log: pino.Logger, db: Transaction, studentFeePaymentId: UUID) => {
  const now = utc().toJSDate()
  return withLog(log, 'updateStudentFeePaymentDaf', () =>
    db
      .update(studentFeePaymentTable)
      .set({
        status: 'received',
        updatedAt: now,
        updateRemarks: 'received',
      })
      .where(and(eq(studentFeePaymentTable.id, studentFeePaymentId), eq(studentFeePaymentTable.status, 'pending'))),
  )
}

// TODO: needs thorough testing
const updateBatchStudentsDaf = (log: pino.Logger, db: Transaction, studentFeePaymentId: UUID) =>
  withLog(log, 'updateBatchStudentDaf', () =>
    db
      .update(batchStudentTable)
      .set({
        paidTillCycle: studentFeePaymentItemTable.cycle,
      })
      .from(studentFeePaymentItemTable)
      .innerJoin(studentFeePaymentTable, eq(studentFeePaymentTable.id, studentFeePaymentItemTable.studentFeePaymentId))
      .where(
        and(
          eq(studentFeePaymentTable.id, studentFeePaymentId),
          eq(batchStudentTable.studentId, studentFeePaymentTable.studentId),
          eq(batchStudentTable.batchId, studentFeePaymentItemTable.batchId),
        ),
      ),
  )

const findBatchStudentDaf = (log: pino.Logger<never, boolean>, db: Transaction, batchId: UUID, studentId: UUID) => {
  return withLog(log, 'findBatchStudentDaf', () =>
    db.query.batchStudentTable.findFirst({
      columns: {
        id: true,
        firstJoinedAt: true,
        paidTillCycle: true,
        lastReminderType: true,
      },
      with: {
        batch: {
          columns: {
            id: true,
            startDate: true,
            timezone: true,
            billingCycle: true,
            graceDays: true,
            cycleCount: true,
          },
          with: {
            teacher: {
              with: {
                user: {
                  columns: {
                    id: true,
                  },
                },
              },
            },
            events: {
              columns: {
                id: true,
              },
            },
          },
        },
      },
      where: and(eq(batchStudentTable.batchId, batchId), eq(batchStudentTable.studentId, studentId)),
    }),
  )
}

/**
 * Receive a student fee payment
 * @param c - The context object
 * @param studentFeePaymentId - The ID of the student fee payment to receive
 * @param receiving - Whether the payment is being received or unreceived
 */
export const receiveStudentFeePayment = async (c: Ctx, studentFeePaymentId: UUID) => {
  ensureUser(c.user)
  ensureProfile(c.profile, { roleAnyOf: ACADEMY_STAFF })

  const payment = await findStudentFeePaymentDaf(c.log, studentFeePaymentId)
  ensureExists(payment, studentFeePaymentId, 'Student Fee Payment')
  ensure(payment.status === 'pending', {
    statusCode: 409,
    issueMessage: `Student Fee Payment status is already ${payment.status}`,
    logMessage: `Attempted to receive student fee payment ${studentFeePaymentId} but the status is already ${payment.status}`,
  })

  await runDLocked(c.log, ['studentFeePayment', payment.studentId], async () => {
    await db.transaction(async (tx) => {
      await updateStudentFeePaymentDaf(c.log, tx, studentFeePaymentId)
      await updateBatchStudentsDaf(c.log, tx, studentFeePaymentId)
      for (const item of payment.items) {
        const batchStudent = await findBatchStudentDaf(c.log, tx, item.batchId, payment.studentId)
        ensureExists(batchStudent, item.batchId, 'Batch Student')
        await updateStudentPaymentReminderType(c.log, tx, batchStudent.batch, batchStudent)
      }
    })
  })
}

import { UUID } from 'crypto'

import { calendar_v3 } from '@googleapis/calendar'
import { eq, isNull, and, ne, or } from 'drizzle-orm'
import { DateTime } from 'luxon'
import { pino } from 'pino'

import { env } from '@/server/common/env.server'
import { db } from '@/server/db/db'
import { profileTable } from '@/server/db/schema'
import { batchEventTable, batchStudentTable } from '@/server/db/schema/batch-schema'
import { userTable } from '@/server/db/schema/user-schema'
import { Email } from '@/shared/common/common-utils.shared'
import { formatDate } from '@/shared/common/date-utils.shared'
import { plusCycles } from '@/shared/common/payment-utils/payment-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { coursePath } from '@/shared/course/course-utils.shared'

const fetchBatchEventDaf = async (log: pino.Logger, eventId: UUID) =>
  withLog(log, 'fetchBatchEventDaf', () =>
    db.query.batchEventTable.findFirst({
      columns: {
        batchId: true,
        at: true,
        durationMinutes: true,
        days: true,
        eventXid: true,
      },
      with: {
        batch: {
          columns: {
            startDate: true,
            billingCycle: true,
            cycleCount: true,
            timezone: true,
          },
          with: {
            course: {
              columns: {
                id: true,
                name: true,
              },
            },
            teacher: {
              with: {
                user: {
                  columns: {
                    email: true,
                    googleRefreshToken: true,
                  },
                },
              },
            },
          },
        },
      },
      where: eq(batchEventTable.id, eventId),
    }),
  )

const fetchUnblockedStudentsForGoogleEventDaf = (log: pino.Logger<never, boolean>, batchId: UUID) =>
  withLog(log, 'fetchUnblockedStudentsForGoogleEventDaf', () =>
    db
      .selectDistinct({
        email: userTable.email,
      })
      .from(batchStudentTable)
      .innerJoin(profileTable, eq(batchStudentTable.studentId, profileTable.id))
      .innerJoin(userTable, eq(profileTable.userId, userTable.id))
      .where(
        and(
          eq(batchStudentTable.batchId, batchId),
          isNull(batchStudentTable.leftAt),
          or(isNull(batchStudentTable.lastReminderType), ne(batchStudentTable.lastReminderType, 'blocked')),
        ),
      ),
  )

const composeAttendees = async (log: pino.Logger, batchId: UUID, teacherEmail: Email) => {
  const students = await fetchUnblockedStudentsForGoogleEventDaf(log, batchId)
  const studentEmails = students.map((attendee) => attendee.email).filter((email) => email !== teacherEmail) // Remove the organizer
  return studentEmails.map((email) => ({ email }))
}

export const composeGoogleEventDataForBatchEvent = async (
  log: pino.Logger,
  eventId: UUID,
): Promise<[string, calendar_v3.Schema$Event] | null> => {
  const batchEvent = await fetchBatchEventDaf(log, eventId)
  if (!batchEvent) {
    log.warn(`Batch already deleted for google event. Batch=${eventId}`)
    return null
  }
  if (!batchEvent.batch.teacher.user.googleRefreshToken) {
    log.error(`Teacher google refresh token not found for batch event. Batch=${eventId}`)
    return null
  }
  const sessionTime = `${batchEvent.at}:00`
  const meetingStartsAt = `${batchEvent.batch.startDate}T${sessionTime}`
  const meetingEndsAt = DateTime.fromISO(`${meetingStartsAt}Z`)
    ?.plus({ minutes: batchEvent.durationMinutes })
    ?.toUTC()
    ?.toISO({ includeOffset: false, suppressMilliseconds: true })
  const endDate = plusCycles(
    DateTime.fromISO(batchEvent.batch.startDate, { zone: batchEvent.batch.timezone }),
    batchEvent.batch.billingCycle,
    batchEvent.batch.cycleCount,
  )
    ?.plus({ days: -1 }) // If start date is 14th, end date should be 13th
    ?.toISODate()
  const recurrenceUntil = DateTime.fromISO(`${endDate}T${sessionTime}`, {
    zone: batchEvent.batch.timezone,
  })
    ?.plus({ minutes: batchEvent.durationMinutes })
    ?.toUTC()
    ?.toISO({ includeOffset: false, suppressMilliseconds: true })
    ?.replaceAll('-', '')
    ?.replaceAll(':', '')
  const recurrence = `RRULE:FREQ=WEEKLY;BYDAY=${batchEvent.days.join(',')};UNTIL=${recurrenceUntil}Z`

  const attendees = await composeAttendees(log, batchEvent.batchId, batchEvent.batch.teacher.user.email)

  const summary = (env.APP_ENV === 'production' ? '' : `[${env.APP_ENV}]`) + batchEvent.batch.course.name
  const event = {
    id: batchEvent.eventXid,
    description: `<b>${summary}</b><br/>        
        ${formatDate(batchEvent.batch.startDate)} to ${formatDate(endDate!)}<br/><br/>`,
    start: {
      dateTime: meetingStartsAt,
      timeZone: batchEvent.batch.timezone,
    },
    end: {
      dateTime: meetingEndsAt,
      timeZone: batchEvent.batch.timezone,
    },
    eventType: 'default',
    guestsCanInviteOthers: false,
    recurrence: [recurrence],
    source: {
      title: summary,
      url: `${env.HOME_URL}${coursePath(batchEvent.batch.course)}`,
    },
    summary,
    conferenceData: {
      createRequest: {
        requestId: batchEvent.eventXid,
        conferenceSolutionKey: {
          type: 'hangoutsMeet',
        },
      },
    },
    attendees,
  }
  log.info(
    {
      event,
      sessionTime,
      batchStartDateTime: meetingStartsAt,
      batchEndDateTime: meetingEndsAt,
      batchEndDate: endDate,
      recurrenceUntil,
    },
    'Composed google event data for batch event',
  )
  return [batchEvent.batch.teacher.user.googleRefreshToken, event]
}

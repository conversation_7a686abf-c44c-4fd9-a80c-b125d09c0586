import { UUID } from 'crypto'

import { useQuery, useSuspenseQuery } from '@tanstack/react-query'

import { isClient } from '@/shared/common/common-utils.shared'
import { useServerQuery } from '@/shared/common/serverQueries.shared'
import { api, withError } from '@ui/api-client'

const courseTagKeys = {
  all: ['course-tag'] as const,
  list: () => [...courseTagKeys.all] as const,
  detail: (id: UUID) => [...courseTagKeys.all, id] as const,
}

export const useCourseTagsQuery = () => {
  return useQuery({
    queryKey: courseTagKeys.list(),
    queryFn: () =>
      api()
        ['course-tags'].$get()
        .then((res) => res.json())
        .then((tags) => tags.rows),
  })
}

export const useCourseTagSuspenseQuery = (tagId: UUID) => {
  const getCourseTag = useServerQuery('getCourseTag')
  return useSuspenseQuery({
    queryKey: courseTagKeys.detail(tagId),
    queryFn: () =>
      isClient() ?
        withError(
          api()
            ['course-tags'][':id'].$get({ param: { id: tagId } })
            .then((res) => res.json()),
        )
      : getCourseTag(tagId),
  })
}

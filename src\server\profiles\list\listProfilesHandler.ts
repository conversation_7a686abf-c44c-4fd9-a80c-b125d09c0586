import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { HonoVars } from '@/server/common/hono-utils.server'
import { $ProfilesSearch } from '@/shared/profiles/profile-utils.shared'

import { listProfiles } from './listProfiles'

export const listProfilesHandler = new Hono<HonoVars>().get('/', zValidator('query', $ProfilesSearch), async (c) => {
  const search = c.req.valid('query')
  const profiles = await listProfiles(c.get('log'), search)
  return c.json({ rows: profiles }, 200)
})

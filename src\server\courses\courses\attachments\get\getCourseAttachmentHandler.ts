import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { getCourseAttachment } from './getCourseAttachment'

export const getCourseAttachmentHandler = new Hono<HonoVars>().get('/', zValidator('param', $HasId), async (c) => {
  const { id: attachmentId } = c.req.valid('param')

  const attachment = await getCourseAttachment(getCtx(c), attachmentId)
  return c.json(attachment, 200)
})

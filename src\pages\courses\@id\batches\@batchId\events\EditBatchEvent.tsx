import { UUID } from 'crypto'

import { useEffect, useState } from 'react'

import { BatchEvent } from '@/server/batches/batches/get/getBatch.server'
import { $EditBatchEventForm, EditBatchEventForm } from '@/shared/batch/batch-utils.shared'
import { OmniError } from '@/shared/common/error-utils.shared'

import { useUpdateBatchEventMutation } from '../../common/batch-queries'

import { BatchEventEditor } from './BatchEventEditor'

export const EditBatchEvent = ({
  batchId,
  event,
  open,
  setOpen,
}: {
  batchId: UUID
  event: BatchEvent
  open: boolean
  setOpen: (open: boolean) => void
}) => {
  const [formData, setFormData] = useState<EditBatchEventForm>({
    days: event.days,
    at: event.at,
    durationMinutes: event.durationMinutes,
  })

  // Error state
  const [omniError, setOmniError] = useState<OmniError | undefined>()

  // Add batch event mutation
  const { mutate: updateBatchEvent, isPending } = useUpdateBatchEventMutation(batchId, event.id)

  // Validate form data when it changes
  useEffect(() => {
    const parsed = $EditBatchEventForm.safeParse(formData)
    setOmniError(parsed.error)
  }, [formData])

  // Form submission handler
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    updateBatchEvent(formData, {
      onSuccess: () => {
        setOpen(false)
      },
      onError: (error) => {
        setOmniError(error.error)
      },
    })
  }

  return (
    <BatchEventEditor
      formData={formData}
      setFormData={setFormData as (form: EditBatchEventForm) => void}
      error={omniError}
      onSubmit={handleSubmit}
      isPending={isPending}
      open={open}
      setOpen={setOpen}
    />
  )
}

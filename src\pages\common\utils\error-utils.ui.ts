import { getPropertyOf } from '@/shared/common/common-utils.shared'
import { ErrorPayload, MyError, MyIssue, OmniError } from '@/shared/common/error-utils.shared'

const isErrorPayload = (payload: unknown): payload is ErrorPayload =>
  typeof payload === 'object' &&
  payload !== null &&
  'error' in payload &&
  typeof payload.error === 'object' &&
  payload.error !== null &&
  'issues' in payload.error

const isMyError = (error: unknown): error is MyError => typeof error === 'object' && error !== null && 'issues' in error

export const getErrors = (error?: OmniError, path: (string | number)[] = []) => {
  if (!error) return []

  return error.issues
    .filter((err) => {
      // Require exact path match
      if (err.path.length !== path.length) return false
      return path.every((segment, index) => err.path[index] === segment)
    })
    .map((err) => err.message)
}

export const getErrorMessage = (error: unknown): string => {
  if (isErrorPayload(error)) {
    return fromIssues(error.error.issues)
  }
  if (isMyError(error)) {
    return fromIssues(error.issues)
  }
  return getPropertyOf(error, 'message') ?? 'Unknown error. Please try again.'
}

const fromIssues = (issues: MyIssue[]) => issues.map((issue) => issue.message).join(', ')

export const getMyError = (message: string): MyError => ({
  issues: [{ path: [], code: 'custom', message }],
})

export const getErrorData = (error?: unknown): Record<string, unknown> | undefined => {
  if (isErrorPayload(error)) {
    const issue = error.error.issues[0]
    if ('data' in issue) return issue.data
  }
  return undefined
}

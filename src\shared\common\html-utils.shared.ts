import DOMPurify from 'isomorphic-dompurify'

export const sanitizedHtml = (html: string) => {
  // Then sanitize the resulting HTML
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: [
      // Typography
      'p',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
      'br',
      'hr',
      // Formatting
      'strong',
      'em',
      'del',
      'code',
      'pre',
      // Lists
      'ul',
      'ol',
      'li',
      // Other
      'a',
      'blockquote',
      'img',
      // Tables
      'table',
      'thead',
      'tbody',
      'tr',
      'th',
      'td',
    ],
    ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'class'],
    ALLOW_DATA_ATTR: false,
    ADD_ATTR: ['target'], // Add target="_blank" to links
    // ADD_TAGS: ['iframe'], // Only if you want to allow embedded content
    SANITIZE_DOM: true,
    FORBID_TAGS: ['style', 'script'],
    FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover'],
  })
}

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { findAcademyStaffDaf } from '@/server/academies/common/academy-staff-check'
import { ensureExists } from '@/server/common/error/ensureExists'
import { db } from '@/server/db/db'
import { batchRecommendationTable, batchTable } from '@/server/db/schema/batch-schema'
import { courseTable } from '@/server/db/schema/course-schema'
import { isGoodProfile } from '@/server/profiles/common/profile-check.server'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { batchColumns } from '../common/batch-select-helper'

const findBatchByIdDaf = (log: pino.Logger, batchId: UUID) =>
  withLog(log, 'findBatchByIdDaf', () =>
    db
      .select({ ...batchColumns, publishedAt: courseTable.publishedAt, academyId: courseTable.academyId })
      .from(batchTable)
      .innerJoin(courseTable, eq(courseTable.id, batchTable.courseId))
      .leftJoin(batchRecommendationTable, eq(batchRecommendationTable.batchId, batchTable.id))
      .where(eq(batchTable.id, batchId))
      .then((rows) => rows[0]),
  )

const isGoodAcademyStaff = async (log: pino.Logger, academyId: UUID, profileId: UUID | undefined) => {
  if (!profileId) return false
  const staff = await findAcademyStaffDaf(log, academyId, profileId)
  return isGoodProfile(staff)
}

export const getBatch = async (log: pino.Logger, batchId: UUID, profileId?: UUID) => {
  const batchWithCourse = await findBatchByIdDaf(log, batchId)
  ensureExists(batchWithCourse, batchId, 'Batch')
  const { publishedAt, academyId, ...batch } = batchWithCourse
  if (publishedAt) return batch

  const goodStaff = await isGoodAcademyStaff(log, academyId, profileId)
  ensureExists(goodStaff, batchId, 'Batch')
  return batch
}

export type GetBatch = typeof getBatch
export type Batch = Awaited<ReturnType<GetBatch>>
export type BatchEvent = Batch['events'][number]

setQuery('getBatch', getBatch)

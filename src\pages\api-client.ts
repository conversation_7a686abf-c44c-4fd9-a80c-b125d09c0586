import { UUID } from 'crypto'

import { hc } from 'hono/client'

import { setSigninModalVisibility } from '@/pages/users/signin/signin-modal-store'
import { isClient } from '@/shared/common/common-utils.shared'
import { PROFILE_HEADER } from '@/shared/profiles/profile-utils.shared'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'
import { getCurrentProfileId } from '@ui/profiles/common/current-profile-store'
import { clearAuth, getAccessToken } from '@ui/users/auth/auth-token-store'

import type { ApiType } from '@/server/api-routes.server'

const defaultHeaders = (token: string | null, profileId?: UUID) => {
  const userHeaders: Record<string, string> = {}

  if (!isClient()) throw new Error('API client is not available on the server')

  if (token) {
    userHeaders.Authorization = `Bearer ${token}`
  }

  if (token && profileId) {
    userHeaders[PROFILE_HEADER] = profileId
  }

  return userHeaders
}

const getDefaultHeaders = () => {
  const token = getAccessToken()
  const profileId = getCurrentProfileId()
  return defaultHeaders(token, profileId ?? undefined)
}

const myFetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
  const response = await fetch(input, init)
  if (response.status === 401) {
    const json = await response.clone().json()
    setSigninModalVisibility(true, getErrorMessage(json))
    clearAuth()
  }
  if (response.status >= 400) throw await response.clone().json()
  return response
}

export const api = () => {
  const headers = getDefaultHeaders()
  return hc<ApiType>('/api', { headers, fetch: myFetch })
}

export const withError = <T>(promise: Promise<T>) =>
  promise.then((data) => ({ data, error: null })).catch((error) => ({ data: undefined, error }))

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { removeOtp, verifyOtp } from '@/server/common/sms/otp/verifyOtp'
import { db, Transaction } from '@/server/db/db'
import { academyTable } from '@/server/db/schema/academy-schema'
import { phoneNumber } from '@/shared/common/common-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { ensureGoodStaff } from '../common/ensureGoodStaff'

const findAcademyDaf = (log: Logger, academyId: UUID) =>
  withLog(log, 'findAcademyDaf', () =>
    db.query.academyTable.findFirst({
      columns: {
        mobile: true,
        mobileVerified: true,
      },
      with: {
        mobileCountry: {
          columns: {
            phonePrefix: true,
          },
        },
      },
      where: eq(academyTable.id, academyId),
    }),
  )

const setAcademyMobileVerified = (log: Logger, db: Transaction, academyId: UUID) =>
  withLog(log, 'setAcademyMobileVerified', () =>
    db.update(academyTable).set({ mobileVerified: true }).where(eq(academyTable.id, academyId)),
  )

export const verifyAcademyMobile = async (c: Ctx, academyId: UUID, otp: string) => {
  await ensureGoodStaff(c, academyId)

  const academy = await findAcademyDaf(c.log, academyId)
  ensureExists(academy, academyId, 'Academy', 422)

  if (academy.mobileVerified) return

  // Get OTP details
  const mobile = phoneNumber(academy.mobileCountry.phonePrefix, academy.mobile)
  await verifyOtp(c.log, mobile, otp)

  // Update academy and delete OTP
  await db.transaction(async (tx) => {
    await setAcademyMobileVerified(c.log, tx, academyId)
    await removeOtp(c.log, tx, mobile)
  })
}

import { UUID } from 'crypto'

import { and, eq, gt, isNotNull, isNull, sql } from 'drizzle-orm'
import pRetry from 'p-retry'
import { Logger } from 'pino'

import { runDLocked } from '@/server/common/distributed-lock/runDLocked'
import { env } from '@/server/common/env.server'
import { mail } from '@/server/common/mail/mail'
import { db } from '@/server/db/db'
import { userTable, userTncAcceptedTable, userTncVersionTable } from '@/server/db/schema/user-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { formatDate } from '@/shared/common/date-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { TnCReminderType } from '@/shared/users/user-utils.shared'

const MAX_RETRIES = 3

/**
 * SQL template for determining TnC reminder type based on effective date
 */
const reminderTypeCase = (effectiveDate: unknown, currentReminderType: unknown) => sql<TnCReminderType>`
  CASE
    WHEN MIN(${effectiveDate}) <= CURRENT_DATE 
      AND COALESCE(${currentReminderType}, '') != 'urgent' THEN 'urgent'
    WHEN MIN(${effectiveDate}) <= CURRENT_DATE + INTERVAL '1 day'
      AND COALESCE(${currentReminderType}, '') != 'last' THEN 'last'
    WHEN MIN(${effectiveDate}) <= CURRENT_DATE + INTERVAL '3 days'
      AND COALESCE(${currentReminderType}, '') != 'prudent' THEN 'prudent'
    WHEN MIN(${effectiveDate}) <= CURRENT_DATE + INTERVAL '7 days'
      AND COALESCE(${currentReminderType}, '') != 'gentle' THEN 'gentle'
    WHEN MIN(${effectiveDate}) <= CURRENT_DATE + INTERVAL '30 days'
      AND COALESCE(${currentReminderType}, '') != 'advance' THEN 'advance'
    ELSE NULL
  END
`

/**
 * Find the next user who needs a TnC reminder
 * Uses a single query to:
 * 1. Find users with pending TnC acceptances
 * 2. Calculate the earliest effective date
 * 3. Determine the appropriate reminder type
 */
const findNextUserToRemindDaf = (log: Logger, lastProcessedUserId: UUID | null) =>
  withLog(log, 'findNextUserToRemindDaf', async () => {
    const latestVersions = db
      .select({
        id: userTncVersionTable.id,
        effectiveDate: userTncVersionTable.effectiveDate,
      })
      .from(userTncVersionTable)
      .where(isNull(userTncVersionTable.expiryDate))
      .as('tv')

    const result = await db
      .select({
        userId: userTable.id,
        email: userTable.email,
        name: userTable.name,
        language: userTable.language,
        tncReminderMailedAt: userTable.tncReminderMailedAt,
        tncReminderType: userTable.tncReminderType,
        earliestEffectiveDate: sql<string>`MIN(${latestVersions.effectiveDate})`,
        pendingTncCount: sql<number>`COUNT(DISTINCT ${latestVersions.id})`,
        reminderTypeToSend: reminderTypeCase(latestVersions.effectiveDate, userTable.tncReminderType),
      })
      .from(userTable)
      .innerJoin(latestVersions, sql`1 = 1`)
      .leftJoin(
        userTncAcceptedTable,
        and(eq(userTncAcceptedTable.userId, userTable.id), eq(userTncAcceptedTable.tncVersionId, latestVersions.id)),
      )
      .where(
        and(isNull(userTncAcceptedTable.id), lastProcessedUserId ? gt(userTable.id, lastProcessedUserId) : undefined),
      )
      .groupBy(
        userTable.id,
        userTable.email,
        userTable.name,
        userTable.language,
        userTable.tncReminderMailedAt,
        userTable.tncReminderType,
      )
      .having(isNotNull(reminderTypeCase(latestVersions.effectiveDate, userTable.tncReminderType)))
      .orderBy(userTable.id)
      .limit(1)

    return result[0]
  })

type UserToRemind = Awaited<ReturnType<typeof findNextUserToRemindDaf>>

/**
 * Update user's reminder state after sending email
 */
const updateUserReminderStateDaf = (log: Logger, userId: UUID, reminderType: TnCReminderType) =>
  withLog(log, 'updateUserReminderStateDaf', () =>
    db
      .update(userTable)
      .set({
        tncReminderMailedAt: utc().toJSDate(),
        tncReminderType: reminderType,
      })
      .where(eq(userTable.id, userId)),
  )

/**
 * Process a single user for TnC reminder
 * Returns the user's ID if processed, null if no action needed
 */
const processUserDaf = async (log: Logger, user: UserToRemind) => {
  // Send reminder email
  await mail(log, false, 'tncReminder', user.language, {
    to: user.email,
    data: {
      name: user.name,
      reminderType: user.reminderTypeToSend,
      effectiveDate: formatDate(user.earliestEffectiveDate, {
        language: user.language,
      }),
      pendingCount: user.pendingTncCount,
      baseUrl: env.HOME_URL,
    },
  })

  // Update user state
  await updateUserReminderStateDaf(log, user.userId, user.reminderTypeToSend)
  return user.userId
}

/**
 * Main function to process TnC reminders
 * Runs with distributed lock to prevent multiple instances
 */
const processTnCRemindersDaf = async (log: Logger) => {
  let lastProcessedUserId: UUID | null = null

  while (true) {
    const user = await findNextUserToRemindDaf(log, lastProcessedUserId)
    if (!user) break

    await processUserDaf(log, user)
    lastProcessedUserId = user.userId
  }
}

/**
 * Entry point for TnC reminder processing
 * Runs with retry logic and distributed lock
 */
export const sendTnCReminders = async (log: Logger) => {
  try {
    await pRetry(
      () =>
        runDLocked(
          log,
          ['send-tnc-reminders', ''],
          () => withLog(log, 'sendTnCReminders', () => processTnCRemindersDaf(log)),
          {
            lockExpiryMinutes: 60 * 2, // 2 hours lock expiry
          },
        ),
      {
        retries: MAX_RETRIES,
        onFailedAttempt: (error) => {
          log.warn(error, `Failed attempt to send TnC reminders. Retrying...`)
        },
      },
    )
  } catch (error) {
    log.error(error, `Failed to send TnC reminders after ${MAX_RETRIES} retries`)
    throw error
  }
}

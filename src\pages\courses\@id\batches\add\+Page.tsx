import { usePageContext } from 'vike-react/usePageContext'

import { extractUuid } from '@/shared/common/common-utils.shared'
import { ShowData } from '@ui/common/ShowData'
import { useCourseSuspenseQuery } from '@ui/courses/common/queries/course-queries'

import { AddBatch } from './AddBatch'

export default () => {
  const pageContext = usePageContext()
  const courseId = extractUuid(pageContext.routeParams.id)

  const { data, error } = useCourseSuspenseQuery(courseId)
  return (
    <ShowData error={error} spinnerSize="1.25rem">
      <AddBatch course={data!} />
    </ShowData>
  )
}

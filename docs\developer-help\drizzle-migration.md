If your DB and schema mismatch, follow these steps:

1. Commit the code, so that you can revert if needed
1. Rename `drizzle` folder to `drizzle-deprecated`
1. Comment `out` and `schema` in `drizzle.config.ts`
1. Run `npx drizzle-kit pull`
1. Delete `schema.ts` and `relations.ts` in the newly created `drizzle` folder
1. Uncomment `out` and `schema` in `drizzle.config.ts`
1. Run `npx drizzle-kit generate --custom --name=seed-masters`
1. Copy the content of `0001-seed-masters.sql` from `drizzle-deprecated` folder, and paste in the new `0001-seed-masters.sql`
1. <PERSON><PERSON><PERSON><PERSON><PERSON>, empty the newly generted `0000_*.sql` file in `drizzle` folder
1. In DB, besides the `public` schema, you'll find  `drizzle` schema. Take a backup `__drizzle_migrations` table in that schema, and then delete that schema.
1. Run `pnpm drizzle-kit migrate` to apply the migrations
1. Restore the newly generted `0000_*.sql` file in `drizzle` folder, and uncomment the commends in there.
1. Run `pnpm drizzle:generate`. This will generate the differences between the DB schema and the schema in the `src/server/db/schema` folder.
1. Run `pnpm drizzle-kit migrate` to apply the migrations
1. Job done. Delete `drizzle-deprecated` folder.


To squash the generated schema files, follow these steps:

1. Create a new database, and let .env use the connection string of this database.
1. After copying the commands from any custom migrations (except seed-masters), delete the drizzle folder
1. Run `pnpm drizzle:generate` to generate the schema files.
1. Run `npx drizzle-kit generate --custom --name=seed-masters`
1. Git rollback seed-masters, so that old seed-masters come back
1. Append the copied commands to the newly generated seed-masters file.
1. Run `pnpm drizzle:migrate` to apply the migrations
1. Copy data from the `drizzle` schema in new DB to the `drizzle` schema in old DB
1. Revert the .env file to the old one
1. Fix initDb.ts to use only the seed-masters file.
1. From `np-tuition-test-db`, delete `drizzle` and `puclic` schema. Then, recreate `public` schema with owner as `test-user`.
1. Run `pnpm test`
1. Manually test the app with the new DB.
1. Copy data from the `drizzle` schema in Staging DB to the `drizzle` schema in old DB
1. Deploy and test on staging.
1. Copy data from the `drizzle` schema in Production DB to the `drizzle` schema in old DB
1. Deploy and test on production.
1. Job done.

To add non-nullable fields to a table, follow these steps:

1. Add the field to the schema, without the constraint.
1. Run `pnpm drizzle:generate` to generate the schema files.
1. `npx drizzle-kit generate --custom --name=introduce-district-id-to-academy`
1. In the above migration, fill the non-nullable fields with actual or placeholder data.
1. Add the non-null constraint to the fields.
1. In a second round, run `pnpm drizzle:generate` to generate the schema files again.
1. Run `pnpm drizzle:migrate` to apply the migrations.
1. Job done.
import { useEffect } from 'react'
import { navigate } from 'vike/client/router'

import { useGetUserTnCsQuery, useUserQuery } from '@/pages/users/common/user-queries'
import { whetherAllTnCsAccepted, whetherRequiredTnCsAccepted } from '@/shared/users/user-utils.shared'

import { TnCWarning } from './TnCWarning'

export const useTnCWarning = () => {
  const { data: user } = useUserQuery()
  const { data: tncs } = useGetUserTnCsQuery()

  const acceptedTnCs = user?.tncAcceptances.map((tnc) => tnc.tncVersionId) ?? []
  const latestTnCs = tncs?.filter((tnc) => !tnc.expiryDate) ?? []
  const latestTnCsAccepted = !user || whetherAllTnCsAccepted(latestTnCs, acceptedTnCs)

  useEffect(() => {
    if (!user) return
    const acceptedTnCs = user.tncAcceptances.map((tnc) => tnc.tncVersionId)
    const requiredTnCsAccepted = whetherRequiredTnCsAccepted(tncs ?? [], acceptedTnCs)
    if (user && !requiredTnCsAccepted) {
      void navigate('/users/me/edit')
    }
  }, [user, tncs])

  if (latestTnCsAccepted) {
    return null
  }

  return TnCWarning
}

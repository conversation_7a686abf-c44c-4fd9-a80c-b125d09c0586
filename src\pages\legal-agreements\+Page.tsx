import { useData } from 'vike-react/useData'

import type { PageData } from './+data'

export default () => {
  const { tncs } = useData<PageData>()

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-base font-semibold text-gray-900">Legal Agreements</h1>
          <p className="mt-2 text-sm text-gray-700">
            A list of all legal agreements and terms of service that govern the use of our platform.
          </p>
        </div>
      </div>
      <div className="mt-8">
        <ul role="list">
          {tncs.map((tnc) => (
            <li key={tnc.id} className="relative py-3 hover:bg-gray-50">
              <div className="px-4 sm:px-3 lg:px-5">
                <div className="max-w-4xl">
                  <a href={tnc.baseUrl}>
                    <p className="text-sm font-semibold text-indigo-600 truncate">{tnc.name}</p>
                    <p className="mt-1 text-sm text-gray-600 line-clamp-2">{tnc.description}</p>
                  </a>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}

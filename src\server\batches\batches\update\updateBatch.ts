import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
// import { appendCommandDaf } from '@/server/common/command/processCommands'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureExists } from '@/server/common/error/ensureExists'
// import { calendarFlock } from '@/server/common/google/calendar/calendar-utils'
import { db, Transaction } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { academyStaffTable, batchTable, courseTable } from '@/server/db/schema'
import { ensureGoodProfile, findProfileForVerificationDaf } from '@/server/profiles/common/profile-check.server'
import { EditBatchForm } from '@/shared/batch/batch-utils.shared'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { ensureCanUpdateBatch } from '../common/ensureCanUpdateBatch'
import { updateGoogleEventForBatch } from '../common/updateGoogleEventForBatch'
// import {
//   ADD_GOOGLE_EVENT_FOR_BATCH_COMMAND,
//   AddGoogleEventForBatchCommandData,
// } from '../events/add/addGoogleEventForBatchEventCommand'
// import { updateBatchEventXidDaf } from '../events/common/updateBatchEventXidDaf'
// import { REMOVE_GOOGLE_EVENT_COMMAND, RemoveGoogleEventCommandData } from '../events/remove/removeGoogleEventCommand'

const findAcademyStaff = (profileId: UUID, batchId: UUID) =>
  db
    .select(dummyColumn.extras)
    .from(academyStaffTable)
    .innerJoin(courseTable, eq(academyStaffTable.academyId, courseTable.academyId))
    .innerJoin(batchTable, eq(courseTable.id, batchTable.courseId))
    .where(and(eq(academyStaffTable.profileId, profileId), eq(batchTable.id, batchId)))
    .limit(1)

const findBatchDaf = (log: pino.Logger, batchId: UUID) =>
  withLog(log, 'findBatchDaf', () =>
    db.query.batchTable.findFirst({
      ...dummyColumn,
      with: {
        teacher: {
          with: {
            user: {
              columns: {
                id: true,
              },
            },
          },
        },
        events: {
          columns: {
            id: true,
            eventXid: true,
          },
        },
      },
      where: eq(batchTable.id, batchId),
    }),
  )

const updateBatchDaf = (db: Transaction, log: pino.Logger, batchId: UUID, form: EditBatchForm, profileId: UUID) =>
  withLog(log, 'updateBatchDaf', () =>
    db
      .update(batchTable)
      .set({
        teacherId: form.teacherId,
        fee: form.fee.amount,
        billingCycle: form.billingCycle,
        graceDays: form.graceDays,
        startDate: form.startDate,
        timezone: form.timezone,
        cycleCount: form.cycleCount,
        seatCount: form.seatCount,
        admissionOpen: form.admissionOpen,

        updatedAt: utc().toJSDate(),
        updateRemarks: `${profileId} profile updated this batch`,
      })
      .where(eq(batchTable.id, batchId)),
  )

export const updateBatch = async (c: Ctx, batchId: UUID, form: EditBatchForm) => {
  const batch = await findBatchDaf(c.log, batchId)
  ensureExists(batch, batchId, 'Batch')

  await ensureCanUpdateBatch(c, batchId)
  const newTeacher = await findProfileForVerificationDaf(c.log, form.teacherId)
  ensureGoodProfile(form.teacherId, newTeacher, 'Teacher')
  ensure(newTeacher.role === 'teacher', {
    statusCode: 409,
    issueMessage: 'The given teacher is not a teacher',
    logMessage: `Teacher profile with id ${form.teacherId} is not a teacher`,
  })
  const staff = await findAcademyStaff(form.teacherId, batchId)
  ensure(staff.length > 0, {
    statusCode: 409,
    issueMessage: 'The given teacher is not a staff of the academy of the batch.',
    logMessage: `Teacher profile ${form.teacherId} is not a staff of the academy of batch ${batchId}`,
  })

  await db.transaction(async (tx) => {
    await updateBatchDaf(tx, c.log, batchId, form, c.profile!.id)
    if (batch.teacher.user.id === newTeacher.userId) {
      await updateGoogleEventForBatch(tx, c.log, newTeacher.userId, batch.events)
    } else {
      // for (const event of batch.events) {
      //   await appendCommandDaf<RemoveGoogleEventCommandData>(tx, c.log, calendarFlock(batch.teacher.user.id), {
      //     command: REMOVE_GOOGLE_EVENT_COMMAND,
      //     data: {
      //       userId: batch.teacher.user.id,
      //       eventXid: event.eventXid,
      //     },
      //   })
      //   await updateBatchEventXidDaf(c.log, tx, event.id)
      //   await appendCommandDaf<AddGoogleEventForBatchCommandData>(tx, c.log, calendarFlock(newTeacher.user.id), {
      //     command: ADD_GOOGLE_EVENT_FOR_BATCH_COMMAND,
      //     data: {
      //       eventId: event.id,
      //     },
      //   })
      // }
    }
  })
}

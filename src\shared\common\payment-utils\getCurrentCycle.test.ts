import { DateTime } from 'luxon'
import { afterEach, describe, expect, test, vi } from 'vitest'

import * as dateUtils from '../date-utils-basic.shared'

import { getCurrentCycle } from './payment-utils.shared'

/**
 * Test suite for getCurrentCycle function
 *
 * This suite tests the calculation of cycles based on a start date and duration:
 * - For months: Tests various day-of-month scenarios and edge cases
 * - For weeks: Tests cycle calculations with week-based durations
 * - For days: Tests cycle calculations with day-based durations
 *
 * Each test mocks the utc() function to simulate specific dates.
 */
describe('getCurrentCycle', () => {
  // Helper function to create a DateTime with a specific date
  const createDateTime = (dateStr: string): DateTime<true> => {
    return DateTime.fromISO(`${dateStr}T00:00:00Z`, { zone: 'utc' }) as DateTime<true>
  }

  afterEach(() => {
    // Restore all mocks after each test
    vi.restoreAllMocks()
  })

  describe('monthly cycles', () => {
    test.each([
      { startDate: '2024-01-01', today: '2024-01-15', expected: 1, description: 'same month as start date' },
      { startDate: '2024-01-20', today: '2024-02-15', expected: 1, description: 'first month of cycle' },
      {
        startDate: '2024-01-20',
        today: '2024-03-15',
        expected: 2,
        description: 'second month after start date but before day-of-month',
      },
      {
        startDate: '2024-01-20',
        today: '2024-03-20',
        expected: 3,
        description: 'third month and on or after day-of-month',
      },
    ])('should return $expected when date is in $description', ({ today, startDate, expected }) => {
      vi.spyOn(dateUtils, 'utc').mockReturnValue(createDateTime(today))
      const result = getCurrentCycle(startDate, 'months')
      expect(result).toBe(expected)
    })

    test.each([
      {
        startDate: '2024-01-31',
        today: '2024-02-29',
        expected: 2,
        description: 'last day of February (leap year)',
      },
      { startDate: '2024-01-31', today: '2024-03-30', expected: 2, description: 'before matching day in March' },
      { startDate: '2024-01-31', today: '2024-03-31', expected: 3, description: 'exactly matching day in March' },
    ])('should handle last day of month edge case: $description', ({ today, startDate, expected }) => {
      vi.spyOn(dateUtils, 'utc').mockReturnValue(createDateTime(today))
      const result = getCurrentCycle(startDate, 'months')
      expect(result).toBe(expected)
    })

    test.each([
      {
        startDate: '2023-12-15',
        today: '2024-01-14',
        expected: 1,
        description: 'before day-of-month in second month',
      },
      {
        startDate: '2023-12-15',
        today: '2024-01-15',
        expected: 2,
        description: 'matching day-of-month in second month',
      },
      {
        startDate: '2023-12-15',
        today: '2024-02-15',
        expected: 3,
        description: 'matching day-of-month in third month',
      },
    ])('should correctly handle year boundaries when $description', ({ today, startDate, expected }) => {
      vi.spyOn(dateUtils, 'utc').mockReturnValue(createDateTime(today))
      const result = getCurrentCycle(startDate, 'months')
      expect(result).toBe(expected)
    })

    describe('February and leap year edge cases', () => {
      test.each([
        // Start date on January 29, testing different February scenarios
        { startDate: '2023-01-29', today: '2023-02-27', expected: 1, description: 'Jan 29 -> Feb 27 (non-leap year)' },
        { startDate: '2023-01-29', today: '2023-03-28', expected: 2, description: 'Jan 29 -> Mar 28 (non-leap year)' },
        { startDate: '2023-01-29', today: '2023-02-28', expected: 2, description: 'Jan 29 -> Feb 28 (non-leap year)' },
        { startDate: '2023-02-28', today: '2023-03-28', expected: 2, description: 'Feb 28 -> Mar 28 (non-leap year)' },
        { startDate: '2023-01-29', today: '2023-03-01', expected: 2, description: 'Jan 29 -> Mar 1 (non-leap year)' },
        { startDate: '2024-01-29', today: '2024-02-29', expected: 2, description: 'Jan 29 -> Feb 29 (leap year)' },

        // Start date on January 30
        { startDate: '2023-01-30', today: '2023-02-27', expected: 1, description: 'Jan 30 -> Feb 27 (non-leap year)' },
        { startDate: '2023-01-30', today: '2023-02-28', expected: 2, description: 'Jan 30 -> Feb 28 (non-leap year)' },
        { startDate: '2023-01-30', today: '2023-03-01', expected: 2, description: 'Jan 30 -> Mar 1 (non-leap year)' },
        { startDate: '2024-01-30', today: '2024-02-28', expected: 1, description: 'Jan 30 -> Feb 29 (leap year)' },
        { startDate: '2024-01-30', today: '2024-02-29', expected: 2, description: 'Jan 30 -> Feb 29 (leap year)' },
        { startDate: '2024-01-30', today: '2024-02-29', expected: 2, description: 'Jan 30 -> Feb 29 (leap year)' },
        { startDate: '2024-01-30', today: '2024-03-30', expected: 3, description: 'Jan 30 -> Mar 30 (leap year)' },

        // Start date on February 29 of leap year
        { startDate: '2024-02-29', today: '2024-03-28', expected: 1, description: 'Feb 29 -> Mar 28 (leap year)' },
        { startDate: '2024-02-29', today: '2024-03-29', expected: 2, description: 'Feb 29 -> Mar 29 (leap year)' },
        { startDate: '2024-02-29', today: '2024-04-29', expected: 3, description: 'Feb 29 -> Apr 29 (leap year)' },

        // Spanning from leap year to non-leap year
        { startDate: '2024-02-29', today: '2025-02-27', expected: 12, description: 'Feb 29, 2024 -> Feb 27, 2025' },
        { startDate: '2024-02-29', today: '2025-02-28', expected: 13, description: 'Feb 29, 2024 -> Feb 28, 2025' },
        { startDate: '2024-02-29', today: '2025-03-01', expected: 13, description: 'Feb 29, 2024 -> Mar 1, 2025' },

        // Starting on February 28
        { startDate: '2023-02-28', today: '2023-03-28', expected: 2, description: 'Feb 28 -> Mar 28 (non-leap year)' },
        { startDate: '2024-02-28', today: '2024-03-28', expected: 2, description: 'Feb 28 -> Mar 28 (leap year)' },

        // Starting on March 31, going to months with different days
        {
          startDate: '2024-03-31',
          today: '2024-04-30',
          expected: 2,
          description: 'Mar 31 -> Apr 30 (month with 30 days)',
        },
        {
          startDate: '2024-03-31',
          today: '2024-05-31',
          expected: 3,
          description: 'Mar 31 -> May 31 (month with 31 days)',
        },
        {
          startDate: '2024-03-31',
          today: '2024-06-30',
          expected: 4,
          description: 'Mar 31 -> Jun 30 (month with 30 days)',
        },

        // February to February cycle spanning multiple years
        {
          startDate: '2023-02-28',
          today: '2025-02-28',
          expected: 25,
          description: 'Feb 28, 2023 -> Feb 28, 2025 (24 months)',
        },
        {
          startDate: '2024-02-29',
          today: '2028-02-29',
          expected: 49,
          description: 'Feb 29, 2024 -> Feb 29, 2028 (48 months)',
        },
      ])('should handle $description', ({ today, startDate, expected }) => {
        vi.spyOn(dateUtils, 'utc').mockReturnValue(createDateTime(today))
        const result = getCurrentCycle(startDate, 'months')
        expect(result).toBe(expected)
      })
    })
  })

  describe('weekly cycles', () => {
    test.each([
      { startDate: '2024-01-01', today: '2024-01-03', expected: 1, description: 'first week' },
      { startDate: '2024-01-01', today: '2024-01-08', expected: 2, description: 'second week' },
      {
        startDate: '2024-01-01',
        today: '2024-02-12',
        expected: 7,
        description: 'multiple weeks (6 weeks after start)',
      },
    ])('should return $expected for $description', ({ today, startDate, expected }) => {
      vi.spyOn(dateUtils, 'utc').mockReturnValue(createDateTime(today))
      const result = getCurrentCycle(startDate, 'weeks')
      expect(result).toBe(expected)
    })
  })

  describe('daily cycles', () => {
    test.each([
      { startDate: '2024-01-01', today: '2024-01-01', expected: 1, description: 'first day' },
      { startDate: '2024-01-01', today: '2024-01-02', expected: 2, description: 'second day' },
      {
        startDate: '2024-01-01',
        today: '2024-01-15',
        expected: 15,
        description: 'multiple days (14 days after start)',
      },
    ])('should return $expected for $description', ({ today, startDate, expected }) => {
      vi.spyOn(dateUtils, 'utc').mockReturnValue(createDateTime(today))
      const result = getCurrentCycle(startDate, 'days')
      expect(result).toBe(expected)
    })

    test.each([
      // Leap year tests for daily cycles
      {
        startDate: '2024-01-01',
        today: '2024-02-29',
        expected: 60,
        description: 'January 1 to February 29 in leap year',
      },
      { startDate: '2024-01-01', today: '2024-03-01', expected: 61, description: 'January 1 to March 1 in leap year' },
      {
        startDate: '2023-01-01',
        today: '2023-03-01',
        expected: 60,
        description: 'January 1 to March 1 in non-leap year',
      },

      // Crossing year boundary
      { startDate: '2024-01-01', today: '2025-01-01', expected: 367, description: 'Full leap year (366 days) plus 1' },
      {
        startDate: '2023-01-01',
        today: '2024-01-01',
        expected: 366,
        description: 'Full non-leap year (365 days) plus 1',
      },
    ])('should handle leap year cases: $description', ({ today, startDate, expected }) => {
      vi.spyOn(dateUtils, 'utc').mockReturnValue(createDateTime(today))
      const result = getCurrentCycle(startDate, 'days')
      expect(result).toBe(expected)
    })
  })
})

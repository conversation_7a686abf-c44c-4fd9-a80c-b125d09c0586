import { usePageContext } from 'vike-react/usePageContext'

import { extractUuid } from '@/shared/common/common-utils.shared'
import { ShowData } from '@ui/common/ShowData'

import { useProfileSuspenseQuery } from '../common/profile-queries'

import { ProfilePage } from './ProfilePage'

export default () => {
  const pageContext = usePageContext()
  const id = extractUuid(pageContext.routeParams.id)

  const { data: profile, error } = useProfileSuspenseQuery(id)

  return (
    <ShowData error={error}>
      <ProfilePage profile={profile!} />
    </ShowData>
  )
}

import { pino } from 'pino'

import { addCommand, Command } from '@/server/common/command/processCommands'
import { removeS3Object } from '@/server/common/s3/s3-utils.server'
import { getLogger } from '@/shared/common/logger.shared'
import { withLog } from '@/shared/common/withLog.shared'

export const REMOVE_S3OBJECT_COMMAND = 'removeS3Object'

export type RemoveS3ObjectCommandData = {
  path: string
}

async function executeCommand(log: pino.Logger, command: Command<RemoveS3ObjectCommandData>) {
  const data = command.data
  await withLog(log, 'removeS3ObjectCommand', () => removeS3Object(data.path), {
    data: {
      path: data.path,
    },
  })
}

addCommand(REMOVE_S3OBJECT_COMMAND, executeCommand)
getLogger().info(`Loaded ${import.meta.url}`)

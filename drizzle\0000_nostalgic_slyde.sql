CREATE TABLE "academy_staff" (
	"profile_id" uuid PRIMARY KEY NOT NULL,
	"academy_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text,
	"suspended_at" timestamp with time zone,
	"suspension_reason" text
);
--> statement-breakpoint
CREATE TABLE "academy" (
	"id" uuid PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"tncs_accepted_at" timestamp with time zone,
	"approved_at" timestamp with time zone,
	"descr" text,
	"email" text NOT NULL,
	"email_verified" boolean DEFAULT false NOT NULL,
	"mobile_country_code" text NOT NULL,
	"mobile" text NOT NULL,
	"mobile_verified" boolean DEFAULT false NOT NULL,
	"currency" text NOT NULL,
	"upi_id" text NOT NULL,
	"trade_name" text,
	"gstin" text,
	"district_id" uuid NOT NULL,
	"pincode" text NOT NULL,
	"area" text NOT NULL,
	"address_line1" text,
	"address_line2" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text,
	"suspended_at" timestamp with time zone,
	"suspension_reason" text,
	CONSTRAINT "academy_name_unique" UNIQUE("name"),
	CONSTRAINT "chk_name_len" CHECK (char_length("academy"."name") <= 255),
	CONSTRAINT "descr_len" CHECK (char_length("academy"."descr") <= 10000),
	CONSTRAINT "chk_currency_len" CHECK (char_length("academy"."currency") <= 10),
	CONSTRAINT "chk_upi_id_len" CHECK (char_length("academy"."upi_id") <= 255),
	CONSTRAINT "suspension_reason_len" CHECK (char_length("academy"."suspension_reason") <= 5000)
);
--> statement-breakpoint
CREATE TABLE "batch_attachment" (
	"id" uuid PRIMARY KEY NOT NULL,
	"batch_id" uuid NOT NULL,
	"attachment_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text
);
--> statement-breakpoint
CREATE TABLE "batch_event" (
	"id" uuid PRIMARY KEY NOT NULL,
	"batch_id" uuid NOT NULL,
	"at" text NOT NULL,
	"duration_minutes" smallint NOT NULL,
	"days" text[] DEFAULT '{}'::text[] NOT NULL,
	"event_type" text DEFAULT 'meet' NOT NULL,
	"event_xid" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text,
	CONSTRAINT "chk_at_len" CHECK (char_length("batch_event"."at") = 5),
	CONSTRAINT "chk_event_type_len" CHECK (char_length("batch_event"."event_type") <= 50),
	CONSTRAINT "chk_event_xid_len" CHECK (char_length("batch_event"."event_xid") <= 255)
);
--> statement-breakpoint
CREATE TABLE "batch_recommendation" (
	"course_id" uuid PRIMARY KEY NOT NULL,
	"batch_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text
);
--> statement-breakpoint
CREATE TABLE "batch_student" (
	"id" uuid PRIMARY KEY NOT NULL,
	"batch_id" uuid NOT NULL,
	"student_id" uuid NOT NULL,
	"first_joined_at" timestamp with time zone NOT NULL,
	"left_at" timestamp with time zone,
	"paid_till_cycle" smallint DEFAULT 0 NOT NULL,
	"last_reminder_type" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text
);
--> statement-breakpoint
CREATE TABLE "batch" (
	"id" uuid PRIMARY KEY NOT NULL,
	"course_id" uuid NOT NULL,
	"teacher_id" uuid NOT NULL,
	"fee" integer NOT NULL,
	"billing_cycle" text NOT NULL,
	"grace_days" smallint DEFAULT 3 NOT NULL,
	"start_date" date NOT NULL,
	"timezone" text NOT NULL,
	"cycle_count" smallint NOT NULL,
	"over" boolean DEFAULT false NOT NULL,
	"seat_count" smallint NOT NULL,
	"student_count" smallint DEFAULT 0 NOT NULL,
	"admission_open" boolean NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text,
	"suspended_at" timestamp with time zone,
	"suspension_reason" text,
	CONSTRAINT "chk_timezone_len" CHECK (char_length("batch"."timezone") <= 50),
	CONSTRAINT "suspension_reason_len" CHECK (char_length("batch"."suspension_reason") <= 5000)
);
--> statement-breakpoint
CREATE TABLE "student_fee_payment_item" (
	"id" uuid PRIMARY KEY NOT NULL,
	"student_fee_payment_id" uuid NOT NULL,
	"batch_id" uuid NOT NULL,
	"cycle" smallint NOT NULL,
	"fee" integer NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text
);
--> statement-breakpoint
CREATE TABLE "student_fee_payment" (
	"id" uuid PRIMARY KEY NOT NULL,
	"student_id" uuid NOT NULL,
	"academy_id" uuid NOT NULL,
	"method" text,
	"currency" text NOT NULL,
	"cents" integer NOT NULL,
	"status" text NOT NULL,
	"paid_at" timestamp with time zone,
	"paid_remarks" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text,
	CONSTRAINT "chk_paid_remarks_len" CHECK (char_length("student_fee_payment"."paid_remarks") <= 255)
);
--> statement-breakpoint
CREATE TABLE "course_attachment" (
	"id" uuid PRIMARY KEY NOT NULL,
	"course_id" uuid NOT NULL,
	"position" text NOT NULL,
	"name" text NOT NULL,
	"content_type" text NOT NULL,
	"size_bytes" integer NOT NULL,
	"free" boolean NOT NULL,
	"uploaded" boolean NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text,
	CONSTRAINT "chk_position_len" CHECK (char_length("course_attachment"."position") <= 255),
	CONSTRAINT "chk_name_len" CHECK (char_length("course_attachment"."name") <= 255),
	CONSTRAINT "chk_content_type_len" CHECK (char_length("course_attachment"."content_type") <= 255),
	CONSTRAINT "chk_size_bytes" CHECK ("course_attachment"."size_bytes" > 0)
);
--> statement-breakpoint
CREATE TABLE "course" (
	"id" uuid PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"descr" text,
	"published_at" timestamp with time zone,
	"academy_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text,
	"suspended_at" timestamp with time zone,
	"suspension_reason" text,
	CONSTRAINT "chk_name_len" CHECK (char_length("course"."name") <= 255),
	CONSTRAINT "descr_len" CHECK (char_length("course"."descr") <= 10000),
	CONSTRAINT "suspension_reason_len" CHECK (char_length("course"."suspension_reason") <= 5000)
);
--> statement-breakpoint
CREATE TABLE "course_tag_assignment" (
	"id" uuid PRIMARY KEY NOT NULL,
	"course_id" uuid NOT NULL,
	"tag_id" uuid NOT NULL
);
--> statement-breakpoint
CREATE TABLE "course_tag_group" (
	"id" uuid PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"position" smallint NOT NULL,
	"suspended_at" timestamp with time zone,
	"suspension_reason" text,
	CONSTRAINT "course_tag_group_name_unique" UNIQUE("name"),
	CONSTRAINT "chk_name_len" CHECK (char_length("course_tag_group"."name") <= 30),
	CONSTRAINT "suspension_reason_len" CHECK (char_length("course_tag_group"."suspension_reason") <= 5000)
);
--> statement-breakpoint
CREATE TABLE "course_tag" (
	"id" uuid PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"group_id" uuid NOT NULL,
	"position" smallint NOT NULL,
	"descr" text NOT NULL,
	"suspended_at" timestamp with time zone,
	"suspension_reason" text,
	CONSTRAINT "course_tag_name_unique" UNIQUE("name"),
	CONSTRAINT "chk_name_len" CHECK (char_length("course_tag"."name") <= 30),
	CONSTRAINT "descr_len" CHECK (char_length("course_tag"."descr") <= 255),
	CONSTRAINT "suspension_reason_len" CHECK (char_length("course_tag"."suspension_reason") <= 5000)
);
--> statement-breakpoint
CREATE TABLE "country" (
	"code" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"phone_prefix" text NOT NULL,
	"suspended_at" timestamp with time zone,
	"suspension_reason" text,
	CONSTRAINT "country_name_unique" UNIQUE("name"),
	CONSTRAINT "country_phonePrefix_unique" UNIQUE("phone_prefix"),
	CONSTRAINT "code_len" CHECK (char_length("country"."code") <= 5),
	CONSTRAINT "name_len" CHECK (char_length("country"."name") <= 100),
	CONSTRAINT "phone_prefix_len" CHECK (char_length("country"."phone_prefix") <= 10),
	CONSTRAINT "suspension_reason_len" CHECK (char_length("country"."suspension_reason") <= 5000)
);
--> statement-breakpoint
CREATE TABLE "district" (
	"id" uuid PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"state_id" uuid NOT NULL,
	"suspended_at" timestamp with time zone,
	"suspension_reason" text,
	CONSTRAINT "district_name_unique" UNIQUE("name"),
	CONSTRAINT "name_len" CHECK (char_length("district"."name") <= 100),
	CONSTRAINT "suspension_reason_len" CHECK (char_length("district"."suspension_reason") <= 5000)
);
--> statement-breakpoint
CREATE TABLE "state" (
	"id" uuid PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"country_code" text NOT NULL,
	"suspended_at" timestamp with time zone,
	"suspension_reason" text,
	CONSTRAINT "state_name_unique" UNIQUE("name"),
	CONSTRAINT "name_len" CHECK (char_length("state"."name") <= 100),
	CONSTRAINT "suspension_reason_len" CHECK (char_length("state"."suspension_reason") <= 5000)
);
--> statement-breakpoint
CREATE TABLE "user_snapshot" (
	"id" uuid PRIMARY KEY NOT NULL,
	"user_id" uuid NOT NULL,
	"snapshot_at" timestamp with time zone DEFAULT now() NOT NULL,
	"data" jsonb NOT NULL
);
--> statement-breakpoint
CREATE TABLE "usr" (
	"id" uuid PRIMARY KEY NOT NULL,
	"google_id" text NOT NULL,
	"google_refresh_token" text,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"email_verified" boolean NOT NULL,
	"google_picture_url" text NOT NULL,
	"language" text NOT NULL,
	"tokens_valid_from" timestamp with time zone NOT NULL,
	"mobile_country_code" text NOT NULL,
	"mobile" text NOT NULL,
	"mobile_verified" boolean DEFAULT false NOT NULL,
	"legal_age_declared_at" timestamp with time zone DEFAULT now() NOT NULL,
	"information_accuracy_declared_at" timestamp with time zone DEFAULT now() NOT NULL,
	"tnc_reminder_mailed_at" timestamp with time zone,
	"tnc_reminder_type" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text,
	"suspended_at" timestamp with time zone,
	"suspension_reason" text,
	CONSTRAINT "usr_googleId_unique" UNIQUE("google_id"),
	CONSTRAINT "name_len" CHECK (char_length("usr"."name") <= 255),
	CONSTRAINT "email_len" CHECK (char_length("usr"."email") <= 255),
	CONSTRAINT "google_picture_url_len" CHECK (char_length("usr"."google_picture_url") <= 2000),
	CONSTRAINT "language_len" CHECK (char_length("usr"."language") <= 100),
	CONSTRAINT "mobile_len" CHECK (char_length("usr"."mobile") <= 20),
	CONSTRAINT "suspension_reason_len" CHECK (char_length("usr"."suspension_reason") <= 5000)
);
--> statement-breakpoint
CREATE TABLE "user_tnc_accepted" (
	"id" uuid PRIMARY KEY NOT NULL,
	"user_id" uuid NOT NULL,
	"tnc_version_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text
);
--> statement-breakpoint
CREATE TABLE "user_tnc_sign" (
	"id" uuid PRIMARY KEY NOT NULL,
	"user_id" uuid NOT NULL,
	"tnc_version_id" uuid NOT NULL,
	"signed_at" timestamp with time zone DEFAULT now() NOT NULL,
	"accepted" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text
);
--> statement-breakpoint
CREATE TABLE "user_tnc" (
	"id" uuid PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"base_url" text DEFAULT '/legal-agreements/terms-of-service' NOT NULL,
	"description" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_tnc_version" (
	"id" uuid PRIMARY KEY NOT NULL,
	"tnc_id" uuid NOT NULL,
	"version" smallint DEFAULT 1 NOT NULL,
	"effective_date" date DEFAULT now() NOT NULL,
	"expiry_date" date
);
--> statement-breakpoint
CREATE TABLE "app_command" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"creation_remarks" text,
	"flock" text NOT NULL,
	"command" text NOT NULL,
	"log_bindings" jsonb NOT NULL,
	"data" jsonb NOT NULL,
	"first_attempted_at" timestamp with time zone,
	"aborted_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "app_lock" (
	"subject" text PRIMARY KEY NOT NULL,
	"expires_at" timestamp with time zone NOT NULL
);
--> statement-breakpoint
CREATE TABLE "mobile_otp" (
	"mobile" text PRIMARY KEY NOT NULL,
	"otp" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"try_failed_at" timestamp with time zone,
	"otp_try_count" smallint DEFAULT 0 NOT NULL
);
--> statement-breakpoint
CREATE TABLE "profile" (
	"id" uuid PRIMARY KEY NOT NULL,
	"display_name" text NOT NULL,
	"role" text NOT NULL,
	"descr" text,
	"user_id" uuid NOT NULL,
	"service_provider_id" uuid,
	"submitted_for_approval_at" timestamp with time zone,
	"approved_at" timestamp with time zone,
	"tncs_accepted_at" timestamp with time zone NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"creation_remarks" text,
	"updated_at" timestamp with time zone,
	"update_remarks" text,
	"suspended_at" timestamp with time zone,
	"suspension_reason" text,
	CONSTRAINT "chk_display_name_len" CHECK (char_length("profile"."display_name") <= 255),
	CONSTRAINT "chk_role" CHECK ("profile"."role" IN ('principal', 'mentor', 'teacher', 'student', 'admin', 'manager', 'executive')),
	CONSTRAINT "descr_len" CHECK (char_length("profile"."descr") <= 10000),
	CONSTRAINT "suspension_reason_len" CHECK (char_length("profile"."suspension_reason") <= 5000)
);
--> statement-breakpoint
ALTER TABLE "academy_staff" ADD CONSTRAINT "academy_staff_profile_id_profile_id_fk" FOREIGN KEY ("profile_id") REFERENCES "public"."profile"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "academy_staff" ADD CONSTRAINT "academy_staff_academy_id_academy_id_fk" FOREIGN KEY ("academy_id") REFERENCES "public"."academy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "academy" ADD CONSTRAINT "academy_mobile_country_code_country_code_fk" FOREIGN KEY ("mobile_country_code") REFERENCES "public"."country"("code") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "academy" ADD CONSTRAINT "academy_district_id_district_id_fk" FOREIGN KEY ("district_id") REFERENCES "public"."district"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "batch_attachment" ADD CONSTRAINT "batch_attachment_batch_id_batch_id_fk" FOREIGN KEY ("batch_id") REFERENCES "public"."batch"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "batch_attachment" ADD CONSTRAINT "batch_attachment_attachment_id_course_attachment_id_fk" FOREIGN KEY ("attachment_id") REFERENCES "public"."course_attachment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "batch_event" ADD CONSTRAINT "batch_event_batch_id_batch_id_fk" FOREIGN KEY ("batch_id") REFERENCES "public"."batch"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "batch_recommendation" ADD CONSTRAINT "batch_recommendation_course_id_course_id_fk" FOREIGN KEY ("course_id") REFERENCES "public"."course"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "batch_recommendation" ADD CONSTRAINT "batch_recommendation_batch_id_batch_id_fk" FOREIGN KEY ("batch_id") REFERENCES "public"."batch"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "batch_student" ADD CONSTRAINT "batch_student_batch_id_batch_id_fk" FOREIGN KEY ("batch_id") REFERENCES "public"."batch"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "batch_student" ADD CONSTRAINT "batch_student_student_id_profile_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."profile"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "batch" ADD CONSTRAINT "batch_course_id_course_id_fk" FOREIGN KEY ("course_id") REFERENCES "public"."course"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "batch" ADD CONSTRAINT "batch_teacher_id_profile_id_fk" FOREIGN KEY ("teacher_id") REFERENCES "public"."profile"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_fee_payment_item" ADD CONSTRAINT "student_fee_payment_item_student_fee_payment_id_student_fee_payment_id_fk" FOREIGN KEY ("student_fee_payment_id") REFERENCES "public"."student_fee_payment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_fee_payment_item" ADD CONSTRAINT "student_fee_payment_item_batch_id_batch_id_fk" FOREIGN KEY ("batch_id") REFERENCES "public"."batch"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_fee_payment" ADD CONSTRAINT "student_fee_payment_student_id_profile_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."profile"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_fee_payment" ADD CONSTRAINT "student_fee_payment_academy_id_academy_id_fk" FOREIGN KEY ("academy_id") REFERENCES "public"."academy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "course_attachment" ADD CONSTRAINT "course_attachment_course_id_course_id_fk" FOREIGN KEY ("course_id") REFERENCES "public"."course"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "course" ADD CONSTRAINT "course_academy_id_academy_id_fk" FOREIGN KEY ("academy_id") REFERENCES "public"."academy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "course_tag_assignment" ADD CONSTRAINT "course_tag_assignment_course_id_course_id_fk" FOREIGN KEY ("course_id") REFERENCES "public"."course"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "course_tag_assignment" ADD CONSTRAINT "course_tag_assignment_tag_id_course_tag_id_fk" FOREIGN KEY ("tag_id") REFERENCES "public"."course_tag"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "course_tag" ADD CONSTRAINT "course_tag_group_id_course_tag_group_id_fk" FOREIGN KEY ("group_id") REFERENCES "public"."course_tag_group"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "district" ADD CONSTRAINT "district_state_id_state_id_fk" FOREIGN KEY ("state_id") REFERENCES "public"."state"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "state" ADD CONSTRAINT "state_country_code_country_code_fk" FOREIGN KEY ("country_code") REFERENCES "public"."country"("code") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_snapshot" ADD CONSTRAINT "user_snapshot_user_id_usr_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."usr"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "usr" ADD CONSTRAINT "usr_mobile_country_code_country_code_fk" FOREIGN KEY ("mobile_country_code") REFERENCES "public"."country"("code") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tnc_accepted" ADD CONSTRAINT "user_tnc_accepted_user_id_usr_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."usr"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tnc_accepted" ADD CONSTRAINT "user_tnc_accepted_tnc_version_id_user_tnc_version_id_fk" FOREIGN KEY ("tnc_version_id") REFERENCES "public"."user_tnc_version"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tnc_sign" ADD CONSTRAINT "user_tnc_sign_user_id_usr_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."usr"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tnc_sign" ADD CONSTRAINT "user_tnc_sign_tnc_version_id_user_tnc_version_id_fk" FOREIGN KEY ("tnc_version_id") REFERENCES "public"."user_tnc_version"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tnc_version" ADD CONSTRAINT "user_tnc_version_tnc_id_user_tnc_id_fk" FOREIGN KEY ("tnc_id") REFERENCES "public"."user_tnc"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "profile" ADD CONSTRAINT "profile_user_id_usr_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."usr"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "profile" ADD CONSTRAINT "profile_service_provider_id_profile_id_fk" FOREIGN KEY ("service_provider_id") REFERENCES "public"."profile"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_academy_employee_academy_id" ON "academy_staff" USING btree ("academy_id");--> statement-breakpoint
CREATE INDEX "idx_academy_name" ON "academy" USING btree ("name");--> statement-breakpoint
CREATE UNIQUE INDEX "idx_batch_attachment_batch_id_attachment_id" ON "batch_attachment" USING btree ("batch_id","attachment_id");--> statement-breakpoint
CREATE INDEX "idx_batch_attachment_attachment_id" ON "batch_attachment" USING btree ("attachment_id");--> statement-breakpoint
CREATE INDEX "idx_batch_event_batch_id" ON "batch_event" USING btree ("batch_id");--> statement-breakpoint
CREATE INDEX "idx_batch_student_batch_id" ON "batch_student" USING btree ("batch_id");--> statement-breakpoint
CREATE INDEX "idx_batch_student_student_id" ON "batch_student" USING btree ("student_id");--> statement-breakpoint
CREATE UNIQUE INDEX "idx_batch_student_batch_id_student_id" ON "batch_student" USING btree ("batch_id","student_id");--> statement-breakpoint
CREATE INDEX "idx_batch_course_id_start_date_created_at" ON "batch" USING btree ("course_id","start_date","created_at");--> statement-breakpoint
CREATE INDEX "idx_batch_teacher_id_start_date_created_at" ON "batch" USING btree ("teacher_id","start_date","created_at");--> statement-breakpoint
CREATE INDEX "idx_student_fee_payment_item_batch_id" ON "student_fee_payment_item" USING btree ("batch_id");--> statement-breakpoint
CREATE INDEX "idx_student_fee_payment_item_student_fee_payment_id" ON "student_fee_payment_item" USING btree ("student_fee_payment_id");--> statement-breakpoint
CREATE INDEX "idx_student_fee_payment_student_id_academy_id_status" ON "student_fee_payment" USING btree ("student_id","academy_id","status");--> statement-breakpoint
CREATE INDEX "idx_student_fee_payment_academy_id" ON "student_fee_payment" USING btree ("academy_id");--> statement-breakpoint
CREATE UNIQUE INDEX "idx_course_attachment_course_id_name" ON "course_attachment" USING btree ("course_id","name");--> statement-breakpoint
CREATE UNIQUE INDEX "idx_course_attachment_course_id_position" ON "course_attachment" USING btree ("course_id","position");--> statement-breakpoint
CREATE INDEX "idx_course_name" ON "course" USING btree ("name");--> statement-breakpoint
CREATE INDEX "idx_course_academy_id" ON "course" USING btree ("academy_id");--> statement-breakpoint
CREATE INDEX "idx_course_tag_assignment_course_id" ON "course_tag_assignment" USING btree ("course_id");--> statement-breakpoint
CREATE INDEX "idx_course_tag_assignment_tag_id" ON "course_tag_assignment" USING btree ("tag_id");--> statement-breakpoint
CREATE INDEX "idx_course_tag_group_id" ON "course_tag" USING btree ("group_id");--> statement-breakpoint
CREATE INDEX "idx_user_snapshot_user_id" ON "user_snapshot" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "idx_user_mobile_country_code" ON "usr" USING btree ("mobile_country_code");--> statement-breakpoint
CREATE INDEX "idx_user_tnc_reminder_scan" ON "usr" USING btree ("tnc_reminder_type","id");--> statement-breakpoint
CREATE INDEX "idx_user_google_refresh_token" ON "usr" USING btree ("google_refresh_token");--> statement-breakpoint
CREATE INDEX "idx_user_tnc_accepted_user_id" ON "user_tnc_accepted" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "idx_user_tnc_accepted_tnc_version_id" ON "user_tnc_accepted" USING btree ("tnc_version_id");--> statement-breakpoint
CREATE UNIQUE INDEX "idx_user_tnc_accepted_user_tnc_version" ON "user_tnc_accepted" USING btree ("user_id","tnc_version_id");--> statement-breakpoint
CREATE INDEX "idx_user_tnc_sign_user_id" ON "user_tnc_sign" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "idx_user_tnc_sign_tnc_version_id" ON "user_tnc_sign" USING btree ("tnc_version_id");--> statement-breakpoint
CREATE INDEX "user_tnc_version_tnc_id_idx" ON "user_tnc_version" USING btree ("tnc_id");--> statement-breakpoint
CREATE INDEX "idx_user_tnc_version_active" ON "user_tnc_version" USING btree ("id","effective_date") WHERE "user_tnc_version"."expiry_date" IS NULL;--> statement-breakpoint
CREATE INDEX "idx_app_command_aborted_at" ON "app_command" USING btree ("aborted_at");--> statement-breakpoint
CREATE INDEX "idx_app_command_flock_id" ON "app_command" USING btree ("flock","id");--> statement-breakpoint
CREATE INDEX "idx_profile_user_id_role" ON "profile" USING btree ("user_id","role");--> statement-breakpoint
CREATE INDEX "idx_profile_supporter_id" ON "profile" USING btree ("service_provider_id");
# How to Implement Pagination for List Rendering

This document explains how to implement cursor-based pagination for rendering lists in our application, following the pattern established in the course list pagination implementation.

## Overview

Our pagination system uses **cursor-based pagination** instead of offset-based pagination for better performance and consistency. This approach uses a cursor (a unique identifier) to mark the position in the dataset and fetch the next or previous set of records.

## Key Components

### 1. Zod schema and type definition for search

In shared/[entity]/[entity]-utils.shared.ts, define your search zod schema and type that extends the base `$PageSearch`. See `$CoursesSearch` in `src\shared\course\course-utils.shared.ts` for example - particularly the "for cursor-based pagination" fields.

### 2. Server-Side Implementation

#### Database Query with Cursor Logic

Closely look at `src\server\courses\courses\list\listCourses.server.ts` and `src\server\batches\batches\list\course-batches\listCourseBatches.server.ts` for examples.

Notice the following:
1. `pageFilter` function, which is used to filter the database query based on the search parameters.
2. `orderBy` variable, which is in line with the pagination cursor attributes.
3. `limit` clause.
4. `reverse` usage.

IMPORTANT:
1. If there are nullable fields in the ordering, follow the pattern in `src\server\courses\courses\list\listCourses.server.ts`. 
2. If there are non-nullable fields in the ordering, follow the pattern in `src\server\batches\batches\list\course-batches\listCourseBatches.server.ts`.
3. If there are timestamp fields in the ordering, you need to add/subtract 1 millisecond to the cursor value to avoid the issue of the cursor value being the same as the last value in the result set. See `src\server\batches\batches\list\course-batches\listCourseBatches.server.ts` for example.

### 3. Client-Side Implementation

#### List Component with Pagination

See `CourseList` component in `src\pages\courses\CoursesPage.tsx` for example. Notice carefully the inline comments in that.
## Testing

### Test Implementation

Write comprehensive tests covering the pagination logic. For example, see 'Pagination scenarios' in `src\server\courses\courses\list\listCoursesHandler.test.ts`.

## Key Implementation Notes

### 1. Ordering Strategy
- Use compound ordering, e.g.: `ORDER BY field_x DESC NULLS LAST, id ASC`
- For backward pagination, e.g.: `ORDER BY field_x NULLS FIRST, id DESC`
- Always include `id` for consistent tie-breaking

### 2. Cursor Logic
- **Forward pagination**: Get records after the cursor
- **Backward pagination**: Get records before the cursor, then reverse the result

### 3. Performance Considerations
- Create composite indexes on `(field_x, id)` for optimal performance
- Limit page size to reasonable values (max 100)
- Use `LIMIT` to prevent large result sets

### 4. Edge Cases
- Empty result sets
- First/last pages (no `beyondId`)
- `NULL` values in ordering fields

import { UUID } from 'crypto'

import { useState } from 'react'
import { navigate } from 'vike/client/router'

import { Batch } from '@/server/batches/batches/get/getBatch.server'
import { CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { batchPath, EditBatchForm } from '@/shared/batch/batch-utils.shared'
import { type OmniError } from '@/shared/common/error-utils.shared'
import { useMyAcademyQuery } from '@ui/academies/common/academy-queries'
import { ErrorAlert } from '@ui/common/alerts/ErrorAlert'
import { FormButtons, PageContent, PageLayout } from '@ui/common/form/page-layout'
import { ShowData } from '@ui/common/ShowData'
import { useCourseSuspenseQuery } from '@ui/courses/common/queries/course-queries'

import { useBatchSuspenseQuery, useUpdateBatchMutation } from '../../common/batch-queries'
import { BatchEditor } from '../../common/BatchEditor'

export const EditBatchPage = ({ courseId, batchId }: { courseId: UUID; batchId: UUID }) => {
  const { data: course, error: courseError } = useCourseSuspenseQuery(courseId)
  const { data: batch, error: batchError } = useBatchSuspenseQuery(batchId)

  return (
    <ShowData error={courseError || batchError} spinnerSize="1.25rem">
      <EditBatch course={course!} batch={batch!} />
    </ShowData>
  )
}

const EditBatch = ({ course, batch }: { course: CourseWithoutDescr; batch: Batch }) => {
  const { data: myAcademy, isPending: myAcademyIsPending, error: myAcademyError } = useMyAcademyQuery()

  // Get batch ID from route params
  const [formData, setFormData] = useState<EditBatchForm>({
    teacherId: batch.teacherId,
    startDate: batch.startDate,
    timezone: batch.timezone,
    billingCycle: batch.billingCycle,
    cycleCount: batch.cycleCount,
    graceDays: batch.graceDays,
    seatCount: batch.seatCount,
    admissionOpen: batch.admissionOpen,
    fee: { currency: 'INR', amount: batch.fee },
  })

  // Error state
  const [omniError, setOmniError] = useState<OmniError | undefined>()
  const batPath = batchPath(course, batch.id)

  // Update mutation
  const { mutate: updateBatch, isPending: updateBatchIsPending } = useUpdateBatchMutation(batch.id)

  // Form submission handler
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    updateBatch(formData, {
      onSuccess: () => {
        void navigate(batPath)
      },
      onError: (error) => {
        setOmniError(error.error)
      },
    })
  }

  return (
    <PageLayout title="Edit Batch" description={course.name} maxWidth="max-w-2xl">
      <ShowData isPending={myAcademyIsPending} error={myAcademyError} spinnerSize="1.25rem">
        {myAcademy?.id === course.academyId ?
          <form onSubmit={handleSubmit}>
            <PageContent>
              <BatchEditor
                formData={formData}
                setFormData={setFormData}
                error={omniError}
                setError={setOmniError}
                courseAcademyId={course.academyId}
              />
              <FormButtons
                omniError={omniError}
                onCancel={() => navigate(batPath)}
                isSubmitting={updateBatchIsPending}
              />
            </PageContent>
          </form>
        : <ErrorAlert>You are not a staff of the academy the course belongs to.</ErrorAlert>}
      </ShowData>
    </PageLayout>
  )
}

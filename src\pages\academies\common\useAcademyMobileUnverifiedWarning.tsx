import { academyPath } from '@/shared/academies/academy-utils.shared'

import { useMyAcademyQuery } from './academy-queries'

export const useAcademyMobileUnverifiedWarning = () => {
  const { data: academy } = useMyAcademyQuery()

  if (!academy || academy.mobileVerified) return null
  return () => (
    <span>
      Please{' '}
      <a href={`${academyPath(academy)}/verify-mobile`} className="link">
        verify
      </a>{' '}
      the mobile number of your academy.
    </span>
  )
}

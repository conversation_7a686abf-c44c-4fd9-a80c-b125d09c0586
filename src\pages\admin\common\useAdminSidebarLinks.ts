import { XCircleIcon } from '@heroicons/react/24/outline'

import { profileOk } from '@/shared/profiles/profile-check-utils.shared'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

export const useAdminSidebarLinks = () => {
  const { currentProfile } = useCurrentProfile()

  if (profileOk(currentProfile, { roleAnyOf: ['admin'] })) {
    return [{ name: 'Revoke Google Tokens', href: '/admin/revoke-all-google-tokens', icon: XCircleIcon }]
  }

  return []
}

import { UUID } from 'crypto'

import { and, eq, sql } from 'drizzle-orm'
import { Logger } from 'pino'

import { ContextProfile } from '@/server/common/auth-context-types.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { db } from '@/server/db/db'
import { academyStaffTable, batchRecommendationTable } from '@/server/db/schema'
import { courseTable } from '@/server/db/schema/course-schema'
import { getLogger } from '@/shared/common/logger.shared'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { profileOk } from '@/shared/profiles/profile-check-utils.shared'
import { ACADEMY_STAFF, Role } from '@/shared/profiles/role-utils.shared'

import { CourseAttachment, courseDetailColumns, courseItemColumns } from '../common/course-select-helper'

const findCourseByIdDaf = (log: Logger, courseId: UUID, full: boolean, isStaff: boolean) =>
  withLog(log, 'findCourseByIdDaf', () =>
    db
      .select(full ? courseDetailColumns(isStaff) : courseItemColumns)
      .from(courseTable)
      .leftJoin(batchRecommendationTable, eq(batchRecommendationTable.courseId, courseTable.id))
      .where(eq(courseTable.id, courseId))
      .then((rows) => rows[0]),
  )

const isPublishedDaf = (courseId: UUID) =>
  withLog(getLogger(), 'isPublishedDaf', () =>
    db
      .select({ publishedAt: courseTable.publishedAt })
      .from(courseTable)
      .where(eq(courseTable.id, courseId))
      .then((rows) => (rows[0]?.publishedAt ? true : false)),
  )

const isStaffOfCourseDaf = (courseId: UUID, profileId: UUID) =>
  withLog(getLogger(), 'isStaffOfCourseDaf', () =>
    db
      .select({ dummy: sql<number>`1` })
      .from(courseTable)
      .innerJoin(academyStaffTable, eq(academyStaffTable.academyId, courseTable.academyId))
      .where(and(eq(courseTable.id, courseId), eq(academyStaffTable.profileId, profileId)))
      .limit(1)
      .then((rows) => rows.length > 0),
  )

const isStaffOfCourse = async (courseId: UUID, profile: ContextProfile | undefined) => {
  if (!profile) return false
  if (!profileOk(profile)) return false
  if (!ACADEMY_STAFF.includes(profile.role)) return false
  return isStaffOfCourseDaf(courseId, profile.id)
}

export const getCourse = async (log: Logger, courseId: UUID, full: boolean, profile?: ContextProfile) => {
  const [isPublished, isStaff] = await Promise.all([isPublishedDaf(courseId), isStaffOfCourse(courseId, profile)])
  ensureExists(isPublished || isStaff, courseId, 'Course')

  const course = await findCourseByIdDaf(log, courseId, full, isStaff)
  ensureExists(course, courseId, 'Course')
  return course
}

export type ProfileGettingCourse = {
  profileId: UUID
  profileOk: boolean
  role: Role
}

export type GetCourse = typeof getCourse
export type CourseWithoutDescr = Awaited<ReturnType<GetCourse>>
export type CourseWithDescr = CourseWithoutDescr & { descr: string | null; attachments: CourseAttachment[] }

setQuery('getCourse', getCourse)

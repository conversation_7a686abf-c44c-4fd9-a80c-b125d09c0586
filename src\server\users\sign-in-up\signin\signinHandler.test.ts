import { DateTime } from 'luxon'
import { http, HttpResponse } from 'msw'
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'

import app from '@/hono-entry'
import { env } from '@/server/common/env.server'
import { db } from '@/server/db/db'
import { profileTable } from '@/server/db/schema/profile-schema'
import { userTable } from '@/server/db/schema/user-schema'
import { initDb } from '@/server/test/initDb'
import { mockServer } from '@/server/test/mockServer'
import { parseAccessToken } from '@/server/users/common/auth-token-utils.server'
import { UUID_REGEX } from '@/shared/common/common-utils.shared'
import { googleOauthScopes } from '@/shared/users/googleOauthScopes.shared'
import { AuthTokenData } from '@/shared/users/user-utils.shared'

import { GOOGLE_OPENID_ENDPOINT } from '../getGoogleUserInfo'
import { fallbackLocale } from '../signin-up-utils.server'

const GOOGLE_USERINFO_ENDPOINT = 'https://openidconnect.googleapis.com/v1/userinfo'
const oauthCode = 'google-oauth-code'

const googleUserInfo = {
  sub: 'google-id',
  name: 'Sanjay Patel',
  picture: 'https://google.com/my-picture',
  email: '<EMAIL>',
  email_verified: true,
  language: 'userInfoLanguage' as string | undefined,
} as const

type GoogleUserInfo = typeof googleUserInfo

const googleTokens = {
  access_token: 'google_access_token',
  refresh_token: 'google_refresh_token',
} as const

type GoogleTokens = typeof googleTokens

const mocked = {
  now: DateTime.now(),
} as const

const user = {
  id: crypto.randomUUID(),
  googleId: googleUserInfo.sub,
  googleRefreshToken: 'old-refresh-token',
  name: 'Old Name',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/old-picture',
  language: 'oldLanguage',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '1234567890',
  mobileVerified: false,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

type User = typeof user

// This demonstrates a way to mock a module for testing.
// However, the other way demonostrated in src\shared\common\payment-utils\getCurrentCycle.test.ts is generally preferable.
vi.mock(import('@/shared/common/date-utils-basic.shared'), async (importOriginal) => {
  const mod = await importOriginal() // type is inferred
  return {
    ...mod,
    utc: () => mocked.now,
  }
})

const mockGoogleCalls = (userInfo: GoogleUserInfo, tokens: GoogleTokens) => {
  // https://mswjs.io/docs/api/setup-server/use
  mockServer.use(
    http.get(
      GOOGLE_OPENID_ENDPOINT,
      () =>
        HttpResponse.json({
          userinfo_endpoint: GOOGLE_USERINFO_ENDPOINT,
        }),
      { once: true },
    ),
  )

  // https://mswjs.io/docs/api/http-response
  mockServer.use(http.get(GOOGLE_USERINFO_ENDPOINT, () => HttpResponse.json(userInfo), { once: true }))
  mockServer.use(http.post('https://oauth2.googleapis.com/token', () => HttpResponse.json(tokens), { once: true }))

  // https://any-api.com/googleapis_com/oauth2/docs/Miscellaneous/oauth2_tokeninfo
  mockServer.use(
    http.post(
      'https://oauth2.googleapis.com/tokeninfo',
      () => HttpResponse.json({ scope: googleOauthScopes.join(' ') }),
      { once: true },
    ),
  )
}

const signInUser = async (body = `{ "code": "${oauthCode}" }`, acceptLanguage?: string) => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  }

  if (acceptLanguage) {
    headers['Accept-Language'] = acceptLanguage
  }

  return app.request(
    '/api/users/signin',
    {
      method: 'POST',
      body,
      headers,
    },
    {},
  )
}

const mockGoogleCallsWithError = () => {
  mockServer.use(http.post('https://oauth2.googleapis.com/token', () => new HttpResponse(null, { status: 401 })))
}

const mockGoogleCallsWithMissingScopes = () => {
  mockServer.use(
    http.post(
      'https://oauth2.googleapis.com/tokeninfo',
      () => HttpResponse.json({ scope: 'email' }), // Missing required scopes
    ),
  )
}

const assertValidTokenResponse = async (token: AuthTokenData, validityDays = 30) => {
  const expectedValidUntil = mocked.now.plus({ days: validityDays }).startOf('second')
  expect(token.userId, 'Response userId check').toMatch(UUID_REGEX)
  expect(token.accessTokenValidUntil, 'Access token validity').toBe(expectedValidUntil.toISO())

  const accessToken = await parseAccessToken(env.JWT_SECRET_KEY, token.accessToken)
  expect(accessToken.sub, 'Access token sub').toBe(token.userId)
  expect(accessToken.aud, 'Access token aud').toBe('auth')
  expect(accessToken.exp, 'Access token exp lower boundary').toBe(expectedValidUntil.toSeconds())
}

const assertValidUserRecord = async (
  oldUser: User,
  userInfo: GoogleUserInfo,
  tokens: GoogleTokens,
  language = fallbackLocale,
) => {
  const users = await db.select().from(userTable)
  expect(users.length, 'User count').toBe(1)

  const updatedUser = users[0]
  expect(updatedUser.id, 'User id').toBe(oldUser.id)
  expect(updatedUser.googleId, 'User googleId').toBe(userInfo.sub)
  expect(updatedUser.googleRefreshToken, 'User googleRefreshToken').toBe(user.googleRefreshToken)
  expect(updatedUser.name, 'User name').toBe(userInfo.name)
  expect(updatedUser.email, 'User email').toBe(userInfo.email)
  expect(updatedUser.emailVerified, 'User emailVerified').toBe(userInfo.email_verified)
  expect(updatedUser.googlePictureUrl, 'User googlePictureUrl').toBe(userInfo.picture)
  expect(updatedUser.language, 'User language').toBe(language)
  expect(updatedUser.tokensValidFrom, 'User tokensValidFrom').toStrictEqual(oldUser.tokensValidFrom)
  expect(updatedUser.suspendedAt, 'User suspendedAt').toBeNull()
}

describe('signInHandler', () => {
  afterEach(() => {
    vi.clearAllMocks()
    mockServer.resetHandlers()
  })

  beforeEach(async () => {
    await initDb()
  })

  test('should return 404 when user does not exist', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)

    // when
    const res = await signInUser()

    // then
    expect(res.status, 'Status check').toBe(404)
    await expect(res).toBeError(404, [
      {
        code: 'custom',
        path: [],
        message: 'User not found, or you are not authorized to access it.',
      },
    ])
  })

  test('should sign in an existing user and update user information from google', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)

    // Pre-create user in database
    await db.insert(userTable).values(user)

    // when
    const res = await signInUser(`{ "code": "${oauthCode}" }`, 'frontendLanguage')

    // then
    expect(res.status, 'Status check').toBe(200)
    const token = (await res.json()) as AuthTokenData
    await assertValidTokenResponse(token)
    // Check that user is updated with new values from Google
    await assertValidUserRecord(user, googleUserInfo, googleTokens, googleUserInfo.language)
  })

  test('should respect the validityDays parameter for existing user', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)

    // Pre-create user in database
    await db.insert(userTable).values(user)

    // when
    const res = await signInUser(`{ "code": "${oauthCode}", "validityDays": 5 }`)

    // then
    expect(res.status, 'Status check').toBe(200)
    const token = (await res.json()) as AuthTokenData
    await assertValidTokenResponse(token, 5)
  })

  test('should save the acceptLanguage header if google does not provide a language', async () => {
    // given
    mockGoogleCalls({ ...googleUserInfo, language: undefined }, googleTokens)
    const frontendLanguage = 'frontendLanguage'

    // Pre-create user in database
    await db.insert(userTable).values(user)

    // when
    const res = await signInUser(`{ "code": "${oauthCode}" }`, frontendLanguage)

    // then
    expect(res.status, 'Status check').toBe(200)
    await assertValidUserRecord(user, googleUserInfo, googleTokens, frontendLanguage)
  })

  test('should allow admin to get token for another user, but only for 1 day', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)

    // Create admin user
    const adminUser = {
      ...user,
      id: crypto.randomUUID(),
      email: '<EMAIL>',
      mobileVerified: true,
    }
    await db.insert(userTable).values(adminUser)

    // Create admin profile
    await db.insert(profileTable).values({
      id: crypto.randomUUID(),
      userId: adminUser.id,
      role: 'admin',
      displayName: 'Admin User',
      approvedAt: DateTime.now().toJSDate(),
      tncsAcceptedAt: DateTime.now().toJSDate(),
    })

    await db.insert(userTable).values({ ...user, googleId: 'regular-google-id' })

    // when
    const res = await signInUser(`{ "code": "${oauthCode}", "forUserId": "${user.id}" }`)

    // then
    expect(res.status, 'Status check').toBe(200)
    const token = (await res.json()) as AuthTokenData
    expect(token.userId, 'Token should be for regular user').toBe(user.id)
    await assertValidTokenResponse(token, 1)
  })

  test('should fail when non-admin tries to get token for another user', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)

    // Create admin user
    const nonAdminUser = {
      ...user,
      id: crypto.randomUUID(),
      email: '<EMAIL>',
      mobileVerified: true,
    }
    await db.insert(userTable).values(nonAdminUser)

    // Create admin profile
    await db.insert(profileTable).values({
      id: crypto.randomUUID(),
      userId: nonAdminUser.id,
      role: 'principal',
      displayName: 'Non-admin User',
      approvedAt: DateTime.now().toJSDate(),
      tncsAcceptedAt: DateTime.now().toJSDate(),
    })

    await db.insert(userTable).values({ ...user, googleId: 'regular-google-id' })

    // when
    const res = await signInUser(`{ "code": "${oauthCode}", "forUserId": "${user.id}" }`)

    // then
    expect(res.status, 'Status check').toBe(403)
    await expect(res).toBeError(403, [
      {
        path: [],
        code: 'custom',
        message: 'You are not an approved admin or manager',
      },
    ])
  })

  test('should fail when admin tries to get token for another admin', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)

    // Create admin user
    const adminUser = {
      ...user,
      id: crypto.randomUUID(),
      email: '<EMAIL>',
      mobileVerified: true,
    }
    await db.insert(userTable).values(adminUser)

    // Create admin profile
    await db.insert(profileTable).values({
      id: crypto.randomUUID(),
      userId: adminUser.id,
      role: 'admin',
      displayName: 'Admin User',
      approvedAt: DateTime.now().toJSDate(),
      tncsAcceptedAt: DateTime.now().toJSDate(),
    })

    await db.insert(userTable).values({ ...user, googleId: 'another-admin-google-id' })
    await db.insert(profileTable).values({
      id: crypto.randomUUID(),
      userId: user.id,
      role: 'admin',
      displayName: 'Another Admin User',
      approvedAt: DateTime.now().toJSDate(),
      tncsAcceptedAt: DateTime.now().toJSDate(),
    })

    // when
    const res = await signInUser(`{ "code": "${oauthCode}", "forUserId": "${user.id}" }`)

    // then
    expect(res.status, 'Status check').toBe(403)
    await expect(res).toBeError(403, [
      {
        path: [],
        code: 'custom',
        message: 'Target user is an admin or manager',
      },
    ])
  })

  test('should handle invalid Google token', async () => {
    mockGoogleCallsWithError()
    const res = await signInUser()
    expect(res.status).toBe(500)
    await expect(res).toBeError(500, [
      {
        path: [],
        code: 'custom',
        message: 'Failed to authenticate with Google. Please try again.',
      },
    ])
  })

  test('should handle missing required Google scopes', async () => {
    mockGoogleCalls(googleUserInfo, googleTokens)
    mockGoogleCallsWithMissingScopes()
    const res = await signInUser()
    expect(res.status).toBe(422)
    await expect(res).toBeError(422, [
      {
        path: [],
        code: 'custom',
        message: 'Please allow us to access your email, profile, calendar and events',
      },
    ])
  })

  test('should handle malformed request body', async () => {
    const res = await signInUser('invalid-json')
    expect(res.status).toBe(400)
  })

  test('should handle missing code in request', async () => {
    const res = await signInUser('{}')
    expect(res.status).toBe(400)
    await expect(res).toBeError(400, [
      {
        path: ['code'],
        code: 'invalid_type',
        message: 'Required',
      },
    ])
  })
})

import { and, eq, isNotNull } from 'drizzle-orm'
import pino from 'pino'

import { db } from '@/server/db/db'
import { academyStaffTable } from '@/server/db/schema/academy-schema'
import { profileTable } from '@/server/db/schema/profile-schema'
import { userTable } from '@/server/db/schema/user-schema'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { ProfilesSearch } from '@/shared/profiles/profile-utils.shared'

const listProfilesDaf = (log: pino.Logger, search: ProfilesSearch) => {
  const profileIdFilter = search.profileId ? eq(profileTable.id, search.profileId) : undefined
  const academyIdFilter =
    search.academyId ?
      and(isNotNull(academyStaffTable.academyId), eq(academyStaffTable.academyId, search.academyId))
    : undefined
  const roleFilter = search.role ? eq(profileTable.role, search.role) : undefined

  return withLog(log, 'listProfilesDaf', () =>
    db
      .select({
        id: profileTable.id,
        displayName: profileTable.displayName,
        role: profileTable.role,
        googlePictureUrl: userTable.googlePictureUrl,
      })
      .from(profileTable)
      .innerJoin(userTable, eq(profileTable.userId, userTable.id))
      .leftJoin(academyStaffTable, eq(profileTable.id, academyStaffTable.profileId))
      .where(and(profileIdFilter, academyIdFilter, roleFilter))
      .orderBy(profileTable.id)
      .limit(search.academyId ? 100 : 10),
  )
}

export const listProfiles = async (log: pino.Logger, search: ProfilesSearch) => listProfilesDaf(log, search)

export type ListProfiles = typeof listProfiles
export type TinyProfile = Awaited<ReturnType<ListProfiles>>[number]
setQuery('listProfiles', listProfiles)

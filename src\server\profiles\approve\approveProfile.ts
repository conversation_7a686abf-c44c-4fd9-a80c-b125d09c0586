import { UUID } from 'crypto'

import { and, eq, isNotNull } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfile } from '@/server/common/error/ensure-profile.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { profileTable } from '@/server/db/schema/profile-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'

const approveProfileDaf = (log: pino.Logger, myProfileId: UUID, targetProfileId: UUID) =>
  withLog(log, 'approveProfileDaf', () =>
    db
      .update(profileTable)
      .set({
        approvedAt: utc().toJSDate(),
        updatedAt: utc().toJSDate(),
        updateRemarks: 'approved',
      })
      .where(
        and(
          eq(profileTable.id, targetProfileId),
          isNotNull(profileTable.serviceProviderId),
          eq(profileTable.serviceProviderId, myProfileId),
          isNotNull(profileTable.submittedForApprovalAt),
        ),
      ),
  )

export const approveProfile = async (c: Ctx, targetProfileId: UUID) => {
  ensureUser(c.user)
  ensureProfile(c.profile)

  const result = await approveProfileDaf(c.log, c.profile.id, targetProfileId)
  ensure(result.count > 0, {
    statusCode: 404,
    issueMessage: 'Profile not found or does not belong to you or not submitted for approval yet.',
    logMessage: `User ${c.profile.id} attempted to approve profile ${targetProfileId} but it was not found or does not belong to them or not submitted for approval yet.`,
  })
}

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $PayStudentFeePaymentForm } from '@/shared/batch/batch-utils.shared'
import { $HasId } from '@/shared/common/common-utils.shared'

import { payStudentFeePayment } from './payStudentFeePayment'

export const payStudentFeePaymentHandler = new Hono<HonoVars>().post(
  '/',
  zValidator('param', $HasId),
  zValidator('json', $PayStudentFeePaymentForm),
  async (c) => {
    const { id: studentFeePaymentId } = c.req.valid('param')
    const form = c.req.valid('json')
    await payStudentFeePayment(getCtx(c), studentFeePaymentId, form)
    return c.body(null, 204)
  },
)

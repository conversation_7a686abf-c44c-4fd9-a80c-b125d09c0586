import { and, eq, isNull } from 'drizzle-orm'
import { Logger } from 'pino'

import { ensure } from '@/server/common/error/ensure.server'
import { db } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { countryTable } from '@/server/db/schema/master-schema'
import { withLog } from '@/shared/common/withLog.shared'
import { whetherRequiredTnCsAccepted, type UserForm } from '@/shared/users/user-utils.shared'

import { getUserTnCs } from '../tncs/get-user-tncs/getUserTnCs'

import { getGoogleUserInfo } from './getGoogleUserInfo'

const findMobileCountryDaf = (log: Logger, mobileCountryCode: string) =>
  withLog(log, 'findMobileCountryDaf', () =>
    db.query.countryTable.findFirst({
      ...dummyColumn,
      where: and(eq(countryTable.code, mobileCountryCode), isNull(countryTable.suspendedAt)),
    }),
  )

export const validateSignupOrUpdate = async (log: Logger, form: UserForm, acceptLanguage?: string) => {
  // Parallelize Google auth and mobile country checks
  const [{ refreshToken, userInfo }, mobileCountry] = await Promise.all([
    getGoogleUserInfo(log, form.authCode, acceptLanguage),
    findMobileCountryDaf(log, form.mobileCountryCode),
  ])

  ensure(mobileCountry, {
    statusCode: 422,
    fieldName: 'mobileCountryCode',
    issueMessage: 'Mobile country not found or suspended.',
    logMessage: `Mobile country ${form.mobileCountryCode} not found or suspended.`,
  })

  ensure(mobileCountry, {
    statusCode: 422,
    fieldName: 'mobile',
    issueMessage: `Mobile country ${form.mobileCountryCode} not found or suspended.`,
    logMessage: `Mobile country ${form.mobileCountryCode} not found or suspended.`,
  })

  const applicableTnCs = await getUserTnCs(log)

  ensure(whetherRequiredTnCsAccepted(applicableTnCs, form.acceptedTnCs), {
    statusCode: 422,
    issueMessage: 'All required terms and conditions must be accepted.',
    logMessage: `User attempted to sign up accepting only TnCs ${form.acceptedTnCs} out of ${JSON.stringify(applicableTnCs)}`,
  })

  return { userInfo, refreshToken, applicableTnCs }
}

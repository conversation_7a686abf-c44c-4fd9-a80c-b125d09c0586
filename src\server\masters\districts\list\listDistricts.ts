import { eq } from 'drizzle-orm'

import { db } from '@/server/db/db'
import { districtTable } from '@/server/db/schema/master-schema'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'

import type { UUID } from 'crypto'
import type { Logger } from 'pino'

const findDistrictsDaf = (log: Logger, stateId: UUID) =>
  withLog(log, 'findDistrictsDaf', () =>
    db.query.districtTable.findMany({
      columns: {
        id: true,
        name: true,
      },
      where: eq(districtTable.stateId, stateId),
      orderBy: districtTable.name,
    }),
  )

export const listDistricts = (log: Logger, stateId: UUID) => findDistrictsDaf(log, stateId)
export type ListDistricts = typeof listDistricts
export type District = Awaited<ReturnType<ListDistricts>>[number]
setQuery('listDistricts', listDistricts)

import { UUID } from 'crypto'

import { pino } from 'pino'

import { addCommand, Command } from '@/server/common/command/processCommands'
import { getCalApiForRefreshToken } from '@/server/common/google/calendar/calendar-utils'
import { isGoogleError } from '@/server/common/google/google-utils'
import { getLogger } from '@/shared/common/logger.shared'

import { composeGoogleEventDataForBatchEvent } from '../common/composeGoogleEventDataForBatchEvent'

export const ADD_GOOGLE_EVENT_FOR_BATCH_COMMAND = 'addGoogleEventForBatch'

export type AddGoogleEventForBatchCommandData = {
  eventId: UUID
}

async function executeCommand(log: pino.Logger, command: Command<AddGoogleEventForBatchCommandData>) {
  const data = command.data
  const eventAndRefreshToken = await composeGoogleEventDataForBatchEvent(log, data.eventId)
  if (!eventAndRefreshToken) {
    return
  }
  const [refreshToken, event] = eventAndRefreshToken
  const calApi = await getCalApiForRefreshToken(refreshToken)

  try {
    await calApi.events.insert({
      // https://developers.google.com/calendar/api/v3/reference/events/insert
      calendarId: 'primary',
      sendUpdates: 'all',
      conferenceDataVersion: 1,
      requestBody: event,
    })
  } catch (e) {
    if (isGoogleError(e, 'duplicate')) {
      // Repeat call
      log.warn(`Google Event already created for batch id ${data.eventId}. Event id=${event.id}`)
    } else {
      throw e
    }
  }
}

addCommand(ADD_GOOGLE_EVENT_FOR_BATCH_COMMAND, executeCommand)
getLogger().info(`Loaded ${import.meta.url}`)

import {
  DeleteObjectCommand,
  GetObjectCommand,
  GetObjectTaggingCommand,
  PutObjectCommand,
  PutObjectTaggingCommand,
  S3Client,
} from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'

import { env } from '@/server/common/env.server'
import {
  S3_BUCKET,
  S3_DOWNLOAD_URL_EXPIRY_SECONDS,
  S3_REGION,
  S3_UPLOAD_URL_EXPIRY_SECONDS,
} from '@/shared/common/s3-utils.shared'

const s3 = new S3Client({
  region: S3_REGION,
  credentials: { accessKeyId: env.AWS_ACCESS_KEY, secretAccessKey: env.AWS_SECRET_KEY },
})

/**
 * Generates a presigned URL for uploading a file to S3
 * @param path The key name for the file in S3
 * @param contentType Optional MIME type of the file
 * @param expiresIn Time in seconds until the URL expires (default: 60 seconds)
 * @returns Presigned URL for file upload
 */
export const generateUploadURL = async (path: string, contentType: string, free: boolean): Promise<string> => {
  const command = new PutObjectCommand({
    Bucket: S3_BUCKET,
    Key: path,
    ContentType: contentType,
    ...(free && { Tagging: 'public=yes' }),
  })

  const signedUrl = await getSignedUrl(s3, command, {
    expiresIn: S3_UPLOAD_URL_EXPIRY_SECONDS,
    // Sign the contentType header if it's provided
    ...(contentType && { signableHeaders: new Set(['content-type']) }),
  })

  return signedUrl
}

/**
 * Generates a presigned URL for downloading a file from S3
 * @param path The key name of the file in S3
 * @returns Presigned URL for file download
 */
export const generateDownloadURL = async (path: string): Promise<string> => {
  const command = new GetObjectCommand({
    Bucket: S3_BUCKET,
    Key: path,
  })

  const signedUrl = await getSignedUrl(s3, command, {
    expiresIn: S3_DOWNLOAD_URL_EXPIRY_SECONDS,
  })

  return signedUrl
}

export const removeS3Object = async (path: string) => {
  const command = new DeleteObjectCommand({
    Bucket: S3_BUCKET,
    Key: path,
  })

  await s3.send(command)
}

// Make an existing object public
export async function makeObjectPublic(key: string) {
  const command = new PutObjectTaggingCommand({
    Bucket: S3_BUCKET,
    Key: key,
    Tagging: {
      TagSet: [
        {
          Key: 'public',
          Value: 'yes',
        },
      ],
    },
  })

  await s3.send(command)
}

// Make an existing object private
export async function makeObjectPrivate(key: string) {
  // Get current tags to preserve any other tags
  const getTagsCommand = new GetObjectTaggingCommand({
    Bucket: S3_BUCKET,
    Key: key,
  })

  const currentTags = await s3.send(getTagsCommand)

  // Remove the public tag but keep all other tags
  const updatedTags = currentTags.TagSet?.filter((tag) => tag.Key !== 'public') || []

  const command = new PutObjectTaggingCommand({
    Bucket: S3_BUCKET,
    Key: key,
    Tagging: {
      TagSet: updatedTags,
    },
  })

  await s3.send(command)
}

// Check if an object is public
export async function isObjectPublic(key: string): Promise<boolean> {
  try {
    const command = new GetObjectTaggingCommand({
      Bucket: S3_BUCKET,
      Key: key,
    })

    const tagsResponse = await s3.send(command)
    return tagsResponse.TagSet?.some((tag) => tag.Key === 'public' && tag.Value === 'yes') || false
  } catch (error) {
    console.error('Error checking if object is public:', error)
    return false
  }
}

// Generate presigned URL for private objects
export async function getPresignedUrl(key: string) {
  const command = new GetObjectCommand({
    Bucket: S3_BUCKET,
    Key: key,
  })

  return getSignedUrl(s3, command, {
    expiresIn: S3_DOWNLOAD_URL_EXPIRY_SECONDS,
  })
}

// For uploading public objects
export async function uploadPublicObject(key: string, body: Buffer | Uint8Array | string, contentType: string) {
  const command = new PutObjectCommand({
    Bucket: S3_BUCKET,
    Key: key,
    Body: body,
    ContentType: contentType,
    Tagging: 'public=yes', // This tag makes the object publicly accessible per our bucket policy
  })

  await s3.send(command)
}

// For uploading private objects
export async function uploadPrivateObject(key: string, body: Buffer | Uint8Array | string, contentType: string) {
  const command = new PutObjectCommand({
    Bucket: S3_BUCKET,
    Key: key,
    Body: body,
    ContentType: contentType,
    // No tagging means private by default
  })

  await s3.send(command)
}

export const s3Flock = () => `s3`

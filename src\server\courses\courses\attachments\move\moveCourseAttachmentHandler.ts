import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'
import { $MoveCourseAttachmentForm } from '@/shared/course/course-utils.shared'

import { moveCourseAttachment } from './moveCourseAttachment'

export const moveCourseAttachmentHandler = new Hono<HonoVars>().put(
  '/',
  zValidator('param', $HasId),
  zValidator('json', $MoveCourseAttachmentForm),
  async (c) => {
    const attachment = c.req.valid('param')
    const form = c.req.valid('json')

    await moveCourseAttachment(getCtx(c), attachment.id, form.beforePosition)
    return c.body(null, 204)
  },
)

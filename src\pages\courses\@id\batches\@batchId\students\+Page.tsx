import { UUID } from 'crypto'

import { usePageContext } from 'vike-react/usePageContext'

import { extractUuid } from '@/shared/common/common-utils.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'
import { WithProfile } from '@ui/profiles/common/WithProfile'

import { BatchStudentsPage } from './BatchStudentsPage'

export default () => {
  const pageContext = usePageContext()
  const courseId = extractUuid(pageContext.routeParams.id)
  const batchId = pageContext.routeParams.batchId as UUID

  return (
    <WithProfile roleAnyOf={ACADEMY_STAFF}>
      <BatchStudentsPage courseId={courseId} batchId={batchId} />
    </WithProfile>
  )
}

import { and, eq, isNotNull, isNull } from 'drizzle-orm'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfile } from '@/server/common/error/ensure-profile.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { profileTable } from '@/server/db/schema/profile-schema'
import { withLog } from '@/shared/common/withLog.shared'
import { ROLES_PROVIDING_SERVICE } from '@/shared/profiles/role-utils.shared'

import type { UUID } from 'crypto'
import type { Logger } from 'pino'

const findProfilesToApproveDaf = (log: Logger, approverId: UUID) =>
  withLog(log, 'findProfilesToApproveDaf', async () =>
    db.query.profileTable.findMany({
      columns: {
        id: true,
        displayName: true,
        role: true,
      },
      where: and(
        isNotNull(profileTable.serviceProviderId),
        eq(profileTable.serviceProviderId, approverId),
        isNotNull(profileTable.submittedForApprovalAt),
        isNull(profileTable.approvedAt),
      ),
    }),
  )

export type ProfilesToApprove = Awaited<ReturnType<typeof findProfilesToApproveDaf>>
export const getProfilesToApprove = async (c: Ctx): Promise<ProfilesToApprove> => {
  ensureUser(c.user, { ignoreEmailVerification: true, ignoreMobileVerification: true })
  ensureProfile(c.profile, { ignoreSuspension: true, ignoreApproval: true })

  return ROLES_PROVIDING_SERVICE.includes(c.profile.role) ? findProfilesToApproveDaf(c.log, c.profile.id) : []
}

#!/usr/bin/env pwsh
# This script simplifies logging into the DigitalOcean Docker registry

# Check if the DO_REGISTRY_PASSWORD environment variable is set
if (-not $env:DO_REGISTRY_PASSWORD) {
    Write-Host "Please set the DO_REGISTRY_PASSWORD environment variable first:" -ForegroundColor Yellow
    Write-Host '$env:DO_REGISTRY_PASSWORD = "your-digitalocean-api-token"' -ForegroundColor Cyan
    exit 1
}

# Login to Docker registry
Write-Host "Logging into DigitalOcean Docker registry..." -ForegroundColor Green
docker login registry.digitalocean.com -u <EMAIL> -p $env:DO_REGISTRY_PASSWORD

if ($LASTEXITCODE -eq 0) {
    Write-Host "Successfully logged in to the DigitalOcean Docker registry." -ForegroundColor Green
    Write-Host "You can now push Docker images using: pnpm push" -ForegroundColor Green
} else {
    Write-Host "Failed to log in to the DigitalOcean Docker registry. Please check your credentials." -ForegroundColor Red
} 
import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { DateTime } from 'luxon'
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'

import app from '@/hono-entry'
import { env } from '@/server/common/env.server'
import { db } from '@/server/db/db'
import {
  academyStaffTable,
  academyTable,
  batchEventTable,
  batchRecommendationTable,
  batchTable,
  courseTable,
  profileTable,
  userTable,
  userTncAcceptedTable,
  userTncSignTable,
} from '@/server/db/schema'
import { initDb, SUNDARGARH_DISTRICT_ID, TNC_PRIVACY_V1_ID, TNC_TOS_V1_ID } from '@/server/test/initDb'
import { createAccessToken } from '@/server/users/common/auth-token-utils.server'
import { PROFILE_HEADER } from '@/shared/profiles/profile-utils.shared'

const mocked = {
  now: DateTime.now(),
} as const

const testUser = {
  id: crypto.randomUUID() as UUID,
  googleId: 'test-google-id',
  googleRefreshToken: null,
  name: 'Test Teacher',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/picture',
  language: 'en-US',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '9876543210',
  mobileVerified: true,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

const testProfile = {
  id: crypto.randomUUID() as UUID,
  userId: testUser.id,
  role: 'teacher' as const,
  displayName: 'Test Teacher',
  approvedAt: mocked.now.toJSDate(),
  tncsAcceptedAt: mocked.now.toJSDate(),
} as const

const testAcademy = {
  id: crypto.randomUUID() as UUID,
  name: 'Test Academy',
  email: '<EMAIL>',
  emailVerified: true,
  mobileCountryCode: 'IN',
  mobile: '9876543210',
  mobileVerified: true,
  currency: 'INR' as const,
  upiId: 'test@upi',
  districtId: SUNDARGARH_DISTRICT_ID as UUID,
  pincode: '770001',
  area: 'Test Area',
  tncsAcceptedAt: mocked.now.toJSDate(),
  approvedAt: mocked.now.toJSDate(),
} as const

const testCourse = {
  id: crypto.randomUUID() as UUID,
  name: 'Mathematics Class 10',
  descr: '<p>Advanced mathematics course</p>\n',
  academyId: testAcademy.id,
  publishedAt: mocked.now.toJSDate(), // Published course
  creationRemarks: `${testProfile.id} profile created this course`,
} as const

const unpublishedCourse = {
  id: crypto.randomUUID() as UUID,
  name: 'Physics Class 10',
  descr: '<p>Physics course</p>\n',
  academyId: testAcademy.id,
  publishedAt: null, // Unpublished course
  creationRemarks: `${testProfile.id} profile created this course`,
} as const

const publishedBatch = {
  id: crypto.randomUUID() as UUID,
  courseId: testCourse.id,
  teacherId: testProfile.id,
  fee: 5000,
  billingCycle: 'months' as const,
  graceDays: 3,
  startDate: '2024-01-15',
  timezone: 'Asia/Kolkata',
  cycleCount: 12,
  over: false,
  seatCount: 30,
  studentCount: 0,
  admissionOpen: true,
  creationRemarks: `${testProfile.id} profile created this batch`,
}

const unpublishedBatch = {
  id: crypto.randomUUID() as UUID,
  courseId: unpublishedCourse.id,
  teacherId: testProfile.id,
  fee: 4000,
  billingCycle: 'months' as const,
  graceDays: 5,
  startDate: '2024-02-01',
  timezone: 'Asia/Kolkata',
  cycleCount: 10,
  over: false,
  seatCount: 25,
  studentCount: 0,
  admissionOpen: true,
  creationRemarks: `${testProfile.id} profile created this batch`,
}

const batchEvent = {
  id: crypto.randomUUID() as UUID,
  batchId: publishedBatch.id,
  at: '18:00',
  durationMinutes: 60,
  days: ['monday', 'wednesday', 'friday'],
  eventType: 'meet',
  eventXid: 'google-calendar-event-id',
}

// Mock date utilities to have consistent test results
vi.mock(import('@/shared/common/date-utils-basic.shared'), async (importOriginal) => {
  const mod = await importOriginal()
  return {
    ...mod,
    utc: () => mocked.now,
  }
})

const setupBasicTestData = async () => {
  await db.insert(userTable).values(testUser)

  // Add TnC acceptance records
  const acceptedTnCs = [TNC_TOS_V1_ID, TNC_PRIVACY_V1_ID] as const
  for (const tncId of acceptedTnCs) {
    await db.insert(userTncSignTable).values({
      id: crypto.randomUUID(),
      userId: testUser.id,
      tncVersionId: tncId,
      accepted: true,
    })

    await db.insert(userTncAcceptedTable).values({
      id: crypto.randomUUID(),
      userId: testUser.id,
      tncVersionId: tncId,
    })
  }

  await db.insert(profileTable).values(testProfile)
  await db.insert(academyTable).values(testAcademy)
  await db.insert(academyStaffTable).values({
    profileId: testProfile.id,
    academyId: testAcademy.id,
  })
  await db.insert(courseTable).values([testCourse, unpublishedCourse])
  await db.insert(batchTable).values(publishedBatch)
  await db.insert(batchTable).values(unpublishedBatch)
  await db.insert(batchEventTable).values(batchEvent)
}

const setupBatchRecommendation = async () => {
  await db.insert(batchRecommendationTable).values({
    courseId: testCourse.id,
    batchId: publishedBatch.id,
  })
}

const makeUnauthenticatedRequest = async (batchId: UUID) => {
  return app.request(`/api/batches/${batchId}`, { method: 'GET' }, {})
}

const makeAuthenticatedRequest = async (batchId: UUID, accessToken?: string, profileId?: UUID) => {
  const token = accessToken || (await createAccessToken(env.JWT_SECRET_KEY, testUser.id))
  const headers: Record<string, string> = {
    Authorization: `Bearer ${token}`,
  }

  if (profileId) {
    headers[PROFILE_HEADER] = profileId
  }

  return app.request(`/api/batches/${batchId}`, { method: 'GET', headers }, {})
}

const assertValidBatchResponse = (
  batch: Record<string, unknown>,
  expectedBatch: typeof publishedBatch | typeof unpublishedBatch,
) => {
  expect(batch.id).toBe(expectedBatch.id)
  expect(batch.courseId).toBe(expectedBatch.courseId)
  expect(batch.teacherId).toBe(expectedBatch.teacherId)
  expect(batch.fee).toBe(expectedBatch.fee)
  expect(batch.billingCycle).toBe(expectedBatch.billingCycle)
  expect(batch.graceDays).toBe(expectedBatch.graceDays)
  expect(batch.startDate).toBe(expectedBatch.startDate)
  expect(batch.timezone).toBe(expectedBatch.timezone)
  expect(batch.cycleCount).toBe(expectedBatch.cycleCount)
  expect(batch.over).toBe(expectedBatch.over)
  expect(batch.seatCount).toBe(expectedBatch.seatCount)
  expect(batch.studentCount).toBeUndefined()
  expect(batch.admissionOpen).toBe(expectedBatch.admissionOpen)

  // Check events array
  expect(Array.isArray(batch.events)).toBe(true)
  if (expectedBatch.id === publishedBatch.id) {
    expect((batch.events as unknown[]).length).toBe(1)
    const event = (batch.events as unknown[])[0] as Record<string, unknown>
    expect(event.id).toBe(batchEvent.id)
    expect(event.at).toBe(batchEvent.at)
    expect(event.durationMinutes).toBe(batchEvent.durationMinutes)
    expect(event.days).toEqual(batchEvent.days)
    expect(event.eventType).toBe(batchEvent.eventType)
    expect(event.eventXid).toBe(batchEvent.eventXid)
  }

  // Check recommended field (boolean)
  expect(typeof batch.recommended).toBe('boolean')

  // Check academy ID
  expect(batch.academyId).toBeUndefined()
}

describe('getBatchHandler', () => {
  beforeEach(async () => {
    await initDb()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Success scenarios - Published batches', () => {
    test('should return published batch for unauthenticated users', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeUnauthenticatedRequest(publishedBatch.id)

      // then
      expect(res.status).toBe(200)
      const batch = await res.json()
      assertValidBatchResponse(batch, publishedBatch)
    })

    test('should return published batch for authenticated users without profile', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(publishedBatch.id)

      // then
      expect(res.status).toBe(200)
      const batch = await res.json()
      assertValidBatchResponse(batch, publishedBatch)
    })

    test('should return published batch for authenticated users with profile', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(publishedBatch.id, undefined, testProfile.id)

      // then
      expect(res.status).toBe(200)
      const batch = await res.json()
      assertValidBatchResponse(batch, publishedBatch)
    })

    test('should return published batch with recommendation when available', async () => {
      // given
      await setupBasicTestData()
      await setupBatchRecommendation()

      // when
      const res = await makeUnauthenticatedRequest(publishedBatch.id)

      // then
      expect(res.status).toBe(200)
      const batch = await res.json()
      assertValidBatchResponse(batch, publishedBatch)
      expect(batch.recommended).toBe(true)
    })

    test('should return published batch without recommendation when not recommended', async () => {
      // given
      await setupBasicTestData()
      // Not setting up recommendation

      // when
      const res = await makeUnauthenticatedRequest(publishedBatch.id)

      // then
      expect(res.status).toBe(200)
      const batch = await res.json()
      assertValidBatchResponse(batch, publishedBatch)
      expect(batch.recommended).toBe(false)
    })
  })

  describe('Success scenarios - Unpublished batches', () => {
    test('should return unpublished batch for academy staff', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(unpublishedBatch.id, undefined, testProfile.id)

      // then
      expect(res.status).toBe(200)
      const batch = await res.json()
      assertValidBatchResponse(batch, unpublishedBatch)
    })
  })

  describe('Error scenarios - Batch not found', () => {
    test('should return 404 when batch does not exist', async () => {
      // given
      await setupBasicTestData()
      const nonExistentBatchId = crypto.randomUUID() as UUID

      // when
      const res = await makeUnauthenticatedRequest(nonExistentBatchId)

      // then
      expect(res.status).toBe(404)
      await expect(res).toBeError(404, [
        {
          code: 'custom',
          path: [],
          message: `Batch not found, or you are not authorized to access it.`,
        },
      ])
    })
  })

  describe('Error scenarios - Access denied to unpublished batches', () => {
    test('should return 404 when unauthenticated user tries to access unpublished batch', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeUnauthenticatedRequest(unpublishedBatch.id)

      // then
      expect(res.status).toBe(404)
      await expect(res).toBeError(404, [
        {
          code: 'custom',
          path: [],
          message: `Batch not found, or you are not authorized to access it.`,
        },
      ])
    })

    test('should return 404 when authenticated user without profile tries to access unpublished batch', async () => {
      // given
      await setupBasicTestData()

      // when (not providing profile header)
      const res = await makeAuthenticatedRequest(unpublishedBatch.id)

      // then
      expect(res.status).toBe(404)
      await expect(res).toBeError(404, [
        {
          code: 'custom',
          path: [],
          message: `Batch not found, or you are not authorized to access it.`,
        },
      ])
    })

    test('should return 404 when non-staff profile tries to access unpublished batch', async () => {
      // given
      await setupBasicTestData()

      // Create a profile that is NOT staff of the academy
      const nonStaffProfile = {
        id: crypto.randomUUID() as UUID,
        userId: testUser.id,
        role: 'student' as const,
        displayName: 'Student Profile',
        approvedAt: mocked.now.toJSDate(),
        tncsAcceptedAt: mocked.now.toJSDate(),
      }
      await db.insert(profileTable).values(nonStaffProfile)

      // when
      const res = await makeAuthenticatedRequest(unpublishedBatch.id, undefined, nonStaffProfile.id)

      // then
      expect(res.status).toBe(404)
      await expect(res).toBeError(404, [
        {
          code: 'custom',
          path: [],
          message: `Batch not found, or you are not authorized to access it.`,
        },
      ])
    })

    test('should return 404 when suspended staff tries to access unpublished batch', async () => {
      // given
      await setupBasicTestData()

      // Create a suspended staff profile
      const suspendedStaffProfile = {
        id: crypto.randomUUID() as UUID,
        userId: testUser.id,
        role: 'teacher' as const,
        displayName: 'Suspended Teacher',
        approvedAt: mocked.now.toJSDate(),
        tncsAcceptedAt: mocked.now.toJSDate(),
        suspendedAt: mocked.now.toJSDate(), // Suspended
      }
      await db.insert(profileTable).values(suspendedStaffProfile)

      // Add as academy staff but suspended
      await db.insert(academyStaffTable).values({
        profileId: suspendedStaffProfile.id,
        academyId: testAcademy.id,
      })

      // when
      const res = await makeAuthenticatedRequest(unpublishedBatch.id, undefined, suspendedStaffProfile.id)

      // then
      expect(res.status).toBe(404)
      await expect(res).toBeError(404, [
        {
          code: 'custom',
          path: [],
          message: `Batch not found, or you are not authorized to access it.`,
        },
      ])
    })
  })

  describe('Error scenarios - Invalid batch ID', () => {
    test('should return 404 when batch ID is invalid UUID', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await app.request('/api/batches/invalid-uuid', { method: 'GET' }, {})

      // then
      expect(res.status).toBe(400)
    })
  })

  describe('Edge cases', () => {
    test('should return batch with empty events array when no events exist', async () => {
      // given
      await setupBasicTestData()

      // Remove the batch event
      await db.delete(batchEventTable).where(eq(batchEventTable.batchId, publishedBatch.id))

      // when
      const res = await makeUnauthenticatedRequest(publishedBatch.id)

      // then
      expect(res.status).toBe(200)
      const batch = await res.json()
      expect(batch.events).toEqual([])
    })

    test('should return batch with multiple events in correct order', async () => {
      // given
      await setupBasicTestData()

      // Add another event with earlier time
      const earlierEvent = {
        id: crypto.randomUUID() as UUID,
        batchId: publishedBatch.id,
        at: '16:00', // Earlier than the existing 18:00 event
        durationMinutes: 90,
        days: ['tuesday', 'thursday'],
        eventType: 'zoom',
        eventXid: 'another-google-calendar-event-id',
      }
      await db.insert(batchEventTable).values(earlierEvent)

      // when
      const res = await makeUnauthenticatedRequest(publishedBatch.id)

      // then
      expect(res.status).toBe(200)
      const batch = await res.json()
      expect(batch.events.length).toBe(2)
      // Events should be ordered by time (16:00 comes before 18:00)
      expect(batch.events[0].at).toBe('16:00')
      expect(batch.events[1].at).toBe('18:00')
    })

    test('should handle batch with zero seat count', async () => {
      // given
      await setupBasicTestData()

      // Update batch to have zero seats
      await db.update(batchTable).set({ seatCount: 0 }).where(eq(batchTable.id, publishedBatch.id))

      // when
      const res = await makeUnauthenticatedRequest(publishedBatch.id)

      // then
      expect(res.status).toBe(200)
      const batch = await res.json()
      expect(batch.seatCount).toBe(0)
    })
  })
})

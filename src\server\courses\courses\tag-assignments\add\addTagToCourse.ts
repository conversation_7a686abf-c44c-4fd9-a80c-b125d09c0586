import { randomUUID, UUID } from 'crypto'

import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureCanUpdateCourse } from '@/server/courses/courses/common/ensureCanUpdateCourse'
import { db } from '@/server/db/db'
import { courseTagAssignmentTable } from '@/server/db/schema/course-schema'
import { withLog } from '@/shared/common/withLog.shared'

const insertCourseTagAssignmentDaf = (log: pino.Logger, courseId: UUID, tagId: UUID) =>
  withLog(log, 'insertCourseTagAssignmentDaf', () => {
    return db
      .insert(courseTagAssignmentTable)
      .values({
        id: randomUUID(),
        courseId,
        tagId,
      })
      .onConflictDoNothing()
  })

export const addTagToCourse = async (c: Ctx, courseId: UUID, tagId: UUID) => {
  await ensureCanUpdateCourse(c, courseId)
  await insertCourseTagAssignmentDaf(c.log, courseId, tagId)
}

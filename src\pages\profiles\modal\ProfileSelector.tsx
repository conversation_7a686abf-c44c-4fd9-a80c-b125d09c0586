import { Dialog, Dialog<PERSON>ackdrop, DialogPanel, DialogTitle } from '@headlessui/react'
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { useState } from 'react'
import { navigate } from 'vike/client/router'

import { MyProfile } from '@/server/profiles/get-my/getMyProfiles'
import { ERoles } from '@/shared/profiles/role-utils.shared'
import { confirmAndRun } from '@ui/common/confirmation/confirmation-store'
import { showNotification } from '@ui/common/notification/notification-store'
import { ShowData } from '@ui/common/ShowData'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'
import { useUserQuery } from '@ui/users/common/user-queries'

import { CheckBox } from '../../common/form/CheckBox'
import { getCurrentProfileId, setCurrentProfileId } from '../common/current-profile-store'
import { useMyProfilesQuery, useRemoveProfileMutation } from '../common/profile-queries'
import { RoleIcon } from '../common/RoleIcon'
import { SuspendedBadge } from '../common/SuspendedBadge'
import { UnapprovedBadge } from '../common/UnapprovedBadge'

import {
  isProfileModalShownAtStartup,
  setProfileModalVisibility,
  showProfileModalAtStartup,
  useProfileModelVisible,
} from './profile-modal-store'

const hide = (e: React.MouseEvent<HTMLButtonElement>) => {
  e.stopPropagation()
  setProfileModalVisibility(false)
}

export const ProfileSelector = () => {
  const { data: user } = useUserQuery()
  const { data, isPending, error } = useMyProfilesQuery()

  return user?.mobileVerified ?
      <ShowData isPending={isPending} error={error}>
        {data && (data.length > 0 ? <ProfileModal profiles={data} /> : <NoProfilesModal />)}
      </ShowData>
    : null
}

const ProfileModal = ({ profiles }: { profiles: MyProfile[] }) => {
  const visible = useProfileModelVisible()
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const [showAtStartup, setShowAtStartup] = useState(isProfileModalShownAtStartup())

  if (selectedIndex === -1) {
    const currentProfileId = getCurrentProfileId()
    if (currentProfileId) {
      const currentIndex = profiles.findIndex((profile) => profile.id === currentProfileId)
      setSelectedIndex(currentIndex === -1 ? 0 : currentIndex)
    } else {
      setSelectedIndex(0)
    }
  }

  const handleOk = (e: React.MouseEvent<HTMLButtonElement>) => {
    hide(e)
    setCurrentProfileId(profiles[selectedIndex].id)
    showProfileModalAtStartup(showAtStartup)
  }

  return (
    <Dialog open={visible} onClose={setProfileModalVisibility} className="relative z-10">
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-gray-500/75 transition-opacity data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in"
      />
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <DialogPanel
            transition
            className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all data-closed:translate-y-4 data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in sm:my-8 sm:w-full sm:max-w-lg data-closed:sm:translate-y-0 data-closed:sm:scale-95"
          >
            <div className="bg-gradient-to-r from-indigo-600 to-indigo-700 px-6 py-4">
              <DialogTitle as="h3" className="text-xl font-semibold text-white text-center">
                Choose Profile
              </DialogTitle>
            </div>

            <div className="px-6 py-5">
              <div className="mb-4 space-y-2.5">
                {profiles.map((profile, index) => (
                  <ProfileCard
                    key={profile.id}
                    profile={profile}
                    index={index}
                    selectedIndex={selectedIndex}
                    setSelectedIndex={setSelectedIndex}
                  />
                ))}
              </div>

              <div className="mt-6 border-t border-gray-200 pt-4">
                <CheckBox
                  id="showAtStartup"
                  checked={showAtStartup}
                  onChange={(e) => setShowAtStartup(e.target.checked)}
                  label="Show at startup"
                  description={
                    <>
                      Access later via <i>Switch Profile</i> in user menu
                    </>
                  }
                />
              </div>

              <div className="mt-8 flex justify-center gap-3">
                <button
                  type="button"
                  onClick={handleOk}
                  className="inline-flex justify-center rounded-md bg-indigo-600 px-4 py-2.5 text-sm font-semibold text-white shadow-sm transition-colors hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                >
                  Ok
                </button>
                <button
                  type="button"
                  onClick={hide}
                  className="inline-flex justify-center rounded-md bg-white px-4 py-2.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-gray-300 ring-inset transition-colors hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={(e) => {
                    hide(e)
                    void navigate('/profiles/add')
                  }}
                  className="inline-flex justify-center rounded-md bg-indigo-50 px-4 py-2.5 text-sm font-semibold text-indigo-600 shadow-sm transition-colors hover:bg-indigo-100"
                >
                  Add Profile
                </button>
              </div>
            </div>
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  )
}

const ProfileCard = ({
  profile,
  index,
  selectedIndex,
  setSelectedIndex,
}: {
  profile: MyProfile
  index: number
  selectedIndex: number
  setSelectedIndex: (index: number) => void
}) => {
  const deleteProfile = useRemoveProfileMutation(profile.id)
  const isSelected = index === selectedIndex

  const handleRemove = () => {
    confirmAndRun(
      () =>
        void deleteProfile.mutate(undefined, {
          onError: (error) => {
            showNotification({
              heading: 'Error removing profile',
              message: getErrorMessage(error),
              type: 'error',
            })
          },
        }),
      {
        heading: 'Remove Profile',
        message: `Are you sure you want to remove ${profile.displayName} (${ERoles[profile.role].name})? This action cannot be undone.`,
        okLabel: 'Remove',
      },
    )
  }

  return (
    <div
      key={profile.id}
      onClick={() => setSelectedIndex(index)}
      className={`relative flex items-center space-x-3 rounded-lg border px-4 py-3.5 shadow-sm transition-all focus-within:ring-2 focus-within:ring-offset-2 hover:border-gray-400 cursor-pointer ${
        isSelected ?
          'border-indigo-500 bg-indigo-50 focus-within:ring-indigo-500 transform scale-[1.02]'
        : 'border-gray-300 bg-white focus-within:ring-primary'
      }`}
    >
      <div className="shrink-0">
        <div
          className={`flex items-center justify-center size-10 rounded-full ${isSelected ? 'bg-indigo-100' : 'bg-gray-100'}`}
        >
          <RoleIcon type="outline24" role={profile.role} className={isSelected ? 'text-indigo-600' : ''} />
        </div>
      </div>
      <div className="min-w-0 flex-1">
        <div className="focus:outline-hidden">
          <p className={`text-sm font-medium ${isSelected ? 'text-indigo-900' : 'text-gray-900'}`}>
            {profile.displayName}
          </p>
          <div className="flex items-end gap-2">
            <p className="truncate text-sm text-gray-500">{ERoles[profile.role].name}</p>
            <SuspendedBadge suspendedAt={profile.suspendedAt} />
            <UnapprovedBadge approvedAt={profile.approvedAt} />
          </div>
        </div>
      </div>
      <div className="flex gap-1">
        <button
          onClick={(e) => {
            e.stopPropagation()
            setProfileModalVisibility(false)
            void navigate(`/profiles/${profile.id}`)
          }}
          className="rounded-full p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 transition-colors"
          title="Edit Profile"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-4 h-4"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
            />
          </svg>
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation()
            handleRemove()
          }}
          className="rounded-full p-1 text-red-500 hover:bg-red-50 transition-colors"
          title="Remove Profile"
          disabled={deleteProfile.isPending}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-4 h-4"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
            />
          </svg>
        </button>
      </div>
    </div>
  )
}

const NoProfilesModal = () => {
  const visible = useProfileModelVisible()

  return (
    <Dialog open={visible} onClose={setProfileModalVisibility} className="relative z-10">
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-gray-500/75 transition-opacity data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in"
      />

      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <DialogPanel
            transition
            className="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all data-closed:translate-y-4 data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in sm:my-8 sm:w-full sm:max-w-lg sm:p-6 data-closed:sm:translate-y-0 data-closed:sm:scale-95"
          >
            <div className="sm:flex sm:items-start">
              <div className="mx-auto flex size-12 shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:size-10">
                <ExclamationTriangleIcon aria-hidden="true" className="size-6 text-yellow-600" />
              </div>
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <DialogTitle as="h3" className="text-base font-semibold text-gray-900">
                  No Profiles?
                </DialogTitle>
                <div className="mt-2">
                  <p className="text-sm text-gray-500">
                    If you are a student, please{' '}
                    <a
                      className="link underline"
                      onClick={() => {
                        setProfileModalVisibility(false)
                        void navigate('/profiles/add')
                      }}
                    >
                      create
                    </a>{' '}
                    a profile.
                    <br />
                    <span className="text-xs text-gray-700">
                      (Parents can create multiple profiles for their children)
                    </span>
                  </p>
                  <p className="text-sm text-gray-500 mt-2">For other profiles, please wait for an invitation.</p>
                </div>
              </div>
            </div>
            <div className="mt-5 sm:mt-4 sm:ml-10 sm:flex">
              <button
                type="button"
                onClick={hide}
                className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 sm:ml-3 sm:w-auto"
              >
                Close
              </button>
            </div>
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  )
}

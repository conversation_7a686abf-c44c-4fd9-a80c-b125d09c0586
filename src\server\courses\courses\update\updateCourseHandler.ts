import { UUID } from 'crypto'

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $EditCourseForm } from '@/shared/course/course-utils.shared'

import { updateCourse } from './updateCourse'

export const updateCourseHandler = new Hono<HonoVars>().put('/', zValidator('json', $EditCourseForm), async (c) => {
  const form = c.req.valid('json')
  const courseId = c.req.param('id') as UUID

  await updateCourse(getCtx(c), courseId, form)
  return c.body(null, 204)
})

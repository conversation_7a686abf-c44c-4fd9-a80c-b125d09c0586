import { UUID } from 'crypto'

import { ArrowDownIcon, ArrowUpIcon } from '@heroicons/react/20/solid'
import { AcademicCapIcon } from '@heroicons/react/24/outline'

import { AcademyBrief } from '@/server/academies/get/getAcademy.server'
import { CourseItem } from '@/server/courses/courses/list/listCourses.server'
import { academyPath } from '@/shared/academies/academy-utils.shared'
import { Currency } from '@/shared/common/common-utils.shared'
import { formatDate, today } from '@/shared/common/date-utils.shared'
import { CoursesSearch, coursePath } from '@/shared/course/course-utils.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'
import { useAcademySuspenseQuery } from '@ui/academies/common/academy-queries'
import { DividerLink } from '@ui/common/DividerLink'
import { ShowData } from '@ui/common/ShowData'
import { getUrl } from '@ui/common/utils/url-utils'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

import { useBatchSuspenseQuery } from './@id/batches/common/batch-queries'
import { CourseTags } from './common/CourseTags'
import { DraftBadge } from './common/DraftBadge'
import { useCoursesSuspenseQuery } from './common/queries/course-queries'
export const CoursesPage = ({ search }: { search: CoursesSearch }) => {
  const { data: courses, error } = useCoursesSuspenseQuery(search)
  return <ShowData error={error}>{<ViewCourses courses={courses!} search={search} />}</ShowData>
}

const ViewCourses = ({ courses, search }: { courses: CourseItem[]; search: CoursesSearch }) => {
  const { currentProfile } = useCurrentProfile()
  const canAddCourse = currentProfile && ACADEMY_STAFF.includes(currentProfile.role)
  return (
    <div>
      <div className="flex items-center justify-between">
        <h1 className="page-title">Courses</h1>
        {canAddCourse && (
          <a href="/courses/add" className="link text-sm">
            Add a Course
          </a>
        )}
      </div>

      {search.academyId && <AcademyHeader academyId={search.academyId} />}
      <CourseList courses={courses} search={search} />
    </div>
  )
}

const CourseList = ({ courses, search }: { courses: CourseItem[]; search: CoursesSearch }) => {
  // Calculate previous and next page search parameters
  const prevSearch: CoursesSearch = { ...search, previous: 'true' }
  const nextSearch: CoursesSearch = { ...search, previous: undefined }
  if (courses.length > 0) {
    // Set cursor for previous page (first item)
    prevSearch.fromStartDate = courses[0].startDate ?? undefined
    prevSearch.beyondId = courses[0].id

    // Set cursor for next page (last item)
    nextSearch.fromStartDate = courses[courses.length - 1].startDate ?? undefined
    nextSearch.beyondId = courses[courses.length - 1].id
  } else {
    // No courses case
    prevSearch.fromStartDate = undefined
    prevSearch.beyondId = undefined
    nextSearch.fromStartDate = undefined
    nextSearch.beyondId = undefined
  }

  return (
    <>
      {/* Previous page link */}
      <DividerLink href={getUrl(`/courses`, prevSearch)}>
        <ArrowUpIcon aria-hidden="true" className="-mr-0.5 -ml-1 size-5 text-gray-400" />
        Previous
      </DividerLink>

      {/* Course list or empty state */}
      {courses.length > 0 ?
        <ul role="list" className="my-6 grid grid-cols-1 gap-x-6 gap-y-3 sm:gap-y-8 lg:grid-cols-3 xl:gap-x-8">
          {courses.map((course) => (
            <Course key={course.id} course={course} />
          ))}
        </ul>
      : <div className="text-center py-12 my-6">
          <AcademicCapIcon className="mx-auto h-12 w-12 text-gray-400" aria-hidden="true" />
          <h3 className="mt-2 text-sm font-semibold text-gray-900">No courses on this page</h3>
          <p className="mt-1 text-sm text-gray-500">Try browsing &uarr; previous or &darr; next page.</p>
        </div>
      }

      {/* Next page link */}
      <DividerLink href={getUrl(`/courses`, nextSearch)}>
        <ArrowDownIcon aria-hidden="true" className="-mr-0.5 -ml-1 size-5 text-gray-400" />
        Next
      </DividerLink>
    </>
  )
}

const AcademyHeader = ({ academyId }: { academyId: UUID }) => {
  const { data: academy } = useAcademySuspenseQuery(academyId, false)

  return (
    <p className="page-subtitle">
      {academy && (
        <a href={academyPath(academy)} className="link">
          {academy.name}
        </a>
      )}
    </p>
  )
}

const Course = ({ course }: { course: CourseItem }) => {
  const { data: academy } = useAcademySuspenseQuery(course.academyId, false)
  if (!academy) return null
  return (
    <li key={course.id} className="overflow-hidden rounded-xl border border-gray-200">
      <div className="relative border-b border-gray-900/5 bg-gray-50 p-6">
        <div className="flex items-start gap-x-2">
          <div className="min-w-0 flex-grow">
            <div className="font-sm/6 text-gray-900 break-words leading-5">
              <a className="link underline" href={coursePath(course)}>
                {course.name}
              </a>
            </div>
            <Academy academy={academy} />
          </div>
          <div>
            <DraftBadge publishedAt={course.publishedAt} />
          </div>
        </div>
        <div className="mt-3">
          <CourseTags tagIds={course.tagIds} />
        </div>
      </div>
      <div className="px-6 py-3 text-sm/6 text-gray-500">
        {course.recommendedBatchId ?
          <RecommendedBatch currency={academy.currency} batchId={course.recommendedBatchId} />
        : <p>No open batches</p>}
        <a href={`${coursePath(course)}/batches`} className="link">
          View all batches
        </a>
      </div>
    </li>
  )
}
const Academy = ({ academy }: { academy: AcademyBrief }) => {
  return (
    <div className="text-sm/6 text-gray-900 break-words leading-5 mt-1">
      by{' '}
      {academy && (
        <a href={academyPath(academy)} className="link">
          {academy.name}
        </a>
      )}
    </div>
  )
}

const RecommendedBatch = ({ currency, batchId }: { currency: Currency; batchId: UUID }) => {
  const { data: batch } = useBatchSuspenseQuery(batchId)

  if (!batch) return null
  return (
    <dl className="">
      <div className="flex justify-between gap-x-2 pt-1">
        <dt className="text-gray-500">{batch.startDate < today() ? 'Started' : 'Starts'} on</dt>
        <dd className="text-gray-700">
          <time dateTime={batch.startDate}>{formatDate(batch.startDate)}</time>
        </dd>
      </div>
      <div className="flex justify-between gap-x-4 pb-1">
        <dt className="text-gray-500">Fee per month</dt>
        <dd className="flex items-start gap-x-2">
          <div className="font-medium text-gray-900">
            {currency} {batch.fee}
          </div>
        </dd>
      </div>
    </dl>
  )
}

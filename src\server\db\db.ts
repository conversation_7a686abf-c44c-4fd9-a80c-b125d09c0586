import { drizzle } from 'drizzle-orm/postgres-js'
import pino from 'pino'
import postgres from 'postgres'

import { getLogger } from '@/shared/common/logger.shared'

import { env } from '../common/env.server'

import * as schema from './schema'

import type { Logger } from 'drizzle-orm'

const sql = postgres(env.DATABASE_URL, {
  ssl: 'prefer', // true, prefer, require, tls.connect options
  max: 10, // Max number of connections
  connect_timeout: 30, // Connect timeout in seconds
  prepare: true, // Automatic creation of prepared statements
  connection: {
    application_name: `tui_${env.APP_ENV}`, // Default application_name
    idle_in_transaction_session_timeout: 20000, // Other connection parameters, see https://www.postgresql.org/docs/current/runtime-config-client.html
  },

  // Essential resilience options (postgres.js handles reconnection automatically)
  idle_timeout: 20, // Prevents "Connection terminated unexpectedly" (hardcoded optimal value)
  // max_lifetime: Let postgres.js use intelligent default (30-60min randomized for OOM killer & prepared statements)

  transform: {
    undefined: null, // Transform undefined to null for consistent behavior
  },

  // Environment-specific logging
  onnotice: (notice) => {
    getLogger().debug({ notice }, 'Database connection notice')
  },
})

class MyLogger implements Logger {
  logQuery(query: string, _params: unknown[]): void {
    if (env.APP_ENV === 'staging' || query.includes('student_fee_payment')) {
      if (query.includes('app_command')) return
      getLogger().debug({ db: true }, `DB QUERY: ${query}`)
    }
    // log.debug(`DB QUERY: ${query}, PARAMS: ${params}`) might be dangerous in production
  }
}

export const db = drizzle(sql, { logger: new MyLogger(), schema, casing: 'snake_case' })

// https://github.com/porsager/postgres?tab=readme-ov-file#teardown--cleanup
export const closeDb = async (timeoutSeconds = 30) => {
  try {
    await sql.end({ timeout: timeoutSeconds })
    getLogger().info('Database connections closed gracefully')
  } catch (error) {
    getLogger().error({ error }, 'Error closing database connections')
    throw error
  }
}

// Health check function for monitoring database connectivity
export const checkDatabaseHealth = async (
  log: pino.Logger,
): Promise<{ healthy: boolean; latencyMillis?: number; error?: string }> => {
  const startTime = Date.now()

  try {
    await sql`SELECT 1 as health_check`
    const latencyMillis = Date.now() - startTime

    return {
      healthy: true,
      latencyMillis,
    }
  } catch (error) {
    log.error({ error }, 'Error checking database health')
    const latencyMillis = Date.now() - startTime

    return {
      healthy: false,
      latencyMillis,
      error: error instanceof Error ? error.message : String(error),
    }
  }
}

export type Database = typeof db
export type Transaction = Parameters<Parameters<Database['transaction']>[0]>[0]

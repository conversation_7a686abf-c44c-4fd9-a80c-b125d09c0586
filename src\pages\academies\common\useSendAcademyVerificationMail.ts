import { UUID } from 'crypto'

import { showNotification } from '@ui/common/notification/notification-store'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'

import { useSendAcademyEmailVerificationMailMutation } from './academy-queries'

export const useSendAcademyVerificationMail = (academyId: UUID) => {
  const sendAcademyEmailVerificationMail = useSendAcademyEmailVerificationMailMutation(academyId)

  const sendAcademyVerificationMail = () => {
    sendAcademyEmailVerificationMail.mutate(undefined, {
      onSuccess: () => {
        showNotification({
          type: 'success',
          heading: 'Please check mail for verification link',
        })
      },
      onError: (error) => {
        showNotification({
          type: 'error',
          heading: 'Error sending verification mail',
          message: getErrorMessage(error),
        })
      },
    })
  }

  return { sendAcademyVerificationMail, isPending: sendAcademyEmailVerificationMail.isPending }
}

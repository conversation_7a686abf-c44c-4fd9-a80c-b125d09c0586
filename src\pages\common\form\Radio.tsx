import clsx from 'clsx'
import { ChangeEvent, ReactNode } from 'react'

type RadioProps = {
  value: string
  checked: boolean
  onChange: (e: ChangeEvent<HTMLInputElement>) => void
  label: ReactNode
  description?: ReactNode
  id?: string
}

export const Radio = ({ id, value, description, label, checked, onChange }: RadioProps) => (
  <div className="relative flex items-start">
    <div className="flex h-6 items-center">
      <input
        id={id}
        value={value}
        checked={checked}
        onChange={onChange}
        type="radio"
        aria-describedby={`${id}-description`}
        className="relative size-4 appearance-none rounded-full border border-gray-300 bg-white before:absolute before:inset-1 before:rounded-full before:bg-white not-checked:before:hidden checked:border-indigo-600 checked:bg-indigo-600 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:before:bg-gray-400 forced-colors:appearance-auto forced-colors:before:hidden"
      />
    </div>
    <div className="ml-3 text-sm/6">
      <label htmlFor={id} className={clsx('text-gray-900', description && 'font-medium')}>
        {label}
      </label>
      {description && (
        <p id={`${id}-description`} className="text-gray-500">
          {description}
        </p>
      )}
    </div>
  </div>
)

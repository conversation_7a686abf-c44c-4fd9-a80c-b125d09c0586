import { z } from 'zod'

export const $Role = z.enum(['principal', 'mentor', 'teacher', 'student', 'admin', 'manager', 'executive'])
export type Role = z.infer<typeof $Role>

// Enhanced Role
export type ERole = {
  value: Role
  name: string
  description: string
  autoApproved: boolean
  serviceRecipients: Role[]
}

export const STUDENT: ERole = {
  value: 'student',
  name: 'Student',
  description: 'A learner',
  autoApproved: true,
  serviceRecipients: [],
}

export const TEACHER: ERole = {
  value: 'teacher',
  name: 'Teacher',
  description: 'A teacher',
  autoApproved: false,
  serviceRecipients: [],
}

export const MENTOR: ERole = {
  value: 'mentor',
  name: '<PERSON>tor',
  description: 'Helps manage academies',
  autoApproved: false,
  serviceRecipients: [],
}

export const PRINCIPAL: ERole = {
  value: 'principal',
  name: 'Principal',
  description: 'Creates and manages academies, supports mentors and teachers',
  autoApproved: false,
  serviceRecipients: ['principal', 'mentor', 'teacher'],
}

export const ADMIN: ERole = {
  value: 'admin',
  name: 'Admin',
  description: 'Platform-wide administrator',
  autoApproved: false,
  serviceRecipients: ['principal', 'admin', 'manager', 'executive'],
}

export const MANAGER: ERole = {
  value: 'manager',
  name: 'Manager',
  description: 'Platform-wide manager',
  autoApproved: false,
  serviceRecipients: ['principal', 'manager', 'executive'],
}

export const EXECUTIVE: ERole = {
  value: 'executive',
  name: 'Executive',
  description: 'Platform-wide support executives',
  autoApproved: false,
  serviceRecipients: [],
}

export const ERoles = {
  student: STUDENT,
  teacher: TEACHER,
  mentor: MENTOR,
  principal: PRINCIPAL,
  admin: ADMIN,
  manager: MANAGER,
  executive: EXECUTIVE,
} as const

export const ROLES = Object.keys(ERoles) as Role[]
export const ROLES_PROVIDING_SERVICE = Object.values(ERoles)
  .filter((role) => role.serviceRecipients.length > 0)
  .map((role) => role.value)

export const ACADEMY_STAFF = ['principal', 'mentor', 'teacher'] as Role[]
export const IMPERSONATING_ROLES = ['admin', 'manager'] as Role[]
export const INTERNAL_ROLES = ['admin', 'manager', 'executive'] as Role[]

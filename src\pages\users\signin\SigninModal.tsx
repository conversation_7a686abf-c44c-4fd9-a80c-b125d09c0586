import { Dialog, DialogBackdrop, DialogPanel, DialogTitle } from '@headlessui/react'
import { useGoogleLogin } from '@react-oauth/google'
import clsx from 'clsx'
import React from 'react'
import { navigate } from 'vike/client/router'
import { usePageContext } from 'vike-react/usePageContext'

import { Radio } from '@/pages/common/form/Radio'
import { showNotification } from '@/pages/common/notification/notification-store'
import { getLogger } from '@/shared/common/logger.shared'
import { googleOauthScopes } from '@/shared/users/googleOauthScopes.shared'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'
import { addDiagnosticLog } from '@ui/diagnostic-logs/diagnostic-store'
import { clearAuth, setAuth } from '@ui/users/auth/auth-token-store'
import { useSignInMutation } from '@ui/users/common/user-queries'

import { GoogleIcon } from '../common/GoogleIcon'

import { setRememberMe, useRememberMe } from './remember-me-store'
import { setSigninModalVisibility, useSigninModalMessage, useSigninModalVisible } from './signin-modal-store'
import styles from './SigninModal.module.css'

export const SigninModal = () => {
  const pageContext = usePageContext()
  const rememberMe = useRememberMe()
  const isOpen = useSigninModalVisible()
  const errorMessage = useSigninModalMessage()
  const oauthState = React.useMemo(() => crypto.randomUUID(), [])
  const log = getLogger()
  const { mutate: signIn, isPending: signInIsPending } = useSignInMutation()

  const handleSuccess = React.useCallback(
    async (response: { code: string; state?: string }) => {
      if (oauthState !== response.state) {
        setSigninModalVisibility(true, 'State mismatch. Please refresh page and try again.')
        log.error(`State mismatch when logging in: ${oauthState} !== ${response.state}`)
        return
      }

      signIn(
        { code: response.code },
        {
          onSuccess: (token) => {
            setSigninModalVisibility(false)
            setAuth(token)
            const currentPage = pageContext.urlPathname
            addDiagnosticLog(`Current page when signed in: ${currentPage}`)
            if (['/users/signup'].includes(currentPage)) {
              void navigate('/')
            }
          },
          onError: (error) => {
            if (error.status === 404) {
              showNotification({
                heading: 'User not found. Please sign up',
                type: 'error',
              })
              setSigninModalVisibility(false)
              void navigate('/users/signup')
            } else {
              setSigninModalVisibility(true, getErrorMessage(error))
              log.error({ error }, 'Error signing in')
            }
          },
        },
      )
    },
    [oauthState],
  )

  const login = useGoogleLogin({
    onSuccess: handleSuccess,
    onError: (error) => {
      setSigninModalVisibility(true, error.error_description ?? 'Error signing in with Google')
      log.error(error, 'Error signing in')
    },
    flow: 'auth-code',
    scope: googleOauthScopes.join(' '),
    state: oauthState,
  })

  const handleSignIn = () => {
    log.info('Signing in...')
    setSigninModalVisibility(true, '')
    login()
  }

  const handleCancel = () => {
    setSigninModalVisibility(false)
    clearAuth()
  }

  return (
    <Dialog open={isOpen} onClose={handleCancel} className="relative z-10">
      <DialogBackdrop className="fixed inset-0 bg-gray-500/75 transition-opacity data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in" />

      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <DialogPanel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 data-closed:translate-y-4 data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in data-closed:sm:translate-y-0 data-closed:sm:scale-95">
            <div>
              <DialogTitle as="div">
                <div className="flex items-center justify-center">
                  <GoogleIcon />
                  <h2 className="font-semibold text-gray-900">Sign In</h2>
                </div>
              </DialogTitle>
              <p className={`mt-1 text-center text-sm ${errorMessage ? 'text-error' : 'text-help'}`}>
                {errorMessage || 'Sign in using your Google account'}
              </p>

              <div className="mt-5">
                <fieldset aria-label="Remember Me">
                  <div className="space-y-5">
                    <Radio
                      id="dont-remember"
                      value="dont-remember"
                      checked={!rememberMe}
                      onChange={() => setRememberMe(false)}
                      label="Do not remember me"
                      description="Select this if you are on a public computer"
                    />
                    <Radio
                      id="remember"
                      value="remember"
                      checked={rememberMe}
                      onChange={() => setRememberMe(true)}
                      label="Remember me for 30 days"
                      description="Select this if you are on a personal computer"
                    />
                  </div>
                </fieldset>
              </div>
            </div>
            <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
              <button
                type="button"
                className={clsx(
                  `${styles.signInButton} inline-flex w-full justify-center rounded-md bg-indigo-50 px-3 py-2 text-sm font-semibold text-indigo-600 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-indigo-100 hover:cursor-pointer focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2`,
                  signInIsPending && 'cursor-wait',
                )}
                disabled={signInIsPending}
                onClick={handleSignIn}
              >
                <GoogleIcon />
                {signInIsPending ? 'Signing in...' : 'Sign In'}
              </button>
              <button
                type="button"
                className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 hover:cursor-pointer sm:col-start-1 sm:mt-0"
                onClick={handleCancel}
              >
                Cancel
              </button>
            </div>
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  )
}

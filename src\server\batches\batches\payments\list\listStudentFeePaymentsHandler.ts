import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $StudentFeePaymentsSearch } from '@/shared/batch/batch-utils.shared'

import { listStudentFeePayments } from './listStudentFeePayments'

export const listStudentFeePaymentsHandler = new Hono<HonoVars>().get(
  '/',
  zValidator('query', $StudentFeePaymentsSearch),
  async (c) => {
    const search = c.req.valid('query')
    const studentFeePayments = await listStudentFeePayments(getCtx(c), search)
    return c.json({ rows: studentFeePayments }, 200)
  },
)

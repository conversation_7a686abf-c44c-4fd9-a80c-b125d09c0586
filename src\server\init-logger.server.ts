import pino from 'pino'

import { initLogger, redact } from '@/shared/common/logger.shared'

import { env } from './common/env.server'

export const createServerLogger = (bindings: pino.Bindings = {}) => {
  const logger =
    env.BETTER_STACK_SOURCE_TOKEN ?
      pino(
        {
          name: 'np-tuition-server',
          level: env.APP_ENV === 'staging' ? 'debug' : 'info',
          redact,
        },
        pino.transport({
          target: '@logtail/pino',
          options: {
            sourceToken: env.BETTER_STACK_SOURCE_TOKEN,
            options: {
              endpoint: 'https://s1279445.eu-nbg-2.betterstackdata.com',
            },
          },
        }),
      )
    : env.APP_ENV === 'test' ?
      pino(
        {
          name: 'np-tuition-server-test',
          level: 'debug',
          redact,
          enabled: true,
          transport: undefined, // Ensure no transport is used in test mode
        },
        // If sync logging is needed, use this
        // Object.assign(pino.destination({ sync: true }), {
        //   write: (msg) => {
        //     console.log(msg)
        //   },
        // This change:
        // 1. Creates a sync destination using pino.destination({ sync: true })
        // 2. Overrides its write method to use console.log
        // 3. Uses Object.assign to merge these properties in a type-safe way
        {
          write: (msg) => {
            console.log(msg)
          },
        },
      )
    : pino({
        name: 'np-tuition-server',
        level: 'debug',
        redact,
        transport: {
          target: 'pino-pretty',
          options: {
            singleLine: true,
          },
        },
      })
  return logger.child({ env: env.APP_ENV, ...bindings })
}

export const initServerLogger = (bindings: pino.Bindings = {}) => initLogger(createServerLogger(bindings), env.APP_ENV)

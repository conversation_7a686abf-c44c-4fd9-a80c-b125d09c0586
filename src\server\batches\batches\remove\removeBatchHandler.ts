import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { removeBatch } from './removeBatch'

export const removeBatchHandler = new Hono<HonoVars>().delete('/', zValidator('param', $HasId), async (c) => {
  const { id } = c.req.valid('param')
  const ctx = getCtx(c)

  await removeBatch(ctx, id)
  return c.body(null, 204)
})

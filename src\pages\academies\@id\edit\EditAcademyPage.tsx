import { UUID } from 'crypto'

import { useEffect, useState } from 'react'
import { navigate } from 'vike/client/router'
import { usePageContext } from 'vike-react/usePageContext'

import { ShowData } from '@/pages/common/ShowData'
import { AcademyDetail } from '@/server/academies/get/getAcademy.server'
import { $EditAcademyForm, academyPath, EditAcademyForm } from '@/shared/academies/academy-utils.shared'
import { UNKNOWN_UUID } from '@/shared/common/common-utils.shared'
import { type OmniError } from '@/shared/common/error-utils.shared'
import { html2Markdown } from '@/shared/common/markdown-utils.shared'
import {
  useAcademySuspenseQuery,
  useMyAcademyQuery,
  useUpdateAcademyMutation,
} from '@ui/academies/common/academy-queries'
import { AcademyCommonEditor } from '@ui/academies/common/AcademyCommonEditor'
import { AcademyHeader } from '@ui/academies/common/AcademyHeader'
import { Error<PERSON>lert } from '@ui/common/alerts/ErrorAlert'
import { FormButtons, PageContent, PageLayout } from '@ui/common/form/page-layout'
import { WithProfile } from '@ui/profiles/common/WithProfile'

export const EditAcademyPage = () => {
  // Get profile ID from route params
  const { routeParams } = usePageContext()
  const academyId = routeParams.id as UUID

  // Query academy data
  const { data: existingAcademy, error: academyError } = useAcademySuspenseQuery(academyId, true)
  const academy = existingAcademy as AcademyDetail | undefined

  const { data: myAcademy, isPending: myAcademyIsPending, error: myAcademyError } = useMyAcademyQuery()

  // Form state
  const [formData, setFormData] = useState<Required<EditAcademyForm>>({
    name: academy?.name ?? '',
    descr: academy?.descr ? html2Markdown(academy.descr) : '',
    email: academy?.email ?? '',
    mobileCountryCode: academy?.mobileCountryCode ?? 'IN',
    mobile: academy?.mobile ?? '',
    currency: academy?.currency ?? 'INR',
    upiId: academy?.upiId ?? '',
    tradeName: academy?.tradeName ?? '',
    gstin: academy?.gstin ?? '',
    districtId: academy?.districtId ?? UNKNOWN_UUID,
    pincode: academy?.pincode ?? '',
    area: academy?.area ?? '',
    addressLine1: academy?.addressLine1 ?? '',
    addressLine2: academy?.addressLine2 ?? '',
  })

  // Error state
  const [omniError, setOmniError] = useState<OmniError | undefined>()

  // Update mutation
  const { mutate: updateAcademy, isPending: updateAcademyIsPending } = useUpdateAcademyMutation(academyId)

  // Validate form data when it changes
  useEffect(() => {
    const parsed = $EditAcademyForm.safeParse(formData)
    setOmniError(parsed.error)
  }, [formData])

  // Form submission handler
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    updateAcademy(formData, {
      onSuccess: () => {
        void navigate(academyPath({ id: academyId, name: formData.name }))
      },
      onError: (error) => {
        setOmniError(error.error)
      },
    })
  }

  return (
    <WithProfile roleAnyOf={['principal']}>
      <ShowData isPending={myAcademyIsPending} error={academyError || myAcademyError} spinnerSize="1.25rem">
        <div>
          <div className="px-4">
            <AcademyHeader academy={academy!} />
          </div>
          {academy?.id === myAcademy?.id ?
            <PageLayout>
              <form onSubmit={handleSubmit}>
                <PageContent>
                  <div>
                    <AcademyCommonEditor formData={formData} setFormData={setFormData} omniError={omniError} />
                    <FormButtons
                      omniError={omniError}
                      onCancel={() => navigate(academyPath({ id: academyId, name: academy!.name }))}
                      isSubmitting={updateAcademyIsPending}
                    />
                  </div>
                </PageContent>
              </form>
            </PageLayout>
          : <ErrorAlert>You are not a staff of this academy.</ErrorAlert>}
        </div>
      </ShowData>
    </WithProfile>
  )
}

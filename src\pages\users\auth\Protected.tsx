import { LockClosedIcon } from '@heroicons/react/24/outline'

import { SigninTriggerButton } from '@ui/users/signin/SigninTriggerButton'

import { useSignedIn } from './auth-token-store'

export const Protected = ({ children }: { children: React.ReactNode }) => {
  const signedIn = useSignedIn()
  if (signedIn) {
    return children
  }
  return (
    <div className="flex items-center justify-center p-8">
      <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
        <div className="text-center">
          <div className="mb-6">
            <LockClosedIcon className="mx-auto h-12 w-12 text-red-400" aria-hidden="true" />
          </div>
          <h3 className="mb-2 text-xl font-semibold text-gray-900">Sign in Required</h3>
          <p className="mb-6 text-gray-600">Please sign in to access this content</p>
          <div className="space-y-4">
            <SigninTriggerButton />
            <div className="text-sm text-gray-600">
              New user?{' '}
              <a href="/users/signup" className="font-medium text-blue-600 hover:text-blue-500">
                Sign up
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

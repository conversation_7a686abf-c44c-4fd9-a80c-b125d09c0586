import { UUID } from 'crypto'

import { and, eq, isNull, ne, sql } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { runDLocked } from '@/server/common/distributed-lock/runDLocked'
import { ensureProfileWithRole } from '@/server/common/error/ensure-profile.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db, Transaction } from '@/server/db/db'
import { studentFeePaymentItemTable, studentFeePaymentTable, batchStudentTable } from '@/server/db/schema/batch-schema'
import { AddBatchStudentPaymentForm } from '@/shared/batch/batch-utils.shared'
import { Currency } from '@/shared/common/common-utils.shared'
import { getCurrentCycle } from '@/shared/common/payment-utils/payment-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

// Find batch and check if student is enrolled
const findBatchAndStudentEnrollmentDaf = (log: pino.Logger, batchId: UUID, studentId: UUID) =>
  withLog(log, 'findBatchAndStudentEnrollmentDaf', () =>
    db.query.batchStudentTable.findFirst({
      columns: {
        id: true,
        paidTillCycle: true,
      },
      with: {
        batch: {
          columns: {
            id: true,
            startDate: true,
            billingCycle: true,
            cycleCount: true,
            fee: true,
          },
          with: {
            course: {
              columns: {
                academyId: true,
              },
              with: {
                academy: {
                  columns: {
                    currency: true,
                  },
                },
              },
            },
          },
        },
      },
      where: and(
        eq(batchStudentTable.batchId, batchId),
        eq(batchStudentTable.studentId, studentId),
        isNull(batchStudentTable.leftAt),
      ),
    }),
  )

// Check for existing pending payment for the student at the academy
const findPendingPaymentDaf = (log: pino.Logger, studentId: UUID, academyId: UUID) =>
  withLog(log, 'findPendingPaymentDaf', () =>
    db.query.studentFeePaymentTable.findFirst({
      columns: {
        id: true,
      },
      where: and(
        eq(studentFeePaymentTable.studentId, studentId),
        eq(studentFeePaymentTable.academyId, academyId),
        eq(studentFeePaymentTable.status, 'pending'),
      ),
    }),
  )

// Check for existing payment item for this cycle that's not failed
const findExistingPaymentItemDaf = (log: pino.Logger, batchId: UUID, studentId: UUID, cycle: number) =>
  withLog(log, 'findExistingPaymentItemDaf', () =>
    db
      .select({
        id: studentFeePaymentItemTable.id,
        paymentStatus: studentFeePaymentTable.status,
      })
      .from(studentFeePaymentItemTable)
      .innerJoin(studentFeePaymentTable, eq(studentFeePaymentItemTable.studentFeePaymentId, studentFeePaymentTable.id))
      .where(
        and(
          eq(studentFeePaymentItemTable.batchId, batchId),
          eq(studentFeePaymentTable.studentId, studentId),
          eq(studentFeePaymentItemTable.cycle, cycle),
          ne(studentFeePaymentTable.status, 'failed'),
        ),
      ),
  )

// Find if there's a draft payment for the student at the academy
const findDraftPaymentDaf = async (log: pino.Logger, studentId: UUID, academyId: UUID) =>
  withLog(log, 'findDraftPaymentDaf', () =>
    db.query.studentFeePaymentTable.findFirst({
      columns: {
        id: true,
      },
      where: and(
        eq(studentFeePaymentTable.studentId, studentId),
        eq(studentFeePaymentTable.academyId, academyId),
        eq(studentFeePaymentTable.status, 'draft'),
      ),
    }),
  )

// Create a draft payment for the student at the academy
const createPaymentDaf = async (
  log: pino.Logger,
  db: Transaction,
  studentId: UUID,
  academyId: UUID,
  currency: Currency,
  fee: number,
) => {
  const paymentId = crypto.randomUUID()
  await withLog(log, 'createPaymentDaf', () =>
    db.insert(studentFeePaymentTable).values({
      id: paymentId,
      studentId,
      academyId,
      currency,
      cents: fee * 100, // Convert to cents for storage
      status: 'draft',
    }),
  )
  return paymentId
}

const updatePaymentDaf = async (log: pino.Logger, db: Transaction, paymentId: UUID, fee: number) => {
  await withLog(log, 'updatePaymentDaf', () =>
    db
      .update(studentFeePaymentTable)
      .set({
        cents: sql`${studentFeePaymentTable.cents} + ${fee * 100}`, // Convert to cents for storage
      })
      .where(eq(studentFeePaymentTable.id, paymentId)),
  )
}

// Create payment item for the given cycle
const createPaymentItemDaf = (
  log: pino.Logger,
  db: Transaction,
  paymentId: UUID,
  batchId: UUID,
  cycle: number,
  fee: number,
) =>
  withLog(log, 'createPaymentItemDaf', () =>
    db.insert(studentFeePaymentItemTable).values({
      id: crypto.randomUUID(),
      studentFeePaymentId: paymentId,
      batchId,
      cycle,
      fee,
    }),
  )

export const addStudentFeePayment = async (c: Ctx, batchId: UUID, form: AddBatchStudentPaymentForm) => {
  ensureUser(c.user)
  ensureProfileWithRole('student', c.profile)

  const studentId = c.profile.id
  const { cycle } = form

  // Verify that student is enrolled in the batch and get batch details
  const batchStudentEnrollment = await findBatchAndStudentEnrollmentDaf(c.log, batchId, studentId)
  ensure(batchStudentEnrollment, {
    statusCode: 409,
    issueMessage: 'You are not enrolled in this batch',
    logMessage: `Student ${studentId} is not enrolled in batch ${batchId}`,
  })

  const academyId = batchStudentEnrollment.batch.course.academyId

  // Ensure no pending payment exists for this student at this academy
  const pendingPayment = await findPendingPaymentDaf(c.log, studentId, academyId)
  ensure(!pendingPayment, {
    statusCode: 409,
    issueMessage: 'You already have a pending payment for this academy',
    logMessage: `Student ${studentId} already has a pending payment for academy ${academyId}`,
  })

  // Validate cycle is within batch's cycle count
  ensure(cycle <= batchStudentEnrollment.batch.cycleCount, {
    statusCode: 422,
    issueMessage: `Cycle must be less than or equal to batch cycle count (${batchStudentEnrollment.batch.cycleCount})`,
    logMessage: `Attempted to add payment for cycle ${cycle} but batch cycle count is ${batchStudentEnrollment.batch.cycleCount}`,
  })

  const currentCycle = getCurrentCycle(
    batchStudentEnrollment.batch.startDate,
    batchStudentEnrollment.batch.billingCycle,
  )

  ensure(cycle <= currentCycle || cycle === batchStudentEnrollment.paidTillCycle + 1, {
    statusCode: 422,
    issueMessage: `Paying for too future cycle isn't allowed`,
    logMessage: `Attempted to add payment for cycle ${cycle} but current cycle is ${currentCycle} and paid till cycle is ${batchStudentEnrollment.paidTillCycle}`,
  })

  // Use a distributed lock to prevent race conditions
  await runDLocked(c.log, ['studentFeePayment', studentId], async () => {
    // Check no existing payment item for this cycle exists (unless failed)
    const existingPaymentItems = await findExistingPaymentItemDaf(c.log, batchId, studentId, cycle)
    ensure(existingPaymentItems.length === 0, {
      statusCode: 409,
      issueMessage: 'A payment for this cycle already exists',
      logMessage: `Payment item for batch ${batchId}, student ${studentId}, cycle ${cycle} already exists with status ${existingPaymentItems[0]?.paymentStatus}`,
    })

    const draftPayment = await findDraftPaymentDaf(c.log, studentId, academyId)
    if (draftPayment) {
      await db.transaction(async (tx) => {
        await updatePaymentDaf(c.log, tx, draftPayment.id, batchStudentEnrollment.batch.fee)
        await createPaymentItemDaf(c.log, tx, draftPayment.id, batchId, cycle, batchStudentEnrollment.batch.fee)
      })
    } else {
      await db.transaction(async (tx) => {
        const paymentId = await createPaymentDaf(
          c.log,
          tx,
          studentId,
          academyId,
          batchStudentEnrollment.batch.course.academy.currency,
          batchStudentEnrollment.batch.fee,
        )
        await createPaymentItemDaf(c.log, tx, paymentId, batchId, cycle, batchStudentEnrollment.batch.fee)
      })
    }
  })
}

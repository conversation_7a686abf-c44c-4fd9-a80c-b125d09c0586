import { defineConfig, mergeConfig } from 'vitest/config'

import viteConfig from './vite.config'

export default defineConfig((configEnv) => {
  return mergeConfig(viteConfig(configEnv), {
    test: {
      sourcemap: true,
      deps: {
        sourcemap: true,
      },
      setupFiles: ['./vitest.setup.ts'],
      poolOptions: {
        forks: {
          singleFork: true,
        },
      },
      includeSource: ['**/*.{js,ts}'],
      testTimeout: 300000,
      env: {
        APP_ENV: 'test',
        HOME_URL: 'http://test.example.com',
        DATABASE_URL: 'postgres://test-user:test-pass@localhost:5432/np-tuition-test-db',
        GOOGLE_CLIENT_ID: 'google-client-id-for-testing',
        GOOGLE_CLIENT_SECRET: 'google-client-secret-for-testing',
        ENCRYPTION_SECRET_KEY: '4FD41DF4E86C90042D851A4201B49A3F24CA277ECA453920D34EBB26D5C625C1',
        JWT_SECRET_KEY: 'dev-env-secret-key',
        CRON_AUTH_KEY: 'e5RG4URdXSzSJKskAn9Yl9O8QzZl7QwB',
      },
    },
  })
})

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { removeCourseAttachment } from './removeCourseAttachment'

export const removeCourseAttachmentHandler = new Hono<HonoVars>().delete(
  '/',
  zValidator('param', $HasId),
  async (c) => {
    const attachment = c.req.valid('param')

    await removeCourseAttachment(getCtx(c), attachment.id)
    return c.body(null, 204)
  },
)

import { UUID } from 'crypto'

import { and, eq, isNotNull } from 'drizzle-orm'
import pino, { Lo<PERSON> } from 'pino'

import { ContextProfile } from '@/server/common/auth-context-types.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { db } from '@/server/db/db'
import { academyTable } from '@/server/db/schema/academy-schema'
import { isGoodProfile } from '@/server/profiles/common/profile-check.server'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { INTERNAL_ROLES } from '@/shared/profiles/role-utils.shared'

import { findAcademyStaffDaf } from '../common/academy-staff-check'

const academyBriefColumns = {
  id: academyTable.id,
  name: academyTable.name,
  email: academyTable.email,
  mobileCountryCode: academyTable.mobileCountryCode,
  mobile: academyTable.mobile,
  currency: academyTable.currency,
  upiId: academyTable.upiId,
}

const academyDetailColumns = {
  ...academyBriefColumns,
  approvedAt: academyTable.approvedAt,
  suspendedAt: academyTable.suspendedAt,
  descr: academyTable.descr,
  emailVerified: academyTable.emailVerified,
  mobileVerified: academyTable.mobileVerified,
  districtId: academyTable.districtId,
  pincode: academyTable.pincode,
  area: academyTable.area,
  addressLine1: academyTable.addressLine1,
  addressLine2: academyTable.addressLine2,
}

const academyDetailWithSensitiveColumns = {
  ...academyDetailColumns,
  tradeName: academyTable.tradeName,
  gstin: academyTable.gstin,
}

const academyBriefSelect = db.select(academyBriefColumns).from(academyTable)

const academyDetailSelect = db.select(academyDetailColumns).from(academyTable)
const academyDetailWithSensitiveColumnsSelect = db.select(academyDetailWithSensitiveColumns).from(academyTable)

const academySelect = (includeDetail: boolean, isPowerUser: boolean) =>
  includeDetail ?
    isPowerUser ? academyDetailWithSensitiveColumnsSelect
    : academyDetailSelect
  : academyBriefSelect

const findAcademyDetailByIdDaf = (log: Logger, academyId: UUID, includeDetail: boolean, isPowerUser: boolean) => {
  return withLog(log, 'findAcademyDetailByIdDaf', () =>
    academySelect(includeDetail, isPowerUser)
      .where(and(eq(academyTable.id, academyId), isPowerUser ? undefined : isNotNull(academyTable.approvedAt)))
      .then((rows) => rows[0]),
  )
}

const isPowerUser = async (log: pino.Logger, academyId: UUID, profile?: ContextProfile) => {
  if (!profile) return false
  if (INTERNAL_ROLES.includes(profile.role)) return true
  const staff = await findAcademyStaffDaf(log, academyId, profile.id)
  return isGoodProfile(staff)
}

export const getAcademy = async (log: Logger, academyId: UUID, includeDetail: boolean, profile?: ContextProfile) => {
  const powerUser = await isPowerUser(log, academyId, profile)
  const academy = await findAcademyDetailByIdDaf(log, academyId, includeDetail, !!powerUser)
  ensureExists(academy, academyId, 'Academy')
  return academy
}

export type AcademyBrief = Awaited<typeof academyBriefSelect>[0]
export type AcademyDetail = Awaited<typeof academyDetailWithSensitiveColumnsSelect>[0]
export type GetAcademy = typeof getAcademy
export type GetAcademyReturnType = Awaited<ReturnType<GetAcademy>>
setQuery('getAcademy', getAcademy)

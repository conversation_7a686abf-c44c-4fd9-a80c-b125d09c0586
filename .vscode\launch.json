{
    "configurations": [
        {
            "type": "chrome",
            "request": "launch",
            "name": "client: chrome",
            "url": "http://localhost:3000",
            "webRoot": "${workspaceFolder}",
            "port": 9876
        },
        {
            "command": "pnpm dev",
            "name": "server",
            "request": "launch",
            "type": "node-terminal",
            "outputCapture": "std", 
            "sourceMaps": true           
        },
    ],
    "compounds": [
        {
            "name": "fullstack",
            "configurations": [
                "server",
                "client: chrome"
            ]
        }
    ]
}
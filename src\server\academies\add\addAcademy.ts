import { randomUUID, UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfileWithRole } from '@/server/common/error/ensure-profile.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db, Transaction } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { academyStaffTable, academyTable } from '@/server/db/schema/academy-schema'
import { AddAcademyForm } from '@/shared/academies/academy-utils.shared'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { markdown2Html } from '@/shared/common/markdown-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

const findAcademyByNameDaf = (log: pino.Logger, name: string) =>
  withLog(log, 'findAcademyByNameDaf', () =>
    db.query.academyTable.findFirst({
      ...dummyColumn,
      where: eq(academyTable.name, name),
    }),
  )

const findPrincipalAcademyStaffDaf = (log: pino.Logger, principalId: UUID) =>
  withLog(log, 'findPrincipalAcademyStaffDaf', () =>
    db.query.academyStaffTable.findFirst({
      columns: {
        academyId: true,
      },
      where: eq(academyStaffTable.profileId, principalId),
    }),
  )

const insertAcademyDaf = (log: pino.Logger, db: Transaction, academyId: UUID, form: AddAcademyForm) =>
  withLog(log, 'insertAcademyDaf', () => {
    const now = utc().toJSDate()
    return db.insert(academyTable).values({
      id: academyId,
      name: form.name,
      tncsAcceptedAt: now,
      email: form.email,
      mobileCountryCode: form.mobileCountryCode,
      mobile: form.mobile,
      descr: form.descr ? markdown2Html(form.descr) : null,
      currency: form.currency,
      upiId: form.upiId,
      tradeName: form.tradeName || null,
      gstin: form.gstin || null,
      districtId: form.districtId,
      pincode: form.pincode,
      area: form.area,
      addressLine1: form.addressLine1 || null,
      addressLine2: form.addressLine2 || null,
    })
  })

const insertAcademyStaffDaf = (log: pino.Logger, db: Transaction, academyId: UUID, principalId: UUID) =>
  withLog(log, 'insertAcademyStaffDaf', () =>
    db.insert(academyStaffTable).values({
      profileId: principalId,
      academyId,
    }),
  )

export const addAcademy = async (c: Ctx, form: AddAcademyForm) => {
  ensureUser(c.user)
  ensureProfileWithRole('principal', c.profile)
  const principalId = c.profile.id

  // Ensure that TnC is accepted
  ensure(form.tncsAccepted, {
    statusCode: 400,
    fieldName: 'tncsAccepted',
    issueMessage: `Please accept the TnCs`,
    logMessage: `Principal ${principalId} attempted to create an academy but has not accepted the TnCs`,
  })

  // Ensure that an academy with same name doesn't exist
  const existingAcademy = await findAcademyByNameDaf(c.log, form.name.trim())
  ensure(!existingAcademy, {
    statusCode: 409,
    issueMessage: `An academy with name ${form.name} already exists.`,
    logMessage: `Principal ${principalId} attempted to create an academy with name ${form.name}, but it already exists.`,
  })

  // Ensure that the principal is not already a staff of an academy
  const existingStaff = await findPrincipalAcademyStaffDaf(c.log, principalId)
  ensure(!existingStaff, {
    statusCode: 409,
    issueMessage: `You are already a staff member of an academy.`,
    logMessage: `Principal ${principalId} attempted to create an academy but is already a staff member of academy ${existingStaff?.academyId}.`,
  })

  // Create the academy
  const academyId = randomUUID()

  await db.transaction(async (tx) => {
    await insertAcademyDaf(c.log, tx, academyId, form)
    await insertAcademyStaffDaf(c.log, tx, academyId, principalId)
  })

  return {
    id: academyId,
  }
}

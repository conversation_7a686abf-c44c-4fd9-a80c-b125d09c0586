import { ContextUser } from '../auth-context-types.server'

import { ensure } from './ensure.server'

export function ensureUser(
  user?: Pick<ContextUser, 'id' | 'emailVerified' | 'mobileVerified'>,
  options?: {
    ignoreEmailVerification?: boolean
    ignoreMobileVerification?: boolean
  },
): asserts user {
  ensure(user, {
    logMessage: `User not found`,
    statusCode: 401,
  })
  ensure(options?.ignoreEmailVerification || user.emailVerified, {
    issueMessage: `Please first verify your email at Google`,
    logMessage: `User having id ${user.id} has not verified their email`,
    statusCode: 403,
  })
  ensure(options?.ignoreMobileVerification || user.mobileVerified, {
    issueMessage: `Please first verify your mobile number`,
    logMessage: `User having id ${user.id} has not verified their mobile number`,
    statusCode: 403,
  })
}

import { Transition } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/20/solid'
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline'
import React from 'react'

import { useNotification, clearNotification } from './notification-store'

/**
 * Global notification component that displays messages with different types (info, success, warning, error).
 * Uses Headless UI's Transition for smooth animations and TanStack store for state management.
 *
 * @example
 * // Place this component at the root level of your app
 * <Notification />
 */
export const Notification = () => {
  const notification = useNotification()
  const show = Boolean(notification.heading || notification.message)

  return (
    <div
      aria-live="assertive"
      className="pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6"
    >
      <div className="flex w-full flex-col items-center space-y-4">
        <Transition
          show={show}
          enter="transform ease-out duration-300 transition"
          enterFrom="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
          enterTo="translate-y-0 opacity-100 sm:translate-x-0"
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-white shadow-lg ring-1 ring-black/5 mt-4">
            <div className="p-4">
              <div className="flex items-start">
                <div className="shrink-0">
                  {notification.type === 'success' && (
                    <CheckCircleIcon className="size-6 text-green-400" aria-hidden="true" />
                  )}
                  {notification.type === 'error' && <XCircleIcon className="size-6 text-red-400" aria-hidden="true" />}
                  {notification.type === 'warning' && (
                    <ExclamationTriangleIcon className="size-6 text-yellow-400" aria-hidden="true" />
                  )}
                  {notification.type === 'info' && (
                    <InformationCircleIcon className="size-6 text-blue-400" aria-hidden="true" />
                  )}
                </div>
                <div className="ml-3 w-0 flex-1 pt-0.5">
                  {notification.heading && (
                    <p className="text-sm mb-1 font-medium text-gray-900">{notification.heading}</p>
                  )}
                  {notification.message && <p className="text-sm text-gray-500">{notification.message}</p>}
                </div>
                <div className="ml-4 flex shrink-0">
                  <button
                    type="button"
                    className="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    onClick={() => clearNotification()}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="size-5" aria-hidden="true" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </div>
  )
}

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { DateTime } from 'luxon'
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'

import app from '@/hono-entry'
import { env } from '@/server/common/env.server'
import { db } from '@/server/db/db'
import { academyStaffTable, academyTable } from '@/server/db/schema/academy-schema'
import { batchEventTable, batchRecommendationTable, batchTable } from '@/server/db/schema/batch-schema'
import { courseTable } from '@/server/db/schema/course-schema'
import { profileTable } from '@/server/db/schema/profile-schema'
import { userTable, userTncAcceptedTable, userTncSignTable } from '@/server/db/schema/user-schema'
import { initDb, SUNDARGARH_DISTRICT_ID, TNC_PRIVACY_V1_ID, TNC_TOS_V1_ID } from '@/server/test/initDb'
import { createAccessToken } from '@/server/users/common/auth-token-utils.server'
import { UUID_REGEX } from '@/shared/common/common-utils.shared'
import { PROFILE_HEADER } from '@/shared/profiles/profile-utils.shared'

const mocked = {
  now: DateTime.now(),
} as const

// Mock date utilities to have consistent test results
vi.mock(import('@/shared/common/date-utils-basic.shared'), async (importOriginal) => {
  const mod = await importOriginal()
  return {
    ...mod,
    utc: () => mocked.now,
  }
})

const testUser = {
  id: crypto.randomUUID() as UUID,
  googleId: 'test-google-id',
  googleRefreshToken: null,
  name: 'Test User',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/picture',
  language: 'en-US',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '9876543210',
  mobileVerified: true,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

const testProfile = {
  id: crypto.randomUUID() as UUID,
  userId: testUser.id,
  role: 'teacher' as const,
  displayName: 'Test Teacher',
  approvedAt: mocked.now.toJSDate(),
  tncsAcceptedAt: mocked.now.toJSDate(),
} as const

const nonStaffUser = {
  id: crypto.randomUUID() as UUID,
  googleId: 'non-staff-google-id',
  googleRefreshToken: null,
  name: 'Non Staff User',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/picture',
  language: 'en-US',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '9876543211',
  mobileVerified: true,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

const nonStaffProfile = {
  id: crypto.randomUUID() as UUID,
  userId: nonStaffUser.id,
  role: 'student' as const,
  displayName: 'Non Staff User',
  approvedAt: mocked.now.toJSDate(),
  tncsAcceptedAt: mocked.now.toJSDate(),
} as const

const testAcademy = {
  id: crypto.randomUUID() as UUID,
  name: 'Test Academy',
  email: '<EMAIL>',
  emailVerified: true,
  mobileCountryCode: 'IN',
  mobile: '9876543210',
  mobileVerified: true,
  currency: 'INR' as const,
  upiId: 'test@upi',
  districtId: SUNDARGARH_DISTRICT_ID as UUID,
  pincode: '770001',
  area: 'Test Area',
  tncsAcceptedAt: mocked.now.toJSDate(),
  approvedAt: mocked.now.toJSDate(),
} as const

const publishedCourse = {
  id: crypto.randomUUID() as UUID,
  name: 'Published Course',
  descr: '<p>Published course description</p>\n',
  academyId: testAcademy.id,
  publishedAt: mocked.now.toJSDate(),
  creationRemarks: `${testProfile.id} profile created this course`,
} as const

const unpublishedCourse = {
  id: crypto.randomUUID() as UUID,
  name: 'Unpublished Course',
  descr: '<p>Unpublished course description</p>\n',
  academyId: testAcademy.id,
  publishedAt: null,
  creationRemarks: `${testProfile.id} profile created this course`,
} as const

const publishedBatch1 = {
  id: crypto.randomUUID() as UUID,
  courseId: publishedCourse.id,
  teacherId: testProfile.id,
  fee: 500000, // 5000.00 in cents
  billingCycle: 'months' as const,
  graceDays: 3,
  startDate: mocked.now.plus({ days: 30 }).toFormat('yyyy-MM-dd'),
  timezone: 'Asia/Kolkata',
  cycleCount: 12,
  over: false,
  seatCount: 50,
  studentCount: 0,
  admissionOpen: true,
  creationRemarks: `${testProfile.id} profile created this batch`,
} as const

const publishedBatch2 = {
  id: crypto.randomUUID() as UUID,
  courseId: publishedCourse.id,
  teacherId: testProfile.id,
  fee: 400000, // 4000.00 in cents
  billingCycle: 'months' as const,
  graceDays: 5,
  startDate: mocked.now.plus({ days: 15 }).toFormat('yyyy-MM-dd'),
  timezone: 'Asia/Kolkata',
  cycleCount: 10,
  over: false,
  seatCount: 30,
  studentCount: 0,
  admissionOpen: true,
  creationRemarks: `${testProfile.id} profile created this batch`,
} as const

const unpublishedBatch = {
  id: crypto.randomUUID() as UUID,
  courseId: unpublishedCourse.id,
  teacherId: testProfile.id,
  fee: 300000, // 3000.00 in cents
  billingCycle: 'months' as const,
  graceDays: 3,
  startDate: mocked.now.plus({ days: 45 }).toFormat('yyyy-MM-dd'),
  timezone: 'Asia/Kolkata',
  cycleCount: 8,
  over: false,
  seatCount: 25,
  studentCount: 0,
  admissionOpen: true,
  creationRemarks: `${testProfile.id} profile created this batch`,
} as const

const batchEvent1 = {
  id: crypto.randomUUID() as UUID,
  batchId: publishedBatch1.id,
  at: '18:00',
  durationMinutes: 60,
  days: ['monday', 'wednesday', 'friday'],
  eventType: 'meet',
  eventXid: 'google-calendar-event-id-1',
}

const batchEvent2 = {
  id: crypto.randomUUID() as UUID,
  batchId: publishedBatch2.id,
  at: '16:00',
  durationMinutes: 90,
  days: ['tuesday', 'thursday'],
  eventType: 'meet',
  eventXid: 'google-calendar-event-id-2',
}

interface BatchListItem {
  id: UUID
  courseId: UUID
  teacherId: UUID
  startDate: string
  timezone: string
  seatCount: number
  admissionOpen: boolean
  fee: number
  billingCycle: string
  cycleCount: number
  graceDays: number
  events: Array<{
    id: UUID
    at: string
    durationMinutes: number
    days: string[]
    eventType: string
    eventXid: string
  }>
  recommended: boolean
  over: boolean
  createdAt: string // Added for pagination cursor
}

interface BatchListResponse {
  rows: BatchListItem[]
}

const makeRequest = async (courseId: UUID, profileId?: UUID, accessToken?: string) => {
  const url = `/api/courses/${courseId}/batches`
  const headers: Record<string, string> = { 'Content-Type': 'application/json' }

  if (accessToken) {
    headers.Authorization = `Bearer ${accessToken}`
  }

  if (profileId) {
    headers[PROFILE_HEADER] = profileId
  }

  return app.request(url, { method: 'GET', headers }, {})
}

const makeUnauthenticatedRequest = async (courseId: UUID) => {
  return makeRequest(courseId)
}

const makeAuthenticatedRequest = async (courseId: UUID, profileId?: UUID, accessToken?: string) => {
  const token = accessToken || (await createAccessToken(env.JWT_SECRET_KEY, testUser.id))
  return makeRequest(courseId, profileId, token)
}

const makeNonStaffAuthenticatedRequest = async (courseId: UUID, profileId?: UUID) => {
  const accessToken = await createAccessToken(env.JWT_SECRET_KEY, nonStaffUser.id)
  return makeRequest(courseId, profileId, accessToken)
}

const setupBasicTestData = async () => {
  // Create academies
  await db.insert(academyTable).values([testAcademy])

  // Create courses
  await db.insert(courseTable).values([publishedCourse, unpublishedCourse])
}

const setupAuthenticatedUserData = async () => {
  // Create users with TnC acceptance
  const users = [testUser, nonStaffUser]
  for (const user of users) {
    await db.insert(userTable).values(user)

    const acceptedTnCs = [TNC_TOS_V1_ID as UUID, TNC_PRIVACY_V1_ID as UUID]
    for (const tncId of acceptedTnCs) {
      await db.insert(userTncSignTable).values({
        id: crypto.randomUUID() as UUID,
        userId: user.id,
        tncVersionId: tncId,
        accepted: true,
      })

      await db.insert(userTncAcceptedTable).values({
        id: crypto.randomUUID() as UUID,
        userId: user.id,
        tncVersionId: tncId,
      })
    }
  }

  // Create profiles
  await db.insert(profileTable).values([testProfile, nonStaffProfile])

  // Create academy staff relationship (only for testProfile, not nonStaffProfile)
  await db.insert(academyStaffTable).values({
    profileId: testProfile.id,
    academyId: testAcademy.id,
  })
}

const setupBatchesAndEvents = async () => {
  // Create batches (profiles must exist first)
  await db.insert(batchTable).values([publishedBatch1, publishedBatch2, unpublishedBatch])

  // Create batch events
  await db.insert(batchEventTable).values([batchEvent1, batchEvent2])
}

const setupBatchRecommendation = async () => {
  // Check if the batch exists, if not create it
  const batchExists = await db.select().from(batchTable).where(eq(batchTable.id, publishedBatch1.id))

  if (batchExists.length === 0) {
    // The batch doesn't exist, so let's create it first
    await db.insert(batchTable).values(publishedBatch1)
  }

  await db.insert(batchRecommendationTable).values({
    batchId: publishedBatch1.id,
    courseId: publishedCourse.id,
  })
}

const assertValidBatchListResponse = (responseBody: BatchListResponse, expectedBatchIds: UUID[]) => {
  expect(responseBody.rows).toBeDefined()
  expect(Array.isArray(responseBody.rows)).toBe(true)
  expect(responseBody.rows.length).toBe(expectedBatchIds.length)

  for (const batch of responseBody.rows) {
    expect(batch.id).toMatch(UUID_REGEX)
    expect(expectedBatchIds).toContain(batch.id)
    expect(batch.courseId).toMatch(UUID_REGEX)
    expect(batch.teacherId).toMatch(UUID_REGEX)
    expect(batch.startDate).toBeDefined()
    expect(batch.timezone).toBeDefined()
    expect(typeof batch.seatCount).toBe('number')
    expect(typeof batch.admissionOpen).toBe('boolean')
    expect(typeof batch.fee).toBe('number')
    expect(batch.billingCycle).toBeDefined()
    expect(typeof batch.cycleCount).toBe('number')
    expect(typeof batch.graceDays).toBe('number')
    expect(Array.isArray(batch.events)).toBe(true)
    expect(typeof batch.recommended).toBe('boolean')
    expect(typeof batch.over).toBe('boolean')

    // Verify events structure
    for (const event of batch.events) {
      expect(event.id).toMatch(UUID_REGEX)
      expect(event.at).toMatch(/^([01]\d|2[0-3]):([0-5]\d)$/)
      expect(typeof event.durationMinutes).toBe('number')
      expect(Array.isArray(event.days)).toBe(true)
      expect(event.eventType).toBeDefined()
      expect(event.eventXid).toBeDefined()
    }
  }
}

describe('listCourseBatchesHandler', () => {
  beforeEach(async () => {
    await initDb()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  test('should return empty array when no courses exist', async () => {
    // given
    const nonExistentCourseId = crypto.randomUUID() as UUID

    // when
    const res = await app.request(`/api/courses/${nonExistentCourseId}/batches`, { method: 'GET' }, {})

    // then
    expect(res.status).toBe(200)
    const responseBody = await res.json()
    expect(responseBody.rows).toEqual([])
  })

  describe('Public access scenarios (unauthenticated)', () => {
    test('should list batches for published course for unauthenticated users', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupBatchesAndEvents()

      // when
      const res = await makeUnauthenticatedRequest(publishedCourse.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      assertValidBatchListResponse(responseBody, [publishedBatch1.id, publishedBatch2.id])

      // Verify batches are ordered by startDate desc, then createdAt desc
      const batch1Index = responseBody.rows.findIndex((b) => b.id === publishedBatch1.id)
      const batch2Index = responseBody.rows.findIndex((b) => b.id === publishedBatch2.id)
      // publishedBatch1 starts later (day 30) than publishedBatch2 (day 15), so batch1 should come first
      expect(batch1Index).toBeLessThan(batch2Index)
    })

    test('should return empty array for unpublished course for unauthenticated users', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupBatchesAndEvents()

      // when
      const res = await makeUnauthenticatedRequest(unpublishedCourse.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      expect(responseBody.rows).toEqual([])
    })

    test('should return empty array for non-existent course', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupBatchesAndEvents()
      const nonExistentCourseId = crypto.randomUUID() as UUID

      // when
      const res = await makeUnauthenticatedRequest(nonExistentCourseId)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      expect(responseBody.rows).toEqual([])
    })

    test('should include batch events in response', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupBatchesAndEvents()

      // when
      const res = await makeUnauthenticatedRequest(publishedCourse.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse

      const batch1InResponse = responseBody.rows.find((b) => b.id === publishedBatch1.id)
      expect(batch1InResponse).toBeDefined()
      expect(batch1InResponse!.events).toHaveLength(1)
      expect(batch1InResponse!.events[0].at).toBe('18:00')
      expect(batch1InResponse!.events[0].durationMinutes).toBe(60)
      expect(batch1InResponse!.events[0].days).toEqual(['monday', 'wednesday', 'friday'])

      const batch2InResponse = responseBody.rows.find((b) => b.id === publishedBatch2.id)
      expect(batch2InResponse).toBeDefined()
      expect(batch2InResponse!.events).toHaveLength(1)
      expect(batch2InResponse!.events[0].at).toBe('16:00')
      expect(batch2InResponse!.events[0].durationMinutes).toBe(90)
      expect(batch2InResponse!.events[0].days).toEqual(['tuesday', 'thursday'])
    })

    test('should show batch recommendation when available', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupBatchesAndEvents()
      await setupBatchRecommendation()

      // when
      const res = await makeUnauthenticatedRequest(publishedCourse.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse

      const recommendedBatch = responseBody.rows.find((b) => b.id === publishedBatch1.id)
      expect(recommendedBatch).toBeDefined()
      expect(recommendedBatch!.recommended).toBe(true)

      const nonRecommendedBatch = responseBody.rows.find((b) => b.id === publishedBatch2.id)
      expect(nonRecommendedBatch).toBeDefined()
      expect(nonRecommendedBatch!.recommended).toBe(false)
    })
  })

  describe('Authenticated access scenarios', () => {
    test('should list batches for published course for authenticated non-staff users', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupBatchesAndEvents()

      // when
      const res = await makeNonStaffAuthenticatedRequest(publishedCourse.id, nonStaffProfile.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      assertValidBatchListResponse(responseBody, [publishedBatch1.id, publishedBatch2.id])
    })

    test('should list batches for published course for authenticated staff users', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupBatchesAndEvents()

      // when
      const res = await makeAuthenticatedRequest(publishedCourse.id, testProfile.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      assertValidBatchListResponse(responseBody, [publishedBatch1.id, publishedBatch2.id])
    })

    test('should include batches for unpublished course when user is course staff', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupBatchesAndEvents()

      // when
      const res = await makeAuthenticatedRequest(unpublishedCourse.id, testProfile.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      assertValidBatchListResponse(responseBody, [unpublishedBatch.id])
    })

    test('should not include batches for unpublished course when user is not course staff', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupBatchesAndEvents()

      // when
      const res = await makeNonStaffAuthenticatedRequest(unpublishedCourse.id, nonStaffProfile.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      expect(responseBody.rows).toEqual([])
    })

    test('should work with authenticated user but no profile header', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupBatchesAndEvents()

      // when
      const res = await makeAuthenticatedRequest(publishedCourse.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      assertValidBatchListResponse(responseBody, [publishedBatch1.id, publishedBatch2.id])
    })
  })

  describe('Validation error scenarios', () => {
    test('should return 400 when courseId is invalid UUID', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await app.request('/api/courses/invalid-uuid/batches', { method: 'GET' }, {})

      // then
      expect(res.status).toBe(400)
      const responseBody = await res.json()
      expect(responseBody.error.issues).toBeDefined()
      expect(responseBody.error.issues.some((issue: { path: string[] }) => issue.path.includes('id'))).toBe(true)
    })
  })

  describe('Edge cases', () => {
    test('should handle empty database', async () => {
      // given - No test data setup
      const nonExistentCourseId = crypto.randomUUID() as UUID

      // when
      const res = await makeUnauthenticatedRequest(nonExistentCourseId)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      expect(responseBody.rows).toEqual([])
    })

    test('should handle course with no batches', async () => {
      // given
      await db.insert(academyTable).values([testAcademy])
      const courseWithNoBatches = {
        ...publishedCourse,
        id: crypto.randomUUID() as UUID,
        name: 'Course With No Batches',
      }
      await db.insert(courseTable).values([courseWithNoBatches])

      // when
      const res = await makeUnauthenticatedRequest(courseWithNoBatches.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      expect(responseBody.rows).toEqual([])
    })

    test('should handle batches with no events', async () => {
      // given
      await db.insert(academyTable).values([testAcademy])
      await db.insert(courseTable).values([publishedCourse])
      await setupAuthenticatedUserData()

      const batchWithNoEvents = {
        ...publishedBatch1,
        id: crypto.randomUUID() as UUID,
      }
      await db.insert(batchTable).values([batchWithNoEvents])

      // when
      const res = await makeUnauthenticatedRequest(publishedCourse.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      assertValidBatchListResponse(responseBody, [batchWithNoEvents.id])

      const batch = responseBody.rows[0]
      expect(batch.events).toEqual([])
    })

    test('should handle special characters in courseId URL encoding', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupBatchesAndEvents()

      // when - Test URL encoding with properly encoded UUID
      const res = await app.request(
        `/api/courses/${encodeURIComponent(publishedCourse.id)}/batches`,
        { method: 'GET' },
        {},
      )

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      assertValidBatchListResponse(responseBody, [publishedBatch1.id, publishedBatch2.id])
    })

    test('should handle suspended profile gracefully', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupBatchesAndEvents()

      // Create a suspended profile
      const suspendedProfile = {
        ...testProfile,
        id: crypto.randomUUID() as UUID,
        displayName: 'Suspended Profile',
        suspendedAt: mocked.now.toJSDate(),
        suspensionReason: 'Test suspension',
      }
      await db.insert(profileTable).values([suspendedProfile])
      await db.insert(academyStaffTable).values({
        profileId: suspendedProfile.id,
        academyId: testAcademy.id,
      })

      // when
      const res = await makeAuthenticatedRequest(unpublishedCourse.id, suspendedProfile.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      // Suspended profile should not be considered good staff, so no unpublished batches
      expect(responseBody.rows).toEqual([])
    })
  })

  describe('Pagination scenarios', () => {
    const setupPaginationTestData = async () => {
      // Create 5 batches to test pagination with pageSize=2
      // Each batch will have different start dates for predictable ordering
      const batches = []
      for (let i = 1; i <= 5; i++) {
        const batch = {
          id: crypto.randomUUID() as UUID,
          courseId: publishedCourse.id,
          teacherId: testProfile.id,
          fee: 500000 + i * 1000, // Different fees for variety
          billingCycle: 'months' as const,
          graceDays: 3,
          startDate: mocked.now.plus({ days: 10 + i * 5 }).toFormat('yyyy-MM-dd'), // Different start dates
          timezone: 'Asia/Kolkata',
          cycleCount: 12,
          over: false,
          seatCount: 50,
          studentCount: 0,
          admissionOpen: true,
          creationRemarks: `${testProfile.id} profile created batch ${i}`,
        }
        batches.push(batch)
      }

      await db.insert(batchTable).values(batches)
      return batches
    }

    const makeRequestWithParams = async (courseId: UUID, params: Record<string, string> = {}) => {
      const queryString = new URLSearchParams(params).toString()
      const url = `/api/courses/${courseId}/batches${queryString ? `?${queryString}` : ''}`
      return app.request(url, { method: 'GET' }, {})
    }

    test('should return first page with specified pageSize', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupPaginationTestData()

      // when
      const res = await makeRequestWithParams(publishedCourse.id, { pageSize: '2' })

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      expect(responseBody.rows).toBeDefined()
      expect(responseBody.rows.length).toBe(2)

      // Verify ordering - should be ordered by startDate DESC, createdAt DESC, id ASC
      const startDates = responseBody.rows.map((b) => b.startDate)
      for (let i = 0; i < startDates.length - 1; i++) {
        expect(startDates[i] >= startDates[i + 1]).toBe(true)
      }

      // Verify all batches are from the correct course
      for (const batch of responseBody.rows) {
        expect(batch.courseId).toBe(publishedCourse.id)
      }
    })

    test('should return second page using forward pagination', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupPaginationTestData()

      // Get first page
      const firstPageRes = await makeRequestWithParams(publishedCourse.id, { pageSize: '2' })
      const firstPageBody = (await firstPageRes.json()) as BatchListResponse
      expect(firstPageBody.rows.length).toBe(2)

      const lastItemFromFirstPage = firstPageBody.rows[firstPageBody.rows.length - 1]

      // when - Get second page using cursor from first page
      const secondPageRes = await makeRequestWithParams(publishedCourse.id, {
        pageSize: '2',
        beyondId: lastItemFromFirstPage.id,
        fromStartDate: lastItemFromFirstPage.startDate,
        fromCreatedAt: lastItemFromFirstPage.createdAt, // Use actual createdAt from cursor
      })

      // then
      expect(secondPageRes.status).toBe(200)
      const secondPageBody = (await secondPageRes.json()) as BatchListResponse
      expect(secondPageBody.rows).toBeDefined()
      expect(secondPageBody.rows.length).toBeGreaterThan(0)
      expect(secondPageBody.rows.length).toBeLessThanOrEqual(2)

      // Verify no overlap between pages
      const firstPageIds = firstPageBody.rows.map((b) => b.id)
      const secondPageIds = secondPageBody.rows.map((b) => b.id)
      const overlap = firstPageIds.filter((id) => secondPageIds.includes(id))
      expect(overlap).toEqual([])

      // Verify ordering continues correctly
      const allBatches = [...firstPageBody.rows, ...secondPageBody.rows]
      const allStartDates = allBatches.map((b) => b.startDate)
      for (let i = 0; i < allStartDates.length - 1; i++) {
        expect(allStartDates[i] >= allStartDates[i + 1]).toBe(true)
      }
    })

    test('should return previous page using backward pagination', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupPaginationTestData()

      // Get first page to establish baseline
      const firstPageRes = await makeRequestWithParams(publishedCourse.id, { pageSize: '2' })
      const firstPageBody = (await firstPageRes.json()) as BatchListResponse
      const lastItemFromFirstPage = firstPageBody.rows[firstPageBody.rows.length - 1]

      // Get second page
      const secondPageRes = await makeRequestWithParams(publishedCourse.id, {
        pageSize: '2',
        beyondId: lastItemFromFirstPage.id,
        fromStartDate: lastItemFromFirstPage.startDate,
        fromCreatedAt: lastItemFromFirstPage.createdAt,
      })
      const secondPageBody = (await secondPageRes.json()) as BatchListResponse
      expect(secondPageBody.rows.length).toBeGreaterThan(0)

      const firstItemFromSecondPage = secondPageBody.rows[0]

      // when - Get previous page using backward pagination
      const backwardRes = await makeRequestWithParams(publishedCourse.id, {
        pageSize: '2',
        beyondId: firstItemFromSecondPage.id,
        fromStartDate: firstItemFromSecondPage.startDate,
        fromCreatedAt: firstItemFromSecondPage.createdAt,
        previous: 'true',
      })

      // then
      expect(backwardRes.status).toBe(200)
      const backwardBody = (await backwardRes.json()) as BatchListResponse
      expect(backwardBody.rows).toBeDefined()
      expect(backwardBody.rows.length).toBeGreaterThan(0)

      // The backward pagination should return batches that come before the cursor
      const backwardIds = backwardBody.rows.map((b) => b.id)
      expect(backwardIds).not.toContain(firstItemFromSecondPage.id)

      // Verify ordering is still correct (DESC)
      const backwardStartDates = backwardBody.rows.map((b) => b.startDate)
      for (let i = 0; i < backwardStartDates.length - 1; i++) {
        expect(backwardStartDates[i] >= backwardStartDates[i + 1]).toBe(true)
      }
    })

    test('should handle pageSize larger than total results', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupPaginationTestData()

      // when
      const res = await makeRequestWithParams(publishedCourse.id, { pageSize: '100' })

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      expect(responseBody.rows).toBeDefined()
      // Should return all 5 batches created in setupPaginationTestData
      expect(responseBody.rows.length).toBe(5)

      // Verify all batches are from the correct course
      for (const batch of responseBody.rows) {
        expect(batch.courseId).toBe(publishedCourse.id)
      }
    })

    test('should handle empty results with pagination parameters', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      const nonExistentCourseId = crypto.randomUUID() as UUID

      // when
      const res = await makeRequestWithParams(nonExistentCourseId, { pageSize: '2' })

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      expect(responseBody.rows).toEqual([])
    })

    test('should handle pagination with batch recommendations', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupPaginationTestData()
      await setupBatchRecommendation()

      // when
      const res = await makeRequestWithParams(publishedCourse.id, { pageSize: '3' })

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      expect(responseBody.rows).toBeDefined()
      expect(responseBody.rows.length).toBe(3)

      // Check recommendation status
      const recommendedBatch = responseBody.rows.find((b) => b.recommended === true)
      const nonRecommendedBatches = responseBody.rows.filter((b) => b.recommended === false)

      // There should be one recommended batch and others should be false
      expect(recommendedBatch).toBeDefined()
      expect(nonRecommendedBatches.length).toBe(2)
    })

    test('should handle pagination for unpublished course when user is staff', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()

      // Create batches for unpublished course
      const unpublishedBatches = []
      for (let i = 1; i <= 3; i++) {
        const batch = {
          id: crypto.randomUUID() as UUID,
          courseId: unpublishedCourse.id,
          teacherId: testProfile.id,
          fee: 300000 + i * 1000,
          billingCycle: 'months' as const,
          graceDays: 3,
          startDate: mocked.now.plus({ days: 20 + i * 3 }).toFormat('yyyy-MM-dd'),
          timezone: 'Asia/Kolkata',
          cycleCount: 8,
          over: false,
          seatCount: 25,
          studentCount: 0,
          admissionOpen: true,
          creationRemarks: `${testProfile.id} profile created unpublished batch ${i}`,
        }
        unpublishedBatches.push(batch)
      }
      await db.insert(batchTable).values(unpublishedBatches)

      // when - Staff user should see unpublished course batches
      const staffRes = await makeAuthenticatedRequest(unpublishedCourse.id, testProfile.id)

      // when - Non-staff user should not see unpublished course batches
      const nonStaffRes = await makeNonStaffAuthenticatedRequest(unpublishedCourse.id, nonStaffProfile.id)

      // then
      expect(staffRes.status).toBe(200)
      expect(nonStaffRes.status).toBe(200)

      const staffBody = (await staffRes.json()) as BatchListResponse
      const nonStaffBody = (await nonStaffRes.json()) as BatchListResponse

      // Staff should see all 3 batches
      expect(staffBody.rows.length).toBe(3)

      // Non-staff should see no batches
      expect(nonStaffBody.rows.length).toBe(0)

      // Test pagination for staff with small pageSize
      await makeRequestWithParams(unpublishedCourse.id, { pageSize: '2' })
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${await createAccessToken(env.JWT_SECRET_KEY, testUser.id)}`,
        [PROFILE_HEADER]: testProfile.id,
      }

      const paginatedRes = await app.request(
        `/api/courses/${unpublishedCourse.id}/batches?pageSize=2`,
        { method: 'GET', headers },
        {},
      )

      expect(paginatedRes.status).toBe(200)
      const paginatedBody = (await paginatedRes.json()) as BatchListResponse
      expect(paginatedBody.rows.length).toBe(2)

      // All returned batches should be from the correct course
      for (const batch of paginatedBody.rows) {
        expect(batch.courseId).toBe(unpublishedCourse.id)
      }
    })

    test('should validate pageSize parameter', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()

      // when - Invalid pageSize (non-numeric)
      const res1 = await makeRequestWithParams(publishedCourse.id, { pageSize: 'invalid' })

      // then
      expect(res1.status).toBe(400)
      const responseBody1 = await res1.json()
      expect(responseBody1.error.issues).toBeDefined()
      expect(responseBody1.error.issues.some((issue: { path: string[] }) => issue.path.includes('pageSize'))).toBe(true)

      // when - PageSize too large
      const res2 = await makeRequestWithParams(publishedCourse.id, { pageSize: '101' })

      // then
      expect(res2.status).toBe(400)
      const responseBody2 = await res2.json()
      expect(responseBody2.error.issues).toBeDefined()
      expect(responseBody2.error.issues.some((issue: { path: string[] }) => issue.path.includes('pageSize'))).toBe(true)

      // when - Zero pageSize
      const res3 = await makeRequestWithParams(publishedCourse.id, { pageSize: '0' })

      // then
      expect(res3.status).toBe(400)
      const responseBody3 = await res3.json()
      expect(responseBody3.error.issues).toBeDefined()
    })

    test('should validate beyondId parameter', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()

      // when - Invalid beyondId (non-UUID)
      const res = await makeRequestWithParams(publishedCourse.id, {
        beyondId: 'invalid-uuid',
        fromStartDate: '2024-01-01',
        fromCreatedAt: '2024-01-01T10:00:00.000Z',
      })

      // then
      expect(res.status).toBe(400)
      const responseBody = await res.json()
      expect(responseBody.error.issues).toBeDefined()
      expect(responseBody.error.issues.some((issue: { path: string[] }) => issue.path.includes('beyondId'))).toBe(true)
    })

    test('should validate date parameters', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()

      // when - Invalid fromStartDate
      const res1 = await makeRequestWithParams(publishedCourse.id, {
        beyondId: crypto.randomUUID(),
        fromStartDate: 'invalid-date',
        fromCreatedAt: '2024-01-01T10:00:00.000Z',
      })

      // then
      expect(res1.status).toBe(400)
      const responseBody1 = await res1.json()
      expect(responseBody1.error.issues).toBeDefined()
      expect(responseBody1.error.issues.some((issue: { path: string[] }) => issue.path.includes('fromStartDate'))).toBe(
        true,
      )

      // when - Invalid fromCreatedAt
      const res2 = await makeRequestWithParams(publishedCourse.id, {
        beyondId: crypto.randomUUID(),
        fromStartDate: '2024-01-01',
        fromCreatedAt: 'invalid-datetime',
      })

      // then
      expect(res2.status).toBe(400)
      const responseBody2 = await res2.json()
      expect(responseBody2.error.issues).toBeDefined()
      expect(responseBody2.error.issues.some((issue: { path: string[] }) => issue.path.includes('fromCreatedAt'))).toBe(
        true,
      )
    })

    test('should handle default pageSize when not specified', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupPaginationTestData()

      // when - No pageSize specified, should use default (20)
      const res = await makeRequestWithParams(publishedCourse.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      expect(responseBody.rows).toBeDefined()
      // Should return all 5 batches since default pageSize (20) is larger than the total
      expect(responseBody.rows.length).toBe(5)
    })

    test('should handle edge case with single batch', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()

      const singleBatch = {
        id: crypto.randomUUID() as UUID,
        courseId: publishedCourse.id,
        teacherId: testProfile.id,
        fee: 500000,
        billingCycle: 'months' as const,
        graceDays: 3,
        startDate: mocked.now.plus({ days: 30 }).toFormat('yyyy-MM-dd'),
        timezone: 'Asia/Kolkata',
        cycleCount: 12,
        over: false,
        seatCount: 50,
        studentCount: 0,
        admissionOpen: true,
        creationRemarks: `${testProfile.id} profile created single batch`,
      }
      await db.insert(batchTable).values([singleBatch])

      // when
      const res = await makeRequestWithParams(publishedCourse.id, { pageSize: '2' })

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      expect(responseBody.rows.length).toBe(1)
      expect(responseBody.rows[0].id).toBe(singleBatch.id)
    })

    test('should handle pagination with mixed batch events', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      const testBatches = await setupPaginationTestData()

      // Add events to some batches
      const batchEvents = [
        {
          id: crypto.randomUUID() as UUID,
          batchId: testBatches[0].id,
          at: '10:00',
          durationMinutes: 60,
          days: ['monday', 'wednesday'],
          eventType: 'meet',
          eventXid: 'event-1',
        },
        {
          id: crypto.randomUUID() as UUID,
          batchId: testBatches[2].id,
          at: '14:00',
          durationMinutes: 90,
          days: ['tuesday', 'thursday'],
          eventType: 'meet',
          eventXid: 'event-2',
        },
      ]
      await db.insert(batchEventTable).values(batchEvents)

      // when
      const res = await makeRequestWithParams(publishedCourse.id, { pageSize: '3' })

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as BatchListResponse
      expect(responseBody.rows.length).toBe(3)

      // Verify some batches have events and some don't
      const batchesWithEvents = responseBody.rows.filter((b) => b.events.length > 0)
      const batchesWithoutEvents = responseBody.rows.filter((b) => b.events.length === 0)

      expect(batchesWithEvents.length).toBeGreaterThan(0)
      expect(batchesWithoutEvents.length).toBeGreaterThan(0)

      // Verify events structure for batches that have them
      for (const batch of batchesWithEvents) {
        for (const event of batch.events) {
          expect(event.id).toMatch(UUID_REGEX)
          expect(event.at).toMatch(/^([01]\d|2[0-3]):([0-5]\d)$/)
          expect(typeof event.durationMinutes).toBe('number')
          expect(Array.isArray(event.days)).toBe(true)
          expect(event.eventType).toBe('meet')
          expect(event.eventXid).toBeDefined()
        }
      }
    })
  })
})

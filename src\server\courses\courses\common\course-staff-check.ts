import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { db } from '@/server/db/db'
import { profileTable, userTable, academyStaffTable, courseTable } from '@/server/db/schema'
import { profileToCheckColumns } from '@/server/profiles/common/profile-check.server'
import { withLog } from '@/shared/common/withLog.shared'

export const findCourseStaffDaf = (log: Logger, courseId: UUID, profileId: UUID) => {
  return withLog(log, 'findStaffDaf', async () => {
    return db
      .select(profileToCheckColumns)
      .from(courseTable)
      .innerJoin(academyStaffTable, eq(academyStaffTable.academyId, courseTable.academyId))
      .innerJoin(profileTable, eq(profileTable.id, profileId))
      .innerJoin(userTable, eq(userTable.id, profileTable.userId))
      .where(and(eq(courseTable.id, courseId), eq(academyStaffTable.profileId, profileId)))
      .then((rows) => rows[0])
  })
}

import { useEffect, useMemo, useState } from 'react'
import { navigate } from 'vike/client/router'

import { showNotification } from '@/pages/common/notification/notification-store'
import { ShowErrors } from '@/pages/common/ShowErrors'
import { WithProfile } from '@/pages/profiles/common/WithProfile'
import { type OmniError } from '@/shared/common/error-utils.shared'
import { $InviteProfileForm, type InviteProfileForm } from '@/shared/profiles/profile-utils.shared'
import { ERoles, ROLES_PROVIDING_SERVICE, type Role } from '@/shared/profiles/role-utils.shared'
import { FormButtons, PageContent, PageLayout } from '@ui/common/form/page-layout'
import { Radio } from '@ui/common/form/Radio'

import { useCurrentProfile } from '../common/current-profile-store'
import { useInviteProfileMutation } from '../common/profile-queries'

export const InviteProfilePage = () => {
  const { currentProfile } = useCurrentProfile()

  // Form state
  const [formData, setFormData] = useState<InviteProfileForm>({
    email: '',
    role: 'teacher',
  })

  // Error state
  const [omniError, setOmniError] = useState<OmniError | undefined>()

  // Invite profile mutation
  const { mutate: inviteProfile, isPending: isInvitingProfile } = useInviteProfileMutation()

  // Initialize role based on current profile's service recipients
  useEffect(() => {
    if (currentProfile) {
      const rolesToInvite = ERoles[currentProfile.role].serviceRecipients
      setFormData((prev) => ({ ...prev, role: rolesToInvite[0] ?? 'teacher' }))
    }
  }, [currentProfile])

  // Validate form data when it changes
  useEffect(() => {
    const parsed = $InviteProfileForm.safeParse(formData)
    setOmniError(parsed.error)
  }, [formData])

  // Compute role options based on current profile
  const roleOptions = useMemo(() => {
    if (!currentProfile) return []
    return ERoles[currentProfile.role].serviceRecipients.map((role) => ({
      id: role,
      name: ERoles[role].name,
      description: ERoles[role].description,
    }))
  }, [currentProfile])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    inviteProfile(formData, {
      onSuccess: () => {
        showNotification({
          heading: 'Invite Sent',
          type: 'success',
        })
        void navigate('/')
      },
      onError: (error) => {
        setOmniError(error.error)
      },
    })
  }

  return (
    <WithProfile roleAnyOf={ROLES_PROVIDING_SERVICE}>
      <PageLayout
        maxWidth="max-w-2xl"
        title="Invite a Profile"
        description={
          <>
            <p>Invite someone to create a profile</p>
            <p className="text-base/8 text-gray-900 italic">First ask the invitee to sign up if they haven't already</p>
          </>
        }
      >
        <form onSubmit={handleSubmit}>
          <PageContent>
            <div className="space-y-8 pt-8 pb-2">
              {/* Email Field */}
              <div>
                <label htmlFor="email" className="form-label">
                  Email
                </label>
                <div className="mt-2 grid grid-cols-1">
                  <input
                    id="email"
                    value={formData.email}
                    onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                    type="email"
                    required
                    className="form-input max-w-lg"
                    placeholder="<EMAIL>"
                  />
                </div>
                <ShowErrors error={omniError} path={['email']} />
              </div>

              {/* Role Selection */}
              <fieldset aria-label="Role">
                <legend className="font-medium text-gray-900">Role</legend>
                <div className="mt-4 space-y-5">
                  {roleOptions.map((option) => (
                    <Radio
                      key={option.id}
                      id={option.id}
                      value={option.id}
                      checked={formData.role === option.id}
                      onChange={(e) => setFormData((prev) => ({ ...prev, role: e.target.value as Role }))}
                      label={option.name}
                      description={option.description}
                    />
                  ))}
                </div>
              </fieldset>
              <div>
                <FormButtons omniError={omniError} onCancel={() => navigate('/')} isSubmitting={isInvitingProfile} />
              </div>
            </div>
          </PageContent>
        </form>
      </PageLayout>
    </WithProfile>
  )
}

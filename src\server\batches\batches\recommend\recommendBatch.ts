import { UUID } from 'crypto'

import { eq, sql } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { db } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { batchRecommendationTable, batchTable } from '@/server/db/schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { ensureCanUpdateBatch } from '../common/ensureCanUpdateBatch'

const findBatchDaf = (log: pino.Logger, batchId: UUID) =>
  withLog(log, 'findBatchDaf', () =>
    db.query.batchTable.findFirst({
      ...dummyColumn,
      where: eq(batchTable.id, batchId),
    }),
  )

const upsertBatchRecommendationDaf = (log: pino.Logger, batchId: UUID) =>
  withLog(log, 'upsertBatchRecommendationDaf', () =>
    db
      .insert(batchRecommendationTable)
      .values({
        courseId: sql`(SELECT ${batchTable.courseId} FROM ${batchTable} WHERE ${batchTable.id} = ${batchId})`,
        batchId,
        createdAt: utc().toJSDate(),
      })
      .onConflictDoUpdate({
        target: batchRecommendationTable.courseId,
        set: {
          batchId,
          updatedAt: utc().toJSDate(),
        },
      }),
  )

export const recommendBatch = async (c: Ctx, batchId: UUID) => {
  const batch = await findBatchDaf(c.log, batchId)
  ensureExists(batch, batchId, 'Batch')

  await ensureCanUpdateBatch(c, batchId)
  await upsertBatchRecommendationDaf(c.log, batchId)
}

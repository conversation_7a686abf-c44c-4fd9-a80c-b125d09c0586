import { usePageContext } from 'vike-react/usePageContext'

import { useSignedIn } from '@ui/users/auth/auth-token-store'

import { CreateAProfileTeaser } from './CreateAProfileTeaser'
import { useMyProfilesQuery } from './profile-queries'

export const useCreateAProfileTeaser = () => {
  const pageContext = usePageContext()

  const signedIn = useSignedIn()
  const { data: profiles } = useMyProfilesQuery()

  return signedIn && profiles && profiles.length === 0 && pageContext.urlPathname !== '/profiles/add' ?
      CreateAProfileTeaser
    : null
}

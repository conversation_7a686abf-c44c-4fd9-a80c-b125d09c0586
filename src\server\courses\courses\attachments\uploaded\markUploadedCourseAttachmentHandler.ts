import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { markUploadedCourseAttachment } from './markUploadedCourseAttachment'

export const markUploadedCourseAttachmentHandler = new Hono<HonoVars>().put(
  '/',
  zValidator('param', $HasId),
  async (c) => {
    const attachment = c.req.valid('param')

    await markUploadedCourseAttachment(getCtx(c), attachment.id)
    return c.body(null, 204)
  },
)

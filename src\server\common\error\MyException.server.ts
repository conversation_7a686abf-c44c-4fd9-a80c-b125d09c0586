import type { MyIssue } from '@/shared/common/error-utils.shared'
import type { ContentfulStatusCode } from 'hono/utils/http-status'

export class MyException extends Error {
  name: string = 'MyException'
  status: ContentfulStatusCode
  issues: MyIssue[]
  constructor(logMessage: string, status: ContentfulStatusCode, issues: MyIssue[]) {
    super(logMessage)
    this.issues = issues
    this.status = status
  }
}

import { UUID } from 'crypto'

import { useEffect, useState } from 'react'
import { navigate } from 'vike/client/router'

import { ShowData } from '@/pages/common/ShowData'
import { CourseWithDescr } from '@/server/courses/courses/get/getCourse.server'
import { type OmniError } from '@/shared/common/error-utils.shared'
import { html2Markdown } from '@/shared/common/markdown-utils.shared'
import { $EditCourseForm, coursePath, EditCourseForm } from '@/shared/course/course-utils.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'
import { useMyAcademyQuery } from '@ui/academies/common/academy-queries'
import { ErrorAlert } from '@ui/common/alerts/ErrorAlert'
import { FormButtons, PageContent, PageLayout } from '@ui/common/form/page-layout'
import { CourseCommonEditor } from '@ui/courses/common/CourseCommonEditor'
import { CourseHeader } from '@ui/courses/common/CourseHeader'
import { useCourseSuspenseQuery, useUpdateCourseMutation } from '@ui/courses/common/queries/course-queries'
import { WithProfile } from '@ui/profiles/common/WithProfile'

export const EditCoursePage = ({ courseId }: { courseId: UUID }) => {
  const { data: myAcademy, isPending: myAcademyIsPending, error: myAcademyError } = useMyAcademyQuery()
  const { data: courseData, error: courseError } = useCourseSuspenseQuery(courseId, true)
  const course = courseData as CourseWithDescr

  const [formData, setFormData] = useState<Required<EditCourseForm>>({
    name: course?.name ?? '',
    descr: course?.descr ? html2Markdown(course.descr) : '',
  })

  const [omniError, setOmniError] = useState<OmniError | undefined>()
  const { mutate: updateCourse, isPending: updateCourseIsPending } = useUpdateCourseMutation(courseId)

  // Validate form data when it changes
  useEffect(() => {
    const parsed = $EditCourseForm.safeParse(formData)
    setOmniError(parsed.error)
  }, [formData])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    updateCourse(formData, {
      onSuccess: () => {
        void navigate(coursePath({ id: courseId, name: formData.name }))
      },
      onError: (error) => {
        setOmniError(error.error)
      },
    })
  }

  return (
    <WithProfile roleAnyOf={ACADEMY_STAFF}>
      <ShowData isPending={myAcademyIsPending} error={myAcademyError || courseError} spinnerSize="1.25rem">
        <div>
          <div className="px-4">
            <CourseHeader course={course!} />
          </div>
          {course!.academyId === myAcademy!.id ?
            <PageLayout>
              <form onSubmit={handleSubmit}>
                <PageContent>
                  <div>
                    <CourseCommonEditor
                      name={formData.name}
                      descr={formData.descr}
                      omniError={omniError}
                      onNameChange={(value) => setFormData((prev) => ({ ...prev, name: value }))}
                      onDescrChange={(value) => setFormData((prev) => ({ ...prev, descr: value }))}
                    />
                    <FormButtons
                      omniError={omniError}
                      onCancel={() => navigate(coursePath({ id: courseId, name: course!.name }))}
                      isSubmitting={updateCourseIsPending}
                    />
                  </div>
                </PageContent>
              </form>
            </PageLayout>
          : <ErrorAlert>You are not a staff of the academy the course belongs to.</ErrorAlert>}
        </div>
      </ShowData>
    </WithProfile>
  )
}

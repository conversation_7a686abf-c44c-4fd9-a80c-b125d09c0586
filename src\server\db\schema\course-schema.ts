import { relations, sql } from 'drizzle-orm'
import { boolean, check, index, integer, pgTable, smallint, text, uniqueIndex } from 'drizzle-orm/pg-core'

import { academyTable } from './academy-schema'
import { auditColumns, suspensionCheck, suspensionColumns, timestampz, uid } from './helpers'

export const courseTagGroupTable = pgTable(
  'course_tag_group',
  {
    id: uid().primaryKey(),
    name: text().notNull().unique(),
    position: smallint().notNull(),
    ...suspensionColumns,
  },
  (t) => [check('chk_name_len', sql`char_length(${t.name}) <= 30`), ...suspensionCheck(t)],
)

export const courseTagTable = pgTable(
  'course_tag',
  {
    id: uid().primaryKey(),
    name: text().notNull().unique(),
    groupId: uid()
      .notNull()
      .references(() => courseTagGroupTable.id),
    position: smallint().notNull(),
    descr: text().notNull(),
    ...suspensionColumns,
  },
  (t) => [
    check('chk_name_len', sql`char_length(${t.name}) <= 30`),
    check('descr_len', sql`char_length(${t.descr}) <= 255`),
    ...suspensionCheck(t),
    index('idx_course_tag_group_id').on(t.groupId),
  ],
)

export const courseTagGroupRelations = relations(courseTagGroupTable, ({ many }) => ({
  tags: many(courseTagTable),
}))

export const courseTable = pgTable(
  'course',
  {
    id: uid().primaryKey(),
    name: text().notNull(),
    descr: text(),
    publishedAt: timestampz(),
    academyId: uid()
      .notNull()
      .references(() => academyTable.id),
    ...auditColumns,
    ...suspensionColumns,
  },
  (t) => [
    check('chk_name_len', sql`char_length(${t.name}) <= 255`),
    check('descr_len', sql`char_length(${t.descr}) <= 10000`),
    ...suspensionCheck(t),
    index('idx_course_name').on(t.name),
    index('idx_course_academy_id').on(t.academyId),
  ],
)

export const courseTagAssignmentTable = pgTable(
  'course_tag_assignment',
  {
    id: uid().primaryKey(),
    courseId: uid()
      .notNull()
      .references(() => courseTable.id, { onDelete: 'cascade' }),
    tagId: uid()
      .notNull()
      .references(() => courseTagTable.id),
  },
  (t) => [
    index('idx_course_tag_assignment_course_id').on(t.courseId),
    index('idx_course_tag_assignment_tag_id').on(t.tagId),
  ],
)

export const courseAttachmentTable = pgTable(
  'course_attachment',
  {
    id: uid().primaryKey(),
    courseId: uid()
      .notNull()
      .references(() => courseTable.id),
    position: text().notNull(),
    name: text().notNull(),
    contentType: text().notNull(),
    sizeBytes: integer().notNull(),
    free: boolean().notNull(),
    uploaded: boolean().notNull(),
    ...auditColumns,
  },
  (t) => [
    check('chk_position_len', sql`char_length(${t.position}) <= 255`),
    check('chk_name_len', sql`char_length(${t.name}) <= 255`),
    check('chk_content_type_len', sql`char_length(${t.contentType}) <= 255`),
    check('chk_size_bytes', sql`${t.sizeBytes} > 0`),
    uniqueIndex('idx_course_attachment_course_id_name').on(t.courseId, t.name),
    uniqueIndex('idx_course_attachment_course_id_position').on(t.courseId, t.position),
  ],
)

// Define relations
export const courseRelations = relations(courseTable, ({ many, one }) => ({
  tagAssignments: many(courseTagAssignmentTable),
  attachments: many(courseAttachmentTable),
  academy: one(academyTable, {
    fields: [courseTable.academyId],
    references: [academyTable.id],
  }),
}))

export const courseTagRelations = relations(courseTagTable, ({ many, one }) => ({
  group: one(courseTagGroupTable, {
    fields: [courseTagTable.groupId],
    references: [courseTagGroupTable.id],
  }),
  courseAssignments: many(courseTagAssignmentTable),
}))

export const courseTagAssignmentRelations = relations(courseTagAssignmentTable, ({ one }) => ({
  course: one(courseTable, {
    fields: [courseTagAssignmentTable.courseId],
    references: [courseTable.id],
  }),
  tag: one(courseTagTable, {
    fields: [courseTagAssignmentTable.tagId],
    references: [courseTagTable.id],
  }),
}))

export const courseAttachmentRelations = relations(courseAttachmentTable, ({ one }) => ({
  course: one(courseTable, {
    fields: [courseAttachmentTable.courseId],
    references: [courseTable.id],
  }),
}))

import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'

import { countStudentFeePaymentItems } from './countStudentFeePaymentItems'

export const countStudentFeePaymentItemsHandler = new Hono<HonoVars>().get('/', async (c) => {
  const paymentCount = await countStudentFeePaymentItems(getCtx(c))
  return c.json(paymentCount, 200)
})

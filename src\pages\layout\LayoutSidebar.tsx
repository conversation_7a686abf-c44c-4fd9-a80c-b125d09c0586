import {
  BookOpenIcon,
  InformationCircleIcon,
  PhoneIcon,
  QuestionMarkCircleIcon,
  DocumentTextIcon,
  DocumentDuplicateIcon,
  WrenchIcon,
} from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { navigate } from 'vike/client/router'
import { usePageContext } from 'vike-react/usePageContext'

import { useAcademySidebarLinks } from '@ui/academies/common/useAcademySidebarLinks'
import { useAdminSidebarLinks } from '@ui/admin/common/useAdminSidebarLinks'
import logoSrc from '@ui/common/assets/tuitionlance-high-resolution-logo.svg'
import { setSidebarOpen } from '@ui/layout/layout-store'
import { useProfileSidebarLinks } from '@ui/profiles/common/useProfileSidebarLinks'
import { useStudentSidebarLinks } from '@ui/students/common/useStudentSidebarLinks'
import { useTeacherSidebarLinks } from '@ui/teachers/common/useTeacherSidebarLinks'

const generalLinks = [{ name: 'Courses', href: '/courses?includeUnpublished=true', icon: BookOpenIcon }]

const helpLinks = [
  { name: 'Help', href: '/help', icon: InformationCircleIcon },
  { name: 'Faq', href: '/faq', icon: QuestionMarkCircleIcon },
  { name: 'Legal Agreements', href: '/legal-agreements', icon: DocumentDuplicateIcon },
  { name: 'Terms of Service', href: '/legal-agreements/terms-of-service', icon: DocumentTextIcon },
  { name: 'Privacy Policy', href: '/legal-agreements/privacy-policy', icon: DocumentTextIcon },
  { name: 'Contact', href: '/contact', icon: PhoneIcon },
  { name: 'Diagnostic Logs', href: '/diagnostic-logs', icon: WrenchIcon },
]

const linkClass = (isActive: boolean) =>
  clsx(
    isActive ? 'bg-indigo-700 text-white' : 'text-indigo-200 hover:bg-indigo-700 hover:text-white',
    'group flex gap-x-3 rounded-md px-2 py-1 text-sm/6 font-semibold',
  )

export const LayoutSidebar = () => {
  return (
    <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-indigo-600 px-6 pb-4">
      <div className="flex h-16 shrink-0 items-center">
        <HomeLogo />
      </div>
      <nav className="flex flex-1 flex-col">
        <ul role="list" className="flex flex-1 flex-col gap-y-7">
          <GroupMenu name="Catalog" links={generalLinks} />
          <StudentMenu />
          <TeacherMenu />
          <AcademyMenu />
          <ProfileMenu />
          <AdminMenu />
          <GroupMenu name="Help" links={helpLinks} />
        </ul>
      </nav>
    </div>
  )
}

const HomeLogo = () => (
  <div className="mt-8 mb-4">
    <Logo />
  </div>
)

const Logo = () => (
  <div>
    <Link href="/" classes={() => ''}>
      <img src={logoSrc} alt="Logo" />
    </Link>
  </div>
)

const ItemIcon = ({ item }: { item: { icon: React.ElementType } }) => (
  <item.icon aria-hidden="true" className="size-6 shrink-0" />
)

const StudentMenu = () => {
  const studentLinks = useStudentSidebarLinks()

  return GroupMenu({
    name: 'Student',
    links: studentLinks,
  })
}

const TeacherMenu = () => {
  const teacherLinks = useTeacherSidebarLinks()

  return GroupMenu({
    name: 'Teacher',
    links: teacherLinks,
  })
}

const ProfileMenu = () => {
  const profileLinks = useProfileSidebarLinks()

  return GroupMenu({
    name: 'Advanced',
    links: profileLinks,
  })
}

const AcademyMenu = () => {
  const academyLinks = useAcademySidebarLinks()

  return GroupMenu({
    name: 'My Academy',
    links: academyLinks,
  })
}

const AdminMenu = () => {
  const adminLinks = useAdminSidebarLinks()

  return GroupMenu({
    name: 'Admin',
    links: adminLinks,
  })
}

const GroupMenu = ({
  name,
  links,
}: {
  name?: string
  links: { name: string; href: string; icon: React.ElementType }[]
}) =>
  links.length > 0 && (
    <li>
      {name && <div className="text-xs/6 font-semibold text-indigo-200 mb-2  border-b border-indigo-200">{name}</div>}
      <ul role="list" className="-mx-2">
        {links.map((link) => (
          <li key={link.name}>
            <Link href={link.href} classes={linkClass}>
              <ItemIcon item={link} />
              <span className="truncate">{link.name}</span>
            </Link>
          </li>
        ))}
      </ul>
    </li>
  )

const Link = ({
  href,
  children,
  classes,
}: {
  href: string
  children: React.ReactNode
  classes: (isActive: boolean) => string
}) => {
  const pageContext = usePageContext()
  const { urlPathname } = pageContext
  const hrefPathname = href.split('?')[0]
  const isActive = urlPathname === hrefPathname

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault()
    // Close the sidebar when a link is clicked
    setSidebarOpen(false)
    void navigate(href)
  }

  return (
    <a href={href} onClick={handleClick} className={classes(isActive)}>
      {children}
    </a>
  )
}

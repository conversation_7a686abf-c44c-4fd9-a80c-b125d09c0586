import { UUID } from 'crypto'

import { useMutation, useQuery, useQueryClient, useSuspenseQuery } from '@tanstack/react-query'
import { useDeferredValue } from 'react'

import { Profile } from '@/server/profiles/get/getProfile.server'
import { MyProfile } from '@/server/profiles/get-my/getMyProfiles'
import { isClient } from '@/shared/common/common-utils.shared'
import { useServerQuery } from '@/shared/common/serverQueries.shared'
import {
  AddProfileForm,
  EditProfileForm,
  InviteProfileForm,
  ProfilesSearch,
} from '@/shared/profiles/profile-utils.shared'
import { ROLES_PROVIDING_SERVICE } from '@/shared/profiles/role-utils.shared'
import { api, withError } from '@ui/api-client'
import { useInvalidateDetail } from '@ui/common/query-helper'
import { dateConverter } from '@ui/common/utils/dateConverter'
import { getCurrentProfileId, setCurrentProfileId, useCurrentProfile } from '@ui/profiles/common/current-profile-store'
import { useUserId } from '@ui/users/auth/auth-token-store'

// Define date fields for profiles
const profileDateFields = ['suspendedAt', 'submittedForApprovalAt', 'approvedAt']

const profileKeys = {
  all: ['profile'] as const,
  lists: () => [...profileKeys.all, 'list'] as const,
  list: (search: ProfilesSearch) => [...profileKeys.lists(), search] as const,
  myList: (userId: UUID | null) => [...profileKeys.lists(), 'my', userId] as const,
  toApproveList: (currentProfileId: UUID | null, toGet: boolean) =>
    [...profileKeys.lists(), 'toApprove', currentProfileId, toGet] as const,
  details: () => [...profileKeys.all, 'detail'] as const,
  detail: (id: UUID) => [...profileKeys.details(), id] as const,
  contact: (id: UUID) => [...profileKeys.all, 'contact', id] as const,
}

export const useAddProfileMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (form: AddProfileForm) =>
      api()
        .profiles.$post({ json: form })
        .then((res) => res.json()),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: profileKeys.lists() })
    },
  })
}

export const useUpdateProfileMutation = (id: UUID) => {
  const invalidateProfile = useInvalidateDetail(profileKeys)

  return useMutation({
    mutationFn: (form: EditProfileForm) => api().profiles[':id'].$put({ param: { id }, json: form }),
    onSuccess: () => {
      invalidateProfile(id)
    },
  })
}

export const useMyProfilesQuery = () => {
  const userId = useUserId()
  const currentProfileId = getCurrentProfileId()

  return useQuery({
    queryKey: profileKeys.myList(userId),
    queryFn: () =>
      userId ?
        api()
          .users.me.profiles.$get()
          .then((res) => res.json())
          .then((profiles) => profiles.rows)
          .then(dateConverter<MyProfile[]>(profileDateFields))
          .then((profiles) => {
            if (!currentProfileId || profiles.findIndex((profile) => profile.id === currentProfileId) === -1) {
              setCurrentProfileId(profiles[0]?.id ?? null)
            }
            return profiles
          })
      : [],
  })
}

export const useProfileSuspenseQuery = (id: UUID) => {
  const getProfile = useServerQuery('getProfile')

  const { data } = useSuspenseQuery({
    queryKey: profileKeys.detail(id),
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .profiles[':id'].$get({ param: { id } })
            .then((res) => res.json())
            .then(dateConverter<Profile>(profileDateFields)),
        )
      : getProfile(id),
  })
  return useDeferredValue(data)
}

export const useProfilesSuspenseQuery = (search: ProfilesSearch) => {
  const listProfiles = useServerQuery('listProfiles')

  const { data } = useSuspenseQuery({
    queryKey: profileKeys.list(search),
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .profiles.$get({ query: search })
            .then((res) => res.json())
            .then((profiles) => profiles.rows),
        )
      : listProfiles(search),
  })
  return useDeferredValue(data)
}

export const useInviteProfileMutation = () => {
  return useMutation({
    mutationFn: (form: InviteProfileForm) => api().profiles.invite.$post({ json: form }),
  })
}

export const useRemoveProfileMutation = (id: UUID) => {
  const invalidateProfile = useInvalidateDetail(profileKeys)

  return useMutation({
    mutationFn: () => api().profiles[':id'].$delete({ param: { id } }),
    onSuccess: () => {
      if (getCurrentProfileId() === id) {
        setCurrentProfileId(null)
      }
      invalidateProfile(id, true)
    },
  })
}

export const useSubmitProfileForApprovalMutation = (id: UUID) => {
  const invalidateProfile = useInvalidateDetail(profileKeys)

  return useMutation({
    mutationFn: () => api().profiles[':id']['submit-for-approval'].$post({ param: { id } }),
    onSuccess: () => {
      invalidateProfile(id)
    },
  })
}

export const useProfilesToApproveQuery = () => {
  const { currentProfile } = useCurrentProfile()
  const toGet = !!currentProfile && ROLES_PROVIDING_SERVICE.includes(currentProfile.role)

  return useQuery({
    queryKey: profileKeys.toApproveList(currentProfile?.id ?? null, toGet),
    queryFn: () =>
      toGet ?
        api()
          .profiles[':id']['to-approve'].$get({ param: { id: currentProfile.id } })
          .then((res) => res.json())
          .then((profiles) => profiles.rows)
      : [],
  })
}

export function useApproveProfileMutation(profileId: UUID) {
  const invalidateProfile = useInvalidateDetail(profileKeys)

  return useMutation({
    mutationFn: () => api().profiles[':id'].approve.$put({ param: { id: profileId } }),
    onSuccess: () => {
      invalidateProfile(profileId)
    },
  })
}

export const useProfileContactQuery = (id: UUID) => {
  return useQuery({
    queryKey: profileKeys.contact(id),
    queryFn: () =>
      api()
        .profiles[':id'].contact.$get({ param: { id } })
        .then((res) => res.json()),
  })
}

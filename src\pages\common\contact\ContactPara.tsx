import { EnvelopeIcon, PhoneIcon } from '@heroicons/react/24/outline'

export const ContactPara = ({ mobile, email }: { mobile: string; email: string }) => {
  return (
    <>
      <div className="flex items-center gap-2 mt-2">
        <EnvelopeIcon className="h-5 w-5 text-blue-700" />
        <a href={`mailto:${email}`} className="text-sm text-gray-500 hover:text-gray-700">
          {email}
        </a>
      </div>
      <div className="flex items-center gap-2 mt-2">
        <PhoneIcon className="h-5 w-5 text-blue-700" />
        <span className="text-sm text-gray-500 hover:text-gray-700">{mobile}</span>
      </div>
    </>
  )
}

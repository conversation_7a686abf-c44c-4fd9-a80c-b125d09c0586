import { useEffect, useState } from 'react'
import { navigate } from 'vike/client/router'
import { usePageContext } from 'vike-react/usePageContext'

import { ProfileCommonEditor } from '@/pages/profiles/common/ProfileCommonEditor'
import { Protected } from '@/pages/users/auth/Protected'
import { type OmniError } from '@/shared/common/error-utils.shared'
import { $AddProfileForm, profilePath, type AddProfileForm } from '@/shared/profiles/profile-utils.shared'
import { ERoles, type Role } from '@/shared/profiles/role-utils.shared'
import { CheckBox } from '@ui/common/form/CheckBox'
import { FormButtons, PageContent, PageLayout, PageSection } from '@ui/common/form/page-layout'

import { setCurrentProfileId } from '../common/current-profile-store'
import { useAddProfileMutation } from '../common/profile-queries'

export const AddProfilePage = () => {
  // Get role from URL params
  const { urlParsed } = usePageContext()
  const role = (urlParsed.search.role as Role | undefined) ?? 'student'
  const invitationToken = urlParsed.search.invitationToken as string | undefined

  // Form state
  const [formData, setFormData] = useState<Required<Omit<AddProfileForm, 'role' | 'invitationToken'>>>({
    displayName: '',
    descr: '',
    tncsAccepted: false,
  })

  // Error state
  const [omniError, setOmniError] = useState<OmniError | undefined>()

  // Add profile mutation
  const { mutate: addProfile, isPending: addProfileIsPending } = useAddProfileMutation()

  // Validate form data when it changes
  useEffect(() => {
    const parsed = $AddProfileForm.safeParse({ ...formData, role, invitationToken })
    setOmniError(parsed.error)
  }, [formData, role, invitationToken])

  // Form submission handler
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    addProfile(
      { ...formData, role, invitationToken },
      {
        onSuccess: (profile) => {
          void navigate(profilePath({ id: profile.id, displayName: formData.displayName, role }))
          setCurrentProfileId(profile.id)
        },
        onError: (error) => {
          setOmniError(error.error)
        },
      },
    )
  }

  return (
    <Protected>
      <PageLayout
        title={`Add ${ERoles[role].name} profile`}
        description={`Please fill in your details to add a ${ERoles[role].name} profile`}
      >
        <form onSubmit={handleSubmit}>
          <PageContent>
            <ProfileCommonEditor
              displayName={formData.displayName}
              descr={formData.descr}
              omniError={omniError}
              onDisplayNameChange={(value) => setFormData((prev) => ({ ...prev, displayName: value }))}
              onDescrChange={(value) => setFormData((prev) => ({ ...prev, descr: value }))}
            />

            <PageSection title="Terms and Conditions" description="Please review the terms and conditions">
              <div className="mt-6 space-y-6">
                <div className="text-sm text-gray-600">
                  By signing up, you have already agreed to our{' '}
                  <a href="/legal-agreements" target="_blank" className="link">
                    Terms of Service and Privacy Policy
                  </a>
                  . Please review the sections specifically related to creating a {ERoles[role].name} profile. By
                  proceeding, you confirm that you have read, understood, and agree to be bound by these terms,
                  including any recent updates. Your use of this feature constitutes your ongoing acceptance of these
                  terms.
                </div>

                <CheckBox
                  id="tncsAccepted"
                  checked={formData.tncsAccepted}
                  onChange={(e) => setFormData((prev) => ({ ...prev, tncsAccepted: e.target.checked }))}
                  label={
                    <>
                      I confirm that I have read, understood, and agree to the Terms of Service and Privacy Policy
                      sections related to creating a {ERoles[role].name} profile.
                    </>
                  }
                />
              </div>
            </PageSection>

            <FormButtons
              omniError={omniError}
              onCancel={() => navigate('/')}
              otherErrors={!formData.tncsAccepted}
              isSubmitting={addProfileIsPending}
            />
          </PageContent>
        </form>
      </PageLayout>
    </Protected>
  )
}

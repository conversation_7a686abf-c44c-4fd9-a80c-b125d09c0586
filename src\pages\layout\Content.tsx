import { Suspense } from 'react'
import { ErrorBoundary } from 'react-error-boundary'

import { ErrorFallback } from '@ui/common/ErrorFallback'
import { SuspenseFallback } from '@ui/common/SuspenseFallback'
import './Content.css'

export const Content = ({ children }: { children: React.ReactNode }) => (
  <div id="page-container">
    <div id="page-content">
      <ErrorBoundary FallbackComponent={({ error }) => <ErrorFallback error={error} />}>
        <Suspense fallback={<SuspenseFallback />}>{children}</Suspense>
      </ErrorBoundary>
    </div>
  </div>
)

import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { db } from '@/server/db/db'
import { profileTable, userTable, academyStaffTable } from '@/server/db/schema'
import { profileToCheckColumns } from '@/server/profiles/common/profile-check.server'
import { withLog } from '@/shared/common/withLog.shared'

export const findAcademyStaffDaf = (log: Logger, academyId: UUID, profileId: UUID) => {
  return withLog(log, 'findStaffDaf', async () => {
    return db
      .select(profileToCheckColumns)
      .from(academyStaffTable)
      .innerJoin(profileTable, eq(profileTable.id, academyStaffTable.profileId))
      .innerJoin(userTable, eq(userTable.id, profileTable.userId))
      .where(and(eq(academyStaffTable.academyId, academyId), eq(academyStaffTable.profileId, profileId)))
      .then((rows) => rows[0])
  })
}

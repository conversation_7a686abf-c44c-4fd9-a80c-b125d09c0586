import { UUID } from 'crypto'

import clsx from 'clsx'

import { useRecommendBatchMutation } from './batch-queries'

export const RecommendBatchButton = ({ batchId, courseId }: { batchId: UUID; courseId: UUID }) => {
  const { mutate: recommendBatch, isPending } = useRecommendBatchMutation(batchId, courseId)
  return (
    <button
      className={clsx('text-xs link underline', isPending && 'cursor-wait')}
      disabled={isPending}
      onClick={() => {
        recommendBatch()
      }}
    >
      {isPending ? 'Recommending...' : 'Recommend'}
    </button>
  )
}

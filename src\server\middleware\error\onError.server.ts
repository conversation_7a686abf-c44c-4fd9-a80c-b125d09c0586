import { Context } from 'hono'
import { HTTPException } from 'hono/http-exception'

import { toErrorPayload } from './toErrorPayload'

export const onError = (error: Error, c: Context) => {
  const log = c.get('log')
  if (error instanceof HTTPException) {
    log.warn({ error }, 'HTTPException')
    return error.getResponse()
  }
  const payload = toErrorPayload(log, error)
  return c.json(payload, payload.status)
}

import { UUID } from 'crypto'

import { zV<PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $AddCourseAttachmentForm } from '@/shared/course/course-utils.shared'

import { addCourseAttachment } from './addCourseAttachment'

export const addCourseAttachmentHandler = new Hono<HonoVars>().post(
  '/',
  zValidator('json', $AddCourseAttachmentForm),
  async (c) => {
    const form = c.req.valid('json')
    const courseId = c.req.param('id') as UUID

    const attachment = await addCourseAttachment(getCtx(c), courseId, form)
    return c.json(attachment, 201)
  },
)

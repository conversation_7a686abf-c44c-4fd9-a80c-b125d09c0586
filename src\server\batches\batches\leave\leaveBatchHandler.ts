import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { leaveBatch } from './leaveBatch'

export const leaveBatchHandler = new Hono<HonoVars>().delete('/', zValidator('param', $HasId), async (c) => {
  const { id: batchId } = c.req.valid('param')

  await leaveBatch(getCtx(c), batchId)
  return c.body(null, 204)
})

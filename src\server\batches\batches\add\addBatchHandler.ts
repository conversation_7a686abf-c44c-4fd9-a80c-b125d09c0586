import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $AddBatchForm } from '@/shared/batch/batch-utils.shared'

import { addBatch } from './addBatch'

export const addBatchHandler = new Hono<HonoVars>().post('/', zValidator('json', $AddBatchForm), async (c) => {
  const form = c.req.valid('json')

  await addBatch(getCtx(c), form)
  return c.body(null, 201)
})

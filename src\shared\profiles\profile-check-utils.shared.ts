import { UUID } from 'crypto'

import { Role } from './role-utils.shared'

export type ProfileToCheck = { id: UUID; role: Role; suspendedAt: Date | null; approvedAt: Date | null }
export type ProfileToCheckOptions = {
  ignoreSuspension?: boolean
  ignoreApproval?: boolean
  roleAnyOf?: readonly Role[]
}

export const suspensionOk = (profile: ProfileToCheck, options?: ProfileToCheckOptions) =>
  options?.ignoreSuspension || profile.suspendedAt === null
export const approvalOk = (profile: ProfileToCheck, options?: ProfileToCheckOptions) =>
  options?.ignoreApproval || profile.approvedAt !== null
export const roleOk = (profile: ProfileToCheck, options?: ProfileToCheckOptions) =>
  !options?.roleAnyOf || options.roleAnyOf.includes(profile.role)

export const profileOk = (profile?: ProfileToCheck | null, options?: ProfileToCheckOptions) =>
  profile && suspensionOk(profile, options) && approvalOk(profile, options) && roleOk(profile, options) ? true : false

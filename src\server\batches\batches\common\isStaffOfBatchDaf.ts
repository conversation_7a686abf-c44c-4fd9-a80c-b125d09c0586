import { UUID } from 'crypto'

import { and, eq, sql } from 'drizzle-orm'
import pino from 'pino'

import { db } from '@/server/db/db'
import { academyStaffTable, batchTable, courseTable } from '@/server/db/schema'
import { withLog } from '@/shared/common/withLog.shared'

export const isStaffOfBatchDaf = (log: pino.Logger, batchId: UUID, profileId: UUID) =>
  withLog(log, 'isStaffOfBatchDaf', () =>
    db
      .select({ dummy: sql<number>`1` })
      .from(batchTable)
      .innerJoin(courseTable, eq(courseTable.id, batchTable.courseId))
      .innerJoin(academyStaffTable, eq(academyStaffTable.academyId, courseTable.academyId))
      .where(and(eq(batchTable.id, batchId), eq(academyStaffTable.profileId, profileId)))
      .limit(1)
      .then((rows) => rows.length > 0),
  )

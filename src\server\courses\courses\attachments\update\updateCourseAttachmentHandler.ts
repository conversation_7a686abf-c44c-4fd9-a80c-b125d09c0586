import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'
import { $EditCourseAttachmentForm } from '@/shared/course/course-utils.shared'

import { updateCourseAttachment } from './updateCourseAttachment'

export const updateCourseAttachmentHandler = new Hono<HonoVars>().put(
  '/',
  zValidator('param', $HasId),
  zValidator('json', $EditCourseAttachmentForm),
  async (c) => {
    const attachment = c.req.valid('param')
    const form = c.req.valid('json')

    await updateCourseAttachment(getCtx(c), attachment.id, form)
    return c.body(null, 204)
  },
)

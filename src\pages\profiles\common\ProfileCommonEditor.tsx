import { type OmniError } from '@/shared/common/error-utils.shared'
import { PROFILE_DESCR_MAX_LEN } from '@/shared/profiles/profile-utils.shared'
import { DISPLAY_NAME_MAX_LEN } from '@/shared/users/user-utils.shared'
import { NameAndDescrEditor } from '@ui/common/form/NameAndDescrEditor'

type ProfileCommonEditorProps = {
  displayName: string
  descr: string
  omniError?: OmniError
  onDisplayNameChange: (value: string) => void
  onDescrChange: (value: string) => void
}

export const ProfileCommonEditor = ({
  displayName,
  descr,
  omniError,
  onDisplayNameChange,
  onDescrChange,
}: ProfileCommonEditorProps) => (
  <NameAndDescrEditor
    name={displayName}
    nameLabel="Display Name"
    namePlaceholder="Enter display name"
    descr={descr}
    omniError={omniError}
    onNameChange={onDisplayNameChange}
    onDescrChange={onDescrChange}
    nameMaxLen={DISPLAY_NAME_MAX_LEN}
    descrMaxLen={PROFILE_DESCR_MAX_LEN}
  />
)

import { useGoogleLogin } from '@react-oauth/google'
import { FormEvent, useEffect, useMemo, useState } from 'react'
import { navigate } from 'vike/client/router'

import { stopWaiting, wait } from '@/pages/common/wait/wait-store'
import { formatDate, today } from '@/shared/common/date-utils.shared'
import { type OmniError } from '@/shared/common/error-utils.shared'
import { getLogger } from '@/shared/common/logger.shared'
import { googleOauthScopes } from '@/shared/users/googleOauthScopes.shared'
import { $UserForm, type UserForm, whetherRequiredTnCsAccepted } from '@/shared/users/user-utils.shared'
import { CheckBox } from '@ui/common/form/CheckBox'
import { Mandatory } from '@ui/common/form/Mandatory'
import { MobileEditor } from '@ui/common/form/MobileEditor'
import { FormButtons, PageContent, PageSection } from '@ui/common/form/page-layout'
import { ShowData } from '@ui/common/ShowData'
import { getMyError } from '@ui/common/utils/error-utils.ui'
import { useGetUserTnCsQuery } from '@ui/users/common/user-queries'

import { GoogleIcon } from '../common/GoogleIcon'

type UserEditorProps = {
  initialFormData?: UserForm
  apiError?: OmniError
  apiIsPending: boolean
  onSubmit: (formData: UserForm) => void
}

const defaultCountryCode = 'IN'

export const UserEditor = ({ initialFormData, apiError, apiIsPending, onSubmit }: UserEditorProps) => {
  const log = getLogger()
  const oauthState = useMemo(() => crypto.randomUUID(), [])

  const [formData, setFormData] = useState<UserForm>(
    initialFormData ?? {
      mobileCountryCode: defaultCountryCode,
      mobile: '',
      acceptedTnCs: [],
      authCode: '',
      legalAgeDeclaration: false,
      informationAccuracyDeclaration: false,
    },
  )

  const [omniError, setOmniError] = useState<OmniError | undefined>()

  const { data: tncs, isPending: tncsIsPending, error: tncsError } = useGetUserTnCsQuery()

  // Validate form data whenever it changes
  useEffect(() => {
    const parsed = $UserForm.safeParse(formData)
    setOmniError(parsed.error)
  }, [formData])

  // Update error when API error changes
  useEffect(() => {
    setOmniError(apiError)
  }, [apiError])

  const requiredTnCsAccepted = whetherRequiredTnCsAccepted(tncs ?? [], formData.acceptedTnCs)
  const handleOnGoogleLoginSuccess = async (response: { code: string; state?: string }) => {
    wait()
    if (oauthState !== response.state) {
      setOmniError(getMyError('State mismatch. Please refresh page and try again.'))
      log.error(`State mismatch when signing up: ${oauthState} !== ${response.state}`)
      return
    }
    onSubmit({ ...formData, authCode: response.code })
  }

  const handleOnError = (error: { error_description?: string }) => {
    stopWaiting()
    setOmniError(getMyError(error.error_description ?? 'Error signing up with Google'))
    log.error(error, 'Error signing in')
  }

  const login = useGoogleLogin({
    onSuccess: handleOnGoogleLoginSuccess,
    onError: handleOnError,
    flow: 'auth-code',
    scope: googleOauthScopes.join(' '),
    state: oauthState,
  })

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    login()
  }

  return (
    <ShowData isPending={tncsIsPending} error={tncsError} spinnerSize="1.25rem">
      <form onSubmit={handleSubmit}>
        <PageContent>
          <PageSection
            title="Legal Declarations"
            description="Please review and accept the following declarations to proceed."
          >
            <div className="mt-10 space-y-6">
              <CheckBox
                checked={formData.legalAgeDeclaration}
                onChange={(e) => setFormData((prev) => ({ ...prev, legalAgeDeclaration: e.target.checked }))}
                label={<Mandatory>Legal Age Declaration</Mandatory>}
                description={
                  <>
                    I declare that I am of legal age to enter into a binding contract.
                    {!formData.legalAgeDeclaration && (
                      <em className="mt-1 block text-xs/5 text-error">
                        If you are a minor, ask your parent or legal guardian to create a profile and use the platform
                        for you. See the terms and conditions for more information.
                      </em>
                    )}
                  </>
                }
                id="legal-age"
              />

              <CheckBox
                checked={formData.informationAccuracyDeclaration}
                onChange={(e) => setFormData((prev) => ({ ...prev, informationAccuracyDeclaration: e.target.checked }))}
                label={<Mandatory>Information Accuracy Declaration</Mandatory>}
                description="I declare that all information provided by me is accurate and complete to the best of my knowledge."
                id="information-accuracy"
              />
            </div>
          </PageSection>

          <PageSection
            title="Contact Information"
            description="Please provide your WhatsApp number for verification and communication."
          >
            <div className="mt-5 space-y-4 sm:grid sm:grid-cols-3 sm:items-start">
              <label htmlFor="mobile-input" className="block text-sm/6 font-medium text-gray-900 sm:pt-1.5">
                WhatsApp Number
              </label>
              <MobileEditor
                mobileCountryCode={formData.mobileCountryCode}
                mobile={formData.mobile}
                setMobileCountryCode={(value) => setFormData({ ...formData, mobileCountryCode: value })}
                setMobile={(value) => setFormData({ ...formData, mobile: value })}
                omniError={omniError}
              />
            </div>
          </PageSection>
          <PageSection title="Terms and Conditions" description="Please read and accept the terms and conditions.">
            <div className="mt-10 mb-2 space-y-6">
              {tncs?.map((tnc) => (
                <CheckBox
                  key={tnc.id}
                  id={tnc.id}
                  checked={formData.acceptedTnCs.includes(tnc.id)}
                  onChange={(e) => {
                    const newAcceptedTnCs =
                      e.target.checked ?
                        [...formData.acceptedTnCs, tnc.id]
                      : formData.acceptedTnCs.filter((id) => id !== tnc.id)
                    setFormData((prev) => ({ ...prev, acceptedTnCs: newAcceptedTnCs }))
                  }}
                  label={
                    <>
                      <a
                        href={tnc.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-indigo-600 hover:text-indigo-500"
                      >
                        {tnc.name}
                      </a>
                      , Version {tnc.version}
                      {!tnc.expiryDate && <span className="font-medium text-gray-900"> (latest)</span>}
                    </>
                  }
                  description={
                    <div className="flex flex-col">
                      {tnc.description}
                      <span className="text-xs">
                        <em>Effective from {formatDate(tnc.effectiveDate)}</em>
                        {tnc.expiryDate && <span>, Expiry date {formatDate(tnc.expiryDate)}</span>}
                        {!tnc.expiryDate && tnc.effectiveDate <= today() && (
                          <span className="font-medium text-gray-900"> (Must accept to continue)</span>
                        )}
                      </span>
                    </div>
                  }
                />
              ))}
            </div>
          </PageSection>

          <FormButtons
            omniError={omniError}
            onCancel={() => navigate('/users/me')}
            otherErrors={
              !requiredTnCsAccepted || !formData.legalAgeDeclaration || !formData.informationAccuracyDeclaration
            }
            isSubmitting={apiIsPending}
          >
            <GoogleIcon />
          </FormButtons>
        </PageContent>
      </form>
    </ShowData>
  )
}

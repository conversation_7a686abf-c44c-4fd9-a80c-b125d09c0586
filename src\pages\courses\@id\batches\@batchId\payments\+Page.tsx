import { UUID } from 'crypto'

import { usePageContext } from 'vike-react/usePageContext'

import { extractUuid } from '@/shared/common/common-utils.shared'

import { BatchPaymentsPage } from './BatchPaymentsPage'

export default () => {
  const pageContext = usePageContext()
  const courseId = extractUuid(pageContext.routeParams.id)
  const batchId = pageContext.routeParams.batchId as UUID

  return <BatchPaymentsPage courseId={courseId} batchId={batchId} />
}

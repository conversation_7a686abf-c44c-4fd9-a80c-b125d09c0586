import { UUID } from 'crypto'

import { useSuspenseQuery } from '@tanstack/react-query'
import { useDeferredValue } from 'react'

import { isClient, UNKNOWN_UUID } from '@/shared/common/common-utils.shared'
import { useServerQuery } from '@/shared/common/serverQueries.shared'
import { api, withError } from '@ui/api-client'

const masterKeys = {
  all: ['master'] as const,
  countries: () => [...masterKeys.all, 'countries'] as const,
  states: (countryCode: string) => [...masterKeys.all, 'states', countryCode] as const,
  state: (stateId: string) => [...masterKeys.all, 'state', stateId] as const,
  districts: (stateId: string) => [...masterKeys.all, 'districts', stateId] as const,
  district: (districtId: string) => [...masterKeys.all, 'district', districtId] as const,
  env: () => [...masterKeys.all, 'env'] as const,
}

export const useCountriesSuspenseQuery = () => {
  const listCountries = useServerQuery('listCountries')

  const { data } = useSuspenseQuery({
    queryKey: masterKeys.countries(),
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .masters.countries.$get()
            .then((res) => res.json())
            .then((countries) => countries.rows),
        )
      : listCountries(),
  })

  return useDeferredValue(data)
}

export const useStatesSuspenseQuery = (countryCode: string) => {
  const listStates = useServerQuery('listStates')

  const { data } = useSuspenseQuery({
    queryKey: masterKeys.states(countryCode),
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .masters.states.$get({
              query: {
                countryCode,
              },
            })
            .then((res) => res.json())
            .then((states) => states.rows),
        )
      : listStates(countryCode),
  })

  return useDeferredValue(data)
}

export const useStateSuspenseQuery = (id: UUID) => {
  const getState = useServerQuery('getState')

  const { data } = useSuspenseQuery({
    queryKey: masterKeys.state(id),
    queryFn: async () => {
      return (
        id === UNKNOWN_UUID ? { data: null, error: null }
        : isClient() ?
          withError(
            api()
              .masters.states[':id'].$get({ param: { id } })
              .then((res) => res.json()),
          )
        : getState(id)
      )
    },
  })

  return useDeferredValue(data)
}

export const useDistrictsSuspenseQuery = (stateId: UUID) => {
  const listDistricts = useServerQuery('listDistricts')

  const { data } = useSuspenseQuery({
    queryKey: masterKeys.districts(stateId),
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .masters.districts.$get({
              query: {
                stateId,
              },
            })
            .then((res) => res.json())
            .then((districts) => districts.rows),
        )
      : listDistricts(stateId),
  })

  return useDeferredValue(data)
}

export const useDistrictSuspenseQuery = (id: UUID) => {
  const getDistrict = useServerQuery('getDistrict')

  const { data } = useSuspenseQuery({
    queryKey: masterKeys.district(id),
    queryFn: async () =>
      id === UNKNOWN_UUID ? { data: null, error: null }
      : isClient() ?
        withError(
          api()
            .masters.districts[':id'].$get({ param: { id } })
            .then((res) => res.json()),
        )
      : getDistrict(id),
  })

  return useDeferredValue(data)
}

export const useCommonEnvSuspenseQuery = () => {
  const getCommonEnv = useServerQuery('getCommonEnv')

  const { data } = useSuspenseQuery({
    queryKey: masterKeys.env(),
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .masters.env.$get()
            .then((res) => res.json()),
        )
      : getCommonEnv(),
    staleTime: Infinity,
    gcTime: Infinity,
  })

  return useDeferredValue(data)
}

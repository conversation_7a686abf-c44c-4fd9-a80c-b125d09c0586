import { ContentfulStatusCode } from 'hono/utils/http-status'
import status from 'statuses'

import { MyException } from './MyException.server'

export type EnsureOptions = {
  statusCode: ContentfulStatusCode
  issueMessage?: string | (() => string)
  fieldName?: string
  logMessage: string | (() => string)
  data?: Record<string, unknown>
}

export function ensure(condition: unknown, options: EnsureOptions): asserts condition {
  if (!condition) throw getMyException(options)
}

export const getMyException = (options: EnsureOptions) => {
  const statusCode = options.statusCode
  const msg = getIssueMessage(statusCode, options.issueMessage)
  const logMsg = typeof options.logMessage === 'string' ? options.logMessage : options.logMessage?.()
  return new MyException(logMsg, statusCode, [
    {
      path: options.fieldName ? [options.fieldName] : [],
      code: 'custom',
      message: msg,
      data: options.data,
    },
  ])
}

const getIssueMessage = (statusCode: ContentfulStatusCode, issueMessage?: string | (() => string)) => {
  if (!issueMessage) return status(statusCode)
  if (typeof issueMessage === 'string') return issueMessage
  return issueMessage()
}

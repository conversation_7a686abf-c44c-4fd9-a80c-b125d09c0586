import { UUID } from 'crypto'

import { PlusIcon } from '@heroicons/react/16/solid'
import { ExclamationTriangleIcon } from '@heroicons/react/20/solid'
import {
  AcademicCapIcon,
  ArrowRightIcon,
  BanknotesIcon,
  CalendarIcon,
  IdentificationIcon,
} from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { useMemo } from 'react'

import { AcademyBrief } from '@/server/academies/get/getAcademy.server'
import { StudentBatch } from '@/server/batches/batches/list/student-batches/listStudentBatches'
import { CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { academyPath } from '@/shared/academies/academy-utils.shared'
import { getNextPaymentDueOn } from '@/shared/batch/batch-due-utils'
import { batchPath, billingCycles } from '@/shared/batch/batch-utils.shared'
import { formatDateTime, formatDate, today } from '@/shared/common/date-utils.shared'
import { coursePath } from '@/shared/course/course-utils.shared'
import { profilePath } from '@/shared/profiles/profile-utils.shared'
import { useAcademySuspenseQuery } from '@ui/academies/common/academy-queries'
import { isPaymentDue } from '@ui/batches/common/batch-utils.ui'
import { ShowData } from '@ui/common/ShowData'
import { useStudentBatchesQuery } from '@ui/courses/@id/batches/common/batch-student-queries'
import { BatchEvents } from '@ui/courses/@id/batches/common/BatchEvents'
import { useAddStudentFeePaymentMutation } from '@ui/courses/@id/batches/common/student-fee-payment-queries'
import { useCourseSuspenseQuery } from '@ui/courses/common/queries/course-queries'
import { useProfilesSuspenseQuery } from '@ui/profiles/common/profile-queries'

export const StudentBatchesPage = () => {
  const { data: batches, isLoading, error } = useStudentBatchesQuery()

  return (
    <ShowData isPending={isLoading} error={error} spinnerSize="1.25rem">
      <ViewBatches batches={batches!} />
    </ShowData>
  )
}

const ViewBatches = ({ batches }: { batches: StudentBatch[] }) => {
  // Group batches by academyId
  const batchesByAcademy = useMemo(() => {
    const grouped: Record<string, StudentBatch[]> = {}
    batches.forEach((batch) => {
      if (!grouped[batch.academyId]) {
        grouped[batch.academyId] = []
      }
      grouped[batch.academyId].push(batch)
    })
    return grouped
  }, [batches])

  return (
    <div className="px-4 sm:px-0">
      <PaymentDueWarning batches={batches} />
      <h1 className="page-title">My Batches</h1>
      <p className="page-subtitle">Courses you have enrolled in</p>
      <div className="mt-6 space-y-8">
        {Object.keys(batchesByAcademy).length > 0 ?
          Object.entries(batchesByAcademy).map(([academyId, academyBatches]) => (
            <AcademyBatches key={academyId} academyId={academyId as UUID} batches={academyBatches} />
          ))
        : <div className="text-gray-500 text-center border border-gray-200 rounded-lg p-4">
            You haven't joined any batches yet.
          </div>
        }
      </div>
    </div>
  )
}

const AcademyBatches = ({ academyId, batches }: { academyId: UUID; batches: StudentBatch[] }) => {
  const { data: academy } = useAcademySuspenseQuery(academyId, false)

  if (!academy) return null

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold">
        <a href={academyPath(academy)} className="text-indigo-600 hover:text-indigo-700">
          {academy.name}
        </a>
      </h2>
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {batches.map((batch) => (
          <BatchCard key={batch.id} academy={academy} batch={batch} />
        ))}
      </div>
    </div>
  )
}

const BatchCard = ({ academy, batch }: { academy: AcademyBrief; batch: StudentBatch }) => {
  const { data: course, error: courseError } = useCourseSuspenseQuery(batch.courseId)
  const { mutate: addBatchStudentPaymentItem } = useAddStudentFeePaymentMutation(batch.id)

  const nextPaymentDueOn = useMemo(() => {
    return getNextPaymentDueOn(batch, batch)
  }, [batch])

  const handleAddBatchStudentPaymentItem = () => {
    addBatchStudentPaymentItem({
      cycle: batch.paidTillCycle + 1,
    })
  }

  return (
    <ShowData error={courseError}>
      {course && (
        <div
          className={`bg-white rounded-lg shadow overflow-hidden mb-2 border ${batch.over ? 'border-gray-200' : 'border-green-500'}`}
        >
          <HeaderCell batch={batch} course={course} />

          <div className="p-4 pt-2">
            <div className="text-sm text-gray-700 flex items-center">
              <CalendarIcon className="h-4 w-4 text-gray-500 mr-2" />
              <div>
                {batch.startDate < today() ? 'Started' : 'Starts'} on {formatDate(batch.startDate)} for{' '}
                {batch.cycleCount} {batch.billingCycle}
              </div>
            </div>

            <div className="ml-6 text-sm text-gray-900">
              {batch.events.length > 0 ?
                <BatchEvents batchId={batch.id} events={batch.events} setEditingEvent={() => {}} showActions={false} />
              : 'No schedules fed yet'}
            </div>

            <div className="text-sm text-gray-700 my-3 font-medium">
              <div className="flex items-center">
                <BanknotesIcon className="h-4 w-4 text-gray-500 mr-2" />
                {academy?.currency} {batch.fee} per {billingCycles[batch.billingCycle].singular}
              </div>
              <div className="font-medium">
                {nextPaymentDueOn === null ?
                  <span className="text-green-600 ml-6">Fully paid</span>
                : <>
                    <div className={clsx('ml-6', batch.lastReminderType && 'text-warning')}>
                      Next payment due on {formatDateTime(nextPaymentDueOn)}
                    </div>
                    <button
                      type="button"
                      onClick={handleAddBatchStudentPaymentItem}
                      className={clsx(
                        'mt-2 ml-6 inline-flex items-center gap-x-1',
                        batch.lastReminderType ?
                          'rounded-md bg-indigo-600 px-2 py-1 text-xs font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600'
                        : 'rounded-md bg-white px-2 py-1 text-xs font-semibold text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50',
                      )}
                    >
                      <PlusIcon className="h-4 w-4" />
                      Add to cart
                    </button>
                  </>
                }
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <a
                href={batchPath(course, batch.id)}
                className="text-indigo-600 hover:text-indigo-800 text-sm font-medium flex items-center"
              >
                View details
                <ArrowRightIcon className="h-3.5 w-3.5 ml-1" />
              </a>
            </div>
          </div>
        </div>
      )}
    </ShowData>
  )
}

const HeaderCell = ({ batch, course }: { batch: StudentBatch; course: CourseWithoutDescr }) => {
  const { data } = useProfilesSuspenseQuery({ profileId: batch.teacherId })
  const teacher = data?.[0]

  if (!teacher) return null

  return (
    <div className="bg-gray-50 p-4 border-b border-gray-200 flex justify-between items-center">
      <div>
        <h3 className="text-base font-medium text-gray-900">
          <a href={coursePath(course)} className="flex items-center">
            <AcademicCapIcon className="h-5 w-5 text-indigo-600 mr-2" />
            {course.name}
          </a>
        </h3>
        <div className="text-sm mt-1 flex items-center text-gray-600">
          <IdentificationIcon className="h-4 w-4 text-gray-500 mr-2" />
          <a href={profilePath(teacher)} className="hover:text-indigo-600">
            {teacher.displayName}
          </a>
        </div>
      </div>
      <div className="size-12 shrink-0">
        <img
          alt="Teacher picture"
          src={teacher.googlePictureUrl}
          className="size-12 rounded-full border-2 border-white shadow"
        />
      </div>
    </div>
  )
}

const PaymentDueWarning = ({ batches }: { batches: StudentBatch[] }) => {
  if (!isPaymentDue(batches)) return null
  return (
    <div className="border-l-4 border-yellow-400 bg-yellow-50 p-4 mb-4">
      <div className="flex">
        <div className="shrink-0">
          <ExclamationTriangleIcon aria-hidden="true" className="size-5 text-yellow-400" />
        </div>
        <div className="ml-3">
          <p className="text-sm text-yellow-700">
            Press "<b>+ Add to cart</b>" for all dues, and then{' '}
            <a href="/students/me/payments" className="font-medium text-yellow-700 underline hover:text-yellow-600">
              visit cart
            </a>{' '}
            to pay.
          </p>
        </div>
      </div>
    </div>
  )
}

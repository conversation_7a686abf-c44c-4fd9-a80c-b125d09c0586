import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfile } from '@/server/common/error/ensure-profile.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { withLog } from '@/shared/common/withLog.shared'
import { Role } from '@/shared/profiles/role-utils.shared'

export const ensureCanUpdateAcademy = async <T extends { length: number }>(
  c: Ctx,
  staffQuery: Promise<T>,
  roleAnyOf: readonly Role[] = ['principal'],
) => {
  ensureUser(c.user)
  ensureProfile(c.profile, { roleAnyOf })
  const staff = await withLog(c.log, 'ensureCanUpdateAcademy', () => staffQuery)
  ensure(staff.length > 0, {
    statusCode: 403,
    issueMessage: 'You must be a staff of the academy to perform this action.',
    logMessage: `Profile ${c.profile?.id} is not authorized for this action`,
  })
}

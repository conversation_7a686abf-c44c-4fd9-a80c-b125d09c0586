# Coding Handler

1. See sample at `src\server\batches\batches\join\joinBatchHandler.ts`
1. The route in the handler should always be `/` (FYI: the actual route is coded in `src\server\api-routes.server.ts`)
1. After coding the handler, add it to `src\server\api-routes.server.ts`.

# Coding Service

1. See sample at `src\server\batches\batches\join\joinBatch.ts`
1. Above sample exhibits
   1. how to ensure the current user and profile
   1. how to use transactions to re-check the conditions, as well as do multi updates

# Don't code tests

Don't code tests. We do that in a later round.
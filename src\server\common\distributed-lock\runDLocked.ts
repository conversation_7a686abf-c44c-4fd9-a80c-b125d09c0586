import { and, eq, lte } from 'drizzle-orm'
import { DateTime } from 'luxon'
import pRetry from 'p-retry'
import { pino } from 'pino'

import { db } from '@/server/db/db'
import { appLockTable } from '@/server/db/schema/operation-schema'
import { AsyncRunnable } from '@/shared/common/common-utils.shared'

export const LOCK_EXPIRY_MINUTES = 2

export class DLockError extends Error {
  constructor(message?: string) {
    super(message)
    this.name = 'DLockError'
    Object.setPrototypeOf(this, DLockError.prototype)
  }
}

export const isDLockError = (err: unknown) =>
  err && typeof err === 'object' && 'name' in err && err.name === 'DLockError'

type SubjectPrefix = 'command' | 'send-tnc-reminders' | 'batch' | 'studentFeePayment'
type Subject = [SubjectPrefix, string]
const toStr = (subject: Subject) => subject.join('#')

const cleanExpiredLockDaf = async (log: pino.Logger, subject: Subject) => {
  const result = await db
    .delete(appLockTable)
    .where(and(eq(appLockTable.subject, toStr(subject)), lte(appLockTable.expiresAt, new Date())))

  if (result.count === 1) log.info(`Removed expired DB lock ${subject}`)
}

const obtainLockDaf = async (log: pino.Logger, subject: Subject, expiresAt: Date, retries: number) => {
  try {
    await pRetry(
      async () => {
        try {
          await db.insert(appLockTable).values({
            subject: toStr(subject),
            expiresAt: expiresAt,
          })
        } catch (error) {
          log.info(error, `Failed to obtain DB lock ${subject}, will retry once`)
          throw error
        }
      },
      {
        retries,
        minTimeout: 300,
        randomize: true,
      },
    )
  } catch (e: unknown) {
    log.info(e, `Could not obtain DB lock ${subject} after retry`)
    throw new DLockError(`Could not obtain DB Lock`)
  }
}

const releaseLockDaf = (log: pino.Logger, subject: Subject) =>
  db.delete(appLockTable).where(eq(appLockTable.subject, toStr(subject)))

export const runDLocked = async <T>(
  log: pino.Logger,
  subject: Subject,
  runnable: AsyncRunnable<T>,
  opts?: { lockRetries?: number; lockExpiryMinutes?: number },
): Promise<T> => {
  await cleanExpiredLockDaf(log, subject)
  const expiresAt = DateTime.now()
    .plus({ minutes: opts?.lockExpiryMinutes ?? LOCK_EXPIRY_MINUTES })
    .toJSDate()
  await obtainLockDaf(log, subject, expiresAt, opts?.lockRetries ?? 1)
  try {
    return await runnable()
  } finally {
    await releaseLockDaf(log, subject)
  }
}

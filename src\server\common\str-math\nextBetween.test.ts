import { describe, it, expect } from 'vitest'

import { MyException } from '@/server/common/error/MyException.server'
import { HIGHEST_CHAR, LOWEST_CHAR } from '@/shared/common/str-math.shared'

import { nextBetween } from './str-math.server'

describe('nextBetween', () => {
  it('should return a string between two given strings', () => {
    const result = nextBetween('a', 'c')
    expect(result).toBeDefined()
    expect(result).toBeTypeOf('string')
    expect(result! > 'a').toBe(true)
    expect(result! < 'c').toBe(true)
  })

  it('should handle single character strings', () => {
    const result = nextBetween('a', 'z')
    expect(result).toBeDefined()
    expect(result!.length).toBeGreaterThanOrEqual(1)
    expect(result! > 'a').toBe(true)
    expect(result! < 'z').toBe(true)
  })

  it('should handle strings of different lengths', () => {
    const result = nextBetween('a', 'abc')
    expect(result).toBeDefined()
    expect(result! > 'a').toBe(true)
    expect(result! < 'abc').toBe(true)
  })

  it('should handle strings with only one character gap', () => {
    const result = nextBetween('a', 'b')
    expect(result).toBe('a' + String.fromCharCode(LOWEST_CHAR.charCodeAt(0) + 4))
    expect(result! > 'a').toBe(true)
    expect(result! < 'b').toBe(true)
  })

  it('should handle consecutive characters with large gaps', () => {
    const result = nextBetween('a', 'z')
    expect(result).toBeDefined()
    expect(result! > 'a').toBe(true)
    expect(result! < 'z').toBe(true)
  })

  it('should handle empty string as lower bound', () => {
    const result = nextBetween('', 'z')
    expect(result).toBeDefined()
    expect(result!.length).toBeGreaterThanOrEqual(1)
    expect(result! < 'z').toBe(true)
  })

  it('should handle empty string as upper bound', () => {
    expect(() => nextBetween('a', '')).toThrow(MyException)
  })

  it('should handle very close strings', () => {
    const result = nextBetween('aaaa', 'aaab')
    expect(result).toBeDefined()
    expect(result! > 'aaaa').toBe(true)
    expect(result! < 'aaab').toBe(true)
  })

  it('should handle boundary values', () => {
    const result = nextBetween(LOWEST_CHAR.repeat(5), HIGHEST_CHAR.repeat(5))
    expect(result).toBeDefined()
    expect(result! > LOWEST_CHAR.repeat(5)).toBe(true)
    expect(result! < HIGHEST_CHAR.repeat(5)).toBe(true)
  })

  it('should throw error if the calculated string is not between low and high', () => {
    // This test verifies the error case (which should never happen with normal input)
    // Mock the implementation to force the error condition
    const mockLow = 'z'
    const mockHigh = 'a' // Deliberately reversed to force error

    expect(() => nextBetween(mockLow, mockHigh)).toThrow(MyException)
  })

  it('should create strings that maintain lexicographical ordering', () => {
    // Test that a sequence of strings created with nextBetween maintains ordering
    const values = ['aaa']
    const current = 'zzz'
    const iterations = 10

    for (let i = 0; i < iterations; i++) {
      const between = nextBetween(values[values.length - 1], current)
      expect(between).toBeDefined()
      expect(between! > values[values.length - 1]).toBe(true)
      expect(between! < current).toBe(true)

      values.push(between!)
    }

    // Add the final upper bound
    values.push(current)

    // Verify the entire sequence is ordered
    const sorted = [...values].sort()
    expect(values).toEqual(sorted)
  })
})

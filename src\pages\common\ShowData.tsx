import React from 'react'

import { getErrorMessage } from '@/pages/common/utils/error-utils.ui'
import { type ErrorPayload } from '@/shared/common/error-utils.shared'
import { Spinner } from '@ui/common/Spinner'

import { ErrorAlert } from './alerts/ErrorAlert'

type ShowDataProps = {
  isPending?: boolean
  error: ErrorPayload | null
  spinnerSize?: string
  children: React.ReactNode
}

/**
 * To wait and show a spinner, use `isPending={isPending}` and `spinnerSize` prop.
 * To wait but not show a spinner, use `isPending={isPending}` and omit `spinnerSize`.
 * To not wait and start showing children or error immediately, don't use `isPending` prop.
 * E.g., for suspense queries that don't have loading state, use
 * ```tsx
 *   <ShowData error={data.error}>
 *     <CoursePage course={data.data!} />
 *   </ShowData>
 * ```
 */
export const ShowData = ({ isPending, error, spinnerSize, children }: ShowDataProps) => {
  if (isPending) {
    return spinnerSize ? <Spinner size={spinnerSize} /> : null
  }

  if (error) {
    return <ErrorAlert>{getErrorMessage(error)}</ErrorAlert>
  }

  return children
}

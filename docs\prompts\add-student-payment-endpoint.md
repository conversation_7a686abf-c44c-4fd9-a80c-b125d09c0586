Let's create an endpoint `POST /batches/:id/payments` for a student to add a payment item.

1. The endpoint should be created in `src\server\batches\batches\payments\add` folder.
1. It should receive `$AddBatchStudentPaymentForm` in `src\shared\batch\batch-utils.shared.ts` as payload.
1. In the servce function, ensure that a student has logged in, by using
   ```typescript
   ensureUser(c.user)
   ensureProfileWithRole('student', c.profile)
   ```
1. ensure that the student is enrolled in the batch
1. Ensure that there's no payment (check `batchStudentPaymentTable` in `src\server\db\schema\batch-schema.ts`) in pending state for the student for the academy of the batch
1. ensure that the given cycle is within batch's cycle count
1. ensure that there's no payment-item (check `batchStudentPaymentItemTable`) already existing for this cycle, with the payment status != failed
1. Find out if there's a payment already existing in 'draft' state for the student for the academy. If not, create a new payment for the academy with draft state, with method, currency, cents and status as 'manual', batch.currency, batch.fee and 'draft'.
1. Create a payment-item.

Once the endpoint is created, add it to `src\server\api-routes.server.ts`, and then to `src\pages\courses\@id\batches\common\batch-queries.ts`.

No need to code tests ATM, we do that in a later round.

Note that we are using Windows machine. E.g., you can't use unix commands like `cat`. 

If you have any questions or need any help tracing existing patterns, please ask!
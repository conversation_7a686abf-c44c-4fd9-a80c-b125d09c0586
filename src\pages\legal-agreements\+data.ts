import { Logger } from 'pino'
import { PageContext } from 'vike/types'

import { db } from '@/server/db/db'
import { withLog } from '@/shared/common/withLog.shared'
import { Data, getDataHook } from '@ui/common/getDataHook'

const findTncs = (log: Logger) =>
  withLog(log, 'findTncs', async () => {
    const tncs = await db.query.userTncTable.findMany({
      columns: {
        id: true,
        name: true,
        baseUrl: true,
        description: true,
      },
    })
    return tncs
  })

const _data = async (pageContext: PageContext) => {
  const tncs = await findTncs(pageContext.log)
  return { tncs }
}

export const data = getDataHook('/legal-agreements', _data)
export type PageData = Data<typeof _data>

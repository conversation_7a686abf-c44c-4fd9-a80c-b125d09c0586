import { UUID } from 'crypto'

import { DateTime } from 'luxon'
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'

import app from '@/hono-entry'
import { env } from '@/server/common/env.server'
import { db } from '@/server/db/db'
import {
  academyTable,
  batchStudentTable,
  batchTable,
  countryTable,
  courseTable,
  profileTable,
  userTable,
} from '@/server/db/schema'
import { initDb, SUNDARGARH_DISTRICT_ID } from '@/server/test/initDb'
import { BillingCycle } from '@/shared/batch/batch-utils.shared'
import { today } from '@/shared/common/date-utils.shared'
import { getLogger } from '@/shared/common/logger.shared'
import { PaymentReminderType } from '@/shared/common/payment-utils/payment-utils.shared'

import { processStudentBilling } from './processStudentBilling'

// Mock the external dependency not directly tested here
vi.mock('@/server/batches/batches/common/updateGoogleEventForBatch', () => ({
  updateGoogleEventForBatch: vi.fn().mockResolvedValue(undefined),
}))

// Mock the command appending functions as their specific logic is tested elsewhere
vi.mock('@/server/common/command/processCommands', async (importOriginal) => {
  const mod = await importOriginal<typeof import('@/server/common/command/processCommands')>()
  return {
    ...mod,
    appendCommandDaf: vi.fn().mockResolvedValue(undefined),
  }
})

describe('processStudentBillingHandler', () => {
  beforeEach(async () => {
    await initDb()
    const processCommands = await import('@/server/common/command/processCommands')
    vi.mocked(processCommands.appendCommandDaf).mockClear()
  })
  afterEach(() => {
    vi.clearAllMocks()
  })

  test('should return 401 when auth key is invalid', async () => {
    // given
    const res = await request('invalid-auth-key')

    // then
    expect(res.status, 'Status check').toBe(401)
    await expect(res).toBeError(401, [
      {
        code: 'custom',
        path: [],
        message: 'Invalid cron auth key',
      },
    ])
    // No need to wait here as processing shouldn't start with invalid auth
  })

  test('should return 202 when auth key is valid', async () => {
    // given
    const res = await request()

    // then
    expect(res.status, 'Status check').toBe(202)
  })

  test('should process student billing for active batches with students needing reminders', async () => {
    // Test data setup
    const teacherProfileId = crypto.randomUUID()
    const studentProfileId1 = crypto.randomUUID()
    const studentProfileId2 = crypto.randomUUID()

    const batchId1 = crypto.randomUUID()
    const batchId2 = crypto.randomUUID()

    await createTestData([
      {
        id: batchId1,
        teacherProfileId,
        startDate: DateTime.fromISO(today()).minus({ days: 5 }).toISODate()!,
        billingCycle: 'months',
        cycleCount: 3,
        graceDays: 5,
        over: false,
        students: [
          {
            profileId: studentProfileId1,
            firstJoinedAt: DateTime.fromISO(today()).minus({ days: 5 }).toJSDate(),
            paidTillCycle: 0, // Needs reminder
            lastReminderType: null,
          },
          {
            profileId: studentProfileId2,
            firstJoinedAt: DateTime.fromISO(today()).minus({ days: 5 }).toJSDate(),
            paidTillCycle: 3, // Fully paid
            lastReminderType: null,
          },
        ],
      },
      {
        id: batchId2,
        teacherProfileId, // Same teacher
        startDate: DateTime.fromISO(today()).minus({ days: 10 }).toISODate()!,
        billingCycle: 'weeks',
        cycleCount: 4,
        graceDays: 2,
        over: false,
        students: [
          {
            profileId: studentProfileId1, // Same student in another batch
            firstJoinedAt: DateTime.fromISO(today()).minus({ days: 10 }).toJSDate(),
            paidTillCycle: 0, // Needs reminder
            lastReminderType: 'gentle',
          },
        ],
      },
    ])

    // when
    await processStudentBilling(getLogger())

    // then
    const student1Batch1 = await db.query.batchStudentTable.findFirst({
      where: (fields, { eq, and }) => and(eq(fields.batchId, batchId1), eq(fields.studentId, studentProfileId1)),
    })
    expect(student1Batch1?.lastReminderType).not.toBeNull()

    const student2Batch1 = await db.query.batchStudentTable.findFirst({
      where: (fields, { eq, and }) => and(eq(fields.batchId, batchId1), eq(fields.studentId, studentProfileId2)),
    })
    expect(student2Batch1?.lastReminderType).toBeNull()

    const processCommands = await import('@/server/common/command/processCommands')
    expect(vi.mocked(processCommands.appendCommandDaf).mock.calls.length).toBeGreaterThanOrEqual(2)

    const batch1Db = await db.query.batchTable.findFirst({ where: (fields, { eq }) => eq(fields.id, batchId1) })
    const batch2Db = await db.query.batchTable.findFirst({ where: (fields, { eq }) => eq(fields.id, batchId2) })
    expect(batch1Db?.over).toBe(false)
    expect(batch2Db?.over).toBe(false)
  })

  test('should mark batches as over if their end date has passed', async () => {
    const teacherProfileId = crypto.randomUUID()
    const studentProfileId1 = crypto.randomUUID()
    const batchId1 = crypto.randomUUID() // Will be over
    const batchId2 = crypto.randomUUID() // Still active

    await createTestData([
      {
        id: batchId1,
        teacherProfileId,
        startDate: DateTime.fromISO(today()).minus({ months: 2, days: 1 }).toISODate()!,
        billingCycle: 'months',
        cycleCount: 2,
        graceDays: 5,
        over: false,
        students: [
          {
            profileId: studentProfileId1,
            firstJoinedAt: DateTime.fromISO(today()).minus({ months: 2, days: 1 }).toJSDate(),
            paidTillCycle: 1,
            lastReminderType: null,
          },
        ],
      },
      {
        id: batchId2,
        teacherProfileId,
        startDate: DateTime.fromISO(today()).minus({ days: 5 }).toISODate()!,
        billingCycle: 'months',
        cycleCount: 3,
        graceDays: 5,
        over: false,
        students: [],
      },
    ])

    // when
    await processStudentBilling(getLogger())

    // then
    const batch1Db = await db.query.batchTable.findFirst({ where: (fields, { eq }) => eq(fields.id, batchId1) })
    expect(batch1Db?.over).toBe(true)

    const batch2Db = await db.query.batchTable.findFirst({ where: (fields, { eq }) => eq(fields.id, batchId2) })
    expect(batch2Db?.over).toBe(false)

    const student1Batch1 = await db.query.batchStudentTable.findFirst({
      where: (fields, { eq, and }) => and(eq(fields.batchId, batchId1), eq(fields.studentId, studentProfileId1)),
    })
    expect(student1Batch1?.lastReminderType).toBeNull()

    const processCommands = await import('@/server/common/command/processCommands')
    expect(vi.mocked(processCommands.appendCommandDaf)).not.toHaveBeenCalled()
  })

  test('should do nothing if no active batches are found', async () => {
    const userId = crypto.randomUUID()
    await db.insert(userTable).values({
      id: userId,
      googleId: 'gid-future',
      name: 'Future User',
      email: '<EMAIL>',
      emailVerified: true,
      googlePictureUrl: 'url',
      language: 'en',
      tokensValidFrom: new Date(),
      mobileCountryCode: 'IN',
      mobile: '111',
      mobileVerified: true,
      legalAgeDeclaredAt: new Date(),
      informationAccuracyDeclaredAt: new Date(),
    })
    const profileId = crypto.randomUUID()
    await db
      .insert(profileTable)
      .values({ id: profileId, userId, displayName: 'Future Profile', role: 'teacher', tncsAcceptedAt: new Date() })
    const academyId = crypto.randomUUID()
    await db.insert(academyTable).values({
      id: academyId,
      name: 'Future Academy',
      email: '<EMAIL>',
      mobileCountryCode: 'IN',
      mobile: '222',
      currency: 'INR',
      upiId: 'upi',
      tncsAcceptedAt: new Date(),
      approvedAt: new Date(),
      districtId: SUNDARGARH_DISTRICT_ID,
      pincode: '770011',
      area: 'Bilaigarh',
    })
    const courseId = crypto.randomUUID()
    await db.insert(courseTable).values({ id: courseId, name: 'Future Course', academyId, publishedAt: new Date() })

    await db.insert(batchTable).values({
      id: crypto.randomUUID(),
      courseId: courseId,
      teacherId: profileId,
      startDate: DateTime.fromISO(today()).plus({ days: 1 }).toISODate()!,
      timezone: 'UTC',
      billingCycle: 'months',
      fee: 100,
      cycleCount: 1,
      graceDays: 0,
      over: false,
      seatCount: 10,
      admissionOpen: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    })
    await db.insert(batchTable).values({
      id: crypto.randomUUID(),
      courseId: courseId,
      teacherId: profileId,
      startDate: DateTime.fromISO(today()).minus({ days: 10 }).toISODate()!,
      timezone: 'UTC',
      billingCycle: 'months',
      fee: 100,
      cycleCount: 1,
      graceDays: 0,
      over: true,
      seatCount: 10,
      admissionOpen: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    })

    // when
    await processStudentBilling(getLogger())

    // then
    const processCommands = await import('@/server/common/command/processCommands')
    expect(vi.mocked(processCommands.appendCommandDaf)).not.toHaveBeenCalled()
  })

  test('should not process students if batch students are fully paid or have left', async () => {
    const teacherProfileId = crypto.randomUUID()
    const studentProfileId1 = crypto.randomUUID() // Fully paid
    const studentProfileId2 = crypto.randomUUID() // Left batch
    const batchId = crypto.randomUUID()

    await createTestData([
      {
        id: batchId,
        teacherProfileId,
        startDate: DateTime.fromISO(today()).minus({ days: 5 }).toISODate()!,
        billingCycle: 'months',
        cycleCount: 3,
        graceDays: 5,
        over: false,
        students: [
          {
            profileId: studentProfileId1,
            firstJoinedAt: DateTime.fromISO(today()).minus({ days: 5 }).toJSDate(),
            paidTillCycle: 3,
            lastReminderType: null,
          },
          {
            profileId: studentProfileId2,
            firstJoinedAt: DateTime.fromISO(today()).minus({ days: 5 }).toJSDate(),
            paidTillCycle: 1,
            leftAt: DateTime.fromISO(today()).minus({ days: 1 }).toJSDate(),
            lastReminderType: null,
          },
        ],
      },
    ])

    // when
    await processStudentBilling(getLogger())

    // then
    const student = await db.query.batchStudentTable.findFirst({
      where: (fields, { eq, and }) => and(eq(fields.batchId, batchId), eq(fields.studentId, studentProfileId1)),
    })
    expect(student?.lastReminderType).toBeNull()

    const student2 = await db.query.batchStudentTable.findFirst({
      where: (fields, { eq, and }) => and(eq(fields.batchId, batchId), eq(fields.studentId, studentProfileId2)),
    })
    expect(student2?.lastReminderType).toBeNull()

    const processCommands = await import('@/server/common/command/processCommands')
    expect(vi.mocked(processCommands.appendCommandDaf)).not.toHaveBeenCalled()

    const batchDb = await db.query.batchTable.findFirst({ where: (fields, { eq }) => eq(fields.id, batchId) })
    expect(batchDb?.over).toBe(false)
  })

  test('should process student billing correctly even if a student is in multiple batches', async () => {
    const teacherProfileId = crypto.randomUUID()
    const studentProfileId = crypto.randomUUID() // Same student

    const batchId1 = crypto.randomUUID()
    const batchId2 = crypto.randomUUID() // Student will be in this batch too, via direct insert below

    // Batch 1 created via helper
    await createTestData([
      {
        id: batchId1,
        teacherProfileId,
        startDate: DateTime.fromISO(today()).minus({ days: 5 }).toISODate()!,
        billingCycle: 'months',
        cycleCount: 3,
        graceDays: 5,
        over: false,
        students: [
          {
            profileId: studentProfileId,
            firstJoinedAt: DateTime.fromISO(today()).minus({ days: 5 }).toJSDate(),
            paidTillCycle: 0,
            lastReminderType: null,
          },
        ],
      },
    ])

    const defaultEntities = await getDefaultEntitiesForTest()

    await db.insert(batchTable).values({
      id: batchId2,
      courseId: defaultEntities.courseId,
      teacherId: teacherProfileId,
      startDate: DateTime.fromISO(today()).minus({ days: 10 }).toISODate()!,
      timezone: 'UTC',
      billingCycle: 'weeks',
      fee: 50,
      cycleCount: 4,
      graceDays: 2,
      over: false,
      seatCount: 10,
      admissionOpen: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    })
    await db.insert(batchStudentTable).values({
      id: crypto.randomUUID(),
      batchId: batchId2,
      studentId: studentProfileId,
      firstJoinedAt: DateTime.fromISO(today()).minus({ days: 10 }).toJSDate(),
      paidTillCycle: 0,
      lastReminderType: 'gentle',
      createdAt: new Date(),
      updatedAt: new Date(),
    })

    // when
    await processStudentBilling(getLogger())

    // then
    const studentInBatch1 = await db.query.batchStudentTable.findFirst({
      where: (fields, { eq, and }) => and(eq(fields.batchId, batchId1), eq(fields.studentId, studentProfileId)),
    })
    expect(studentInBatch1?.lastReminderType).not.toBeNull()

    const studentInBatch2 = await db.query.batchStudentTable.findFirst({
      where: (fields, { eq, and }) => and(eq(fields.batchId, batchId2), eq(fields.studentId, studentProfileId)),
    })
    expect(studentInBatch2).toBeDefined()

    const processCommands = await import('@/server/common/command/processCommands')
    expect(vi.mocked(processCommands.appendCommandDaf).mock.calls.length).toBeGreaterThanOrEqual(2)
  })
})

const request = async (authKeyHeader = env.CRON_AUTH_KEY) => {
  return app.request('/api/schedule/student-billing', {
    method: 'POST',
    headers: {
      'TL-Cron-Auth-Key': authKeyHeader,
    },
  })
}

type TestStudentInput = {
  profileId: UUID
  firstJoinedAt: Date
  paidTillCycle: number
  lastReminderType: PaymentReminderType | null
  leftAt?: Date
}

type TestBatchInput = {
  id: UUID
  teacherProfileId: UUID
  startDate: string
  billingCycle: BillingCycle
  cycleCount: number
  graceDays: number
  over: boolean
  students: TestStudentInput[]
  courseId?: UUID
}

const defaultTestEntities = {
  countryCode: 'IN',
  academyId: crypto.randomUUID(),
  courseId: crypto.randomUUID(),
}

const getDefaultEntitiesForTest = async () => {
  const country = await db.query.countryTable.findFirst({
    where: (f, { eq }) => eq(f.code, defaultTestEntities.countryCode),
  })
  if (!country) {
    await db.insert(countryTable).values({ code: defaultTestEntities.countryCode, name: 'India', phonePrefix: '+91' })
  }
  const academy = await db.query.academyTable.findFirst({
    where: (f, { eq }) => eq(f.id, defaultTestEntities.academyId),
  })
  if (!academy) {
    await db.insert(academyTable).values({
      id: defaultTestEntities.academyId,
      name: 'Default Test Academy',
      email: '<EMAIL>',
      mobileCountryCode: defaultTestEntities.countryCode,
      mobile: '000000000',
      currency: 'INR',
      upiId: 'test@upi',
      tncsAcceptedAt: new Date(),
      approvedAt: new Date(),
      districtId: SUNDARGARH_DISTRICT_ID,
      pincode: '770011',
      area: 'Bilaigarh',
    })
  }
  const course = await db.query.courseTable.findFirst({ where: (f, { eq }) => eq(f.id, defaultTestEntities.courseId) })
  if (!course) {
    await db.insert(courseTable).values({
      id: defaultTestEntities.courseId,
      name: 'Default Test Course',
      academyId: defaultTestEntities.academyId,
      publishedAt: new Date(),
    })
  }
  return defaultTestEntities
}

const createTestData = async (batchesInput: TestBatchInput[]) => {
  const countryCode = defaultTestEntities.countryCode
  const country = await db.query.countryTable.findFirst({ where: (f, { eq }) => eq(f.code, countryCode) })
  if (!country) {
    await db.insert(countryTable).values({ code: countryCode, name: 'India', phonePrefix: '+91' })
  }

  const academyId = defaultTestEntities.academyId
  const academy = await db.query.academyTable.findFirst({ where: (f, { eq }) => eq(f.id, academyId) })
  if (!academy) {
    await db.insert(academyTable).values({
      id: academyId,
      name: 'Test Academy',
      email: '<EMAIL>',
      mobileCountryCode: countryCode,
      mobile: '1234567890',
      currency: 'INR',
      upiId: 'test@upi',
      tncsAcceptedAt: new Date(),
      approvedAt: new Date(),
      districtId: SUNDARGARH_DISTRICT_ID,
      pincode: '770011',
      area: 'Bilaigarh',
    })
  }

  const allProfileIds = new Set<UUID>()
  batchesInput.forEach((b) => {
    allProfileIds.add(b.teacherProfileId)
    b.students.forEach((s) => allProfileIds.add(s.profileId))
  })

  for (const profileId of allProfileIds) {
    const userId = crypto.randomUUID()
    const existingUser = await db.query.userTable.findFirst({ where: (f, { eq }) => eq(f.id, userId) })
    if (!existingUser) {
      await db.insert(userTable).values({
        id: userId,
        googleId: `gid-${profileId}`,
        name: `User for Profile ${profileId}`,
        email: `user-${profileId}@example.com`,
        emailVerified: true,
        googlePictureUrl: 'http://example.com/pic.jpg',
        language: 'en',
        tokensValidFrom: new Date(),
        mobileCountryCode: countryCode,
        mobile: `000${profileId.substring(0, 7)}`,
        mobileVerified: true,
        legalAgeDeclaredAt: new Date(),
        informationAccuracyDeclaredAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      })
    }

    const existingProfile = await db.query.profileTable.findFirst({ where: (f, { eq }) => eq(f.id, profileId) })
    if (!existingProfile) {
      const isTeacher = batchesInput.some((b) => b.teacherProfileId === profileId)
      await db.insert(profileTable).values({
        id: profileId,
        userId: userId,
        displayName: `${isTeacher ? 'Teacher' : 'Student'} Profile ${profileId}`,
        role: isTeacher ? 'teacher' : 'student',
        tncsAcceptedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      })
    }
  }

  for (const batchInput of batchesInput) {
    const courseIdToUse = batchInput.courseId || defaultTestEntities.courseId
    const course = await db.query.courseTable.findFirst({ where: (f, { eq }) => eq(f.id, courseIdToUse) })
    if (!course) {
      await db.insert(courseTable).values({
        id: courseIdToUse,
        name: `Test Course ${courseIdToUse}`,
        academyId: academyId,
        publishedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      })
    }

    await db.insert(batchTable).values({
      id: batchInput.id,
      courseId: courseIdToUse,
      teacherId: batchInput.teacherProfileId,
      startDate: batchInput.startDate,
      timezone: 'UTC',
      billingCycle: batchInput.billingCycle,
      fee: 100,
      cycleCount: batchInput.cycleCount,
      graceDays: batchInput.graceDays,
      over: batchInput.over,
      seatCount: 20,
      admissionOpen: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    })

    for (const studentInput of batchInput.students) {
      await db.insert(batchStudentTable).values({
        id: crypto.randomUUID(),
        batchId: batchInput.id,
        studentId: studentInput.profileId,
        firstJoinedAt: studentInput.firstJoinedAt,
        paidTillCycle: studentInput.paidTillCycle,
        lastReminderType: studentInput.lastReminderType,
        leftAt: studentInput.leftAt,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
    }
  }
}

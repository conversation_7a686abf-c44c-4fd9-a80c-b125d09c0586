import { UUID } from 'crypto'

import clsx from 'clsx'
import { DateTime } from 'luxon'

import { BatchStudent } from '@/server/batches/batch-students/list/listStudentsOfBatch'
import { Batch } from '@/server/batches/batches/get/getBatch.server'
import { CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { getNextPaymentDueOn } from '@/shared/batch/batch-due-utils'
import { formatDateTime, formatJsDate2Timestamp } from '@/shared/common/date-utils.shared'
import { profilePath } from '@/shared/profiles/profile-utils.shared'
import { PaymentReminderTypeBadge } from '@ui/common/payment/PaymentReminderTypeBadge'
import { ShowData } from '@ui/common/ShowData'
import { useCourseSuspenseQuery } from '@ui/courses/common/queries/course-queries'

import { useBatchSuspenseQuery } from '../../common/batch-queries'
import { useBatchStudentsQuery } from '../../common/batch-student-queries'
import { BatchHeader } from '../../common/BatchHeader'
import { CopyStudentsEmailsButton } from '../../common/CopyStudentsEmailsButton'

export const BatchStudentsPage = ({ courseId, batchId }: { courseId: UUID; batchId: UUID }) => {
  const { data: course, error: courseError } = useCourseSuspenseQuery(courseId)
  const { data: batch, error: batchError } = useBatchSuspenseQuery(batchId)

  return (
    <ShowData error={courseError || batchError} spinnerSize="1.25rem">
      <BatchStudents course={course!} batch={batch!} />
    </ShowData>
  )
}

const BatchStudents = ({ course, batch }: { course: CourseWithoutDescr; batch: Batch }) => {
  const { data: students, isPending, error } = useBatchStudentsQuery(batch.id)

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center">
        <BatchHeader course={course} batch={batch} h1="Students of Batch" />
      </div>
      <div className="mt-2">
        <CopyStudentsEmailsButton batchId={batch.id} />
      </div>
      <ShowData isPending={isPending} error={error} spinnerSize="1.25rem">
        {students && (
          <div className="mt-8 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                {students.length > 0 ?
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead>
                      <tr>
                        <th
                          scope="col"
                          className="py-3.5 pr-3 pl-4 text-left text-sm font-semibold text-gray-900 sm:pl-0"
                        >
                          Name
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Joined On
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Next Due On
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Urgency
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {students.map((student) => (
                        <StudentRow key={student.studentId} student={student} batch={batch} />
                      ))}
                    </tbody>
                  </table>
                : <div className="text-center py-10 bg-gray-50 rounded-md">
                    <p className="text-sm text-gray-500">No students enrolled in this batch yet.</p>
                  </div>
                }
              </div>
            </div>
          </div>
        )}
      </ShowData>
    </div>
  )
}

const StudentRow = ({ student, batch }: { student: BatchStudent; batch: Batch }) => {
  const nextDueOn = getNextPaymentDueOn(batch, student)
  const dueDateArrived = nextDueOn && nextDueOn < DateTime.now().startOf('day')
  return (
    <tr>
      <td className="py-4 pr-3 pl-4 text-sm whitespace-nowrap sm:pl-0">
        <div className="flex items-center">
          <div className="ml-1">
            <div className="font-medium text-gray-900">
              <a href={profilePath({ id: student.studentId, displayName: student.studentName, role: 'student' })}>
                {student.studentName}
              </a>
            </div>
          </div>
        </div>
      </td>
      <td className="px-3 py-4 text-sm whitespace-nowrap text-gray-500">
        <p>{formatJsDate2Timestamp(student.firstJoinedAt, { format: DateTime.DATE_MED })}</p>
        {student.leftAt && (
          <p className="text-gray-500 text-xs">
            Left on {formatJsDate2Timestamp(student.leftAt, { format: DateTime.DATE_MED })}
          </p>
        )}
      </td>
      <td className={clsx('px-3 py-4 text-sm whitespace-nowrap', dueDateArrived ? 'text-error' : 'text-gray-500')}>
        {nextDueOn ? formatDateTime(nextDueOn!) : 'Fully paid'}
      </td>
      <td className="px-3 py-4">
        <PaymentReminderTypeBadge reminderType={student.lastReminderType} />
      </td>
    </tr>
  )
}

import languageParser from 'accept-language-parser'
import { DateTime } from 'luxon'

import { utc } from './date-utils-basic.shared'

let locale = 'en'

export const setLocale = (language: string) => {
  locale = language2Locale(language)
}

// See https://www.npmjs.com/package/accept-language-parser
export const language2locales = (language: string | null | undefined) =>
  language ?
    languageParser.parse(language).map((locale) => (locale.region ? `${locale.code}-${locale.region}` : locale.code))
  : []

const language2Locale = (language: string | null | undefined) => language2locales(language)[0]

export const parseUTCDate = (dateStr: string): Date => DateTime.fromISO(dateStr, { zone: 'utc' }).toJSDate()

export const nowTruncated = (utc: DateTime) => utc.startOf('second').toJSDate()
export const today = () => utc().toISODate()

export const formatJsDate2Timestamp = (
  date: Date,
  opts?: {
    format?: Intl.DateTimeFormatOptions
    language?: string
  },
) =>
  DateTime.fromJSDate(date).toLocaleString(opts?.format ?? DateTime.DATETIME_MED, {
    locale: language2Locale(opts?.language) ?? locale,
  })

export const formatDate = (
  date: string,
  opts?: {
    format?: Intl.DateTimeFormatOptions
    language?: string
    zone?: string
  },
) => formatDateTime(DateTime.fromISO(date, { zone: opts?.zone ?? 'utc' }), opts)

export const formatDateTime = (
  dateTime: DateTime,
  opts?: {
    format?: Intl.DateTimeFormatOptions
    language?: string
  },
) =>
  dateTime.toLocaleString(opts?.format ?? DateTime.DATE_MED, {
    locale: language2Locale(opts?.language) ?? locale,
  })

export const formatTime = (
  timeString: string,
  opts?: {
    format?: Intl.DateTimeFormatOptions
    language?: string
  },
) =>
  DateTime.fromISO(`2001-01-01T${timeString.substring(0, 5)}Z`, { zone: 'utc' }).toLocaleString(
    opts?.format ?? DateTime.TIME_SIMPLE,
    {
      locale: language2Locale(opts?.language) ?? locale,
    },
  )

export const timezoneOffset = (date: string, tz: string) => `GMT${DateTime.fromISO(date).setZone(tz).toFormat('ZZ')}`

export const weekDays = {
  MO: { name: 'Monday' },
  TU: { name: 'Tuesday' },
  WE: { name: 'Wednesday' },
  TH: { name: 'Thursday' },
  FR: { name: 'Friday' },
  SA: { name: 'Saturday' },
  SU: { name: 'Sunday' },
} as const

export type WeekDay = keyof typeof weekDays

import { UUID } from 'crypto'

import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'

import { removeProfile } from './removeProfile'

export const removeProfileHandler = new Hono<HonoVars>().delete('/', async (c) => {
  const profileId = c.req.param('id') as UUID
  await removeProfile(getCtx(c), profileId)
  return c.body(null, 204)
})

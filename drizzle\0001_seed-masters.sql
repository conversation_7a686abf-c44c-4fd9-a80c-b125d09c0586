-- Custom SQL migration file, put your code below! --

-- Insert countries
INSERT INTO "country" ("code", "name", "phone_prefix") VALUES
('IN', 'India', '91'),
('US', 'United States', '1')
ON CONFLICT DO NOTHING;

-- Insert into state
INSERT INTO state(id, name, country_code) VALUES ('1b6a7a7e-d4df-4f89-ba0e-5360eec731f0', 'Odisha', 'IN');

-- Insert into district
INSERT INTO district(id, name, state_id) VALUES ('b3b76d88-1336-4e97-a38a-c49cec97fe61', 'Sundargarh', '1b6a7a7e-d4df-4f89-ba0e-5360eec731f0');

-- Insert into userTncTable
INSERT INTO user_tnc (id, name, base_url, description)
VALUES 
  ('589cdbed-ed3c-461e-b0dd-635c19611597', 'Terms of Service', '/legal-agreements/terms-of-service', 'General terms and conditions for using our service'),
  ('ea63e118-11ad-43de-84b0-8fe05e7f828c', 'Privacy Policy', '/legal-agreements/privacy-policy', 'Policy describing how we handle and protect user data')
ON CONFLICT DO NOTHING;

-- Insert into userTncVersionTable
INSERT INTO user_tnc_version (id, tnc_id, version, effective_date)
VALUES 
  ('a44b55fa-3339-45c5-b71d-92f60390d995', '589cdbed-ed3c-461e-b0dd-635c19611597', 1, CURRENT_DATE),
  ('b9352aab-f724-421b-8b2c-b4d91405e78f', 'ea63e118-11ad-43de-84b0-8fe05e7f828c', 1, CURRENT_DATE)
ON CONFLICT DO NOTHING;

INSERT INTO course_tag_group(id, name, position)
  VALUES
    ('36def8d5-c9df-4240-a96b-1fc941aa40d5','Subject',2),
    ('3b4345cb-c7f8-4761-a061-0ae27ea6371b','Level',4),
    ('58566b5b-de4a-4571-ab92-db617497e802','Standard',1),
    ('b6d2221f-e807-4958-8804-aa81938fed50','Board',3),
    ('e40703f8-cbf7-4d3e-8151-8ded3ce78de3','Stream',5)
ON CONFLICT DO NOTHING;

INSERT INTO course_tag(id, name, descr, position, group_id)
  VALUES
    ('c47f5c3d-8f2a-4b9e-a6e1-1a8b3f5d2c9a','Std I','Standard I',1,'58566b5b-de4a-4571-ab92-db617497e802'),
    ('e9d2b4a7-6c1f-4d8e-9b3a-5f7c2d1e8b4a','Std II','Standard II',2,'58566b5b-de4a-4571-ab92-db617497e802'),
    ('a1b2c3d4-e5f6-4a3b-8c7d-9e0f1a2b3c4d','Std III','Standard III',3,'58566b5b-de4a-4571-ab92-db617497e802'),
    ('b4a7c9e2-d5f8-4e1b-9a3c-7d5e8f1a2b4c','Std IV','Standard IV',4,'58566b5b-de4a-4571-ab92-db617497e802'),
    ('f1e2d3c4-b5a6-4c7b-8d9e-0a1b2c3d4e5f','Std V','Standard V',5,'58566b5b-de4a-4571-ab92-db617497e802'),
    ('d7e8f9a0-b1c2-4d3e-9f5a-6b7c8d9e0f1a','Std VI','Standard VI',6,'58566b5b-de4a-4571-ab92-db617497e802'),
    ('e5f6d7a8-b9c0-4d1e-8f2a-3b4c5d6e7f8a','Std VII','Standard VII',7,'58566b5b-de4a-4571-ab92-db617497e802'),
    ('a9b8c7d6-e5f4-4a3b-8c7d-6e5f4d3c2b1a','Std VIII','Standard VIII',8,'58566b5b-de4a-4571-ab92-db617497e802'),
    ('a1d2e3f4-a5b6-4c7d-8e9f-1a2b3c4d5e6f','Std IX','Standard IX',9,'58566b5b-de4a-4571-ab92-db617497e802'),
    ('b7a8b9c0-d1e2-4f3a-8c5d-6e7a8b9c0d1e','Std X','Standard X',10,'58566b5b-de4a-4571-ab92-db617497e802'),
    ('b2c3d4e5-f6a7-4b8c-9d0e-1f2a3b4c5d6e','Std XI','Standard XI',11,'58566b5b-de4a-4571-ab92-db617497e802'),
    ('a9b8c7d6-a5b4-4c3d-8e7f-6a5b4c3d2e1f','Std XII','Standard XII',12,'58566b5b-de4a-4571-ab92-db617497e802'),
    ('d4e5f6a7-b8c9-4d0e-8f1a-2b3c4d5e6f7a','Science','Science',1,'36def8d5-c9df-4240-a96b-1fc941aa40d5'),
    ('a2b3c4d5-a6b7-4c8d-9e0f-1a2b3c4d5e6f','Math','Mathematics',2,'36def8d5-c9df-4240-a96b-1fc941aa40d5'),
    ('a4b5c6d7-a8b9-4c0d-8e1f-2a3b4c5d6e7f','English','English',3,'36def8d5-c9df-4240-a96b-1fc941aa40d5'),
    ('f47d2c1b-a983-4e76-95f2-0d1c3b4a5e6d','History','History',4,'36def8d5-c9df-4240-a96b-1fc941aa40d5'),
    ('e8d7c6b5-a4f3-42e1-b0d9-c8a7b6e5d4c3','BSE Odisha','Board of Secondary Education, Odisha',1,'b6d2221f-e807-4958-8804-aa81938fed50'),
    ('2eeac613-fbb1-4036-a2f1-00d86d4bc4b4','CHSE Odisha','Council of Higher Secondary Education, Odisha',2,'b6d2221f-e807-4958-8804-aa81938fed50'),
    ('7f1bb4a7-4f1b-4e5f-9e46-7bbf82fbeaab','CBSE','Central Board of Secondary Education',3,'b6d2221f-e807-4958-8804-aa81938fed50'),
    ('a69a602f-ada1-4e5b-ace9-1921bfd7ef64','ICSE','Indian Certificate of Secondary Education',4,'b6d2221f-e807-4958-8804-aa81938fed50'),
    ('b9a8c7d6-e5f4-43a2-b1c0-d9e8f7a6b5c4','Foundation','Foundation',1,'3b4345cb-c7f8-4761-a061-0ae27ea6371b'),
    ('d2e1f0a9-b8c7-46d5-e4f3-a2b1c0d9e8f7','Advanced','Advanced',2,'3b4345cb-c7f8-4761-a061-0ae27ea6371b'),
    ('53c6b646-b9e6-4d93-909b-b612590b2c75','Arts Stream','Arts',1,'e40703f8-cbf7-4d3e-8151-8ded3ce78de3'),
    ('70ae28a2-a840-44ea-968e-2d2c613d8c85','Science Stream','Science',2,'e40703f8-cbf7-4d3e-8151-8ded3ce78de3'),
    ('0c8651c9-6060-4677-88cf-918feb4c2191','Commerce Stream','Commerce',3,'e40703f8-cbf7-4d3e-8151-8ded3ce78de3')
ON CONFLICT DO NOTHING;

import { relations, sql } from 'drizzle-orm'
import { AnyPgColumn, check, index, pgTable, text } from 'drizzle-orm/pg-core'

import { Role } from '@/shared/profiles/role-utils.shared'

import { auditColumns, suspensionCheck, suspensionColumns, timestampz, uid } from './helpers'
import { userTable } from './user-schema'

/**
 * Profile table stores user profile information including:
 * - Display name and role
 * - References to user and supporter
 * - Approval timestamps
 */
export const profileTable = pgTable(
  'profile',
  {
    id: uid().primaryKey(),

    displayName: text().notNull(),
    role: text('role').$type<Role>().notNull(),
    descr: text(),

    userId: uid()
      .notNull()
      .references(() => userTable.id, { onDelete: 'cascade' }),

    serviceProviderId: uid().references((): AnyPgColumn => profileTable.id),

    submittedForApprovalAt: timestampz(),
    approvedAt: timestampz(),
    tncsAcceptedAt: timestampz().notNull(),
    ...auditColumns,
    ...suspensionColumns,
  },
  (t) => [
    check('chk_display_name_len', sql`char_length(${t.displayName}) <= 255`),
    check('chk_role', sql`${t.role} IN ('principal', 'mentor', 'teacher', 'student', 'admin', 'manager', 'executive')`),
    check('descr_len', sql`char_length(${t.descr}) <= 10000`),
    ...suspensionCheck(t),
    index('idx_profile_user_id_role').on(t.userId, t.role),
    index('idx_profile_supporter_id').on(t.serviceProviderId),
  ],
)

// Define self-referential relation for service provider
export const profileRelations = relations(profileTable, ({ one }) => ({
  user: one(userTable, {
    fields: [profileTable.userId],
    references: [userTable.id],
  }),
  serviceProvider: one(profileTable, {
    fields: [profileTable.serviceProviderId],
    references: [profileTable.id],
  }),
}))

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { db } from '@/server/db/db'
import { batchAttachmentTable, batchTable } from '@/server/db/schema/batch-schema'
import { courseAttachmentTable } from '@/server/db/schema/course-schema'
import { AddBatchAttachmentForm } from '@/shared/batch/batch-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { ensureCanUpdateBatch } from '../../common/ensureCanUpdateBatch'

const findBatchDaf = (log: pino.Logger, batchId: UUID) =>
  withLog(log, 'findBatchDaf', () =>
    db.query.batchTable.findFirst({
      columns: {
        courseId: true,
      },
      where: eq(batchTable.id, batchId),
    }),
  )

const findCourseAttachmentDaf = (log: pino.Logger, attachmentId: UUID) =>
  withLog(log, 'findCourseAttachmentDaf', () =>
    db.query.courseAttachmentTable.findFirst({
      columns: {
        courseId: true,
      },
      where: eq(courseAttachmentTable.id, attachmentId),
    }),
  )

const insertBatchAttachmentDaf = (log: pino.Logger, batchId: UUID, attachmentId: UUID) =>
  withLog(log, 'insertBatchAttachmentDaf', () =>
    db
      .insert(batchAttachmentTable)
      .values({
        id: crypto.randomUUID(),
        batchId,
        attachmentId,
      })
      .onConflictDoNothing(),
  )

export const addBatchAttachment = async (c: Ctx, batchId: UUID, form: AddBatchAttachmentForm) => {
  await ensureCanUpdateBatch(c, batchId)

  const [batch, attachment] = await Promise.all([
    findBatchDaf(c.log, batchId),
    findCourseAttachmentDaf(c.log, form.attachmentId),
  ])
  ensureExists(batch, batchId, 'Batch')
  ensureExists(attachment, form.attachmentId, 'Course Attachment')

  ensure(batch.courseId === attachment.courseId, {
    statusCode: 409,
    issueMessage: 'The given attachment is not of the course of the batch',
    logMessage: `Attachment ${form.attachmentId} is not of the course of batch ${batchId}`,
  })

  await insertBatchAttachmentDaf(c.log, batchId, form.attachmentId)
}

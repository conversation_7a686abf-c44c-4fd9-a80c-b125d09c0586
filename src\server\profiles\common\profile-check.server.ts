import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { ensure } from '@/server/common/error/ensure.server'
import { db } from '@/server/db/db'
import { profileTable } from '@/server/db/schema/profile-schema'
import { userTable } from '@/server/db/schema/user-schema'
import { withLog } from '@/shared/common/withLog.shared'
import { ACADEMY_STAFF, Role } from '@/shared/profiles/role-utils.shared'

export type ProfileAndUserToCheck = {
  role: Role
  suspendedAt: Date | null
  approvedAt: Date | null
  userId: UUID
  userSuspendedAt: Date | null
  emailVerified: boolean
  mobileVerified: boolean
}

export const profileToCheckColumns = {
  role: profileTable.role,
  suspendedAt: profileTable.suspendedAt,
  approvedAt: profileTable.approvedAt,
  userId: profileTable.userId,
  userSuspendedAt: userTable.suspendedAt,
  emailVerified: userTable.emailVerified,
  mobileVerified: userTable.mobileVerified,
} as const

export const findProfileForVerificationDaf = async (log: pino.Logger, profileId: UUID) =>
  withLog(log, 'findProfileForVerificationDaf', () =>
    db
      .select(profileToCheckColumns)
      .from(profileTable)
      .innerJoin(userTable, eq(profileTable.userId, userTable.id))
      .where(eq(profileTable.id, profileId))
      .then((rows) => rows[0]),
  )

export const isGoodProfile = (
  profile: ProfileAndUserToCheck | undefined,
  roleAnyOf: readonly Role[] = ACADEMY_STAFF,
) => {
  return (
    profile &&
    roleAnyOf.includes(profile.role) &&
    profile.approvedAt &&
    profile.emailVerified &&
    profile.mobileVerified &&
    !profile.suspendedAt &&
    !profile.userSuspendedAt
  )
}

export function ensureGoodProfile(
  id: UUID,
  profile: ProfileAndUserToCheck | undefined,
  subject?: string,
): asserts profile {
  const sub = subject ?? 'Profile'

  ensure(profile, {
    statusCode: 404,
    issueMessage: `${sub} not found.`,
    logMessage: `${sub} with id ${id} not found.`,
  })

  ensure(!profile.suspendedAt, {
    statusCode: 409,
    issueMessage: `${sub} is suspended.`,
    logMessage: `${sub} with id ${id} is suspended.`,
  })

  ensure(profile.emailVerified, {
    statusCode: 409,
    issueMessage: `Email of ${sub} is not verified.`,
    logMessage: `Email of ${sub} having id ${id} with user id ${profile.userId} is not verified.`,
  })

  ensure(profile.mobileVerified, {
    statusCode: 409,
    issueMessage: `Mobile of ${sub} is not verified.`,
    logMessage: `Mobile of ${sub} having id ${id} with user id ${profile.userId} is not verified.`,
  })

  ensure(!profile.userSuspendedAt, {
    statusCode: 409,
    issueMessage: `User of ${sub} is suspended.`,
    logMessage: `User of ${sub} having id ${id} is suspended.`,
  })
}

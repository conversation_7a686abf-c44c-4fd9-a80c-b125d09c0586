import { Combobox, ComboboxButton, ComboboxInput, ComboboxOption, ComboboxOptions, Label } from '@headlessui/react'
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/react/20/solid'
import { useState } from 'react'

import { UNKNOWN_UUID } from '@/shared/common/common-utils.shared'

type ComboBoxItem = {
  id: string
  name: string
}

type ComboBoxProps = {
  items: ComboBoxItem[]
  selectedItemId: string
  setSelectedItemId: (itemId: string) => void
  label: string
}

export const ComboBox = ({ items, selectedItemId, setSelectedItemId, label }: ComboBoxProps) => {
  const [query, setQuery] = useState('')

  const filteredItem =
    query === '' ? items : (
      items.filter((item) => {
        return item.name.toLowerCase().includes(query.toLowerCase())
      })
    )

  const selectedItem = items.find((item) => item.id === selectedItemId) ?? { id: UNKNOWN_UUID, name: '' }

  return (
    <Combobox
      as="div"
      value={selectedItem}
      onChange={(item) => {
        setQuery('')
        setSelectedItemId(item?.id ?? '')
      }}
    >
      <Label className="block text-sm/6 font-medium text-gray-900">{label}</Label>
      <div className="relative mt-2">
        <ComboboxInput
          className="block w-full rounded-md bg-white py-1.5 pr-12 pl-3 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
          onChange={(event) => setQuery(event.target.value)}
          onBlur={() => setQuery('')}
          displayValue={(item) => (item as ComboBoxItem)?.name}
        />
        <ComboboxButton className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-hidden">
          <ChevronUpDownIcon className="size-5 text-gray-400" aria-hidden="true" />
        </ComboboxButton>

        {filteredItem.length > 0 && (
          <ComboboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-hidden sm:text-sm">
            {filteredItem.map((item) => (
              <ComboboxOption
                key={item.id}
                value={item}
                className="group relative cursor-default py-2 pr-9 pl-3 text-gray-900 select-none data-focus:bg-indigo-600 data-focus:text-white data-focus:outline-hidden"
              >
                <span className="block truncate group-data-selected:font-semibold">{item.name}</span>

                <span className="absolute inset-y-0 right-0 hidden items-center pr-4 text-indigo-600 group-data-focus:text-white group-data-selected:flex">
                  <CheckIcon className="size-5" aria-hidden="true" />
                </span>
              </ComboboxOption>
            ))}
          </ComboboxOptions>
        )}
      </div>
    </Combobox>
  )
}

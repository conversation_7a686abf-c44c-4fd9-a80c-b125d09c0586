import { UUID } from 'crypto'

import { eq, isNotNull } from 'drizzle-orm'
import pino from 'pino'

import { appendCommandDaf } from '@/server/common/command/processCommands'
import { mailFlock } from '@/server/common/mail/mail'
import { smsFlock } from '@/server/common/sms/sms'
import { db, Transaction } from '@/server/db/db'
import { userTable } from '@/server/db/schema'
import { phoneNumber } from '@/shared/common/common-utils.shared'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { nowTruncated } from '@/shared/common/date-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { MAIL_PLEASE_LOGIN_ASAP_COMMAND, MailPleaseLoginAsapCommandData } from './mailPleaseLoginAsapCommand'
import { SMS_PLEASE_LOGIN_ASAP_COMMAND, SmsPleaseLoginAsapCommandData } from './smsPleaseLoginAsapCommand'

export const fetchNextUserWithRefreshTokenDaf = async (log: pino.Logger) =>
  withLog(log, 'fetchNextUserWithRefreshTokenDaf', () =>
    db.query.userTable.findFirst({
      columns: {
        id: true,
        email: true,
        mobile: true,
        name: true,
        language: true,
        googleRefreshToken: true,
      },
      with: {
        mobileCountry: {
          columns: {
            phonePrefix: true,
          },
        },
      },
      where: isNotNull(userTable.googleRefreshToken),
    }),
  )

type User = NonNullable<Awaited<ReturnType<typeof fetchNextUserWithRefreshTokenDaf>>>

export const nullifyGoogleRefreshTokenAndNotifyUserDaf = async (log: pino.Logger, user: User) =>
  db.transaction(async (tx) => {
    await nullifyGoogleRefreshTokenAndLogoutDaf(log, tx, user.id)
    await appendCommandDaf<MailPleaseLoginAsapCommandData>(tx, log, mailFlock(), {
      command: MAIL_PLEASE_LOGIN_ASAP_COMMAND,
      data: {
        userEmail: user.email,
        userName: user.name,
        language: user.language,
      },
    })
    await appendCommandDaf<SmsPleaseLoginAsapCommandData>(tx, log, smsFlock(), {
      command: SMS_PLEASE_LOGIN_ASAP_COMMAND,
      data: {
        userMobile: phoneNumber(user.mobileCountry.phonePrefix, user.mobile),
        userName: user.name,
        language: user.language,
      },
    })
  })

const nullifyGoogleRefreshTokenAndLogoutDaf = async (log: pino.Logger, db: Transaction, userId: UUID) =>
  withLog(log, 'nullifyGoogleRefreshTokenDaf', () =>
    db
      .update(userTable)
      .set({ googleRefreshToken: null, tokensValidFrom: nowTruncated(utc()) })
      .where(eq(userTable.id, userId)),
  )

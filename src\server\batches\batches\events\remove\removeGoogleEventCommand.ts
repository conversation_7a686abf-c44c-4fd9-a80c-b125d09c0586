import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { pino } from 'pino'

import { addCommand, Command } from '@/server/common/command/processCommands.js'
import { getCalApiForRefreshToken } from '@/server/common/google/calendar/calendar-utils'
import { db } from '@/server/db/db'
import { userTable } from '@/server/db/schema'
import { isNotFoundOrGone } from '@/shared/common/common-utils.shared'
import { getLogger } from '@/shared/common/logger.shared'
import { withLog } from '@/shared/common/withLog.shared'

export const REMOVE_GOOGLE_EVENT_COMMAND = 'removeGoogleEvent'

export type RemoveGoogleEventCommandData = {
  userId: UUID
  eventXid: string
}

const findUserDaf = (log: pino.Logger, userId: UUID) =>
  withLog(log, 'findUserDaf', () =>
    db.query.userTable.findFirst({ columns: { googleRefreshToken: true }, where: eq(userTable.id, userId) }),
  )

async function executeCommand(log: pino.Logger, command: Command<RemoveGoogleEventCommandData>) {
  const data = command.data
  const user = await findUserDaf(log, data.userId)

  if (!user) {
    log.warn(`User with ID ${data.userId} not found`)
    return
  }

  if (!user.googleRefreshToken) {
    log.error(`User with ID ${data.userId} has no google refresh token`)
    return
  }
  const calApi = await getCalApiForRefreshToken(user.googleRefreshToken)

  try {
    await calApi.events.delete({
      calendarId: 'primary',
      eventId: data.eventXid,
    })
  } catch (ex: unknown) {
    if (isNotFoundOrGone(ex)) {
      log.warn(`Event with ID ${data.eventXid} is already deleted`)
    } else {
      throw ex
    }
  }
}

addCommand(REMOVE_GOOGLE_EVENT_COMMAND, executeCommand)
getLogger().info(`Loaded ${import.meta.url}`)

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { userTable } from '@/server/db/schema/user-schema'
import { withLog } from '@/shared/common/withLog.shared'

const shortColumns = {
  id: true,
  name: true,
  mobileVerified: true,
  googlePictureUrl: true,
} as const

const fullColumns = {
  ...shortColumns,
  email: true,
  mobileCountryCode: true,
  mobile: true,
} as const

export const getUser = async (c: Ctx, full: boolean) => {
  ensureUser(c.user, { ignoreEmailVerification: true, ignoreMobileVerification: true })
  return full ? findFullUserByIdDaf(c.log, c.user.id) : findUserByIdDaf(c.log, c.user.id)
}

const findUserByIdDaf = (log: Logger, userId: UUID) =>
  withLog(log, 'findUserByIdDaf', () =>
    db.query.userTable.findFirst({
      columns: shortColumns,
      with: {
        tncAcceptances: {
          columns: {
            tncVersionId: true,
          },
        },
      },
      where: eq(userTable.id, userId),
    }),
  )

const findFullUserByIdDaf = (log: Logger, userId: UUID) =>
  withLog(log, 'findFullUserByIdDaf', () =>
    db.query.userTable.findFirst({
      columns: fullColumns,
      with: {
        mobileCountry: {
          columns: {
            phonePrefix: true,
          },
        },
        tncAcceptances: {
          columns: {
            tncVersionId: true,
          },
        },
      },
      where: eq(userTable.id, userId),
    }),
  )

export type ShortUserData = Awaited<ReturnType<typeof findUserByIdDaf>>
export type FullUserData = Awaited<ReturnType<typeof findFullUserByIdDaf>>
export type UserData = ShortUserData | FullUserData

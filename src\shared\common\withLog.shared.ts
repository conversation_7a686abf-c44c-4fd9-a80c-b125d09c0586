import type { Logger } from 'pino'

const getLogObj = (
  operationName: string,
  duration: number,
  success: boolean,
  options?: { data: Record<string, unknown> },
) => {
  const obj: Record<string, unknown> = { op: { name: operationName, ms: duration, success } }
  if (options?.data) obj.data = options.data
  return obj
}

export const withLog = async <R>(
  log: Logger,
  operationName: string,
  fn: () => Promise<R>,
  options?: { data: Record<string, unknown> },
): Promise<R> => {
  const startTime = Date.now()

  try {
    const result = await fn()
    const duration = Date.now() - startTime
    log.info(
      getLogObj(operationName, duration, true, options),
      `${operationName} operation succeeded. Took ${duration} ms.`,
    )
    return result
  } catch (error: unknown) {
    const duration = Date.now() - startTime
    log.error(
      getLogObj(operationName, duration, false, options),
      `${operationName} operation failed. Took ${duration} ms.`,
    )
    throw error
  }
}

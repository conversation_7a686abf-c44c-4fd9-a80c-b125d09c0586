import { UUID } from 'crypto'

import { calendar_v3 } from '@googleapis/calendar'
import { pino } from 'pino'

import { addCommand, appendCommandDaf, Command } from '@/server/common/command/processCommands'
import { calendarFlock, getCalApiForRefreshToken } from '@/server/common/google/calendar/calendar-utils'
import { db } from '@/server/db/db'
import { isNotFoundOrGone } from '@/shared/common/common-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

import {
  ADD_GOOGLE_EVENT_FOR_BATCH_COMMAND,
  AddGoogleEventForBatchCommandData,
} from '../add/addGoogleEventForBatchEventCommand'
import { composeGoogleEventDataForBatchEvent } from '../common/composeGoogleEventDataForBatchEvent'
import { updateBatchEventXidDaf } from '../common/updateBatchEventXidDaf'

export const UPDATE_GOOGLE_EVENT_FOR_BATCH_COMMAND = 'updateGoogleEventForBatch'

export type UpdateGoogleEventForBatchEventCommandData = {
  eventId: UUID
  teacherUserId: UUID
}

const recreateEvent = async (log: pino.Logger, command: Command<UpdateGoogleEventForBatchEventCommandData>) => {
  const { data } = command
  await db.transaction(async (tx) => {
    await updateBatchEventXidDaf(log, tx, data.eventId)
    await appendCommandDaf<AddGoogleEventForBatchCommandData>(tx, log, calendarFlock(data.teacherUserId), {
      command: ADD_GOOGLE_EVENT_FOR_BATCH_COMMAND,
      data: {
        eventId: data.eventId,
      },
    })
  })
}

const getGoogleEvent = async (log: pino.Logger, calApi: calendar_v3.Calendar, eventXid: string) => {
  try {
    const eventResponse = await calApi.events.get({
      calendarId: 'primary',
      eventId: eventXid,
    })
    if (eventResponse.data.status === 'cancelled') {
      log.warn(`Google event with ID ${eventXid} is cancelled`)
      return null
    }
    return eventResponse.data
  } catch (ex: unknown) {
    if (isNotFoundOrGone(ex)) {
      log.warn(`Google event with ID ${eventXid} not found`)
      return null
    }
    throw ex
  }
}

const executeCommand = async (log: pino.Logger, command: Command<UpdateGoogleEventForBatchEventCommandData>) => {
  const data = command.data
  const latestEventAndRefreshToken = await composeGoogleEventDataForBatchEvent(log, data.eventId)

  if (!latestEventAndRefreshToken) return
  const [refreshToken, latestEvent] = latestEventAndRefreshToken
  const calApi = await getCalApiForRefreshToken(refreshToken)

  const oldEvent = await getGoogleEvent(log, calApi, latestEvent.id!)
  if (!oldEvent) {
    // deleted
    await recreateEvent(log, command)
    return
  }

  try {
    //  https://developers.google.com/calendar/api/v3/reference/events/update
    await withLog(log, 'updateGoogleEvent', () =>
      calApi.events.update({
        calendarId: 'primary',
        eventId: latestEvent.id!,
        sendUpdates: 'all',
        conferenceDataVersion: 1,
        requestBody: {
          ...oldEvent, // etag will ensure atomicity
          ...latestEvent,
        },
      }),
    )
  } catch (ex: unknown) {
    if (isNotFoundOrGone(ex)) {
      await recreateEvent(log, command)
    } else throw ex
  }
}

addCommand(UPDATE_GOOGLE_EVENT_FOR_BATCH_COMMAND, executeCommand)

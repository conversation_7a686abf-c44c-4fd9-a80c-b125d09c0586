import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { ensureCanUpdateCourse } from '@/server/courses/courses/common/ensureCanUpdateCourse'
import { db, Transaction } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { academyStaffTable } from '@/server/db/schema/academy-schema'
import { batchRecommendationTable, batchTable } from '@/server/db/schema/batch-schema'
import { courseTable } from '@/server/db/schema/course-schema'
import { findProfileForVerificationDaf, ensureGoodProfile } from '@/server/profiles/common/profile-check.server'
import { AddBatchForm, MAX_BATCHES_PER_COURSE } from '@/shared/batch/batch-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

const findCourseDaf = (log: pino.Logger, courseId: UUID) =>
  withLog(log, 'findCourseDaf', () =>
    db
      .select({
        batchCount: db.$count(batchTable, eq(batchTable.courseId, courseId)).as('batchCount'),
      })
      .from(courseTable)
      .where(eq(courseTable.id, courseId))
      .then((rows) => rows[0]),
  )

const findAcademyStaff = (profileId: UUID, courseId: UUID) =>
  db
    .select(dummyColumn.extras)
    .from(academyStaffTable)
    .innerJoin(courseTable, eq(academyStaffTable.academyId, courseTable.academyId))
    .where(and(eq(academyStaffTable.profileId, profileId), eq(courseTable.id, courseId)))
    .limit(1)

const insertBatchDaf = (log: pino.Logger, db: Transaction, form: AddBatchForm) =>
  withLog(log, 'insertBatchDaf', () =>
    db.insert(batchTable).values({
      id: form.batchId,
      courseId: form.courseId,
      teacherId: form.teacherId,
      fee: form.fee.amount,
      billingCycle: form.billingCycle,
      graceDays: form.graceDays,
      startDate: form.startDate,
      timezone: form.timezone,
      cycleCount: form.cycleCount,
      seatCount: form.seatCount,
      admissionOpen: form.admissionOpen,
    }),
  )

const insertBatchRecommendationDaf = (log: pino.Logger, db: Transaction, courseId: UUID, batchId: UUID) =>
  withLog(log, 'insertBatchRecommendationDaf', () =>
    db
      .insert(batchRecommendationTable)
      .values({
        courseId,
        batchId,
      })
      .onConflictDoNothing(),
  )

export const addBatch = async (c: Ctx, form: AddBatchForm) => {
  const course = await findCourseDaf(c.log, form.courseId)
  ensureExists(course, form.courseId, 'Course')
  await ensureCanUpdateCourse(c, form.courseId)

  // Ensure batch count is within limits
  ensure(course.batchCount < MAX_BATCHES_PER_COURSE, {
    statusCode: 409,
    issueMessage: `Maximum number of batches (${MAX_BATCHES_PER_COURSE}) reached for this course.`,
    logMessage: `Attempted to add batch to course ${form.courseId} which already has ${course.batchCount} batches.`,
  })

  const teacher = await findProfileForVerificationDaf(c.log, form.teacherId)
  ensureGoodProfile(form.teacherId, teacher, 'Teacher')
  const staff = await findAcademyStaff(form.teacherId, form.courseId)
  ensure(staff.length > 0, {
    statusCode: 409,
    issueMessage: 'The given teacher is not a staff of the academy of the course.',
    logMessage: `Teacher ${form.teacherId} is not a staff of the academy of course ${form.courseId}`,
  })

  await db.transaction(async (tx) => {
    await insertBatchDaf(c.log, tx, form)
    await insertBatchRecommendationDaf(c.log, tx, form.courseId, form.batchId)
  })
}

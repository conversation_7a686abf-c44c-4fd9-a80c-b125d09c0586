import { UUID } from 'crypto'

import { utc } from '@/shared/common/date-utils-basic.shared'
import { DEFAULT_VALIDITY_DAYS } from '@/shared/users/user-utils.shared'

import { createAccessToken } from '../common/auth-token-utils.server'
export const fallbackLocale = 'en-US,en;q=0.9'

export const createAuthTokenForUser = async (
  jwtSecret: string,
  userId: UUID,
  validityDays = DEFAULT_VALIDITY_DAYS,
) => ({
  accessToken: await createAccessToken(jwtSecret, userId, validityDays),
  userId: userId,
  accessTokenValidUntil: utc().plus({ days: validityDays }).startOf('second').toISO(),
})

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { DateTime } from 'luxon'
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'

import app from '@/hono-entry'
import { env } from '@/server/common/env.server'
import { db } from '@/server/db/db'
import { academyTable, academyStaffTable } from '@/server/db/schema/academy-schema'
import { courseTable } from '@/server/db/schema/course-schema'
import { profileTable } from '@/server/db/schema/profile-schema'
import { userTable, userTncAcceptedTable, userTncSignTable } from '@/server/db/schema/user-schema'
import { initDb, SUNDARGARH_DISTRICT_ID, TNC_PRIVACY_V1_ID, TNC_TOS_V1_ID } from '@/server/test/initDb'
import { createAccessToken } from '@/server/users/common/auth-token-utils.server'
import { UUID_REGEX } from '@/shared/common/common-utils.shared'
import { AddCourseForm, MAX_COURSES_PER_ACADEMY } from '@/shared/course/course-utils.shared'
import { PROFILE_HEADER } from '@/shared/profiles/profile-utils.shared'

const mocked = {
  now: DateTime.now(),
} as const

// Mock date utilities to have consistent test results
vi.mock(import('@/shared/common/date-utils-basic.shared'), async (importOriginal) => {
  const mod = await importOriginal()
  return {
    ...mod,
    utc: () => mocked.now,
  }
})

const testUser = {
  id: crypto.randomUUID() as UUID,
  googleId: 'test-google-id',
  googleRefreshToken: null,
  name: 'Test Principal',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/picture',
  language: 'en-US',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '9876543210',
  mobileVerified: true,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

const testProfile = {
  id: crypto.randomUUID() as UUID,
  userId: testUser.id,
  role: 'principal' as const,
  displayName: 'Test Principal',
  approvedAt: mocked.now.toJSDate(),
  tncsAcceptedAt: mocked.now.toJSDate(),
} as const

const testAcademy = {
  id: crypto.randomUUID() as UUID,
  name: 'Test Academy',
  email: '<EMAIL>',
  emailVerified: true,
  mobileCountryCode: 'IN',
  mobile: '9876543210',
  mobileVerified: true,
  currency: 'INR' as const,
  upiId: 'test@upi',
  districtId: SUNDARGARH_DISTRICT_ID as UUID,
  pincode: '770001',
  area: 'Test Area',
  tncsAcceptedAt: mocked.now.toJSDate(),
  approvedAt: mocked.now.toJSDate(),
} as const

const validCourseForm: AddCourseForm = {
  name: 'Mathematics Class 10',
  descr: 'Advanced mathematics course for class 10 students',
  academyId: testAcademy.id,
}

const makeAuthenticatedRequest = async (form: AddCourseForm, accessToken?: string) => {
  const token = accessToken || (await createAccessToken(env.JWT_SECRET_KEY, testUser.id))

  return app.request(
    '/api/courses',
    {
      method: 'POST',
      body: JSON.stringify(form),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        [PROFILE_HEADER]: testProfile.id,
      },
    },
    {},
  )
}

const makeUnauthenticatedRequest = async (form: AddCourseForm) => {
  return app.request(
    '/api/courses',
    {
      method: 'POST',
      body: JSON.stringify(form),
      headers: {
        'Content-Type': 'application/json',
      },
    },
    {},
  )
}

const setupBasicTestData = async () => {
  // Create user
  await db.insert(userTable).values(testUser)

  // Add TnC acceptance records (required by authMiddleware for API endpoints)
  const acceptedTnCs = [TNC_TOS_V1_ID as UUID, TNC_PRIVACY_V1_ID as UUID]
  for (const tncId of acceptedTnCs) {
    await db.insert(userTncSignTable).values({
      id: crypto.randomUUID() as UUID,
      userId: testUser.id,
      tncVersionId: tncId,
      accepted: true,
    })

    await db.insert(userTncAcceptedTable).values({
      id: crypto.randomUUID() as UUID,
      userId: testUser.id,
      tncVersionId: tncId,
    })
  }

  // Create profile
  await db.insert(profileTable).values(testProfile)

  // Create academy
  await db.insert(academyTable).values(testAcademy)

  // Create academy staff relationship
  await db.insert(academyStaffTable).values({
    profileId: testProfile.id,
    academyId: testAcademy.id,
  })
}

const assertValidCourseResponse = async (responseBody: { id: UUID }, expectedForm: AddCourseForm) => {
  expect(responseBody.id, 'Course ID should be a valid UUID').toMatch(UUID_REGEX)

  // Verify course was created in database
  const courses = await db.select().from(courseTable).where(eq(courseTable.id, responseBody.id))
  expect(courses.length, 'Course should exist in database').toBe(1)

  const course = courses[0]
  expect(course.name, 'Course name').toBe(expectedForm.name)
  expect(course.descr, 'Course description').toBe(expectedForm.descr ? `<p>${expectedForm.descr}</p>\n` : null)
  expect(course.academyId, 'Course academy ID').toBe(expectedForm.academyId)
  expect(course.creationRemarks, 'Creation remarks').toBe(`${testProfile.id} profilecreated this course`)
  expect(course.suspendedAt, 'Course should not be suspended').toBeNull()
  expect(course.publishedAt, 'Course should not be published yet').toBeNull()
}

describe('addCourseHandler', () => {
  beforeEach(async () => {
    await initDb()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Success scenarios', () => {
    test('should create course with valid data', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(validCourseForm)

      // then
      expect(res.status, 'Status check').toBe(201)
      const responseBody = await res.json()
      await assertValidCourseResponse(responseBody, validCourseForm)
    })

    test('should create course without description', async () => {
      // given
      await setupBasicTestData()
      const formWithoutDescription = { ...validCourseForm, descr: undefined }

      // when
      const res = await makeAuthenticatedRequest(formWithoutDescription)

      // then
      expect(res.status, 'Status check').toBe(201)
      const responseBody = await res.json()
      await assertValidCourseResponse(responseBody, formWithoutDescription)
    })

    test('should create course with markdown description', async () => {
      // given
      await setupBasicTestData()
      const formWithMarkdown = {
        ...validCourseForm,
        descr: '**Bold text** and *italic text*',
      }

      // when
      const res = await makeAuthenticatedRequest(formWithMarkdown)

      // then
      expect(res.status, 'Status check').toBe(201)
      const responseBody = await res.json()

      // Verify markdown was converted to HTML
      const courses = await db.select().from(courseTable).where(eq(courseTable.id, responseBody.id))
      expect(courses[0].descr).toBe('<p><strong>Bold text</strong> and <em>italic text</em></p>\n')
    })
  })

  describe('Authentication and Authorization errors', () => {
    test('should return 401 when no authorization header', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeUnauthenticatedRequest(validCourseForm)

      // then
      expect(res.status, 'Status check').toBe(401)
    })

    test('should return 401 with invalid token', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(validCourseForm, 'invalid-token')

      // then
      expect(res.status, 'Status check').toBe(401)
    })

    test('should return 403 when user has no profile', async () => {
      // given
      await db.insert(userTable).values(testUser)

      // Add TnC acceptance records
      const acceptedTnCs = [TNC_TOS_V1_ID, TNC_PRIVACY_V1_ID] as const
      for (const tncId of acceptedTnCs) {
        await db.insert(userTncSignTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
          accepted: true,
        })

        await db.insert(userTncAcceptedTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
        })
      }

      await db.insert(academyTable).values(testAcademy)

      // when
      const res = await makeAuthenticatedRequest(validCourseForm)

      // then
      expect(res.status, 'Status check').toBe(403)
      await expect(res).toBeError(403, [
        {
          code: 'custom',
          path: [],
          message: 'TL-Profile request header missing',
        },
      ])
    })

    test('should return 403 when user is not staff of the academy', async () => {
      // given
      await db.insert(userTable).values(testUser)

      // Add TnC acceptance records
      const acceptedTnCs = [TNC_TOS_V1_ID, TNC_PRIVACY_V1_ID] as const
      for (const tncId of acceptedTnCs) {
        await db.insert(userTncSignTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
          accepted: true,
        })

        await db.insert(userTncAcceptedTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
        })
      }

      await db.insert(profileTable).values(testProfile)
      await db.insert(academyTable).values(testAcademy)
      // Not adding academy staff relationship

      // when
      const res = await makeAuthenticatedRequest(validCourseForm)

      // then
      expect(res.status, 'Status check').toBe(403)
      await expect(res).toBeError(403, [
        {
          code: 'custom',
          path: [],
          message: 'You must be a staff of the academy to perform this action.',
        },
      ])
    })

    test('should return 403 when profile is suspended', async () => {
      // given
      await db.insert(userTable).values(testUser)

      // Add TnC acceptance records
      const acceptedTnCs = [TNC_TOS_V1_ID, TNC_PRIVACY_V1_ID] as const
      for (const tncId of acceptedTnCs) {
        await db.insert(userTncSignTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
          accepted: true,
        })

        await db.insert(userTncAcceptedTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
        })
      }

      await db.insert(profileTable).values({
        ...testProfile,
        suspendedAt: mocked.now.toJSDate(),
      })
      await db.insert(academyTable).values(testAcademy)
      await db.insert(academyStaffTable).values({
        profileId: testProfile.id,
        academyId: testAcademy.id,
      })

      // when
      const res = await makeAuthenticatedRequest(validCourseForm)

      // then
      expect(res.status, 'Status check').toBe(403)
      await expect(res).toBeError(403, [
        {
          code: 'custom',
          path: [],
          message: 'Profile is suspended',
        },
      ])
    })

    test('should return 403 when user email is not verified', async () => {
      // given
      await db.insert(userTable).values({ ...testUser, emailVerified: false })

      // Add TnC acceptance records
      const acceptedTnCs = [TNC_TOS_V1_ID, TNC_PRIVACY_V1_ID] as const
      for (const tncId of acceptedTnCs) {
        await db.insert(userTncSignTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
          accepted: true,
        })

        await db.insert(userTncAcceptedTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
        })
      }

      await db.insert(profileTable).values(testProfile)
      await db.insert(academyTable).values(testAcademy)
      await db.insert(academyStaffTable).values({
        profileId: testProfile.id,
        academyId: testAcademy.id,
      })

      // when
      const res = await makeAuthenticatedRequest(validCourseForm)

      // then
      expect(res.status, 'Status check').toBe(403)
      await expect(res).toBeError(403, [
        {
          code: 'custom',
          path: [],
          message: 'Please first verify your email at Google',
        },
      ])
    })

    test('should return 403 when user has not accepted TnCs', async () => {
      // given
      await db.insert(userTable).values(testUser)
      // Not adding TnC acceptance records
      await db.insert(profileTable).values(testProfile)
      await db.insert(academyTable).values(testAcademy)
      await db.insert(academyStaffTable).values({
        profileId: testProfile.id,
        academyId: testAcademy.id,
      })

      // when
      const res = await makeAuthenticatedRequest(validCourseForm)

      // then
      expect(res.status, 'Status check').toBe(403)
      await expect(res).toBeError(403, [
        {
          code: 'custom',
          path: [],
          message: 'Please first accept all TnCs',
        },
      ])
    })
  })

  describe('Validation errors', () => {
    test('should return 400 for missing name', async () => {
      // given
      await setupBasicTestData()
      const invalidForm = { ...validCourseForm, name: undefined } as unknown as AddCourseForm

      // when
      const res = await makeAuthenticatedRequest(invalidForm)

      // then
      expect(res.status, 'Status check').toBe(400)
      await expect(res).toBeError(400, [
        {
          code: 'invalid_type',
          path: ['name'],
          message: 'Required',
        },
      ])
    })

    test('should return 400 for short name', async () => {
      // given
      await setupBasicTestData()
      const invalidForm = { ...validCourseForm, name: 'AB' }

      // when
      const res = await makeAuthenticatedRequest(invalidForm)

      // then
      expect(res.status, 'Status check').toBe(400)
      await expect(res).toBeError(400, [
        {
          code: 'too_small',
          path: ['name'],
          message: 'String must contain at least 3 character(s)',
        },
      ])
    })

    test('should return 400 for long name', async () => {
      // given
      await setupBasicTestData()
      const invalidForm = { ...validCourseForm, name: 'A'.repeat(101) }

      // when
      const res = await makeAuthenticatedRequest(invalidForm)

      // then
      expect(res.status, 'Status check').toBe(400)
      await expect(res).toBeError(400, [
        {
          code: 'too_big',
          path: ['name'],
          message: 'String must contain at most 100 character(s)',
        },
      ])
    })

    test('should return 400 for long description', async () => {
      // given
      await setupBasicTestData()
      const invalidForm = { ...validCourseForm, descr: 'A'.repeat(5001) }

      // when
      const res = await makeAuthenticatedRequest(invalidForm)

      // then
      expect(res.status, 'Status check').toBe(400)
      await expect(res).toBeError(400, [
        {
          code: 'too_big',
          path: ['descr'],
          message: 'String must contain at most 5000 character(s)',
        },
      ])
    })

    test('should return 400 for invalid academy ID', async () => {
      // given
      await setupBasicTestData()
      const invalidForm = { ...validCourseForm, academyId: 'invalid-uuid' as unknown as UUID }

      // when
      const res = await makeAuthenticatedRequest(invalidForm)

      // then
      expect(res.status, 'Status check').toBe(400)
      await expect(res).toBeError(400, [
        {
          code: 'custom',
          path: ['academyId'],
          message: 'Must be a UUID v4 string',
        },
      ])
    })

    test('should return 400 for missing academy ID', async () => {
      // given
      await setupBasicTestData()
      const invalidForm = { ...validCourseForm, academyId: undefined } as unknown as AddCourseForm

      // when
      const res = await makeAuthenticatedRequest(invalidForm)

      // then
      expect(res.status, 'Status check').toBe(400)
      await expect(res).toBeError(400, [
        {
          code: 'custom',
          path: ['academyId'],
          message: 'Must be a UUID v4 string',
        },
      ])
    })

    test('should return 400 for malformed JSON', async () => {
      // given
      await setupBasicTestData()
      const token = await createAccessToken(env.JWT_SECRET_KEY, testUser.id)

      // when
      const res = await app.request(
        '/api/courses',
        {
          method: 'POST',
          body: 'invalid-json',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
            [PROFILE_HEADER]: testProfile.id,
          },
        },
        {},
      )

      // then
      expect(res.status, 'Status check').toBe(400)
    })
  })

  describe('Business logic errors', () => {
    test('should return 422 when course with same name already exists', async () => {
      // given
      await setupBasicTestData()

      // Create an existing course with the same name
      await db.insert(courseTable).values({
        id: crypto.randomUUID(),
        name: validCourseForm.name,
        academyId: testAcademy.id,
        creationRemarks: 'test',
      })

      // when
      const res = await makeAuthenticatedRequest(validCourseForm)

      // then
      expect(res.status, 'Status check').toBe(422)
      await expect(res).toBeError(422, [
        {
          code: 'custom',
          path: [],
          message: `Course with name ${validCourseForm.name} already exists.`,
        },
      ])
    })

    test('should return 422 when academy has reached maximum courses limit', async () => {
      // given
      await setupBasicTestData()

      // Create maximum number of courses
      const courseInserts = Array.from({ length: MAX_COURSES_PER_ACADEMY }, (_, i) => ({
        id: crypto.randomUUID(),
        name: `Course ${i + 1}`,
        academyId: testAcademy.id,
        creationRemarks: 'test',
      }))

      await db.insert(courseTable).values(courseInserts)

      // when
      const res = await makeAuthenticatedRequest(validCourseForm)

      // then
      expect(res.status, 'Status check').toBe(422)
      await expect(res).toBeError(422, [
        {
          code: 'custom',
          path: [],
          message: `Academy already has ${MAX_COURSES_PER_ACADEMY} courses.`,
        },
      ])
    })

    test('should allow creating course when under the limit', async () => {
      // given
      await setupBasicTestData()

      // Create courses just under the limit
      const courseInserts = Array.from({ length: MAX_COURSES_PER_ACADEMY - 1 }, (_, i) => ({
        id: crypto.randomUUID(),
        name: `Course ${i + 1}`,
        academyId: testAcademy.id,
        creationRemarks: 'test',
      }))

      await db.insert(courseTable).values(courseInserts)

      // when
      const res = await makeAuthenticatedRequest(validCourseForm)

      // then
      expect(res.status, 'Status check').toBe(201)
      const responseBody = await res.json()
      await assertValidCourseResponse(responseBody, validCourseForm)
    })
  })

  describe('Edge cases', () => {
    test('should trim whitespace from course name', async () => {
      // given
      await setupBasicTestData()
      const formWithWhitespace = {
        ...validCourseForm,
        name: '  Mathematics Class 10  ',
      }

      // when
      const res = await makeAuthenticatedRequest(formWithWhitespace)

      // then
      expect(res.status, 'Status check').toBe(201)
      const responseBody = await res.json()

      const courses = await db.select().from(courseTable).where(eq(courseTable.id, responseBody.id))
      expect(courses[0].name).toBe('Mathematics Class 10')
    })

    test('should trim whitespace from description', async () => {
      // given
      await setupBasicTestData()
      const formWithWhitespace = {
        ...validCourseForm,
        descr: '  Advanced mathematics course  ',
      }

      // when
      const res = await makeAuthenticatedRequest(formWithWhitespace)

      // then
      expect(res.status, 'Status check').toBe(201)
      const responseBody = await res.json()

      const courses = await db.select().from(courseTable).where(eq(courseTable.id, responseBody.id))
      expect(courses[0].descr).toBe('<p>Advanced mathematics course</p>\n')
    })

    test('should handle empty description after trimming', async () => {
      // given
      await setupBasicTestData()
      const formWithEmptyDescription = {
        ...validCourseForm,
        descr: '   ',
      }

      // when
      const res = await makeAuthenticatedRequest(formWithEmptyDescription)

      // then
      expect(res.status, 'Status check').toBe(201)
      const responseBody = await res.json()

      const courses = await db.select().from(courseTable).where(eq(courseTable.id, responseBody.id))
      expect(courses[0].descr).toBeNull()
    })

    test('should allow course names that differ only in case', async () => {
      // given
      await setupBasicTestData()

      // Create a course with lowercase name
      await db.insert(courseTable).values({
        id: crypto.randomUUID(),
        name: 'mathematics class 10',
        academyId: testAcademy.id,
        creationRemarks: 'test',
      })

      const formWithUpperCase = {
        ...validCourseForm,
        name: 'MATHEMATICS CLASS 10',
      }

      // when
      const res = await makeAuthenticatedRequest(formWithUpperCase)

      // then
      expect(res.status, 'Status check').toBe(201)
      const responseBody = await res.json()
      await assertValidCourseResponse(responseBody, formWithUpperCase)
    })
  })
})

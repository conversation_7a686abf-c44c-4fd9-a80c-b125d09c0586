import { pino } from 'pino'

import { addCommand, Command } from '@/server/common/command/processCommands'
import { env } from '@/server/common/env.server'
import { mail } from '@/server/common/mail/mail'
import { Email } from '@/shared/common/common-utils.shared'

export const MAIL_PLEASE_LOGIN_ASAP_COMMAND = 'mailPleaseLoginAsap'

export type MailPleaseLoginAsapCommandData = {
  userEmail: Email
  userName: string
  language: string
}

const executeCommand = async (log: pino.Logger, command: Command<MailPleaseLoginAsapCommandData>) => {
  const data = command.data
  await mail(log, false, 'pleaseLoginAsap', data.language, {
    to: data.userEmail,
    data: {
      userName: data.userName,
      homeUrl: env.HOME_URL,
    },
  })
}

addCommand(MAIL_PLEASE_LOGIN_ASAP_COMMAND, executeCommand)

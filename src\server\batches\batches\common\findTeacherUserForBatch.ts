import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { db } from '@/server/db/db'
import { batchTable } from '@/server/db/schema/batch-schema'
import { profileTable } from '@/server/db/schema/profile-schema'
import { userTable } from '@/server/db/schema/user-schema'
import { withLog } from '@/shared/common/withLog.shared'

export const findTeacherUserForBatch = (log: pino.Logger, batchId: UUID) =>
  withLog(log, 'findTeacherUserForBatch', () =>
    db
      .select({
        userId: userTable.id,
      })
      .from(batchTable)
      .innerJoin(profileTable, eq(batchTable.teacherId, profileTable.id))
      .innerJoin(userTable, eq(profileTable.userId, userTable.id))
      .where(eq(batchTable.id, batchId)),
  )

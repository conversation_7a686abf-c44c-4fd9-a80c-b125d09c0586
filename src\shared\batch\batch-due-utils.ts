import { DateTime } from 'luxon'

import { plusCycles } from '../common/payment-utils/payment-utils.shared'

import { BillingCycle } from './batch-utils.shared'

type Batch = {
  startDate: string
  timezone: string
  billingCycle: BillingCycle
  cycleCount: number
}

type BatchStudent = {
  firstJoinedAt: Date
  paidTillCycle: number
}

/**
 * Returns a DateTime with time set to 00:00:00 in the **batch's timezone**.
 */
export const getNextPaymentDueOn = (batch: Batch, batchStudent: BatchStudent) => {
  if (batchStudent.paidTillCycle === batch.cycleCount) {
    return null // fully paid
  }
  // start of next billing cycle after the paid cycles
  const nextDueOn = plusCycles(
    DateTime.fromISO(batch.startDate, { zone: batch.timezone }),
    batch.billingCycle,
    batchStudent.paidTillCycle,
  )
  // if the student joined after the start of the billing cycle, use the join date as the due date
  const joinedOn = DateTime.fromJSDate(batchStudent.firstJoinedAt, { zone: batch.timezone }).startOf('day')
  return joinedOn > nextDueOn ? joinedOn : nextDueOn
}

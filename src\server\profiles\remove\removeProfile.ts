import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { profileTable } from '@/server/db/schema/profile-schema'
import { withLog } from '@/shared/common/withLog.shared'

const removeProfileDaf = (log: pino.Logger, userId: UUID, profileId: UUID) =>
  withLog(log, 'removeProfileDaf', () =>
    db.delete(profileTable).where(and(eq(profileTable.id, profileId), eq(profileTable.userId, userId))),
  )

export const removeProfile = async (c: Ctx, profileId: UUID) => {
  ensureUser(c.user, { ignoreEmailVerification: true, ignoreMobileVerification: true })

  // Remove profile
  const result = await removeProfileDaf(c.log, c.user.id, profileId)

  // Ensure profile was found and removed
  ensure(result.count > 0, {
    statusCode: 404,
    issueMessage: 'Profile not found or does not belong to you.',
    logMessage: `User ${c.user.id} attempted to remove profile ${profileId} but it was not found or does not belong to them.`,
  })
}

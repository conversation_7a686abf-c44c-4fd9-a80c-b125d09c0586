import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { env } from '@/server/common/env.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { createEmailVerificationToken } from '@/server/common/mail/email-verification-token-utils.server'
import { mail } from '@/server/common/mail/mail'
import { db } from '@/server/db/db'
import { academyTable } from '@/server/db/schema/academy-schema'
import { academyPath } from '@/shared/academies/academy-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { ensureGoodStaff } from '../common/ensureGoodStaff'

const findAcademyDaf = (log: pino.Logger, academyId: UUID) =>
  withLog(log, 'findAcademyDaf', () =>
    db.query.academyTable.findFirst({
      columns: {
        id: true,
        name: true,
        email: true,
        emailVerified: true,
      },
      where: eq(academyTable.id, academyId),
    }),
  )

export const sendAcademyVerificationMail = async (c: Ctx, academyId: UUID) => {
  await ensureGoodStaff(c, academyId)

  const academy = await findAcademyDaf(c.log, academyId)
  ensureExists(academy, academyId, 'Academy', 422)

  ensure(!academy.emailVerified, {
    logMessage: `Email already verified for academy: ${academyId}`,
    issueMessage: 'Academy email is already verified',
    statusCode: 422,
  })

  const token = await createEmailVerificationToken(env.JWT_SECRET_KEY, academy.email)
  const verificationUrl = `${env.HOME_URL}${academyPath(academy)}/verify-email?token=${token}`

  await withLog(c.log, 'sendAcademyVerificationMail', () =>
    mail(c.log, true, 'academyEmailVerification', c.user!.language, {
      to: academy.email,
      data: {
        academyName: academy.name,
        verificationUrl,
      },
    }),
  )
}

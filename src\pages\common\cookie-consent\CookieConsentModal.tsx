import { Transition } from '@headlessui/react'
import { Fragment, useRef, useState, useEffect } from 'react'
import { navigate } from 'vike/client/router'

import { acceptCookies, useCookieConsent } from './cookie-consent-store'

export const CookieConsentModal = () => {
  const { visible } = useCookieConsent()
  const [position, setPosition] = useState({ x: 20, y: window.innerHeight - 200 })
  const [isDragging, setIsDragging] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const dragRef = useRef({ startX: 0, startY: 0 })

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth <= 640)
    }

    checkIfMobile()
    window.addEventListener('resize', checkIfMobile)

    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  useEffect(() => {
    if (isMobile) {
      // For mobile, position at bottom center
      setPosition({
        x: window.innerWidth / 2 - 160,
        y: window.innerHeight - 220,
      })
    }
  }, [isMobile])

  const handleMouseDown = (e: React.MouseEvent) => {
    if (isMobile) return
    setIsDragging(true)
    dragRef.current = {
      startX: e.clientX - position.x,
      startY: e.clientY - position.y,
    }
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return

    setPosition({
      x: e.clientX - dragRef.current.startX,
      y: e.clientY - dragRef.current.startY,
    })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  if (!visible) return null

  return (
    <Transition
      show={visible}
      as={Fragment}
      enter="transition ease-out duration-300"
      enterFrom="opacity-0 scale-95"
      enterTo="opacity-100 scale-100"
      leave="transition ease-in duration-200"
      leaveFrom="opacity-100 scale-100"
      leaveTo="opacity-0 scale-95"
    >
      <div
        style={{
          position: 'fixed',
          left: position.x,
          top: position.y,
          zIndex: 50,
          cursor:
            isDragging ? 'grabbing'
            : isMobile ? 'default'
            : 'grab',
          width: isMobile ? '320px' : '24rem',
          boxSizing: 'border-box',
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        className="select-none bg-white shadow-lg sm:rounded-lg overflow-hidden"
      >
        <div className="p-4">
          <h3 className="text-base font-semibold text-gray-900">Cookie Consent</h3>
          <div className="mt-2 text-sm text-gray-500">
            <p className="break-words">
              We use cookies to enhance your browsing experience and analyze our traffic. By continuing to use this
              website, you agree to our use of cookies.{' '}
              <button
                onClick={() => navigate('/legal-agreements/privacy-policy')}
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                Learn more
              </button>
            </p>
          </div>
          <div className="mt-5">
            <button
              onClick={acceptCookies}
              style={{ width: '100%' }}
              className="rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
              Got it
            </button>
          </div>
        </div>
      </div>
    </Transition>
  )
}

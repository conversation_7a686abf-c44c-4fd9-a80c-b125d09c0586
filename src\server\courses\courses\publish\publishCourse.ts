import { UUID } from 'crypto'

import { and, eq, isNull } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { db } from '@/server/db/db'
import { courseTable } from '@/server/db/schema/course-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { ensureCanUpdateCourse } from '../common/ensureCanUpdateCourse'

const publishCourseDaf = (log: pino.Logger, courseId: UUID, profileId: UUID) =>
  withLog(log, 'publishCourseDaf', () => {
    const now = utc().toJSDate()
    return db
      .update(courseTable)
      .set({
        publishedAt: now,
        updatedAt: now,
        updateRemarks: `${profileId} profile published this course`,
      })
      .where(and(eq(courseTable.id, courseId), isNull(courseTable.publishedAt)))
  })

export const publishCourse = async (c: Ctx, courseId: UUID) => {
  await ensureCanUpdateCourse(c, courseId)
  await publishCourseDaf(c.log, courseId, c.profile!.id)
}

import { UUID } from 'crypto'

import { Suspense, useEffect, useState, useTransition } from 'react'

import { ComboBox } from '@/pages/common/form/ComboBox'
import {
  ACADEMY_DESCR_MAX_LEN,
  ACADEMY_NAME_MAX_LEN,
  EditAcademyForm,
  ACADEMY_PINCODE_LEN,
  ACADEMY_AREA_MAX_LEN,
  ACADEMY_ADDRESS_LINE_MAX_LEN,
  GSTIN_LEN,
} from '@/shared/academies/academy-utils.shared'
import { Currency, CURRENCIES, UNKNOWN_UUID } from '@/shared/common/common-utils.shared'
import { type OmniError } from '@/shared/common/error-utils.shared'
import { EMAIL_MAX_LEN } from '@/shared/users/user-utils.shared'
import { MobileEditor } from '@ui/common/form/MobileEditor'
import { NameAndDescrEditor } from '@ui/common/form/NameAndDescrEditor'
import { PageSection } from '@ui/common/form/page-layout'
import { ShowData } from '@ui/common/ShowData'
import { ShowErrors } from '@ui/common/ShowErrors'
import {
  useCountriesSuspenseQuery,
  useDistrictsSuspenseQuery,
  useDistrictSuspenseQuery,
  useStatesSuspenseQuery,
  useStateSuspenseQuery,
} from '@ui/masters/common/master-queries'

type AcademyCommonEditorProps = {
  formData: Required<EditAcademyForm>
  setFormData: (formData: Required<EditAcademyForm>) => void
  omniError: OmniError | undefined
}

export const AcademyCommonEditor = ({ formData, setFormData, omniError }: AcademyCommonEditorProps) => (
  <>
    <NameAndDescrEditor
      name={formData.name}
      descr={formData.descr}
      omniError={omniError}
      onNameChange={(value) => setFormData({ ...formData, name: value })}
      onDescrChange={(value) => setFormData({ ...formData, descr: value })}
      nameMaxLen={ACADEMY_NAME_MAX_LEN}
      descrMaxLen={ACADEMY_DESCR_MAX_LEN}
    />
    <PageSection title="Contact Information" description="Please fill in the support contact information">
      <div className="space-y-4 mt-4">
        <div>
          <label htmlFor="email" className="form-label">
            Email
          </label>
          <div className="mt-2">
            <input
              id="email"
              value={formData.email}
              type="text"
              required
              className="form-input max-w-lg"
              placeholder="Email"
              aria-describedby="email-description"
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            />
          </div>
          <p id="email-description" className="form-descr">
            Maximum {EMAIL_MAX_LEN} characters.
          </p>
          <ShowErrors error={omniError} path={['email']} />
        </div>

        <div className="mt-5">
          <label htmlFor="mobile-input" className="form-label mb-2">
            WhatsApp Number
          </label>
          <MobileEditor
            mobileCountryCode={formData.mobileCountryCode}
            mobile={formData.mobile}
            setMobileCountryCode={(value) => setFormData({ ...formData, mobileCountryCode: value })}
            setMobile={(value) => setFormData({ ...formData, mobile: value })}
            omniError={omniError}
          />
        </div>
      </div>
    </PageSection>
    <PageSection title="Payment Information" description="Please fill in the payment information">
      <div className="space-y-4 mt-4">
        <div>
          <label htmlFor="currency" className="form-label">
            Currency
          </label>
          <div className="mt-2">
            <select
              id="currency"
              value={formData.currency}
              onChange={(e) => setFormData({ ...formData, currency: e.target.value as Currency })}
            >
              {CURRENCIES.map((currency) => (
                <option key={currency} value={currency}>
                  {currency}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div>
          <label htmlFor="upi-id" className="form-label">
            UPI ID
          </label>
          <div className="mt-2">
            <input
              id="upi-id"
              value={formData.upiId}
              type="text"
              required
              className="form-input max-w-lg"
              placeholder="UPI ID"
              aria-describedby="upi-id-description"
              onChange={(e) => setFormData({ ...formData, upiId: e.target.value })}
            />
          </div>
          <p id="upi-id-description" className="form-descr">
            Enter UPI ID for receiving payments.
          </p>
          <ShowErrors error={omniError} path={['upiId']} />
        </div>
      </div>
    </PageSection>
    <Suspense
      fallback={
        <PageSection title="Address" description="Please fill in the address">
          <div className="space-y-4 mt-4">
            <p>Loading address details...</p>
          </div>
        </PageSection>
      }
    >
      <AddressEditor formData={formData} setFormData={setFormData} omniError={omniError} />
    </Suspense>
    <PageSection title="Legal Information" description="Please fill in the legal information">
      <div className="space-y-4 mt-4">
        <div>
          <label htmlFor="address-line-2" className="form-label">
            Trade Legal Name
          </label>
          <div className="mt-2">
            <input
              id="trade-name"
              value={formData.tradeName}
              type="text"
              className="form-input max-w-lg"
              placeholder="Trade Legal Name"
              aria-describedby="trade-name-description"
              onChange={(e) => setFormData({ ...formData, tradeName: e.target.value })}
            />
          </div>
          <p id="trade-name-description" className="form-descr">
            Maximum {ACADEMY_NAME_MAX_LEN} characters.
          </p>
          <ShowErrors error={omniError} path={['tradeName']} />
        </div>
        <div>
          <label htmlFor="gstin" className="form-label">
            GSTIN
          </label>
          <div className="mt-2">
            <input
              id="gstin"
              value={formData.gstin}
              type="text"
              className="form-input max-w-lg"
              placeholder="GSTIN"
              aria-describedby="gstin-description"
              onChange={(e) => setFormData({ ...formData, gstin: e.target.value })}
            />
          </div>
          <p id="gstin-description" className="form-descr">
            {GSTIN_LEN} digit GSTIN.
          </p>
          <ShowErrors error={omniError} path={['gstin']} />
        </div>
      </div>
    </PageSection>
  </>
)

const AddressEditor = ({ formData, setFormData, omniError }: AcademyCommonEditorProps) => {
  const { data: countries, error: countriesError } = useCountriesSuspenseQuery()
  const [countryCode, setCountryCode] = useState('IN')
  const [isPendingCountry, startTransitionCountry] = useTransition()

  const { data: states, error: statesError } = useStatesSuspenseQuery(countryCode)
  const [stateId, setStateId] = useState(states?.[0]?.id ?? UNKNOWN_UUID)
  const [isPendingState, startTransitionState] = useTransition()

  const { data: state, error: stateError } = useStateSuspenseQuery(stateId)

  const { data: districts, error: districtsError } = useDistrictsSuspenseQuery(stateId)
  const [isPendingDistrict, startTransitionDistrict] = useTransition()

  const { data: district, error: districtError } = useDistrictSuspenseQuery(formData.districtId)

  useEffect(() => {
    if (district && district.state.id !== stateId) {
      startTransitionState(() => {
        setStateId(district.state.id)
      })
    }
  }, [district, stateId])

  useEffect(() => {
    if (state && state.country.code !== countryCode) {
      startTransitionCountry(() => {
        setCountryCode(state.country.code)
      })
    }
  }, [state, countryCode])

  useEffect(() => {
    if (states && states.findIndex((s) => s.id === stateId) === -1 && states.length > 0) {
      startTransitionState(() => {
        setStateId(states[0].id)
      })
    }
  }, [states, stateId])

  useEffect(() => {
    if (districts && districts.findIndex((d) => d.id === formData.districtId) === -1 && districts.length > 0) {
      startTransitionDistrict(() => {
        setFormData({ ...formData, districtId: districts[0].id })
      })
    }
  }, [districts, formData, setFormData])

  // Transform countries to match ComboBox interface
  const countryItems =
    countries?.map((country) => ({
      id: country.code,
      name: country.name,
    })) ?? []

  // Create wrapper functions to fix type issues
  const handleStateChange = (itemId: string) => {
    startTransitionState(() => {
      setStateId(itemId as UUID)
    })
  }

  const handleDistrictChange = (itemId: string) => {
    startTransitionDistrict(() => {
      setFormData({ ...formData, districtId: itemId as UUID })
    })
  }

  const handleCountryChange = (itemId: string) => {
    startTransitionCountry(() => {
      setCountryCode(itemId)
    })
  }

  const isTransitioning = isPendingCountry || isPendingState || isPendingDistrict

  return (
    <PageSection title="Address" description="Please fill in the address">
      <ShowData error={countriesError || statesError || stateError || districtsError || districtError}>
        <div className={`space-y-4 mt-4 ${isTransitioning ? 'opacity-50 cursor-wait' : ''}`}>
          <div>
            <ComboBox
              items={countryItems}
              selectedItemId={countryCode}
              setSelectedItemId={handleCountryChange}
              label="Country"
            />
            <ShowErrors error={omniError} path={['countryCode']} />
          </div>

          <div>
            <ComboBox
              items={states ?? []}
              selectedItemId={stateId}
              setSelectedItemId={handleStateChange}
              label="State"
            />
            <ShowErrors error={omniError} path={['stateId']} />
          </div>

          <div>
            <ComboBox
              items={districts ?? []}
              selectedItemId={formData.districtId}
              setSelectedItemId={handleDistrictChange}
              label="District"
            />
            <ShowErrors error={omniError} path={['districtId']} />
          </div>

          <div>
            <label htmlFor="pincode" className="form-label">
              Pincode
            </label>
            <div className="mt-2">
              <input
                id="pincode"
                value={formData.pincode}
                type="text"
                required
                className="form-input max-w-lg"
                placeholder="Pincode"
                aria-describedby="pincode-description"
                onChange={(e) => setFormData({ ...formData, pincode: e.target.value })}
              />
            </div>
            <p id="pincode-description" className="form-descr">
              {ACADEMY_PINCODE_LEN} digit pincode.
            </p>
            <ShowErrors error={omniError} path={['pincode']} />
          </div>

          <div>
            <label htmlFor="area" className="form-label">
              Area
            </label>
            <div className="mt-2">
              <input
                id="area"
                value={formData.area}
                type="text"
                required
                className="form-input max-w-lg"
                placeholder="Area"
                aria-describedby="area-description"
                onChange={(e) => setFormData({ ...formData, area: e.target.value })}
              />
            </div>
            <p id="area-description" className="form-descr">
              Maximum {ACADEMY_AREA_MAX_LEN} characters.
            </p>
            <ShowErrors error={omniError} path={['area']} />
          </div>

          {/* Address Line 1 */}
          <div>
            <label htmlFor="address-line-1" className="form-label">
              Address Line 1
            </label>
            <div className="mt-2">
              <input
                id="address-line-1"
                value={formData.addressLine1 ?? ''}
                type="text"
                className="form-input max-w-lg"
                placeholder="Address Line 1"
                aria-describedby="address-line-1-description"
                onChange={(e) => setFormData({ ...formData, addressLine1: e.target.value })}
              />
            </div>
            <p id="address-line-1-description" className="form-descr">
              Maximum {ACADEMY_ADDRESS_LINE_MAX_LEN} characters.
            </p>
            <ShowErrors error={omniError} path={['addressLine1']} />
          </div>

          {/* Address Line 2 */}
          <div>
            <label htmlFor="address-line-2" className="form-label">
              Address Line 2
            </label>
            <div className="mt-2">
              <input
                id="address-line-2"
                value={formData.addressLine2 ?? ''}
                type="text"
                className="form-input max-w-lg"
                placeholder="Address Line 2"
                aria-describedby="address-line-2-description"
                onChange={(e) => setFormData({ ...formData, addressLine2: e.target.value })}
              />
            </div>
            <p id="address-line-2-description" className="form-descr">
              Maximum {ACADEMY_ADDRESS_LINE_MAX_LEN} characters.
            </p>
            <ShowErrors error={omniError} path={['addressLine2']} />
          </div>
        </div>
      </ShowData>
    </PageSection>
  )
}

import { navigate } from 'vike/client/router'

import { showNotification } from '@/pages/common/notification/notification-store'
import { ShowData } from '@/pages/common/ShowData'
import { getErrorMessage } from '@/pages/common/utils/error-utils.ui'
import { CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { coursePath } from '@/shared/course/course-utils.shared'
import { profileOk } from '@/shared/profiles/profile-check-utils.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'
import { useMyAcademyQuery } from '@ui/academies/common/academy-queries'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

import { usePublishCourseMutation } from '../common/queries/course-queries'

export const CourseActions = ({
  course,
  setShowTagEditor,
}: {
  course: NonNullable<CourseWithoutDescr>
  setShowTagEditor: (show: boolean) => void
}) => {
  const { currentProfile, profileError } = useCurrentProfile()
  const { data: myAcademy, error: myAcademyError } = useMyAcademyQuery()

  const publishCourseMutation = usePublishCourseMutation(course.id)

  const handlePublish = async () => {
    try {
      await publishCourseMutation.mutateAsync()
      showNotification({
        heading: 'Course published',
        type: 'success',
      })
    } catch (error) {
      showNotification({
        heading: getErrorMessage(error),
        type: 'error',
      })
    }
  }

  const canEdit = myAcademy?.id === course.academyId && profileOk(currentProfile, { roleAnyOf: ACADEMY_STAFF })
  const canPublish = course.publishedAt === null && profileOk(currentProfile, { roleAnyOf: ['principal'] })

  return (
    <ShowData error={profileError || myAcademyError}>
      {(canEdit || canPublish) && (
        <div className="flex flex-row sm:flex-col gap-x-2 text-sm link underline items-start mt-2">
          {canPublish && (
            <button type="button" disabled={publishCourseMutation.isPending} onClick={handlePublish}>
              Publish
            </button>
          )}
          {canEdit && (
            <>
              <button type="button" onClick={() => navigate(`/courses/${course.id}/edit`)}>
                Edit
              </button>
              <button type="button" className="whitespace-nowrap" onClick={() => setShowTagEditor(true)}>
                Edit Tags
              </button>
              <button type="button" onClick={() => navigate(`${coursePath(course)}/batches/add`)}>
                Add Batch
              </button>
            </>
          )}
        </div>
      )}
    </ShowData>
  )
}

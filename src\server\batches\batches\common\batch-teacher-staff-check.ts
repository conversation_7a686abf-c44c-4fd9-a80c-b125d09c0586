import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { db } from '@/server/db/db'
import { profileTable, userTable, academyStaffTable, courseTable, batchTable } from '@/server/db/schema'
import { profileToCheckColumns } from '@/server/profiles/common/profile-check.server'
import { withLog } from '@/shared/common/withLog.shared'

export const findBatchStaffByTeacherIdDaf = (log: Logger, teacherId: UUID, profileId: UUID) => {
  return withLog(log, 'findBatchStaffByTeacherIdDaf', async () => {
    return db
      .select(profileToCheckColumns)
      .from(batchTable)
      .innerJoin(courseTable, eq(courseTable.id, batchTable.courseId))
      .innerJoin(academyStaffTable, eq(academyStaffTable.academyId, courseTable.academyId))
      .innerJoin(profileTable, eq(profileTable.id, academyStaffTable.profileId))
      .innerJoin(userTable, eq(userTable.id, profileTable.userId))
      .where(and(eq(batchTable.teacherId, teacherId), eq(academyStaffTable.profileId, profileId)))
      .then((rows) => rows[0])
  })
}

import { type Profile } from '@/server/profiles/get/getProfile.server'

import { ProfileRole } from './ProfileRole'
import { RoleIcon } from './RoleIcon'
import { SuspendedBadge } from './SuspendedBadge'
import { UnapprovedBadge } from './UnapprovedBadge'

export const ProfileHeader = ({ profile }: { profile?: Profile }) => {
  if (!profile) return null

  return (
    <div className="py-2">
      <div className="flex items-center gap-x-3">
        <h1 className="page-title">{profile.displayName}</h1>
        {/* Role icon */}
        <RoleIcon role={profile.role} type="solid20" />
      </div>
      <div className="flex items-center gap-x-2">
        <div>
          <ProfileRole role={profile.role} />
        </div>
        <div className="flex items-center gap-x-2">
          <div>
            {/* Status badges */}
            <UnapprovedBadge approvedAt={profile.approvedAt} />
            <SuspendedBadge suspendedAt={profile.suspendedAt} />
          </div>
        </div>
      </div>
    </div>
  )
}

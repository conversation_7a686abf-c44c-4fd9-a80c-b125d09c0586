import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'
import { z } from 'zod'

import { HonoVars } from '@/server/common/hono-utils.server'
import { $CountryCode } from '@/shared/masters/master-utils.shared'

import { listStates } from './listStates'

export const listStatesHandler = new Hono<HonoVars>().get(
  '/',
  zValidator(
    'query',
    z.object({
      countryCode: $CountryCode,
    }),
  ),
  async (c) => {
    const query = c.req.valid('query')
    const states = await listStates(c.get('log'), query.countryCode)
    return c.json({ rows: states }, 200)
  },
)

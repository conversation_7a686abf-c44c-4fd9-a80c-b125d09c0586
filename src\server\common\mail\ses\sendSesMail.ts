import { SESClient, SendEmailCommand, SendEmailCommandInput } from '@aws-sdk/client-ses'
import { Logger } from 'pino'

import { env } from '@/server/common/env.server'
import { Email } from '@/shared/common/common-utils.shared'

import { SENDER_EMAIL_WITH_NAME } from '../mail-utils'

const sesClient = new SESClient({
  region: 'ap-south-1',
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY,
    secretAccessKey: env.AWS_SECRET_KEY,
  },
})

export const sendSesMail = async (log: Logger, to: Email, subject: string, body: string) => {
  const params: SendEmailCommandInput = {
    Source: SENDER_EMAIL_WITH_NAME, // Verified sender email
    Destination: {
      ToAddresses: [to],
    },
    Message: {
      Subject: {
        Data: subject,
        Charset: 'UTF-8',
      },
      Body: {
        Html: {
          Data: body,
          Charset: 'UTF-8',
        },
      },
    },
  }

  try {
    const command = new SendEmailCommand(params)
    const response = await sesClient.send(command)
    log.info({ ses: { to, subject, response } }, `Email sent to ${to}: ${subject}`)
    return response
  } catch (error) {
    log.error(error)
    throw error
  }
}

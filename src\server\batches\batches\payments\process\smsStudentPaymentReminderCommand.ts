import { UUID } from 'crypto'

import { aliasedTable, eq } from 'drizzle-orm'
import { pino } from 'pino'

import { addCommand, Command } from '@/server/common/command/processCommands'
import { env } from '@/server/common/env.server'
import { sms } from '@/server/common/sms/sms'
import { db } from '@/server/db/db'
import { academyTable } from '@/server/db/schema/academy-schema'
import { batchStudentTable, batchTable } from '@/server/db/schema/batch-schema'
import { courseTable } from '@/server/db/schema/course-schema'
import { countryTable } from '@/server/db/schema/master-schema'
import { profileTable } from '@/server/db/schema/profile-schema'
import { userTable } from '@/server/db/schema/user-schema'
import { batchPath } from '@/shared/batch/batch-utils.shared'
import { phoneNumber } from '@/shared/common/common-utils.shared'
import { formatDate } from '@/shared/common/date-utils.shared'
import { PaymentReminderType } from '@/shared/common/payment-utils/payment-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

export const SMS_STUDENT_PAYMENT_REMINDER_COMMAND = 'smsStudentPaymentReminder'

export type SmsStudentPaymentReminderCommandData = {
  batchStudentId: UUID
  reminderType: PaymentReminderType
  dueDate: string
}

const studentCountryTable = aliasedTable(countryTable, 'studentCountry')
const academyCountryTable = aliasedTable(countryTable, 'academyCountry')

const getStudentPaymentReminderSmsDataDaf = async (log: pino.Logger, batchStudentId: UUID) =>
  withLog(log, 'getStudentPaymentReminderSmsDataDaf', () =>
    db
      .select({
        studentMobile: userTable.mobile,
        studentMobilePrefix: studentCountryTable.phonePrefix,
        studentName: profileTable.displayName,
        language: userTable.language,
        courseId: batchTable.courseId,
        courseName: courseTable.name,
        batchId: batchTable.id,
        currency: academyTable.currency,
        fee: batchTable.fee,
        graceDays: batchTable.graceDays,
        academyName: academyTable.name,
        supportMobile: academyTable.mobile,
        supportMobilePrefix: academyCountryTable.phonePrefix,
      })
      .from(batchStudentTable)
      .innerJoin(profileTable, eq(batchStudentTable.studentId, profileTable.id))
      .innerJoin(userTable, eq(profileTable.userId, userTable.id))
      .innerJoin(studentCountryTable, eq(userTable.mobileCountryCode, studentCountryTable.code))
      .innerJoin(batchTable, eq(batchStudentTable.batchId, batchTable.id))
      .innerJoin(courseTable, eq(batchTable.courseId, courseTable.id))
      .innerJoin(academyTable, eq(courseTable.academyId, academyTable.id))
      .innerJoin(academyCountryTable, eq(academyTable.mobileCountryCode, academyCountryTable.code))
      .where(eq(batchStudentTable.id, batchStudentId)),
  )

type SmsData = Awaited<ReturnType<typeof getStudentPaymentReminderSmsDataDaf>>[number]
const executeCommand = async (log: pino.Logger, command: Command<SmsStudentPaymentReminderCommandData>) => {
  const data = command.data
  const [smsData] = await getStudentPaymentReminderSmsDataDaf(log, data.batchStudentId)
  await sms(log, false, templateName(data.reminderType), smsData.language, {
    to: phoneNumber(smsData.studentMobilePrefix, smsData.studentMobile),
    ...getParams(data.reminderType, smsData, data.dueDate),
  })
}

const templateName = (reminderType: PaymentReminderType) => {
  switch (reminderType) {
    case 'blocked':
      return 'tl_student_payment_reminder_blocked'
    case 'gentle':
      return 'tl_student_payment_reminder_gentle'
    case 'last':
      return 'tl_student_payment_reminder_last'
    case 'prudent':
      return 'tl_student_payment_reminder_prudent'
    case 'urgent':
      return 'tl_student_payment_reminder_urgent'
    default:
      throw new Error(`Invalid reminder type: ${reminderType}`)
  }
}

const getParams = (reminderType: PaymentReminderType, smsData: SmsData, dueDate: string) => {
  const headerParams: Record<string, string> = {}
  const bodyParams: Record<string, string> = {}

  if (reminderType === 'gentle') {
    headerParams.academy_name = smsData.academyName
    bodyParams.due_date = formatDate(dueDate, { language: smsData.language })
  }

  if (['prudent', 'last'].includes(reminderType)) {
    bodyParams.grace_days = smsData.graceDays.toString()
  }

  bodyParams.student_name = smsData.studentName
  bodyParams.currency = smsData.currency
  bodyParams.fee = smsData.fee.toString()
  bodyParams.course_name = smsData.courseName
  bodyParams.batch_url = `${env.HOME_URL}${batchPath({ id: smsData.courseId, name: smsData.courseName }, smsData.batchId)}`
  bodyParams.support_mobile = phoneNumber(smsData.supportMobilePrefix, smsData.supportMobile)

  return {
    headerParams,
    bodyParams,
  }
}

addCommand(SMS_STUDENT_PAYMENT_REMINDER_COMMAND, executeCommand)

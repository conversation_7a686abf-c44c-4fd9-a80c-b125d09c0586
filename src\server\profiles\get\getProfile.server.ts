import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { ensureExists } from '@/server/common/error/ensureExists'
import { db } from '@/server/db/db'
import { profileTable } from '@/server/db/schema/profile-schema'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'

const findProfileByIdDaf = (log: Logger, profileId: UUID) =>
  withLog(log, 'findProfileByIdDaf', () =>
    db.query.profileTable.findFirst({
      columns: {
        id: true,
        displayName: true,
        descr: true,
        role: true,
        submittedForApprovalAt: true,
        approvedAt: true,
        suspendedAt: true,
        userId: true,
        serviceProviderId: true,
      },
      with: {
        user: {
          columns: {
            googlePictureUrl: true,
          },
        },
      },
      where: eq(profileTable.id, profileId),
    }),
  )

/**
 * Get a profile by its id
 * Returns basic profile information including approval and suspension status
 */
export const getProfile = async (log: Logger, profileId: UUID) => {
  const profile = await findProfileByIdDaf(log, profileId)

  ensureExists(profile, profileId, 'Profile')
  return profile
}

export type GetProfile = typeof getProfile
export type Profile = Awaited<ReturnType<GetProfile>>
setQuery('getProfile', getProfile)

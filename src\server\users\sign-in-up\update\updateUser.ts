import { randomUUID, UUID } from 'crypto'

import { and, eq, notInArray, sql } from 'drizzle-orm'
import { Logger } from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { encrypt } from '@/server/common/encryption-utils.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db, Transaction } from '@/server/db/db'
import { userSnapshotTable, userTable, userTncAcceptedTable, userTncSignTable } from '@/server/db/schema/user-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { UserForm } from '@/shared/users/user-utils.shared'

import { ApplicableUserTnCs } from '../../tncs/get-user-tncs/getUserTnCs'
import { GoogleUserInfo } from '../getGoogleUserInfo'
import { validateSignupOrUpdate } from '../validateSignupOrUpdate'

const findExistingUserDaf = async (log: Logger, userId: UUID) =>
  withLog(log, 'findExistingUserDaf', () =>
    db.query.userTable.findFirst({
      with: {
        tncSigns: true,
        tncAcceptances: true,
      },
      where: eq(userTable.id, userId),
    }),
  )

type User = NonNullable<Awaited<ReturnType<typeof findExistingUserDaf>>>

const calculateMobileVerified = (existingUser: User, form: UserForm) => {
  if (!existingUser.mobileVerified) {
    return false
  }
  return existingUser.mobileCountryCode === form.mobileCountryCode && existingUser.mobile === form.mobile
}

const findPreviouslySignedTnCsDaf = (log: Logger, userId: UUID) =>
  withLog(log, 'findPreviouslySignedTnCsDaf', () =>
    db
      .selectDistinct({ tncVersionId: userTncSignTable.tncVersionId })
      .from(userTncSignTable)
      .where(eq(userTncSignTable.userId, userId)),
  )

const fetchRejectedTnCs = async (
  log: Logger,
  userId: UUID,
  applicableTnCs: ApplicableUserTnCs,
  acceptedTnCs: UUID[],
) => {
  const previouslySignedTnCs = await findPreviouslySignedTnCsDaf(log, userId)
  const previouslySignedTncIds = previouslySignedTnCs.map((row) => row.tncVersionId)

  return applicableTnCs.filter((tnc) => !acceptedTnCs.includes(tnc.id) && previouslySignedTncIds.includes(tnc.id))
}

const insertUserSnapshotDaf = (log: Logger, db: Transaction, userId: UUID, existingUser: User) =>
  withLog(log, 'insertUserSnapshotDaf', () =>
    db.insert(userSnapshotTable).values({
      id: randomUUID(),
      userId: existingUser.id,
      data: existingUser,
    }),
  )

const updateUserDaf = (
  log: Logger,
  db: Transaction,
  userInfo: GoogleUserInfo,
  encryptedRefreshToken: string | null,
  form: UserForm,
  existingUser: User,
) =>
  withLog(
    log,
    'updateUserDaf',
    () =>
      db
        .update(userTable)
        .set({
          name: userInfo.name,
          email: userInfo.email,
          emailVerified: userInfo.email_verified,
          googlePictureUrl: userInfo.picture,
          language: userInfo.language,
          googleRefreshToken: sql`COALESCE(${encryptedRefreshToken}, google_refresh_token)`,
          mobileCountryCode: form.mobileCountryCode,
          mobile: form.mobile,
          mobileVerified: calculateMobileVerified(existingUser, form),
          legalAgeDeclaredAt: utc().toJSDate(),
          informationAccuracyDeclaredAt: utc().toJSDate(),
          updatedAt: utc().toJSDate(),
        })
        .where(eq(userTable.id, existingUser.id)),
    {
      data: { form },
    },
  )

/**
 * Updates user's TnC acceptances and signatures
 * See "TnC Re-acceptance Process" section of `user-management.md` domain documentation
 *
 * @param log Logger instance
 * @param db Database transaction
 * @param userId User's UUID
 * @param acceptedTnCs List of accepted TnC version IDs
 * @param rejectedTnCs List of applicable TnCs that were rejected
 */
const updateTnCsDaf = (
  log: Logger,
  db: Transaction,
  userId: UUID,
  acceptedTnCs: UUID[],
  rejectedTnCs: ApplicableUserTnCs,
) => {
  const dbCalls = [
    // Record signatures for accepted TnCs
    withLog(log, 'insertUserTncSignDaf', () =>
      db.insert(userTncSignTable).values(
        acceptedTnCs.map((tncVersionId) => ({
          id: randomUUID(),
          userId,
          tncVersionId,
          accepted: true,
        })),
      ),
    ),
    // and insert into userTncAcceptedTable if not already exists
    withLog(log, 'insertUserTncAcceptedDaf', () =>
      db
        .insert(userTncAcceptedTable)
        .values(
          acceptedTnCs.map((tncVersionId) => ({
            id: randomUUID(),
            userId,
            tncVersionId,
          })),
        )
        .onConflictDoNothing({ target: [userTncAcceptedTable.userId, userTncAcceptedTable.tncVersionId] }),
    ),
    // Delete rows from userTncAcceptedTable that are no longer accepted
    withLog(log, 'deleteUserTncAcceptedDaf', () =>
      db
        .delete(userTncAcceptedTable)
        .where(
          and(eq(userTncAcceptedTable.userId, userId), notInArray(userTncAcceptedTable.tncVersionId, acceptedTnCs)),
        ),
    ),
  ]

  // Record signatures for rejected TnCs
  if (rejectedTnCs.length > 0) {
    dbCalls.push(
      withLog(log, 'insertUserTncRejectedSignDaf', () =>
        db.insert(userTncSignTable).values(
          rejectedTnCs.map((tnc) => ({
            id: randomUUID(),
            userId,
            tncVersionId: tnc.id,
            accepted: false,
          })),
        ),
      ),
    )
  }
  return Promise.all(dbCalls)
}

export const updateUser = async (c: Ctx, form: UserForm, acceptLanguage?: string) => {
  ensureUser(c.user, { ignoreEmailVerification: true, ignoreMobileVerification: true })
  const userId = c.user.id

  const [{ userInfo, refreshToken, applicableTnCs }, existingUser] = await Promise.all([
    validateSignupOrUpdate(c.log, form, acceptLanguage),
    findExistingUserDaf(c.log, userId),
  ])

  ensure(existingUser && existingUser.googleId === userInfo.sub, {
    statusCode: 409,
    issueMessage: 'This user is linked to a different Google account.',
    logMessage: `User with Google ID ${userInfo.sub} attempted to update a different user with ID ${userId}.`,
  })

  // For applicable TnCs not in accepted list, record rejection if previously interacted
  const rejectedTnCs = await fetchRejectedTnCs(c.log, userId, applicableTnCs, form.acceptedTnCs)

  // Update user
  await db.transaction(async (tx) => {
    await Promise.all([
      insertUserSnapshotDaf(c.log, tx, userId, existingUser),
      updateUserDaf(c.log, tx, userInfo, refreshToken ? encrypt(refreshToken) : null, form, existingUser),
      updateTnCsDaf(c.log, tx, userId, form.acceptedTnCs, rejectedTnCs),
    ])
  })
}

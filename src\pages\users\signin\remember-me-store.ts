import { Store, useStore } from '@tanstack/react-store'
import { useEffect } from 'react'

import { updateStore } from '@ui/common/utils/updateStore'

const REMEMBER_ME_KEY = 'rememberMe'
const rememberMeStore = new Store({
  rememberMe: false,
})

let isRememberMeInitialized = false
export const useRememberMe = () => {
  useEffect(() => {
    if (!isRememberMeInitialized) {
      updateStore(rememberMeStore, { rememberMe: localStorage.getItem(REMEMBER_ME_KEY) === 'true' })
      isRememberMeInitialized = true
    }
  }, [])

  return useStore(rememberMeStore, (state) => state.rememberMe)
}

export const getRememberMe = () => rememberMeStore.state.rememberMe
export const setRememberMe = (rememberMe: boolean) => {
  updateStore(rememberMeStore, { rememberMe })
  localStorage.setItem(REMEMBER_ME_KEY, rememberMe.toString())
}

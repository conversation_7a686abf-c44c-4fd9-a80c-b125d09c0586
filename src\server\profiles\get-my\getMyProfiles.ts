import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { profileTable } from '@/server/db/schema/profile-schema'
import { withLog } from '@/shared/common/withLog.shared'

const findMyProfilesDaf = (log: Logger, userId: UUID) =>
  withLog(log, 'findMyProfilesDaf', () =>
    db.query.profileTable.findMany({
      columns: {
        id: true,
        displayName: true,
        role: true,
        approvedAt: true,
        suspendedAt: true,
        userId: true,
      },
      where: eq(profileTable.userId, userId),
      orderBy: profileTable.displayName,
    }),
  )

/**
 * Get all profiles for a given user
 * Returns basic profile information including approval and suspension status
 */
export const getMyProfiles = (c: Ctx) => {
  ensureUser(c.user, { ignoreEmailVerification: true, ignoreMobileVerification: true })
  return findMyProfilesDaf(c.log, c.user.id)
}

export type MyProfile = Awaited<ReturnType<typeof findMyProfilesDaf>>[0]

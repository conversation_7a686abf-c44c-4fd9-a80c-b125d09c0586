import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { listStudentsOfBatch } from './listStudentsOfBatch'

export const listStudentsOfBatchHandler = new Hono<HonoVars>().get('/', zValidator('param', $HasId), async (c) => {
  const { id: batchId } = c.req.valid('param')
  const batchStudents = await listStudentsOfBatch(getCtx(c), batchId)
  return c.json({ rows: batchStudents }, 200)
})

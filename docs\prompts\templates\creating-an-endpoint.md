Now that we have coded endpoints for adding and updating batches ( see inside 'src\server\batches\batches\add' and 'src\server\batches\batches\update' folders), let's code an endpoint for removing batches in 'src\server\batches\batches\remove'. If needed, you could refer to 'src\server\batches\batches\events\remove'. No need to check for referential integriety of deeper entities, e.g., if a batch has events, the removal would anyway be prevented by DB, we don't worry aboout it.

Once the endpoint is created, add it to 'src\server\api-routes.server.ts', and then to 'src\pages\courses\@id\batches\common\batch-queries.ts'.

No need to code tests ATM, we do that in a later round.

import { ClipboardDocumentIcon, ClipboardDocumentCheckIcon } from '@heroicons/react/24/outline'
import { useGoogleLogin } from '@react-oauth/google'
import clsx from 'clsx'
import { FormEvent, useEffect, useMemo, useState } from 'react'

import { Protected } from '@/pages/users/auth/Protected'
import { OmniError } from '@/shared/common/error-utils.shared'
import { googleOauthScopes } from '@/shared/users/googleOauthScopes.shared'
import { $SignInForm, MAX_VALIDITY_DAYS, MIN_VALIDITY_DAYS, SignInForm } from '@/shared/users/user-utils.shared'
import { ShowErrors } from '@ui/common/ShowErrors'
import { getMyError } from '@ui/common/utils/error-utils.ui'
import { useSignInMutation } from '@ui/users/common/user-queries'

export const CreateTokenPage = () => {
  const oauthState = useMemo(() => crypto.randomUUID(), [])
  const [formData, setFormData] = useState<Pick<SignInForm, 'validityDays'>>({
    validityDays: MAX_VALIDITY_DAYS,
  })

  const [omniError, setOmniError] = useState<OmniError | undefined>()
  const [apiKey, setApiKey] = useState<string | undefined>()
  const [copied, setCopied] = useState(false)
  const { mutate: signIn, isPending: signInIsPending } = useSignInMutation()

  useEffect(() => {
    const parsed = $SignInForm.pick({ validityDays: true }).safeParse({ validityDays: formData.validityDays })
    setOmniError(parsed.error)
  }, [formData])

  const handleOnGoogleLoginSuccess = async (response: { code: string; state?: string }) => {
    if (oauthState !== response.state) {
      setOmniError(getMyError('State mismatch. Please refresh page and try again.'))
      return
    }
    signIn(
      { ...formData, code: response.code },
      {
        onSuccess: (token) => {
          setApiKey(token.accessToken)
        },
        onError: (error) => {
          setOmniError(error.error)
        },
      },
    )
  }

  const login = useGoogleLogin({
    onSuccess: handleOnGoogleLoginSuccess,
    onError: (error) => {
      setOmniError(getMyError(error.error_description ?? 'Error signing in with Google'))
    },
    flow: 'auth-code',
    scope: googleOauthScopes.join(' '),
    state: oauthState,
  })

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    login()
  }

  const copyToClipboard = () => {
    if (apiKey) {
      void navigator.clipboard.writeText(apiKey)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  return (
    <Protected>
      <div className="flex min-h-full flex-1 flex-col justify-center px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <h2 className="mt-10 text-center text-2xl font-bold tracking-tight text-gray-900">Create an API Key</h2>
        </div>

        <ShowErrors error={omniError} />
        <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-md">
          <form onSubmit={handleSubmit} className="space-y-6 bg-white p-8 rounded-lg shadow-sm">
            <div>
              <label htmlFor="validityDays" className="block text-sm/6 font-medium text-gray-900">
                Validity Days
              </label>
              <div className="mt-2">
                <input
                  id="validityDays"
                  name="validityDays"
                  type="number"
                  className="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                  value={formData.validityDays}
                  onChange={(e) => setFormData({ ...formData, validityDays: parseInt(e.target.value) })}
                />
              </div>
              <p className="form-descr">
                Min {MIN_VALIDITY_DAYS}, max {MAX_VALIDITY_DAYS}
              </p>
              <ShowErrors error={omniError} path={['validityDays']} />
            </div>

            <div>
              <button
                type="submit"
                className={clsx(
                  'flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-colors',
                  {
                    'opacity-50 cursor-wait': signInIsPending,
                  },
                )}
                disabled={signInIsPending}
              >
                {signInIsPending ? 'Creating key...' : 'Create Key'}
              </button>
            </div>
          </form>

          {apiKey && (
            <div className="mt-8 bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-base font-medium text-gray-900 mb-3">Your API Key</h3>
              <div className="relative">
                <div className="text-xs border border-gray-300 rounded-md p-3 bg-gray-50 text-gray-800 break-all overflow-hidden max-h-24 overflow-y-auto">
                  {apiKey}
                </div>
                <button
                  onClick={copyToClipboard}
                  className="absolute top-2 right-2 p-1.5 bg-indigo-50 rounded-md hover:bg-indigo-100 transition-colors"
                  title="Copy to clipboard"
                >
                  {copied ?
                    <ClipboardDocumentCheckIcon className="h-4.5 w-4 text-green-600" />
                  : <ClipboardDocumentIcon className="h-4.5 w-4.5 text-indigo-600" />}
                </button>
              </div>
              <p className="text-xs text-gray-500 mt-2">This key will be valid for {formData.validityDays} days.</p>
            </div>
          )}
        </div>
      </div>
    </Protected>
  )
}

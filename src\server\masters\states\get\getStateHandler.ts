import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { getState } from './getState'

export const getStateHandler = new Hono<HonoVars>().get('/', zValidator('param', $HasId), async (c) => {
  const params = c.req.valid('param')
  const state = await getState(c.get('log'), params.id)
  return c.json(state, 200)
})

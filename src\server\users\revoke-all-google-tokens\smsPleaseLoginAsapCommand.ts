import { pino } from 'pino'

import { addCommand, Command } from '@/server/common/command/processCommands'
import { env } from '@/server/common/env.server'
import { sms } from '@/server/common/sms/sms'

export const SMS_PLEASE_LOGIN_ASAP_COMMAND = 'smsPleaseLoginAsap'

export type SmsPleaseLoginAsapCommandData = {
  userMobile: string
  userName: string
  language: string
}

const executeCommand = async (log: pino.Logger, command: Command<SmsPleaseLoginAsapCommandData>) => {
  const data = command.data
  await sms(log, false, 'tl_please_login_asap', data.language, {
    to: data.userMobile,
    bodyParams: { user_name: data.userName, home_url: env.HOME_URL },
  })
}

addCommand(SMS_PLEASE_LOGIN_ASAP_COMMAND, executeCommand)

import { eq } from 'drizzle-orm'

import { db } from '@/server/db/db'
import { stateTable } from '@/server/db/schema/master-schema'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'

import type { Logger } from 'pino'

const findStatesDaf = (log: Logger, countryCode: string) =>
  withLog(log, 'findStatesDaf', () =>
    db.query.stateTable.findMany({
      columns: {
        id: true,
        name: true,
      },
      where: eq(stateTable.countryCode, countryCode),
      orderBy: stateTable.name,
    }),
  )

export const listStates = (log: Logger, countryCode: string) => findStatesDaf(log, countryCode)
export type ListStates = typeof listStates
export type State = Awaited<ReturnType<ListStates>>[number]
setQuery('listStates', listStates)

import { UUID } from 'crypto'

import { PaperClipIcon } from '@heroicons/react/20/solid'
import { useEffect, useState } from 'react'

import { courseAttachmentUrl } from '@/shared/course/course-utils.shared'
import '@ui/common/assets/rich-content.css'
import { showNotification } from '@ui/common/notification/notification-store'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'
import { useCommonEnvSuspenseQuery } from '@ui/masters/common/master-queries'

import { useCourseAttachmentUrlQuery } from './queries/course-queries'

type CourseAttachment = { id: UUID; name: string; free: boolean; sizeBytes: number }

export const CourseAttachmentBlock = ({
  attachment,
  actions,
  grayedOut = false,
}: {
  attachment: CourseAttachment
  actions: React.ReactNode
  grayedOut?: boolean
}) => {
  return (
    <>
      <PaperClipIcon className="h-5 w-5 flex-shrink-0 text-gray-400" aria-hidden="true" />
      <div className="ml-3 flex-1 min-w-0">
        <div className="flex items-center">
          <span className="truncate font-medium text-gray-900">
            <CourseAttachmentLink
              label={attachment.name}
              attachment={attachment}
              className={grayedOut ? 'text-gray-400' : 'hover:text-indigo-600'}
            />
          </span>
          {attachment.free && (
            <span className="ml-2 inline-flex items-center rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">
              Free
            </span>
          )}
        </div>
        <div className="flex items-center mt-1 space-x-2 text-xs">
          <span className="text-gray-500">{Math.round((attachment.sizeBytes / 1024 / 1024) * 100) / 100}MB</span>
          {actions}
        </div>
      </div>
    </>
  )
}

const CourseAttachmentLink = ({
  label,
  attachment,
  className,
}: {
  label: string
  attachment: CourseAttachment
  className?: string
}) => {
  const { data: env } = useCommonEnvSuspenseQuery()
  const [toFetchUrl, setToFetchUrl] = useState(false)
  const { data, error } = useCourseAttachmentUrlQuery(attachment.id, toFetchUrl)
  useEffect(() => {
    if (error) {
      showNotification({
        heading: 'Error getting attachment URL',
        message: getErrorMessage(error),
        type: 'error',
      })
    }
  }, [error])

  useEffect(() => {
    if (data) {
      // Open the URL in a new browser tab
      window.open(data.url, '_blank', 'noopener,noreferrer')
      // Reset the fetch state after opening
      setToFetchUrl(false)
    }
  }, [data])

  const fetchSignedUrl = () => {
    setToFetchUrl(true)
  }

  if (attachment.free)
    return (
      <a
        href={courseAttachmentUrl(env?.APP_ENV ?? 'development', attachment.id)}
        target="_blank"
        rel="noopener noreferrer"
        className={className}
      >
        {label}
      </a>
    )

  return (
    <button className={className} onClick={fetchSignedUrl}>
      {label}
    </button>
  )
}

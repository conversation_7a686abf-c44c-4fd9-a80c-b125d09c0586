import { UUID } from 'crypto'

import { z } from 'zod'

import { $Currency, $Email, $Mobile, $UUID } from '../common/common-utils.shared'
import { slugOf } from '../common/string-utils.shared'
import { $CountryCode } from '../masters/master-utils.shared'

export const ACADEMY_NAME_MAX_LEN = 100
export const ACADEMY_DESCR_MAX_LEN = 9000
export const ACADEMY_UPI_ID_MAX_LEN = 100
export const GSTIN_LEN = 15
export const ACADEMY_PINCODE_LEN = 6
export const ACADEMY_AREA_MAX_LEN = 100
export const ACADEMY_ADDRESS_LINE_MAX_LEN = 100

export const $EditAcademyForm = z.object({
  name: z.string().trim().min(1).max(ACADEMY_NAME_MAX_LEN),
  descr: z.string().trim().max(ACADEMY_DESCR_MAX_LEN).optional(),
  email: $Email,
  mobileCountryCode: $CountryCode,
  mobile: $Mobile,
  currency: $Currency,
  upiId: z.string().trim().min(1).max(ACADEMY_UPI_ID_MAX_LEN),
  tradeName: z.string().trim().max(ACADEMY_NAME_MAX_LEN).optional(),
  gstin: z.union([z.literal(''), z.string().trim().min(GSTIN_LEN).max(GSTIN_LEN)]),
  districtId: $UUID,
  pincode: z.string().trim().min(ACADEMY_PINCODE_LEN).max(ACADEMY_PINCODE_LEN),
  area: z.string().trim().min(1).max(ACADEMY_AREA_MAX_LEN),
  addressLine1: z.string().trim().max(ACADEMY_ADDRESS_LINE_MAX_LEN).optional(),
  addressLine2: z.string().trim().max(ACADEMY_ADDRESS_LINE_MAX_LEN).optional(),
})
export type EditAcademyForm = z.infer<typeof $EditAcademyForm>

export const $AddAcademyForm = $EditAcademyForm.extend({
  tncsAccepted: z.boolean().refine((val) => val === true, {
    message: 'Please accept the TnCs',
  }),
})
export type AddAcademyForm = z.infer<typeof $AddAcademyForm>

export const $AcademySearch = z.object({
  profileId: $UUID,
})
export type AcademySearch = z.infer<typeof $AcademySearch>

export const academyPath = (academy: { id: UUID; name: string }) => `/academies/${slugOf(academy.name)}-${academy.id}`

import { clientOnly } from 'vike-react/clientOnly'
import { Config } from 'vike-react/Config'
import { Head } from 'vike-react/Head'

import { type Profile } from '@/server/profiles/get/getProfile.server'
import '@ui/common/assets/rich-content.css'
import { profilePath } from '@/shared/profiles/profile-utils.shared'

import { ProfileHeader } from '../common/ProfileHeader'

const ProfileActions = clientOnly(async () => (await import('./ProfileActions')).ProfileActions)

export const ProfilePage = ({ profile }: { profile: Profile }) => {
  return (
    <>
      <Config title={`${profile.displayName}`} />
      <Head>
        <meta name="description" content={`${profile.displayName}`} />
      </Head>
      <div className="px-4 sm:px-0">
        {/* Header section with name and role */}
        <div className="flex flex-col sm:flex-row items-start justify-between">
          <ProfileHeader profile={profile} />
          <ProfileActions profile={profile} />
        </div>
        {/* About section */}
        <div className="mt-6">
          {profile.descr ?
            <div className="rich-content">
              <div dangerouslySetInnerHTML={{ __html: profile.descr }} />
            </div>
          : <div className="mt-6 rounded-lg bg-gray-50 px-6 py-12">
              <div className="text-center">
                <p className="text-sm text-gray-500 italic">No profile description available</p>
              </div>
            </div>
          }
        </div>

        {profile.role === 'teacher' && (
          <div className="flex justify-center">
            <a
              href={`${profilePath(profile)}/batches`}
              className="my-5 rounded-sm bg-indigo-50 px-2 py-1 text-sm font-semibold text-indigo-600 shadow-xs hover:bg-indigo-100"
            >
              Batches taught by {profile.displayName} &rarr;
            </a>
          </div>
        )}
      </div>
    </>
  )
}

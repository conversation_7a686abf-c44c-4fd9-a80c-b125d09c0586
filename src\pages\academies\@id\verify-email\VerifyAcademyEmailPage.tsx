import { UUID } from 'crypto'

import { useEffect } from 'react'

import { AcademyItem } from '@/server/academies/list/listAcademies'
import { academyPath } from '@/shared/academies/academy-utils.shared'
import { useMyAcademyQuery, useVerifyAcademyEmailMutation } from '@ui/academies/common/academy-queries'
import { ErrorAlert } from '@ui/common/alerts/ErrorAlert'
import { SuccessAlert } from '@ui/common/alerts/SuccessAlert'
import { ShowData } from '@ui/common/ShowData'
import { Protected } from '@ui/users/auth/Protected'

export const VerifyAcademyEmailPage = ({ academyId, token }: { academyId: UUID; token: string | undefined }) => {
  const { data: myAcademy, isPending: myAcademyIsPending, error: myAcademyError } = useMyAcademyQuery()

  return (
    <Protected>
      <ShowData isPending={myAcademyIsPending} error={myAcademyError}>
        {myAcademy?.id === academyId ?
          <VerifyAcademyEmail academy={myAcademy} token={token} />
        : <div>You are not authorized to verify this academy's email</div>}
      </ShowData>
    </Protected>
  )
}

const VerifyAcademyEmail = ({ academy, token }: { academy: AcademyItem; token: string | undefined }) => {
  const { mutate: verifyAcademyEmail, isPending, error } = useVerifyAcademyEmailMutation(academy.id)

  useEffect(() => {
    if (token) {
      verifyAcademyEmail({ token })
    }
  }, [token])

  if (!token) {
    return <ErrorAlert>Missing verification token</ErrorAlert>
  }

  return (
    <ShowData isPending={isPending} error={error}>
      <SuccessAlert>
        Academy's email is verified!{' '}
        <a className="link" href={academyPath(academy)}>
          Click here
        </a>{' '}
        to visit academy.
      </SuccessAlert>
    </ShowData>
  )
}

import { UUID } from 'crypto'

import { Context } from 'hono'
import pino from 'pino'

import { Email } from '@/shared/common/common-utils.shared'
import { Role } from '@/shared/profiles/role-utils.shared'

import { HonoVars } from './hono-utils.server'

export type ContextUser = {
  id: UUID
  email: Email
  emailVerified: boolean
  mobileVerified: boolean
  suspendedAt: Date | null
  language: string
}

export type ContextProfile = {
  id: UUID
  displayName: string
  role: Role
  suspendedAt: Date | null
  approvedAt: Date | null
}

export type Ctx = {
  log: pino.Logger
  user?: ContextUser
  profile?: ContextProfile
}

export const getCtx = (c: Context<HonoVars>): Ctx => ({
  log: c.get('log'),
  user: c.get('user'),
  profile: c.get('profile'),
})

import { GaxiosError } from 'gaxios'
import pino from 'pino'

import { decrypt } from '@/server/common/encryption-utils.server'
import { getGoogleAuth } from '@/server/common/google/google-utils'

import {
  fetchNextUserWithRefreshTokenDaf,
  nullifyGoogleRefreshTokenAndNotifyUserDaf,
} from './revokeAllGoogleTokensHelper'

const tokenAlreadyRevoked = (ex: unknown) => {
  if (!ex || typeof ex !== 'object') return false
  const err = ex as GaxiosError
  return err.response?.data.error === 'invalid_token'
}

export const revokeAllGoogleTokens = async (log: pino.Logger) => {
  log.info('Revoking all Google tokens')
  while (true) {
    const user = await fetchNextUserWithRefreshTokenDaf(log)
    if (!user) break
    const refreshToken = decrypt(user.googleRefreshToken!)
    const oAuth2Client = getGoogleAuth(refreshToken)
    try {
      await oAuth2Client.revokeToken(refreshToken) // revokes user consent, thus all refresh tokens
    } catch (error) {
      if (tokenAlreadyRevoked(error)) {
        log.warn('Google token already revoked')
      } else {
        log.error({ error }, 'Error revoking Google token')
        throw error
      }
    }
    await nullifyGoogleRefreshTokenAndNotifyUserDaf(log, user)
  }
  log.info('Revoked all Google tokens')
}

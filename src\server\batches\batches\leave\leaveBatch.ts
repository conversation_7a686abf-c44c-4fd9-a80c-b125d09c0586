import { UUID } from 'crypto'

import { and, eq, isNull, sql } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { runDLocked } from '@/server/common/distributed-lock/runDLocked'
import { ensureProfile } from '@/server/common/error/ensure-profile.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db, Transaction } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { batchStudentTable, batchTable } from '@/server/db/schema/batch-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { updateGoogleEventForBatch } from '../common/updateGoogleEventForBatch'

const findBatchStudentDaf = (log: pino.Logger, batchId: UUID, studentId: UUID) =>
  withLog(log, 'findBatchStudentDaf', () =>
    db.query.batchStudentTable.findFirst({
      ...dummyColumn,
      with: {
        batch: {
          with: {
            teacher: {
              with: {
                user: {
                  columns: {
                    id: true,
                  },
                },
              },
            },
            events: {
              columns: {
                id: true,
                eventXid: true,
              },
            },
          },
        },
      },
      where: and(
        eq(batchStudentTable.batchId, batchId),
        eq(batchStudentTable.studentId, studentId),
        isNull(batchStudentTable.leftAt),
      ),
    }),
  )

const updateBatchStudentDaf = (log: pino.Logger, db: Transaction, batchId: UUID, studentId: UUID) =>
  withLog(log, 'updateBatchStudentDaf', () =>
    db
      .update(batchStudentTable)
      .set({
        leftAt: utc().toJSDate(),
        lastReminderType: null,
        updatedAt: utc().toJSDate(),
        updateRemarks: 'left',
      })
      .where(
        and(
          eq(batchStudentTable.batchId, batchId),
          eq(batchStudentTable.studentId, studentId),
          isNull(batchStudentTable.leftAt),
        ),
      ),
  )

// Decrement the student count in the batch
const decrementBatchStudentCountDaf = (log: pino.Logger, db: Transaction, batchId: UUID) =>
  withLog(log, 'decrementBatchStudentCountDaf', () =>
    db
      .update(batchTable)
      .set({
        studentCount: sql`${batchTable.studentCount} - 1`,
      })
      .where(and(eq(batchTable.id, batchId), sql`${batchTable.studentCount} > 0`)),
  )

export const leaveBatch = async (c: Ctx, batchId: UUID) => {
  ensureUser(c.user, {
    ignoreEmailVerification: true,
    ignoreMobileVerification: true,
  })
  ensureProfile(c.profile, {
    ignoreSuspension: true,
    ignoreApproval: true,
    roleAnyOf: ['student'],
  })
  const studentId = c.profile.id

  await runDLocked(c.log, ['batch', batchId], async () => {
    // Ensure the student is currently enrolled in this batch
    const batchStudent = await findBatchStudentDaf(c.log, batchId, studentId)

    ensure(batchStudent, {
      statusCode: 404,
      issueMessage: 'Student is not enrolled in this batch.',
      logMessage: `Student ${studentId} is not enrolled in batch ${batchId}.`,
    })

    await db.transaction(async (tx) => {
      await updateBatchStudentDaf(c.log, tx, batchId, studentId)
      await decrementBatchStudentCountDaf(c.log, tx, batchId)
      await updateGoogleEventForBatch(tx, c.log, batchStudent.batch.teacher.user.id, batchStudent.batch.events)
    })
  })
}

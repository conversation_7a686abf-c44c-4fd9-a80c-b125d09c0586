import { ProfileMenu } from '@ui/profiles/menu/ProfileMenu'
import { useSignedIn } from '@ui/users/auth/auth-token-store'
import { SigninTriggerButton } from '@ui/users/signin/SigninTriggerButton'

export const UserSection = () => {
  const signedIn = useSignedIn()
  return signedIn ?
      <ProfileMenu />
    : <div>
        <SigninTriggerButton />
        <div className="mt-1 text-xs">
          New user?{' '}
          <a href="/users/signup" className="link">
            Sign up
          </a>
        </div>
      </div>
}

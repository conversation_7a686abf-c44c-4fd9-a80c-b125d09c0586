import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $AddCourseForm } from '@/shared/course/course-utils.shared'

import { addCourse } from './addCourse'

export const addCourseHandler = new Hono<HonoVars>().post('/', zValidator('json', $AddCourseForm), async (c) => {
  const form = c.req.valid('json')

  const result = await addCourse(getCtx(c), form)
  return c.json(result, 201)
})

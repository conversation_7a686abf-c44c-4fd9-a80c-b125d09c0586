import { HTTPException } from 'hono/http-exception'
import { sign, verify } from 'hono/jwt'

import { utc } from '@/shared/common/date-utils-basic.shared'

import type { UUID } from 'crypto'

export const createAccessToken = (jwtSecret: string, userId: UUID, validityDays = 30) =>
  sign(
    {
      aud: 'auth',
      sub: userId,
      iat: utc().startOf('second').toSeconds(),
      exp: utc().plus({ days: validityDays }).startOf('second').toSeconds(),
    },
    jwtSecret,
  )

export const parseAccessToken = async (jwtSecret: string, token: string) => {
  const payload = await verify(token, jwtSecret)
  if (!payload.aud || payload.aud !== 'auth')
    throw new HTTPException(401, { message: 'Invalid audience in auth token' })
  return payload
}

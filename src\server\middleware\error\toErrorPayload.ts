import pino from 'pino'
import { PostgresError } from 'postgres'
import { ZodError } from 'zod'

import { MyException } from '@/server/common/error/MyException.server'
import { ErrorPayload } from '@/shared/common/error-utils.shared'
import { setToError } from '@/shared/common/serverQueries.shared'

export const toErrorPayload = (log: pino.Logger, error: Error): ErrorPayload => {
  if (isMyException(error)) {
    const payload = fromMyException(error)
    log.info({ payload }, `Error: ${error.message}`)
    return payload
  }

  if (isZodError(error)) {
    const payload = fromZodError(error)
    log.info({ error, payload }, 'Zod Error')
    return payload
  }

  if (isPostgresError(error)) {
    const payload = fromPostgresError(error)
    log.error({ error, payload, stack: error.stack }, 'Postgres Error')
    return payload
  }

  if (isJwtError(error)) {
    const payload = fromJwtError(error)
    log.info({ error, payload }, 'JWT Error')
    return payload
  }

  const payload = unknownError()
  log.error({ error, payload, stack: (error as Error).stack }, (error as Error).message ?? 'Unknown error')
  return payload
}

const fromMyException = (error: MyException): ErrorPayload => ({
  id: crypto.randomUUID(),
  status: error.status,
  error: {
    issues: error.issues,
  },
})

const fromZodError = (error: ZodError): ErrorPayload => ({
  id: crypto.randomUUID(),
  status: 422,
  error: {
    issues: error.issues,
  },
})

function isPostgresError(
  err: unknown,
  options?: {
    code?: string
    schemaName?: string
    tableName?: string
    constraintName?: string
  },
): err is PostgresError {
  const error = err as PostgresError | undefined
  if (error?.name !== 'PostgresError') return false
  if (options?.schemaName && options.schemaName !== error.schema_name) return false
  if (options?.tableName && options.tableName !== error.table_name) return false
  if (options?.constraintName && options.constraintName !== error.constraint_name) return false
  return true
}

const fromPostgresError = (error: PostgresError): ErrorPayload => ({
  id: crypto.randomUUID(),
  status: 409,
  error: {
    issues: [
      {
        path: [],
        code: 'postgresError',
        message: getErrorMessage(error),
      },
    ],
  },
})

const getErrorMessage = (error: PostgresError) => {
  switch (error.code) {
    case '23503':
      return `Constraint violation related to ${error.table_name}`
    case '23505':
      return `Duplicate row`
    default:
      return `Unexpected postgres error`
  }
}

const unknownError = (): ErrorPayload => ({
  id: crypto.randomUUID(),
  status: 500,
  error: {
    issues: [
      {
        path: [],
        code: 'unknownError',
        message: 'Internal server error. Please try again later.',
      },
    ],
  },
})

function isMyException(err: unknown): err is MyException {
  const error = err as MyException | undefined
  return error?.name === 'MyException'
}

function isZodError(err: unknown): err is ZodError {
  const error = err as ZodError | undefined
  return error?.name === 'ZodError'
}

type JwtError = { name: string }

function isJwtError(err: unknown): err is JwtError {
  const error = err as JwtError | undefined
  return error?.name === 'JwtTokenInvalid'
}

const fromJwtError = (error: JwtError): ErrorPayload => ({
  id: crypto.randomUUID(),
  status: 401,
  error: {
    issues: [
      {
        path: [],
        code: 'jwtError',
        message: error.name,
      },
    ],
  },
})

setToError(toErrorPayload)

import { UUID } from 'crypto'

import { z } from 'zod'

export const UNSAFE_CHARS = /[<>%]/g
export const SAFE_REGEX = /^[^<>%]*$/
export const UUID_REGEX = /^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i
export const EMAIL_REGEX = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/
export const OTP_REGEX = /^\d{4}$/
export const TIME_REGEX = /^([01]\d|2[0-3]):([0-5]\d)$/

export const UNKNOWN_UUID = crypto.randomUUID()

export const CRON_AUTH_KEY_HEADER = 'TL-Cron-Auth-Key'

// https://typeofnan.dev/making-every-object-property-nullable-in-typescript/
export type Nullable<T> = { [K in keyof T]: T[K] | null }

export type SyncRunnable<T = void> = () => T
export type AsyncRunnable<T = void> = () => Promise<T>
export type Runnable<T = void> = SyncRunnable<T> | AsyncRunnable<T>
export type DeploymentEnv = 'development' | 'local' | 'staging' | 'production' | 'test'

export const $Date = z.string().date()
export const $Time = z.string().regex(TIME_REGEX)
export type Time = z.infer<typeof $Time>

export const $DateRange = z
  .object({
    startDate: $Date,
    endDate: $Date,
  })
  .refine((data) => data.startDate <= data.endDate, 'Start date must be on or before end date')

export type DateRange = z.infer<typeof $DateRange>

export const CURRENCIES = ['INR', 'USD'] as const
export const $Currency = z.enum(CURRENCIES)
export type Currency = z.infer<typeof $Currency>

export const $Money = z.object({
  currency: $Currency,
  amount: z.number().positive(),
})

export type Money = z.infer<typeof $Money>

export const $UUID = z
  .custom<UUID>()
  .refine((val) => !val || val !== UNKNOWN_UUID, {
    message: 'Please provide a value',
  })
  .refine((val) => UUID_REGEX.test(val), {
    message: 'Must be a UUID v4 string',
  })
export const $OptionalUUID = z.union([z.literal(''), $UUID])

export type Email = string
export const $Email = z.string().max(100).email()
export const $OptionalEmail = z.union([z.literal(''), $Email])

export const $Mobile = z.string().regex(/^\d{10}$/, 'Mobile number must be 10 digits')

export const $HasId = z.object({
  id: $UUID,
})

export type HasId = z.infer<typeof $HasId>

export const $PageSearch = z.object({
  pageSize: z
    .string()
    .regex(/^\d+$/, 'Page size must be a positive integer')
    .refine((val) => !val || parseInt(val) >= 1, 'Page size must be at least 1')
    .refine((val) => !val || parseInt(val) <= 100, 'Page size must not exceed 100')
    .optional(),
  previous: z.string().optional(),
})

export type PageSearch = z.infer<typeof $PageSearch>

export const bool2str = (val: boolean) => (val ? 'true' : 'false')

export const getPropertyOf = <T = string>(obj: unknown, propertyName: string): T | null => {
  if (obj && typeof obj === 'object' && propertyName in obj) {
    return (obj as Record<string, T>)[propertyName]
  }
  return null
}

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

export const removeAttrs = <T extends object, K extends keyof T>(obj: T, attributesToRemove: K[]): Omit<T, K> => {
  const result = { ...obj }
  attributesToRemove.forEach((attr) => delete result[attr])
  return result
}

export const isClient = () => typeof window !== 'undefined' && window.document

export const queryStr = (search: Record<string, string>) =>
  Object.entries(search).length > 0 ? `?${new URLSearchParams(search).toString()}` : ''

export const extractUuid = (input: string | undefined): UUID => {
  if (input && input.length >= 36) {
    const match = input.match(/[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i)
    if (match) return match[0] as UUID
  }
  throw new Error(`Invalid UUID: ${input}`)
}

export const isNotFoundOrGone = (
  err: unknown, // Not found or Gone (already deleted)
) => err && typeof err === 'object' && 'status' in err && (err.status === 404 || err.status === 410)

export const phoneNumber = (countryPrefix: string, number: string) => {
  if (!countryPrefix) return number
  return `+${countryPrefix}-${number}`
}

export const OTP_SENDING_INTERVAL_SECONDS = 60
export const OTP_EXPIRY_INTERVAL_MINUTES = 5
export const OTP_MAX_TRIES_ALLOWED = 10

export const $VerifyMobileForm = z.object({
  otp: z.string().regex(/^\d{4}$/, 'OTP must be 4 a digit number'),
})

export type VerifyMobileForm = z.infer<typeof $VerifyMobileForm>

export const $VerifyEmailForm = z.object({
  token: z.string().min(1).max(5000),
})

export type VerifyEmailForm = z.infer<typeof $VerifyEmailForm>

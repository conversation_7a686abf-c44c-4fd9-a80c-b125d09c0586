import { UUID } from 'crypto'

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $VerifyEmailForm } from '@/shared/common/common-utils.shared'

import { verifyAcademyEmail } from './verifyAcademyEmail'

export const verifyAcademyEmailHandler = new Hono<HonoVars>().put(
  '/',
  zValidator('json', $VerifyEmailForm),
  async (c) => {
    const academyId = c.req.param('id') as UUID
    const form = c.req.valid('json')

    await verifyAcademyEmail(getCtx(c), academyId, form.token)
    return c.body(null, 204)
  },
)

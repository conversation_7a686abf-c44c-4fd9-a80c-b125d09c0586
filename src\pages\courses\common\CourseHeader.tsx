import { CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { academyPath } from '@/shared/academies/academy-utils.shared'
import { useAcademySuspenseQuery } from '@ui/academies/common/academy-queries'

import { CourseTags } from './CourseTags'
import { DraftBadge } from './DraftBadge'

export const CourseHeader = ({ course }: { course: CourseWithoutDescr }) => {
  const { data: academy } = useAcademySuspenseQuery(course.academyId, false)

  return academy ?
      <div className="sm:flex-auto flex flex-col gap-y-3 mr-2">
        <div>
          <h1 className="page-title">{course.name}</h1>
          <p className="page-subtitle">
            <a href={academyPath({ id: course.academyId, name: academy.name })}>{academy.name}</a>
          </p>
        </div>
        <div className="flex items-center gap-x-2">
          <DraftBadge publishedAt={course.publishedAt} />
          <CourseTags tagIds={course.tagIds} />
        </div>
      </div>
    : null
}

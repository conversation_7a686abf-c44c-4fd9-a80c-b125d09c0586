import { UUID } from 'crypto'

import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'

import { submitProfileForApproval } from './submitProfileForApproval'

export const submitProfileForApprovalHandler = new Hono<HonoVars>().post('/', async (c) => {
  const profileId = c.req.param('id') as UUID

  await submitProfileForApproval(getCtx(c), profileId)
  return c.body(null, 204)
})

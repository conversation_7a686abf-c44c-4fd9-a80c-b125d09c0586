import clsx from 'clsx'
import { ReactNode } from 'react'

import { OmniError } from '@/shared/common/error-utils.shared'

import { ShowErrors } from '../ShowErrors'

export const PageLayout = ({
  title,
  description,
  children,
  maxWidth,
}: {
  children: ReactNode
  title?: ReactNode
  description?: ReactNode
  maxWidth?: string
}) => {
  return (
    <div className={`mx-auto ${maxWidth} px-4`}>
      <div className="pb-2">
        {title && <h1 className="text-2xl font-bold leading-7 text-gray-900">{title}</h1>}
        {description && <p className="mt-2 text-sm/4 text-gray-600">{description}</p>}
      </div>
      {children}
    </div>
  )
}

/**
 * Contains muliple PageSection or any div, optionally with a border and dividers between each div.
 */
export const PageContent = ({
  children,
  border = true,
  divide = true,
}: {
  children: ReactNode
  border?: boolean
  divide?: boolean
}) => {
  return (
    <div
      className={clsx(
        'bg-white px-6',
        divide && 'divide-y divide-gray-900/10',
        border && 'mt-5 ring-1 shadow-xs ring-gray-900/10 rounded-xl border border-gray-900/10',
      )}
    >
      {children}
    </div>
  )
}

/**
 * Contains a section with optional title and description.
 */
export const PageSection = ({
  title,
  description,
  children,
  className,
}: {
  children: ReactNode
  title?: ReactNode
  description?: ReactNode
  className?: string
}) => {
  return (
    <div className={className ?? 'py-8'}>
      {title && <h2 className="text-base/7 font-semibold text-gray-900">{title}</h2>}
      {description && <p className="mt-1 max-w-2xl text-sm/6 text-gray-600">{description}</p>}
      {children}
    </div>
  )
}

export const FormButtons = ({
  omniError,
  onCancel,
  otherErrors,
  isSubmitting,
  submitLabel,
  children,
}: {
  omniError?: OmniError
  onCancel: () => void
  isSubmitting: boolean
  otherErrors?: unknown
  submitLabel?: string
  children?: ReactNode
}) => (
  <div className="mt-3">
    <div className="pb-1">
      <ShowErrors error={omniError} />
    </div>
    <div className="py-5 flex items-center justify-end gap-x-6">
      <button type="button" className="text-sm/6 font-semibold text-gray-900 hover:text-gray-700" onClick={onCancel}>
        Cancel
      </button>
      <SubmitButton hasError={!!omniError || !!otherErrors} isSubmitting={isSubmitting} label={submitLabel}>
        {children}
      </SubmitButton>
    </div>
  </div>
)

export const SubmitButton = ({
  isSubmitting,
  hasError,
  label,
  children,
}: {
  isSubmitting: boolean
  hasError: unknown
  label?: string
  children?: ReactNode
}) => (
  <button
    type="submit"
    disabled={isSubmitting || !!hasError}
    className={clsx(
      'inline-flex items-center gap-2 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50',
      isSubmitting ? 'cursor-wait'
      : hasError ? 'cursor-not-allowed'
      : 'cursor-pointer',
    )}
  >
    {children}
    {isSubmitting ? 'Submitting...' : (label ?? 'Submit')}
  </button>
)

/**
 * Provides a grid layout for PageSections.
 */
export const Grid6 = ({ children }: { children: ReactNode }) => {
  return <div className="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 py-6">{children}</div>
}

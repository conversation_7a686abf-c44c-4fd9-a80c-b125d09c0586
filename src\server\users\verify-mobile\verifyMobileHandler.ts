import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $VerifyMobileForm } from '@/shared/common/common-utils.shared'

import { verifyMobile } from './verifyMobile'

export const verifyMobileHandler = new Hono<HonoVars>().put('/', zValidator('json', $VerifyMobileForm), async (c) => {
  const form = c.req.valid('json')

  await verifyMobile(getCtx(c), form.otp)
  return c.body(null, 204)
})

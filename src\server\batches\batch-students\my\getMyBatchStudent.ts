import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfileWithRole } from '@/server/common/error/ensure-profile.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { batchStudentTable } from '@/server/db/schema'
import { withLog } from '@/shared/common/withLog.shared'

const findBatchStudentDaf = (log: pino.Logger, batchId: UUID, studentId: UUID) =>
  withLog(log, 'findBatchStudentDaf', () =>
    db
      .select({
        firstJoinedAt: batchStudentTable.firstJoinedAt,
        leftAt: batchStudentTable.leftAt,
        paidTillCycle: batchStudentTable.paidTillCycle,
      })
      .from(batchStudentTable)
      .where(and(eq(batchStudentTable.batchId, batchId), eq(batchStudentTable.studentId, studentId)))
      .then((res) => res[0]),
  )

export const getMyBatchStudent = async (c: Ctx, batchId: UUID) => {
  ensureUser(c.user)
  ensureProfileWithRole('student', c.profile)

  return findBatchStudentDaf(c.log, batchId, c.profile.id)
}

export type MyBatchStudent = Awaited<ReturnType<typeof findBatchStudentDaf>>

import { UUID } from 'crypto'

import { and, eq, isNull, ne, or, sql } from 'drizzle-orm'
import pino from 'pino'

import { ContextProfile } from '@/server/common/auth-context-types.server'
import { db } from '@/server/db/db'
import { batchAttachmentTable, batchStudentTable, batchTable, courseAttachmentTable } from '@/server/db/schema'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { profileOk } from '@/shared/profiles/profile-check-utils.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'

import { isStaffOfBatchDaf } from '../../common/isStaffOfBatchDaf'

const attachmentFields = {
  id: courseAttachmentTable.id,
  name: courseAttachmentTable.name,
  sizeBytes: courseAttachmentTable.sizeBytes,
  free: courseAttachmentTable.free,
}

const findBatchAttachmentsForStaffDaf = (log: pino.Logger, batchId: UUID) => {
  return withLog(log, 'findBatchAttachmentsDaf', () => {
    return db
      .select({
        ...attachmentFields,
        hidden: sql<boolean>`${batchAttachmentTable.id} is null`.as('hidden'),
      })
      .from(batchTable)
      .innerJoin(courseAttachmentTable, eq(batchTable.courseId, courseAttachmentTable.courseId))
      .leftJoin(batchAttachmentTable, eq(courseAttachmentTable.id, batchAttachmentTable.attachmentId))
      .where(eq(batchTable.id, batchId))
      .orderBy(courseAttachmentTable.position)
  })
}

const findBatchAttachmentsForStudentDaf = (log: pino.Logger, batchId: UUID, studentId: UUID) => {
  return withLog(
    log,
    'findBatchAttachmentsForStudentDaf',
    () => {
      return db
        .select(attachmentFields)
        .from(batchAttachmentTable)
        .innerJoin(batchStudentTable, eq(batchAttachmentTable.batchId, batchStudentTable.batchId))
        .innerJoin(courseAttachmentTable, eq(batchAttachmentTable.attachmentId, courseAttachmentTable.id))
        .where(
          and(
            eq(batchAttachmentTable.batchId, batchId),
            eq(batchStudentTable.studentId, studentId),
            isNull(batchStudentTable.leftAt),
            or(isNull(batchStudentTable.lastReminderType), ne(batchStudentTable.lastReminderType, 'blocked')),
          ),
        )
        .orderBy(courseAttachmentTable.position)
    },
    { data: { batchId, studentId } },
  )
}

export const listBatchAttachments = async (log: pino.Logger, batchId: UUID, profile?: ContextProfile) => {
  if (!profile || !profileOk(profile)) return []

  if (ACADEMY_STAFF.includes(profile.role)) {
    const isStaff = await isStaffOfBatchDaf(log, batchId, profile.id)
    return isStaff ? findBatchAttachmentsForStaffDaf(log, batchId) : []
  }

  if (profile.role === 'student') {
    return findBatchAttachmentsForStudentDaf(log, batchId, profile.id)
  }

  return []
}

setQuery('listBatchAttachments', listBatchAttachments)
export type ListBatchAttachments = typeof listBatchAttachments
export type BatchAttachment = Awaited<ReturnType<typeof findBatchAttachmentsForStaffDaf>>[number]

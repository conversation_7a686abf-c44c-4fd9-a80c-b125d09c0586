import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'

import { ensureCanUpdateAcademy } from '@/server/academies/common/ensureCanUpdateAcademy'
import { Ctx } from '@/server/common/auth-context-types.server'
import { db } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { batchTable, courseTable } from '@/server/db/schema'
import { academyStaffTable } from '@/server/db/schema/academy-schema'
import { UNKNOWN_UUID } from '@/shared/common/common-utils.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'

const findAcademyStaff = (profileId: UUID, batchId: UUID) =>
  db
    .select(dummyColumn.extras)
    .from(academyStaffTable)
    .innerJoin(courseTable, eq(academyStaffTable.academyId, courseTable.academyId))
    .innerJoin(batchTable, eq(courseTable.id, batchTable.courseId))
    .where(and(eq(academyStaffTable.profileId, profileId), eq(batchTable.id, batchId)))
    .limit(1)

export const ensureCanUpdateBatch = async (c: Ctx, batchId: UUID) =>
  ensureCanUpdateAcademy(c, findAcademyStaff(c.profile?.id ?? UNKNOWN_UUID, batchId), ACADEMY_STAFF)

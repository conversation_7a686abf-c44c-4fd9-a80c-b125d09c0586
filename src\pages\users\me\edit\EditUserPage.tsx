import { useEffect, useState } from 'react'
import { navigate } from 'vike/client/router'

import { showNotification } from '@/pages/common/notification/notification-store'
import { ShowData } from '@/pages/common/ShowData'
import { stopWaiting } from '@/pages/common/wait/wait-store'
import { Protected } from '@/pages/users/auth/Protected'
import { UserEditor } from '@/pages/users/common/UserEditor'
import { type UserForm } from '@/shared/users/user-utils.shared'
import { PageLayout } from '@ui/common/form/page-layout'
import { useUpdateUserMutation, useUserQuery } from '@ui/users/common/user-queries'

import type { FullUserData } from '@/server/users/get-user/getUser.server'

export const EditUserPage = () => {
  const [formData, setFormData] = useState<UserForm>()

  const { data, isPending, error } = useUserQuery(true)
  const user = data as FullUserData

  const {
    mutate: updateUser,
    isSuccess: updateUserIsSuccess,
    isPending: updateUserIsPending,
    error: updateUserError,
  } = useUpdateUserMutation()

  // Initialize form data when user data is loaded
  useEffect(() => {
    if (user) {
      setFormData({
        mobileCountryCode: user.mobileCountryCode,
        mobile: user.mobile,
        acceptedTnCs: user.tncAcceptances.map((tnc) => tnc.tncVersionId),
        authCode: '',
        legalAgeDeclaration: false,
        informationAccuracyDeclaration: false,
      })
    }
  }, [user])

  // Handle successful update
  useEffect(() => {
    if (updateUserIsSuccess) {
      showNotification({ heading: 'User data updated successfully', type: 'success' })
      void navigate('/')
      stopWaiting()
    }
  }, [updateUserIsSuccess])

  return (
    <Protected>
      <ShowData isPending={isPending} error={error} spinnerSize="1.25rem">
        <PageLayout title={user?.name ?? 'Loading ...'} description="Edit your user data" maxWidth="max-w-2xl">
          {formData && (
            <UserEditor
              initialFormData={formData}
              apiIsPending={updateUserIsPending}
              apiError={updateUserError?.error}
              onSubmit={updateUser}
            />
          )}
        </PageLayout>
      </ShowData>
    </Protected>
  )
}

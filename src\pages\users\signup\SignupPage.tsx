import { useEffect } from 'react'
import { navigate } from 'vike/client/router'

import { showNotification } from '@/pages/common/notification/notification-store'
import { stopWaiting } from '@/pages/common/wait/wait-store'
import { setAuth } from '@/pages/users/auth/auth-token-store'
import { PublicOnly } from '@/pages/users/auth/PublicOnly'
import { UserEditor } from '@/pages/users/common/UserEditor'
import { setSigninModalVisibility } from '@/pages/users/signin/signin-modal-store'
import { PageLayout } from '@ui/common/form/page-layout'
import { useSignupMutation } from '@ui/users/common/user-queries'

export const SignupPage = () => {
  const { mutate: signup, isPending: signupIsPending, data: token, error: signupError } = useSignupMutation()

  useEffect(() => {
    if (token) {
      setSigninModalVisibility(false)
      setAuth(token)
      showNotification({
        heading: 'User created successfully',
        type: 'success',
      })
      void navigate('/')
    }
    stopWaiting()
  }, [token])

  return (
    <PublicOnly pageName="Sign Up">
      <PageLayout title="Sign Up" description="Please fill in your details to create an account" maxWidth="max-w-2xl">
        <UserEditor apiIsPending={signupIsPending} apiError={signupError?.error} onSubmit={signup} />
      </PageLayout>
    </PublicOnly>
  )
}

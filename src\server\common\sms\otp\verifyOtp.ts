import { eq } from 'drizzle-orm'
import { DateTime } from 'luxon'
import { Logger, pino } from 'pino'

import { decrypt } from '@/server/common/encryption-utils.server'
import { ensure, getMyException } from '@/server/common/error/ensure.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { db, Transaction } from '@/server/db/db'
import { mobileOtpTable } from '@/server/db/schema/operation-schema'
import { OTP_EXPIRY_INTERVAL_MINUTES, OTP_MAX_TRIES_ALLOWED } from '@/shared/common/common-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

const findOtpDaf = (log: Logger, mobile: string) =>
  withLog(log, 'findOtpDaf', () =>
    db.query.mobileOtpTable.findFirst({
      where: eq(mobileOtpTable.mobile, mobile),
    }),
  )

const incrementOtpTryCountDaf = (log: Logger, mobile: string, currentCount: number) =>
  withLog(log, 'incrementOtpTryCountDaf', () =>
    db
      .update(mobileOtpTable)
      .set({
        otpTryCount: currentCount + 1,
        tryFailedAt: DateTime.now().toJSDate(),
      })
      .where(eq(mobileOtpTable.mobile, mobile)),
  )

export const verifyOtp = async (log: pino.Logger, mobile: string, otp: string) => {
  const otpRow = await findOtpDaf(log, mobile)
  ensureExists(otpRow, `for mobile: ${mobile}`, 'OTP')

  // Check OTP expiry
  const otpAge = DateTime.now().diff(DateTime.fromJSDate(otpRow.createdAt), 'minutes')
  ensure(otpAge.minutes <= OTP_EXPIRY_INTERVAL_MINUTES, {
    logMessage: `OTP expired for ${mobile}. Created at: ${otpRow.createdAt}`,
    issueMessage: 'OTP has expired. Please request a new OTP',
    statusCode: 410,
  })

  // Check max tries
  ensure(otpRow.otpTryCount <= OTP_MAX_TRIES_ALLOWED, {
    logMessage: `Max OTP attempts exceeded for ${mobile}. Attempts: ${otpRow.otpTryCount}`,
    issueMessage: 'Maximum OTP attempts exceeded. Please request a new OTP',
    statusCode: 429,
  })

  // Verify OTP
  const decryptedOtp = decrypt(otpRow.otp)
  if (otp !== decryptedOtp) {
    await incrementOtpTryCountDaf(log, mobile, otpRow.otpTryCount)
    throw getMyException({
      logMessage: `Invalid OTP for ${mobile}. Attempts: ${otpRow.otpTryCount + 1}`,
      issueMessage: 'Invalid OTP',
      statusCode: 422,
    })
  }
}

export const removeOtp = async (log: pino.Logger, db: Transaction, mobile: string) =>
  withLog(log, 'removeOtp', () => db.delete(mobileOtpTable).where(eq(mobileOtpTable.mobile, mobile)))

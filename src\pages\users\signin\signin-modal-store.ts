import { Store, useStore } from '@tanstack/react-store'

import { updateStore } from '@ui/common/utils/updateStore'

const signinModalStore = new Store({
  visible: false,
  message: null as null | string,
})

export const setSigninModalVisibility = (visible: boolean, message: string | null = null) =>
  updateStore(signinModalStore, { visible, message })

export const useSigninModalVisible = () => useStore(signinModalStore, (state) => state.visible)
export const useSigninModalMessage = () => useStore(signinModalStore, (state) => state.message)

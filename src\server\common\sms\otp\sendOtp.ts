import { eq } from 'drizzle-orm'
import { DateTime } from 'luxon'
import pino, { Lo<PERSON> } from 'pino'

import { encrypt } from '@/server/common/encryption-utils.server'
import { ensure } from '@/server/common/error/ensure.server'
import { sms } from '@/server/common/sms/sms'
import { db } from '@/server/db/db'
import { mobileOtpTable } from '@/server/db/schema/operation-schema'
import { OTP_SENDING_INTERVAL_SECONDS } from '@/shared/common/common-utils.shared'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'

const generateOtp = () => Math.floor(1000 + Math.random() * 9000).toString()

const findOtpDaf = (log: Logger, mobile: string) =>
  withLog(log, 'findOtpDaf', () =>
    db.query.mobileOtpTable.findFirst({
      where: eq(mobileOtpTable.mobile, mobile),
    }),
  )

const upsertOtpDaf = (log: Logger, mobile: string, encryptedOtp: string) =>
  withLog(log, 'upsertOtpDaf', () =>
    db
      .insert(mobileOtpTable)
      .values({
        mobile,
        otp: encryptedOtp,
      })
      .onConflictDoUpdate({
        target: [mobileOtpTable.mobile],
        set: {
          otp: encryptedOtp,
          createdAt: utc().toJSDate(),
        },
      }),
  )

export const sendOtp = async (log: pino.Logger, mobile: string, language: string) => {
  const otpRow = await findOtpDaf(log, mobile)

  if (otpRow) {
    const elapsed = DateTime.now().diff(DateTime.fromJSDate(otpRow.createdAt), 'seconds')
    ensure(elapsed.seconds >= OTP_SENDING_INTERVAL_SECONDS, {
      logMessage: `Rate limit exceeded for mobile: ${mobile}. Last OTP sent at: ${otpRow.createdAt}`,
      issueMessage: `Please wait ${OTP_SENDING_INTERVAL_SECONDS} seconds before requesting another OTP`,
      statusCode: 429,
    })
  }

  // Generate and encrypt OTP
  const otp = generateOtp()
  const encryptedOtp = encrypt(otp)

  // Store OTP
  await upsertOtpDaf(log, mobile, encryptedOtp)

  // Send OTP
  await sms(log, true, 'tl_verify_mobile', language, {
    to: mobile,
    bodyParams: { code: otp },
    additionalComponents: [
      {
        type: 'button',
        sub_type: 'url',
        index: 0,
        parameters: [
          {
            type: 'text',
            text: otp,
          },
        ],
      },
    ],
  })
}

import crypto, { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { ensureCanUpdateBatch } from '@/server/batches/batches/common/ensureCanUpdateBatch'
import { Ctx } from '@/server/common/auth-context-types.server'
// import { appendCommandDaf } from '@/server/common/command/processCommands'
import { ensureExists } from '@/server/common/error/ensureExists'
// import { calendarFlock } from '@/server/common/google/calendar/calendar-utils'
import { db, Transaction } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { batchEventTable, batchTable } from '@/server/db/schema/batch-schema'
import { AddBatchEventForm } from '@/shared/batch/batch-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

// import {
//   ADD_GOOGLE_EVENT_FOR_BATCH_COMMAND,
//   AddGoogleEventForBatchCommandData,
// } from './addGoogleEventForBatchEventCommand'

const findBatchDaf = (log: pino.Logger, batchId: UUID) =>
  withLog(log, 'findBatchDaf', () =>
    db.query.batchTable.findFirst({
      ...dummyColumn,
      with: {
        teacher: {
          with: {
            user: {
              columns: {
                id: true,
              },
            },
          },
        },
      },
      where: eq(batchTable.id, batchId),
    }),
  )

const insertBatchEventDaf = (log: pino.Logger, db: Transaction, form: AddBatchEventForm) =>
  withLog(log, 'insertBatchEventDaf', () =>
    db.insert(batchEventTable).values({
      id: form.id,
      batchId: form.batchId,
      at: form.at,
      durationMinutes: form.durationMinutes,
      days: form.days,
      eventType: form.eventType,
      eventXid: crypto.randomUUID().replaceAll('-', ''),
    }),
  )

export const addBatchEvent = async (c: Ctx, form: AddBatchEventForm) => {
  const batch = await findBatchDaf(c.log, form.batchId)
  ensureExists(batch, form.batchId, 'Batch')
  await ensureCanUpdateBatch(c, form.batchId)

  // Insert the batch event into the database
  await db.transaction(async (tx) => {
    await insertBatchEventDaf(c.log, tx, form)
    // await appendCommandDaf<AddGoogleEventForBatchCommandData>(tx, c.log, calendarFlock(batch.teacher.user.id), {
    //   command: ADD_GOOGLE_EVENT_FOR_BATCH_COMMAND,
    //   data: {
    //     eventId: form.id,
    //   },
    // })
  })
}

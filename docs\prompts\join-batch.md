Let's create an endpoint `POST /batches/{id}/students` for letting a student to join a batch. The endpoint should be coded in `src\server\batches\batches\join` folder, following the existig patterns for coding endpoints. Refer to the sample in `src\server\batches\batches\add` for example, and `src\server\db\schema\batch-schema.ts` and `src\server\db\schema\profile-schema.ts` for database schema.

The service code should ensure that the current profile is of type `student`, and has not already joined the given batch. It also ensure that the batch is open for admission, and seats are not full. Then it should insert a row in `batchStudentTable`, as well as increase the `studentCount` in batchTable transactionally. See `src\server\batches\batches\events\add\addBatchEvent.ts` for exmple of how transactions are coded.

Once the endpoint is created, add it to `src\server\api-routes.server.ts`, and then to `src\pages\courses\@id\batches\common\batch-queries.ts`.

No need to code tests ATM, we do that in a later round.
import { UUID } from 'crypto'

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import { StudentFeePaymentDetail } from '@/server/batches/batches/payments/get/getStudentFeePayment'
import { StudentFeePaymentItemOfBatch } from '@/server/batches/batches/payments/items/list-for-batch/listStudentFeePaymentItemsOfBatch'
import { StudentFeePayment } from '@/server/batches/batches/payments/list/listStudentFeePayments'
import { StudentFeePaymentOfAcademy } from '@/server/batches/batches/payments/list-for-academy/listStudentFeePaymentsOfAcademy'
import {
  AddBatchStudentPaymentForm,
  PayStudentFeePaymentForm,
  StudentFeePaymentOfAcademySearch,
  StudentFeePaymentsSearch,
} from '@/shared/batch/batch-utils.shared'
import { api } from '@ui/api-client'
import { showNotification } from '@ui/common/notification/notification-store'
import { dateConverter } from '@ui/common/utils/dateConverter'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

import { batchStudentKeys } from './batch-student-queries'

export const studentFeePaymentKeys = {
  all: ['student-fee-payment'] as const,
  lists: () => [...studentFeePaymentKeys.all, 'list'] as const,
  listForStudent: (currentProfileId: UUID | undefined, search: StudentFeePaymentsSearch = {}) =>
    [...studentFeePaymentKeys.lists(), 'for-student', currentProfileId, search] as const,
  listForAcademy: (academyId: UUID, search: StudentFeePaymentOfAcademySearch, currentProfileId: UUID | undefined) =>
    [...studentFeePaymentKeys.lists(), 'for-academy', academyId, search, currentProfileId] as const,
  listForBatch: (batchId: UUID, currentProfileId: UUID | undefined) =>
    [...studentFeePaymentKeys.lists(), 'for-batch', batchId, currentProfileId] as const,
  itemCount: (currentProfileId: UUID | undefined) =>
    [...studentFeePaymentKeys.lists(), 'item-count', currentProfileId] as const,
  details: () => [...studentFeePaymentKeys.all, 'detail'] as const,
  detail: (studentFeePaymentId: UUID, currentProfileId: UUID | undefined) =>
    [...studentFeePaymentKeys.details(), studentFeePaymentId, currentProfileId] as const,
}

const listStudentFeePaymentsDateFields = ['createdAt']
const studentFeePaymentDateFields = ['createdAt', 'paidAt']
const listStudentFeePaymentsOfAcademyDateFields = ['createdAt', 'paidAt']
const listStudentFeePaymentsOfBatchDateFields = ['paidAt']

export const useAddStudentFeePaymentMutation = (batchId: UUID) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (form: AddBatchStudentPaymentForm) =>
      api().batches[':id'].payments.$post({ param: { id: batchId }, json: form }),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: studentFeePaymentKeys.all })
      showNotification({
        heading: 'Payment added',
        type: 'success',
      })
    },
    onError: (error) => {
      showNotification({
        heading: 'Error adding payment',
        message: getErrorMessage(error),
        type: 'error',
      })
    },
  })
}

export const useStudentFeePaymentsQuery = (search: StudentFeePaymentsSearch = {}) => {
  const { currentProfile } = useCurrentProfile()

  return useQuery({
    queryKey: studentFeePaymentKeys.listForStudent(currentProfile?.id, search),
    queryFn: () =>
      api()
        .students.me.payments.$get({ query: search })
        .then((res) => res.json())
        .then((studentFeePayments) => studentFeePayments.rows)
        .then(dateConverter<StudentFeePayment[]>(listStudentFeePaymentsDateFields)),
  })
}

export const useStudentFeePaymentQuery = (studentFeePaymentId: UUID) => {
  const { currentProfile } = useCurrentProfile()

  return useQuery({
    queryKey: studentFeePaymentKeys.detail(studentFeePaymentId, currentProfile?.id),
    queryFn: () =>
      api()
        ['students-fee-payments'][':id'].$get({ param: { id: studentFeePaymentId } })
        .then((res) => res.json())
        .then(dateConverter<StudentFeePaymentDetail>(studentFeePaymentDateFields)),
  })
}

export const useStudentFeePaymentItemCountQuery = () => {
  const { currentProfile } = useCurrentProfile()

  return useQuery({
    queryKey: studentFeePaymentKeys.itemCount(currentProfile?.id),
    queryFn: () =>
      api()
        .students.me.payments['item-count'].$get()
        .then((res) => res.json()),
  })
}

export const usePayStudentFeePaymentMutation = (studentFeePaymentId: UUID) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (form: PayStudentFeePaymentForm) =>
      api()['students-fee-payments'][':id'].pay.$post({ param: { id: studentFeePaymentId }, json: form }),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: studentFeePaymentKeys.all })
    },
  })
}

export const useRemoveStudentFeePaymentItemMutation = (studentFeePaymentItemId: UUID) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: () => api()['students-fee-payment-items'][':id'].$delete({ param: { id: studentFeePaymentItemId } }),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: studentFeePaymentKeys.all })
    },
    onError: (error) => {
      showNotification({
        heading: 'Error removing payment item',
        message: getErrorMessage(error),
        type: 'error',
      })
    },
  })
}

export const useStudentFeePaymentsOfAcademyQuery = (academyId: UUID, search: StudentFeePaymentOfAcademySearch) => {
  const { currentProfile } = useCurrentProfile()

  return useQuery({
    queryKey: studentFeePaymentKeys.listForAcademy(academyId, search, currentProfile?.id),
    queryFn: () =>
      api()
        .academies[':id'].payments.$get({ param: { id: academyId }, query: search })
        .then((res) => res.json())
        .then((studentFeePayments) => studentFeePayments.rows)
        .then(dateConverter<StudentFeePaymentOfAcademy[]>(listStudentFeePaymentsOfAcademyDateFields)),
  })
}

export const useStudentFeePaymentsOfBatchQuery = (batchId: UUID) => {
  const { currentProfile } = useCurrentProfile()

  return useQuery({
    queryKey: studentFeePaymentKeys.listForBatch(batchId, currentProfile?.id),
    queryFn: () =>
      api()
        .batches[':id'].payments.$get({ param: { id: batchId } })
        .then((res) => res.json())
        .then((studentFeePayments) => studentFeePayments.rows)
        .then(dateConverter<StudentFeePaymentItemOfBatch[]>(listStudentFeePaymentsOfBatchDateFields)),
  })
}

export const useReceiveStudentFeePaymentMutation = (studentFeePaymentId: UUID) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: () => api()['students-fee-payments'][':id'].receive.$post({ param: { id: studentFeePaymentId } }),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: studentFeePaymentKeys.all })
      void queryClient.invalidateQueries({ queryKey: batchStudentKeys.all })
    },
    onError: (error) => {
      showNotification({
        heading: 'Error receiving payment',
        message: getErrorMessage(error),
        type: 'error',
      })
    },
  })
}

import { approvalOk, ProfileTo<PERSON>heck, roleOk, suspensionOk } from '@/shared/profiles/profile-check-utils.shared'
import { PROFILE_HEADER } from '@/shared/profiles/profile-utils.shared'
import { Role } from '@/shared/profiles/role-utils.shared'

import { ensure } from './ensure.server'

export function ensureProfile(
  profile?: ProfileToCheck,
  options?: { ignoreSuspension?: boolean; ignoreApproval?: boolean; roleAnyOf?: readonly Role[] },
): asserts profile {
  ensure(profile, {
    logMessage: `Profile not found in context. ${PROFILE_HEADER} request header missing?`,
    issueMessage: `${PROFILE_HEADER} request header missing`,
    statusCode: 403,
  })
  ensure(suspensionOk(profile, options), {
    logMessage: `Current profile ${profile.id} is suspended.`,
    issueMessage: `Profile is suspended`,
    statusCode: 403,
  })
  ensure(approvalOk(profile, options), {
    logMessage: `Current profile ${profile.id} is not approved.`,
    issueMessage: `Profile is not approved`,
    statusCode: 403,
  })
  ensure(roleOk(profile, options), {
    logMessage: `Current profile ${profile.id} is a ${profile.role}, which is not one of ${options?.roleAnyOf}`,
    issueMessage: `Only one of ${options?.roleAnyOf} is allowed to do this operation`,
    statusCode: 403,
  })
}

export function ensureProfileWithRole(role: Role, profile?: ProfileToCheck): asserts profile {
  ensureProfile(profile, { roleAnyOf: [role] })
}

import { UUID } from 'crypto'

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $EditProfileForm } from '@/shared/profiles/profile-utils.shared'

import { updateProfile } from './updateProfile'

export const updateProfileHandler = new Hono<HonoVars>().put('/', zValidator('json', $EditProfileForm), async (c) => {
  const formData = c.req.valid('json')
  const profileId = c.req.param('id') as UUID

  await updateProfile(getCtx(c), profileId, formData)
  return c.body(null, 204)
})

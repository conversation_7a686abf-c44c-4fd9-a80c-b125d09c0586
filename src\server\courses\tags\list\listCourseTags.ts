import { and, isNull } from 'drizzle-orm'
import pino from 'pino'

import { db } from '@/server/db/db'
import { courseTagGroupTable, courseTagTable } from '@/server/db/schema/course-schema'
import { withLog } from '@/shared/common/withLog.shared'

import { courseTagColumns } from '../common/course-tag-select-helper'
export const findCourseTagsDaf = (log: pino.Logger) =>
  withLog(log, 'findCourseTagsDaf', () =>
    db.query.courseTagGroupTable.findMany({
      columns: {
        id: true,
        name: true,
      },
      with: {
        tags: {
          columns: courseTagColumns,
        },
      },
      where: and(isNull(courseTagTable.suspendedAt), isNull(courseTagGroupTable.suspendedAt)),
      orderBy: [courseTagGroupTable.position, courseTagTable.position],
    }),
  )

export const listCourseTags = (log: pino.Logger) => findCourseTagsDaf(log)

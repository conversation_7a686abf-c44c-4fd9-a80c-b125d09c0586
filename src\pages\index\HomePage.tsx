import {
  AcademicCapIcon,
  BookOpenIcon,
  ClockIcon,
  CurrencyDollarIcon,
  GlobeAltIcon,
  UserGroupIcon,
  KeyIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline'

// Academy perspective features
const academyFeatures = [
  {
    name: 'Seamless Online Learning',
    description:
      'Bridge the gap between traditional and digital education with scheduled live classes and interactive learning sessions.',
    icon: AcademicCapIcon,
  },
  {
    name: 'Role-Based Administration',
    description:
      'Comprehensive role management system allows academies to efficiently organize principals, mentors, and teachers with appropriate permissions.',
    icon: KeyIcon,
  },
  {
    name: 'Personalized Learning Experience',
    description: 'Small batch sizes enable individualized attention and targeted feedback on MCQs and assignments.',
    icon: UserGroupIcon,
  },
  {
    name: 'Secure & Reliable',
    description: 'Robust architecture ensures student data protection and consistent platform availability.',
    icon: ShieldCheckIcon,
  },
]

// Student perspective features
const studentFeatures = [
  {
    name: 'Convenient Access',
    description:
      'Attend quality classes from anywhere, eliminating commute time and transportation costs associated with physical tuition centers.',
    icon: GlobeAltIcon,
  },
  {
    name: 'Cost-Effective Education',
    description:
      'Reduced overhead costs for academies translate to more affordable tuition fees without sacrificing quality.',
    icon: CurrencyDollarIcon,
  },
  {
    name: 'Interactive Experience',
    description:
      'Engage directly with teachers in real-time through live sessions, providing a more dynamic learning environment than pre-recorded content.',
    icon: ClockIcon,
  },
  {
    name: 'Progressive Learning Resources',
    description:
      'Access to course materials that are released strategically as the batch progresses, enhancing your learning experience with timely resources.',
    icon: BookOpenIcon,
  },
]

export const HomePage = () => {
  return (
    <div className="bg-white">
      {/* Hero section */}
      <div>
        <div className="py-12 sm:py-20 lg:pb-32">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-semibold tracking-tight text-balance text-gray-900 sm:text-5xl">
                Transform Your Learning Experience
              </h1>
              <p className="mt-6 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8">
                Learn from the best teachers at the comfort of your home.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <a
                  href="/courses"
                  className="rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                >
                  Get Started
                </a>
                <a href="/help" className="text-sm/6 font-semibold text-gray-900">
                  Learn more <span aria-hidden="true">→</span>
                </a>
              </div>
            </div>
            <div className="mt-16 flow-root sm:mt-24">
              <div className="-m-2 rounded-xl bg-gray-900/5 p-2 ring-1 ring-gray-900/10 ring-inset lg:-m-4 lg:rounded-2xl lg:p-4">
                <img
                  alt="Academy dashboard"
                  src="https://images.unsplash.com/photo-1523240795612-9a054b0db644?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3"
                  width={2432}
                  height={1442}
                  className="rounded-md shadow-2xl ring-1 ring-gray-900/10"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Feature sections */}
      <div id="features" className="mx-auto mt-20 max-w-7xl px-6 sm:mt-40 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="text-base/7 font-semibold text-indigo-600">For Academies</h2>
          <p className="mt-2 text-3xl font-semibold tracking-tight text-pretty text-gray-900 sm:text-4xl lg:text-balance">
            Elevate Your Academy's Online Presence
          </p>
          <p className="mt-6 text-lg/8 text-pretty text-gray-600">
            Designed for academies of all sizes with an intuitive interface that can handle numerous courses, batches,
            and students without compromising performance.
          </p>
        </div>
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16">
            {academyFeatures.map((feature) => (
              <div key={feature.name} className="relative pl-16">
                <dt className="text-base/7 font-semibold text-gray-900">
                  <div className="absolute top-0 left-0 flex size-10 items-center justify-center rounded-lg bg-indigo-600">
                    <feature.icon aria-hidden="true" className="size-6 text-white" />
                  </div>
                  {feature.name}
                </dt>
                <dd className="mt-2 text-base/7 text-gray-600">{feature.description}</dd>
              </div>
            ))}
          </dl>
        </div>
      </div>

      {/* Testimonial section */}
      <div className="mx-auto mt-32 max-w-7xl sm:mt-56 sm:px-6 lg:px-8">
        <div className="relative overflow-hidden bg-gray-900 px-6 py-20 shadow-xl sm:rounded-3xl sm:px-10 sm:py-24 md:px-12 lg:px-20">
          <img
            alt="Students in digital classroom"
            src="https://images.unsplash.com/photo-1501504905252-473c47e087f8?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3"
            className="absolute inset-0 size-full object-cover brightness-150 saturate-0"
          />
          <div className="absolute inset-0 bg-gray-900/90 mix-blend-multiply" />
          <div aria-hidden="true" className="absolute -top-56 -left-80 transform-gpu blur-3xl">
            <div
              style={{
                clipPath:
                  'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
              }}
              className="aspect-[1097/845] w-[68.5625rem] bg-gradient-to-r from-[#6366F1] to-[#4F46E5] opacity-[0.45]"
            />
          </div>

          <div className="relative mx-auto max-w-2xl lg:mx-0">
            <figure>
              <blockquote className="mt-6 text-lg font-semibold text-white sm:text-xl/8">
                <p>
                  "TuitionLance has transformed our academy's operations. We now reach students across the country, our
                  administrative load is reduced, and our teachers can focus on what they do best - teaching."
                </p>
              </blockquote>
              <figcaption className="mt-6 text-base text-white">
                <div className="font-semibold">Priyaranjan Naik</div>
                <div className="mt-1">Principal, Excel Academy</div>
              </figcaption>
            </figure>
          </div>
        </div>
      </div>

      {/* Student features section */}
      <div className="mx-auto mt-32 max-w-7xl px-6 sm:mt-56 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="text-base/7 font-semibold text-indigo-600">For Students</h2>
          <p className="mt-2 text-3xl font-semibold tracking-tight text-pretty text-gray-900 sm:text-4xl lg:text-balance">
            Learn Without Boundaries
          </p>
          <p className="mt-6 text-lg/8 text-pretty text-gray-600">
            Access quality education from anywhere, breaking down geographical barriers while enjoying a personalized
            learning experience.
          </p>
        </div>
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16">
            {studentFeatures.map((feature) => (
              <div key={feature.name} className="relative pl-16">
                <dt className="text-base/7 font-semibold text-gray-900">
                  <div className="absolute top-0 left-0 flex size-10 items-center justify-center rounded-lg bg-indigo-600">
                    <feature.icon aria-hidden="true" className="size-6 text-white" />
                  </div>
                  {feature.name}
                </dt>
                <dd className="mt-2 text-base/7 text-gray-600">{feature.description}</dd>
              </div>
            ))}
          </dl>
        </div>
      </div>

      {/* CTA section */}
      <div className="relative mt-32 px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-semibold tracking-tight text-balance text-gray-900 sm:text-4xl">
            Ready to transform your educational journey?
          </h2>
          <p className="mx-auto mt-6 max-w-xl text-lg/8 text-pretty text-gray-600">
            Join TuitionLance today and experience the perfect blend of traditional teaching values with modern
            technology.
          </p>
          <div className="mt-10 flex items-center justify-center gap-x-6">
            <a
              href="/users/signup"
              className="rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
              Sign Up Now
            </a>
            <a href="/contact" className="text-sm/6 font-semibold text-gray-900">
              Contact Us <span aria-hidden="true">→</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}

import { UUID } from 'crypto'

import { and, eq, isNull } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { profileTable } from '@/server/db/schema/profile-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'

const submitProfileForApprovalDaf = (log: pino.Logger, userId: UUID, profileId: UUID) =>
  withLog(log, 'submitProfileForApprovalDaf', () => {
    const now = utc().toJSDate()
    return db
      .update(profileTable)
      .set({
        submittedForApprovalAt: now,
        updatedAt: now,
        updateRemarks: 'submittedForApproval',
      })
      .where(
        and(
          eq(profileTable.id, profileId),
          eq(profileTable.userId, userId),
          isNull(profileTable.approvedAt),
          isNull(profileTable.submittedForApprovalAt),
        ),
      )
  })

export const submitProfileForApproval = async (c: Ctx, profileId: UUID) => {
  ensureUser(c.user)

  // Submit profile for approval
  const result = await submitProfileForApprovalDaf(c.log, c.user.id, profileId)

  // Ensure profile was found and updated (if not already submitted)
  ensure(result.count > 0, {
    statusCode: 404,
    issueMessage: 'Profile not found, already approved, already submitted for approval, or does not belong to you.',
    logMessage: `User ${c.user.id} attempted to submit profile ${profileId} for approval but it was not found, already approved, already submitted, or does not belong to them.`,
  })
}

import { UUID } from 'crypto'

import { useEffect, useState } from 'react'
import { navigate } from 'vike/client/router'

import { ShowData } from '@/pages/common/ShowData'
import { extractUuid } from '@/shared/common/common-utils.shared'
import { type OmniError } from '@/shared/common/error-utils.shared'
import { $EditCourseAttachmentForm, EditCourseAttachmentForm } from '@/shared/course/course-utils.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'
import { CheckBox } from '@ui/common/form/CheckBox'
import { FormButtons, PageContent, PageLayout, PageSection } from '@ui/common/form/page-layout'
import { useCourseAttachmentQuery, useUpdateCourseAttachmentMutation } from '@ui/courses/common/queries/course-queries'
import { WithProfile } from '@ui/profiles/common/WithProfile'

export const EditAttachmentPage = ({ courseParam, attachmentId }: { courseParam: string; attachmentId: UUID }) => {
  const { data: attachment, isPending, error } = useCourseAttachmentQuery(attachmentId)

  const [formData, setFormData] = useState<EditCourseAttachmentForm>({
    name: '',
    free: false,
  })

  // Update form data when attachment data is loaded
  useEffect(() => {
    if (attachment) {
      setFormData({
        name: attachment.name,
        free: attachment.free,
      })
    }
  }, [attachment])

  const [omniError, setOmniError] = useState<OmniError | undefined>()

  const { mutate: updateAttachment, isPending: updateAttachmentIsPending } = useUpdateCourseAttachmentMutation(
    extractUuid(courseParam),
    attachmentId,
  )

  // Validate form data when it changes
  useEffect(() => {
    const parsed = $EditCourseAttachmentForm.safeParse(formData)
    setOmniError(parsed.error)
  }, [formData])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    updateAttachment(formData, {
      onSuccess: () => {
        void navigate(`/courses/${courseParam}`)
      },
      onError: (error) => {
        setOmniError(error.error)
      },
    })
  }

  return (
    <WithProfile roleAnyOf={ACADEMY_STAFF}>
      <ShowData isPending={isPending} error={error} spinnerSize="1.25rem">
        <PageLayout title="Edit Attachment" maxWidth="max-w-3xl">
          <form onSubmit={handleSubmit}>
            <PageContent>
              <PageSection>
                <div className="space-y-6">
                  <div>
                    <label htmlFor="name" className="form-label">
                      Name
                    </label>
                    <div className="mt-2">
                      <input
                        id="name"
                        value={formData.name}
                        type="text"
                        required
                        className="form-input max-w-lg"
                        placeholder="Enter attachment name"
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      />
                    </div>
                  </div>
                  <div>
                    <CheckBox
                      id="free"
                      label="Free"
                      description="Make this attachment available to all students without enrollment"
                      checked={formData.free}
                      onChange={(e) => setFormData({ ...formData, free: e.target.checked })}
                    />
                  </div>
                </div>
              </PageSection>
            </PageContent>
            <FormButtons
              omniError={omniError}
              onCancel={() => navigate(`/courses/${courseParam}`)}
              isSubmitting={updateAttachmentIsPending}
              submitLabel="Update"
            />
          </form>
        </PageLayout>
      </ShowData>
    </WithProfile>
  )
}

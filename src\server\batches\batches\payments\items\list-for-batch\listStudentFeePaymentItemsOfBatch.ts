import { UUID } from 'crypto'

import { and, asc, desc, eq, gt, lt, or } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfile } from '@/server/common/error/ensure-profile.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { batchTable, studentFeePaymentItemTable, studentFeePaymentTable } from '@/server/db/schema'
import { BatchPaymentsSearch } from '@/shared/batch/batch-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'

import { isStaffOfBatchDaf } from '../../../common/isStaffOfBatchDaf'

const DEFAULT_BATCH_PAYMENTS_PAGE_SIZE = 20

const pageFilter = (search: BatchPaymentsSearch) => {
  const firstOrLastPage = !search.beyondStudentId || !search.beyondCycle // if no beyondStudentId/beyondCycle and `previous` is true, its the last page
  if (firstOrLastPage) return undefined

  // Since beyondStudentId and beyondCycle exist, fromStudentId and fromCycle must also exist (set by frontend)
  const cursorStudentId = search.beyondStudentId!
  const cursorCycle = parseInt(search.beyondCycle!)

  if (search.previous === 'true') {
    // Backward pagination - get rows that come before the current cursor
    // With compound ordering studentId DESC, cycle DESC, get rows with:
    //   1. studentId < cursor.studentId, OR
    //   2. studentId = cursor.studentId AND cycle < cursor.cycle
    return or(
      lt(studentFeePaymentTable.studentId, cursorStudentId),
      and(eq(studentFeePaymentTable.studentId, cursorStudentId), lt(studentFeePaymentItemTable.cycle, cursorCycle)),
    )
  }

  // Forward pagination - get rows that come after the current cursor
  // With compound ordering studentId ASC, cycle ASC, get rows with:
  //   1. studentId > cursor.studentId, OR
  //   2. studentId = cursor.studentId AND cycle > cursor.cycle
  return or(
    gt(studentFeePaymentTable.studentId, cursorStudentId),
    and(eq(studentFeePaymentTable.studentId, cursorStudentId), gt(studentFeePaymentItemTable.cycle, cursorCycle)),
  )
}

const findStudentFeePaymentItemsDaf = (
  log: pino.Logger,
  batchId: UUID,
  search: BatchPaymentsSearch,
  studentId?: UUID,
) => {
  const orderBy =
    search.previous === 'true' ?
      [desc(studentFeePaymentTable.studentId), desc(studentFeePaymentItemTable.cycle)]
    : [asc(studentFeePaymentTable.studentId), asc(studentFeePaymentItemTable.cycle)]

  return withLog(log, 'findStudentFeePaymentItemsDaf', () => {
    return db
      .select({
        studentId: studentFeePaymentTable.studentId,
        billingCycle: batchTable.billingCycle,
        cycle: studentFeePaymentItemTable.cycle,
        currency: studentFeePaymentTable.currency,
        fee: studentFeePaymentItemTable.fee,
        paidAt: studentFeePaymentTable.paidAt,
        status: studentFeePaymentTable.status,
        studentFeePaymentId: studentFeePaymentItemTable.studentFeePaymentId,
      })
      .from(studentFeePaymentItemTable)
      .innerJoin(studentFeePaymentTable, eq(studentFeePaymentItemTable.studentFeePaymentId, studentFeePaymentTable.id))
      .innerJoin(batchTable, eq(studentFeePaymentItemTable.batchId, batchTable.id))
      .where(
        and(
          eq(studentFeePaymentItemTable.batchId, batchId),
          studentId ? eq(studentFeePaymentTable.studentId, studentId) : undefined,
          pageFilter(search),
        ),
      )
      .orderBy(...orderBy)
      .limit(search.pageSize ? parseInt(search.pageSize) : DEFAULT_BATCH_PAYMENTS_PAGE_SIZE)
  })
}

export const listStudentFeePaymentItemsOfBatch = async (c: Ctx, batchId: UUID, search: BatchPaymentsSearch) => {
  ensureUser(c.user)
  ensureProfile(c.profile)
  let payments: Awaited<ReturnType<typeof findStudentFeePaymentItemsDaf>>

  if (c.profile.role === 'student') {
    payments = await findStudentFeePaymentItemsDaf(c.log, batchId, search, c.profile.id)
  } else if (ACADEMY_STAFF.includes(c.profile.role)) {
    const isStaff = await isStaffOfBatchDaf(c.log, batchId, c.profile.id)
    payments = isStaff ? await findStudentFeePaymentItemsDaf(c.log, batchId, search) : []
  } else {
    payments = []
  }

  if (search.previous === 'true') payments.reverse()
  return payments
}

export type StudentFeePaymentItemOfBatch = Awaited<ReturnType<typeof findStudentFeePaymentItemsDaf>>[number]

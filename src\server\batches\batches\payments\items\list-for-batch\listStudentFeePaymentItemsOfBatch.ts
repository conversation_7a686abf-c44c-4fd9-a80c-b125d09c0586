import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfile } from '@/server/common/error/ensure-profile.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { batchTable, studentFeePaymentItemTable, studentFeePaymentTable } from '@/server/db/schema'
import { withLog } from '@/shared/common/withLog.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'

import { isStaffOfBatchDaf } from '../../../common/isStaffOfBatchDaf'

const findStudentFeePaymentItemsDaf = (log: pino.Logger, batchId: UUID, studentId?: UUID) => {
  return withLog(log, 'findStudentFeePaymentItemsDaf', () => {
    return db
      .select({
        studentId: studentFeePaymentTable.studentId,
        billingCycle: batchTable.billingCycle,
        cycle: studentFeePaymentItemTable.cycle,
        currency: studentFeePaymentTable.currency,
        fee: studentFeePaymentItemTable.fee,
        paidAt: studentFeePaymentTable.paidAt,
        status: studentFeePaymentTable.status,
        studentFeePaymentId: studentFeePaymentItemTable.studentFeePaymentId,
      })
      .from(studentFeePaymentItemTable)
      .innerJoin(studentFeePaymentTable, eq(studentFeePaymentItemTable.studentFeePaymentId, studentFeePaymentTable.id))
      .innerJoin(batchTable, eq(studentFeePaymentItemTable.batchId, batchTable.id))
      .where(
        and(
          eq(studentFeePaymentItemTable.batchId, batchId),
          studentId ? eq(studentFeePaymentTable.studentId, studentId) : undefined,
        ),
      )
      .orderBy(studentFeePaymentTable.studentId, studentFeePaymentItemTable.cycle)
  })
}

export const listStudentFeePaymentItemsOfBatch = async (c: Ctx, batchId: UUID) => {
  ensureUser(c.user)
  ensureProfile(c.profile)
  if (c.profile.role === 'student') {
    return findStudentFeePaymentItemsDaf(c.log, batchId, c.profile.id)
  }
  if (ACADEMY_STAFF.includes(c.profile.role)) {
    const isStaff = await isStaffOfBatchDaf(c.log, batchId, c.profile.id)
    return isStaff ? findStudentFeePaymentItemsDaf(c.log, batchId) : []
  }
  return []
}

export type StudentFeePaymentItemOfBatch = Awaited<ReturnType<typeof findStudentFeePaymentItemsDaf>>[number]

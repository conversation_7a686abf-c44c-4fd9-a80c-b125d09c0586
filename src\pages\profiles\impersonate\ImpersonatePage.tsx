import { UUID } from 'crypto'

import { UserIcon } from '@heroicons/react/24/outline'
import { useGoogleLogin } from '@react-oauth/google'
import clsx from 'clsx'
import { FormEvent, useEffect, useMemo, useState } from 'react'
import { navigate } from 'vike/client/router'

import { Protected } from '@/pages/users/auth/Protected'
import { OmniError } from '@/shared/common/error-utils.shared'
import { googleOauthScopes } from '@/shared/users/googleOauthScopes.shared'
import { $SignInForm } from '@/shared/users/user-utils.shared'
import { ShowErrors } from '@ui/common/ShowErrors'
import { getMyError } from '@ui/common/utils/error-utils.ui'
import { useSignInMutation } from '@ui/users/common/user-queries'

import { setAuth } from '../../users/auth/auth-token-store'

export const ImpersonatePage = () => {
  const oauthState = useMemo(() => crypto.randomUUID(), [])
  const [forUserId, setForUserId] = useState<UUID | ''>('')

  const [omniError, setOmniError] = useState<OmniError | undefined>()
  const { mutate: signIn, isPending: signInIsPending } = useSignInMutation()

  useEffect(() => {
    const parsed = $SignInForm.pick({ forUserId: true }).safeParse({ forUserId })
    setOmniError(parsed.error)
  }, [forUserId])

  const handleOnGoogleLoginSuccess = async (response: { code: string; state?: string }) => {
    if (oauthState !== response.state) {
      setOmniError(getMyError('State mismatch. Please refresh page and try again.'))
      return
    }
    signIn(
      { forUserId: forUserId as UUID, code: response.code },
      {
        onSuccess: (token) => {
          setAuth(token)
          void navigate('/')
        },
        onError: (error) => {
          setOmniError(error.error)
        },
      },
    )
  }

  const login = useGoogleLogin({
    onSuccess: handleOnGoogleLoginSuccess,
    onError: (error) => {
      setOmniError(getMyError(error.error_description ?? 'Error signing in with Google'))
    },
    flow: 'auth-code',
    scope: googleOauthScopes.join(' '),
    state: oauthState,
  })

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    login()
  }

  return (
    <Protected>
      <div className="flex min-h-full flex-1 flex-col justify-center px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-lg">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-indigo-100 mb-4">
              <UserIcon className="h-8 w-8 text-indigo-600" aria-hidden="true" />
            </div>
            <h2 className="text-2xl font-bold tracking-tight text-gray-900">Impersonate User</h2>
            <p className="mt-2 text-sm text-gray-600">
              Enter the user ID you want to impersonate and sign in with Google
            </p>
          </div>
          <ShowErrors error={omniError} />
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-lg">
          <div className="bg-white ring-1 shadow-sm ring-gray-900/5 sm:rounded-xl">
            <form onSubmit={handleSubmit} className="px-6 py-8 sm:px-8">
              <div className="space-y-6">
                <div>
                  <label htmlFor="otherUserId" className="block text-sm/6 font-medium text-gray-900">
                    User ID to impersonate
                  </label>
                  <div className="mt-2">
                    <input
                      id="otherUserId"
                      name="otherUserId"
                      type="text"
                      placeholder="Enter user ID here"
                      className="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                      value={forUserId}
                      onChange={(e) => setForUserId(e.target.value as UUID)}
                    />
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    This will allow you to temporarily access the system as this user
                  </p>
                  <ShowErrors error={omniError} path={['forUserId']} />
                </div>

                <div className="pt-4">
                  <button
                    type="submit"
                    className={clsx(
                      'flex w-full justify-center rounded-md bg-indigo-600 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-colors',
                      {
                        'opacity-50 cursor-wait': signInIsPending,
                      },
                    )}
                    disabled={signInIsPending}
                  >
                    {signInIsPending ? 'Signing in...' : 'Sign In with Google'}
                  </button>
                </div>

                <div className="flex items-center my-4">
                  <div className="flex-grow border-t border-gray-200"></div>
                  <div className="px-3 text-xs text-gray-500">Important</div>
                  <div className="flex-grow border-t border-gray-200"></div>
                </div>

                <div className="text-xs text-gray-500 text-center">
                  Impersonating another user is logged and monitored. Only use this feature for authorized support or
                  testing purposes.
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Protected>
  )
}

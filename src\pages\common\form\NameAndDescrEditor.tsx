import { RichTextEditor } from '@/pages/common/rte/RichTextEditor'
import { type OmniError } from '@/shared/common/error-utils.shared'
import { PageSection } from '@ui/common/form/page-layout'
import { ShowErrors } from '@ui/common/ShowErrors'

type NameAndDescrEditorProps = {
  name: string
  nameLabel?: string
  namePlaceholder?: string
  nameMaxLen: number
  descr: string
  descrLabel?: string
  descrMaxLen: number
  omniError: OmniError | undefined
  onNameChange: (value: string) => void
  onDescrChange: (value: string) => void
}

export const NameAndDescrEditor = ({
  name,
  descr,
  omniError,
  onNameChange,
  onDescrChange,
  nameLabel,
  namePlaceholder,
  nameMaxLen,
  descrLabel,
  descrMaxLen,
}: NameAndDescrEditorProps) => (
  <PageSection>
    <div className="space-y-8">
      <div>
        <label htmlFor="name" className="form-label">
          {nameLabel ?? 'Name'}
        </label>
        <div className="mt-2">
          <input
            id="name"
            value={name}
            type="text"
            required
            className="form-input max-w-lg"
            placeholder={namePlaceholder}
            aria-describedby="name-description"
            onChange={(e) => onNameChange(e.target.value)}
          />
        </div>
        <p id="name-description" className="form-descr">
          Maximum {nameMaxLen} characters.
        </p>
        <ShowErrors error={omniError} path={['name']} />
        <ShowErrors error={omniError} path={['displayName']} />
      </div>

      {/* Description Field */}
      <div>
        <label htmlFor="descr" className="form-label">
          {descrLabel ?? 'Description'}
        </label>
        <div className="mt-2">
          <RichTextEditor value={descr} onChange={onDescrChange} />
        </div>
        <p id="descr-description" className="form-descr">
          Maximum {descrMaxLen} characters.
        </p>
        <ShowErrors error={omniError} path={['descr']} />
      </div>
    </div>
  </PageSection>
)

import { UUID } from 'crypto'

import { useEffect, useState } from 'react'
import { navigate } from 'vike/client/router'
import { usePageContext } from 'vike-react/usePageContext'

import { ShowData } from '@/pages/common/ShowData'
import { ProfileCommonEditor } from '@/pages/profiles/common/ProfileCommonEditor'
import { ProfileHeader } from '@/pages/profiles/common/ProfileHeader'
import { Protected } from '@/pages/users/auth/Protected'
import { type OmniError } from '@/shared/common/error-utils.shared'
import { html2Markdown } from '@/shared/common/markdown-utils.shared'
import { type EditProfileForm, $EditProfileForm, profilePath } from '@/shared/profiles/profile-utils.shared'
import { ErrorAlert } from '@ui/common/alerts/ErrorAlert'
import { FormButtons, PageContent, PageLayout } from '@ui/common/form/page-layout'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'
import { useProfileSuspenseQuery, useUpdateProfileMutation } from '@ui/profiles/common/profile-queries'

export const EditProfilePage = () => {
  // Get profile ID from route params
  const { routeParams } = usePageContext()
  const profileId = routeParams.id as UUID

  // Query profile data
  const { data: profile, error: profileError } = useProfileSuspenseQuery(profileId)
  const { currentProfile, profileIsPending, profileError: currentProfileError } = useCurrentProfile()

  // Form state
  const [formData, setFormData] = useState<Required<EditProfileForm>>({
    displayName: profile?.displayName ?? '',
    descr: profile?.descr ? html2Markdown(profile.descr) : '',
  })

  // Error state
  const [omniError, setOmniError] = useState<OmniError | undefined>()

  // Update mutation
  const { mutate: updateProfile, isPending: updateProfileIsPending } = useUpdateProfileMutation(profileId)

  // Validate form data when it changes
  useEffect(() => {
    const parsed = $EditProfileForm.safeParse(formData)
    setOmniError(parsed.error)
  }, [formData])

  // Form submission handler
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    updateProfile(formData, {
      onSuccess: () => {
        void navigate(profilePath({ id: profileId, displayName: formData.displayName, role: profile!.role }))
      },
      onError: (error) => {
        setOmniError(error.error)
      },
    })
  }

  if (currentProfile?.id !== profileId) return <ErrorAlert>You can not edit profile of others!</ErrorAlert>

  return (
    <Protected>
      <ShowData isPending={profileIsPending} error={currentProfileError || profileError} spinnerSize="1.25rem">
        <div>
          <div className="px-4">
            <ProfileHeader profile={profile} />
          </div>
          <PageLayout>
            <form onSubmit={handleSubmit}>
              <PageContent>
                <div>
                  <ProfileCommonEditor
                    displayName={formData.displayName}
                    descr={formData.descr}
                    omniError={omniError}
                    onDisplayNameChange={(value) => setFormData((prev) => ({ ...prev, displayName: value }))}
                    onDescrChange={(value) => setFormData((prev) => ({ ...prev, descr: value }))}
                  />
                  <FormButtons
                    omniError={omniError}
                    onCancel={() =>
                      navigate(profilePath({ id: profileId, displayName: formData.displayName, role: profile!.role }))
                    }
                    isSubmitting={updateProfileIsPending}
                  />
                </div>
              </PageContent>
            </form>
          </PageLayout>
        </div>
      </ShowData>
    </Protected>
  )
}

import { UUID } from 'crypto'

import { eq, and, desc } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { appendCommandDaf } from '@/server/common/command/processCommands'
import { env } from '@/server/common/env.server'
import { ensure } from '@/server/common/error/ensure.server'
import { generateUploadURL, s3Flock } from '@/server/common/s3/s3-utils.server'
import { nextBetween } from '@/server/common/str-math/str-math.server'
import { db, Transaction } from '@/server/db/db'
import { courseAttachmentTable } from '@/server/db/schema/course-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'
import {
  AddCourseAttachmentForm,
  courseAttachmentS3Path,
  COURSE_ATTACHMENT_MAX_POSITION,
  COURSE_ATTACHMENT_MIN_POSITION,
} from '@/shared/course/course-utils.shared'

import { ensureCanUpdateCourse } from '../../common/ensureCanUpdateCourse'
import { RemoveS3ObjectCommandData, REMOVE_S3OBJECT_COMMAND } from '../remove/removeS3ObjectCommand'

const findCourseAttachmentDaf = async (log: pino.Logger, courseId: UUID, name: string) =>
  withLog(log, 'findCourseAttachmentDaf', () =>
    db
      .select({
        id: courseAttachmentTable.id,
        createdAt: courseAttachmentTable.createdAt,
        uploaded: courseAttachmentTable.uploaded,
      })
      .from(courseAttachmentTable)
      .where(and(eq(courseAttachmentTable.courseId, courseId), eq(courseAttachmentTable.name, name))),
  )

export const findLastAttachmentDaf = async (log: pino.Logger, courseId: UUID) =>
  withLog(log, 'findLastAttachmentPositionDaf', () =>
    db
      .select({ position: courseAttachmentTable.position })
      .from(courseAttachmentTable)
      .where(eq(courseAttachmentTable.courseId, courseId))
      .orderBy(desc(courseAttachmentTable.position))
      .limit(1),
  )

const insertCourseAttachmentDaf = async (
  log: pino.Logger,
  courseId: UUID,
  form: AddCourseAttachmentForm,
  position: string,
) => {
  const id = crypto.randomUUID()
  await withLog(log, 'insertCourseAttachmentDaf', () =>
    db.insert(courseAttachmentTable).values({
      id,
      courseId,
      name: form.name,
      contentType: form.contentType,
      sizeBytes: form.sizeBytes,
      free: form.free,
      uploaded: false,
      createdAt: utc().toJSDate(),
      position,
    }),
  )
  return id
}

const removeCourseAttachmentDaf = async (log: pino.Logger, db: Transaction, attachmentId: UUID) =>
  withLog(log, 'removeCourseAttachmentDaf', () =>
    db.delete(courseAttachmentTable).where(eq(courseAttachmentTable.id, attachmentId)),
  )

export const addCourseAttachment = async (c: Ctx, courseId: UUID, form: AddCourseAttachmentForm) => {
  await ensureCanUpdateCourse(c, courseId)
  const [attachment] = await findCourseAttachmentDaf(c.log, courseId, form.name)
  if (attachment) {
    const createdSufficientlyAgo = attachment.createdAt < utc().minus({ minutes: 3 }).toJSDate()
    const previousUploadFailed = createdSufficientlyAgo && !attachment.uploaded
    ensure(previousUploadFailed, {
      statusCode: 409,
      issueMessage: 'Attachment with same name already exists or being uploaded',
      logMessage: `Attachment for course ${courseId} with name ${form.name} already exists or being uploaded`,
    })
    await db.transaction(async (tx) => {
      await removeCourseAttachmentDaf(c.log, tx, attachment.id)
      await appendCommandDaf<RemoveS3ObjectCommandData>(tx, c.log, s3Flock(), {
        command: REMOVE_S3OBJECT_COMMAND,
        data: {
          path: courseAttachmentS3Path(env.APP_ENV, attachment.id),
        },
      })
    })
  }

  // Get last position and calculate new position
  const [lastAttachment] = await findLastAttachmentDaf(c.log, courseId)
  const lastPosition = lastAttachment?.position ?? COURSE_ATTACHMENT_MIN_POSITION
  const position = nextBetween(lastPosition, COURSE_ATTACHMENT_MAX_POSITION)

  const id = await insertCourseAttachmentDaf(c.log, courseId, form, position)
  const url = await generateUploadURL(courseAttachmentS3Path(env.APP_ENV, id), form.contentType, form.free)
  return { id, url }
}

export type CourseAttachmentUrl = Awaited<ReturnType<typeof addCourseAttachment>>

import { UUID } from 'crypto'

import { eq, sql } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensure } from '@/server/common/error/ensure.server'
import { db } from '@/server/db/db'
import { academyTable } from '@/server/db/schema/academy-schema'
import { EditAcademyForm } from '@/shared/academies/academy-utils.shared'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { markdown2Html } from '@/shared/common/markdown-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { ensureGoodStaff } from '../common/ensureGoodStaff'

const updateAcademyDaf = (log: pino.Logger, academyId: UUID, form: EditAcademyForm) =>
  withLog(log, 'updateAcademyDaf', () =>
    db
      .update(academyTable)
      .set({
        name: form.name,
        email: form.email,
        emailVerified: sql`${academyTable.emailVerified} AND ${form.email} = ${academyTable.email}`,
        mobileCountryCode: form.mobileCountryCode,
        mobile: form.mobile,
        mobileVerified: sql`${academyTable.mobileVerified} AND ${form.mobileCountryCode} = ${academyTable.mobileCountryCode} AND ${form.mobile} = ${academyTable.mobile}`,
        descr: form.descr ? markdown2Html(form.descr) : null,
        currency: form.currency,
        upiId: form.upiId,
        tradeName: form.tradeName || null,
        gstin: form.gstin || null,
        districtId: form.districtId,
        pincode: form.pincode,
        area: form.area,
        addressLine1: form.addressLine1 || null,
        addressLine2: form.addressLine2 || null,
        updatedAt: utc().toJSDate(),
      })
      .where(eq(academyTable.id, academyId)),
  )

export const updateAcademy = async (c: Ctx, academyId: UUID, form: EditAcademyForm) => {
  await ensureGoodStaff(c, academyId)

  // Update academy
  const result = await updateAcademyDaf(c.log, academyId, form)

  // Ensure academy was found and updated
  ensure(result.count > 0, {
    statusCode: 404,
    issueMessage: 'Academy not found.',
    logMessage: `Academy ${academyId} not found for update`,
  })
}

import { UUID } from 'crypto'

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import { VerifyMobileForm, bool2str } from '@/shared/common/common-utils.shared'
import { SignInForm, UserForm } from '@/shared/users/user-utils.shared'
import { api } from '@ui/api-client'
import { clearAuth, useUserId } from '@ui/users/auth/auth-token-store'

import type { UserData } from '@/server/users/get-user/getUser.server'

const userKeys = {
  all: ['user'] as const,
  user: (id: UUID | null) => [...userKeys.all, id] as const,
  detail: (id: UUID | null) => [...userKeys.user(id), 'detail'] as const,
  detailFull: (id: UUID | null, full: boolean) => [...userKeys.detail(id), full] as const,
  tncs: (id: UUID | null) => [...userKeys.user(id), 'tncs'] as const,
}

export const useGetUserTnCsQuery = () => {
  const userId = useUserId()

  return useQuery({
    queryKey: userKeys.tncs(userId),
    queryFn: () =>
      api()
        .users.tncs.$get()
        .then((res) => res.json())
        .then((json) => json.rows),
  })
}

export const useUserQuery = (full: boolean = false) => {
  const userId = useUserId()

  return useQuery({
    queryKey: userKeys.detailFull(userId, full),
    // eslint-disable-next-line @tanstack/query/no-void-query-fn
    queryFn: () =>
      api()
        .users.me.$get({ query: { full: bool2str(full) } })
        .then((res) => res.json()) as Promise<UserData>,
    enabled: !!userId,
  })
}

export const useSignupMutation = () => {
  return useMutation({
    mutationFn: (form: UserForm) =>
      api()
        .users.signup.$post({ json: form })
        .then((res) => res.json()),
  })
}

export const useSignInMutation = () => {
  return useMutation({
    mutationFn: (form: SignInForm) =>
      api()
        .users.signin.$post({ json: form })
        .then((res) => res.json()),
  })
}

export const useSendUserMobileOtpMutation = () => {
  return useMutation({
    mutationFn: () => api().users.me['mobile-otp'].$post(),
  })
}

export const useVerifyUserMobileMutation = () => {
  const userId = useUserId()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (json: VerifyMobileForm) =>
      api().users.me['mobile-verification'].$put({
        json,
      }),
    onSuccess: () => {
      void queryClient.invalidateQueries({
        queryKey: userKeys.detail(userId),
      })
    },
  })
}

export const useUpdateUserMutation = () => {
  const userId = useUserId()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (json: UserForm) => api().users.me.$put({ json }),
    onSuccess: () => {
      void queryClient.invalidateQueries({
        queryKey: userKeys.user(userId),
      })
    },
  })
}

export const useInvalidateTokensMutation = () => {
  return useMutation({
    mutationFn: () => api().users.me['invalidate-tokens'].$post(),
    onSuccess: () => {
      clearAuth()
    },
  })
}

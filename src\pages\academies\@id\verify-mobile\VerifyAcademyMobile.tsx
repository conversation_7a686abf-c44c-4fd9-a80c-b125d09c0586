import { UseMutationResult } from '@tanstack/react-query'

import { AcademyDetail } from '@/server/academies/get/getAcademy.server'
import { academyPath } from '@/shared/academies/academy-utils.shared'
import { useSendAcademyMobileOtpMutation, useVerifyAcademyMobileMutation } from '@ui/academies/common/academy-queries'
import { SuccessAlert } from '@ui/common/alerts/SuccessAlert'
import { VerifyMobile } from '@ui/common/sms/VerifyMobile'

export const VerifyAcademyMobile = ({ academy, academyMobile }: { academy: AcademyDetail; academyMobile: string }) => {
  const sendOtpMutation = useSendAcademyMobileOtpMutation(academy.id)
  const verifyMutation = useVerifyAcademyMobileMutation(academy.id)

  if (academy.mobileVerified) {
    return (
      <SuccessAlert>
        Academy's mobile number {academyMobile} is verified!{' '}
        <a className="link" href={academyPath(academy)}>
          Click here
        </a>{' '}
        to visit academy.
      </SuccessAlert>
    )
  }

  return (
    <VerifyMobile
      title="Verify Academy Mobile"
      description={`Let's verify academy WhatsApp number ${academyMobile}`}
      sendOtpMutation={sendOtpMutation as UseMutationResult}
      verifyMutation={verifyMutation as UseMutationResult}
    />
  )
}

import { useEffect, useState } from 'react'

export const useTimer = (): number => {
  const [timer, setTimer] = useState<number>(0)

  useEffect(() => {
    const interval = window.setInterval(() => {
      setTimer((prevTimer) => prevTimer + 1)
    }, 1000)

    // Cleanup function that runs when component unmounts
    return () => {
      window.clearInterval(interval)
    }
  }, []) // Empty dependency array means this effect runs once on mount

  return timer
}

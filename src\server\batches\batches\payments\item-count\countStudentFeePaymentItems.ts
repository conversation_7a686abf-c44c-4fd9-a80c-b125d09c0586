import { UUID } from 'crypto'

import { and, count, eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfileWithRole } from '@/server/common/error/ensure-profile.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { studentFeePaymentItemTable, studentFeePaymentTable } from '@/server/db/schema'
import { withLog } from '@/shared/common/withLog.shared'

const countStudentFeePaymentItemsDaf = (log: pino.Logger, studentId: UUID) => {
  return withLog(log, 'countStudentFeePaymentItemsDaf', () => {
    return db
      .select({ count: count() })
      .from(studentFeePaymentItemTable)
      .innerJoin(studentFeePaymentTable, eq(studentFeePaymentItemTable.studentFeePaymentId, studentFeePaymentTable.id))
      .where(and(eq(studentFeePaymentTable.studentId, studentId), eq(studentFeePaymentTable.status, 'draft')))
  })
}

export const countStudentFeePaymentItems = async (c: Ctx) => {
  ensureUser(c.user)
  ensureProfileWithRole('student', c.profile)
  return countStudentFeePaymentItemsDaf(c.log, c.profile.id).then((r) => r[0])
}

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { removeBatchStudentPaymentItem } from './removeStudentFeePaymentItem'

export const removeStudentFeePaymentItemHandler = new Hono<HonoVars>().delete(
  '/',
  zValidator('param', $HasId),
  async (c) => {
    const { id: studentFeePaymentItemId } = c.req.valid('param')
    await removeBatchStudentPaymentItem(getCtx(c), studentFeePaymentItemId)
    return c.body(null, 204)
  },
)

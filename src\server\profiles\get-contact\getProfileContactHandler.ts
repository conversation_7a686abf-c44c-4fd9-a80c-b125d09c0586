import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { getProfileContact } from './getProfileContact'

export const getProfileContactHandler = new Hono<HonoVars>().get('/', zValidator('param', $HasId), async (c) => {
  const { id } = c.req.valid('param')
  const profile = await getProfileContact(getCtx(c), id)
  return c.json(profile, 200)
})

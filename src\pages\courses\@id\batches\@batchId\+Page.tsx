import { UUID } from 'crypto'

import { usePageContext } from 'vike-react/usePageContext'

import { extractUuid } from '@/shared/common/common-utils.shared'

import { BatchPage } from './BatchPage'

export default () => {
  const pageContext = usePageContext()
  const courseId = extractUuid(pageContext.routeParams.id)
  const batchId = pageContext.routeParams.batchId as UUID

  return <BatchPage courseId={courseId} batchId={batchId} />
}

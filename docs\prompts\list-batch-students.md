Let's create an endpoint `GET /batch-students?batchId={UUID}&studentId={UUID}` for listing batch-student from `src\server\db\schema\batch-schema.ts`. The endpoint should be coded in `src\server\batches\batch-students\list` folder, strictly following the existig patterns for coding endpoints. Use 'fs-mcp' MCP server to understand the source code patterns thoroughly.

Either batchId or studentId must be provided as query parameter. Both can also be provided. `$BatchStudentsSearch` schema in `src\shared\batch\batch-utils.shared.ts` is already coded for the purpose.

The list should be rttuurned only if the logged in profile is either of
1. A student of the given batchId
1. A staff of the academy the course of the batch belongs to
1. the given studentId matches the logged in profileId

Once the endpoint is created, add it to `src\server\api-routes.server.ts`, and then to `src\pages\courses\@id\batches\common\batch-queries.ts`.

No need to code tests ATM, we do that in a later round.

Note that we are using Windows machine. E.g., you can't use unix commands like `cat`. 

If you have any questions or need any help tracing existing patterns, please ask!
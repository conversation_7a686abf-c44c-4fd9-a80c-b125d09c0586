import { UUID } from 'crypto'

import { z } from 'zod'

import { $Date, $Money, $Time, $UUID, $OptionalEmail, $PageSearch } from '@/shared/common/common-utils.shared'

import { WeekDay, weekDays } from '../common/date-utils.shared'
import { $PaymentStatus, PAYMENT_STATUSES } from '../common/payment-utils/payment-utils.shared'
import { coursePath } from '../course/course-utils.shared'

export const MAX_SESSION_DURATION_MINUTES = 300
export const MAX_SEAT_COUNT = 100
export const MAX_PAID_REMARKS_LENGTH = 100

const feeLimits = {
  INR: {
    min: 200,
    max: 100000,
  },
  USD: {
    min: 10,
    max: 2000,
  },
} as const

export const $WeekDay = z.enum(Object.keys(weekDays) as [WeekDay, ...WeekDay[]])

export const $BillingCycle = z.enum(['months', 'weeks', 'days'])
export type BillingCycle = z.infer<typeof $BillingCycle>

export const billingCycles = {
  months: {
    singular: 'month',
  },
  weeks: {
    singular: 'week',
  },
  days: {
    singular: 'day',
  },
} as const

export const BILLING_CYCLES = Object.keys(billingCycles) as BillingCycle[]

export const $EditBatchForm = z.object({
  teacherId: $UUID,
  startDate: $Date,
  timezone: z.string().trim().min(1).max(50),
  billingCycle: $BillingCycle,
  cycleCount: z.number().min(1).max(12),
  graceDays: z.number().min(0).max(5),
  seatCount: z.number().min(1).max(MAX_SEAT_COUNT),
  admissionOpen: z.boolean(),

  fee: $Money
    .refine(
      (money) => money.amount >= feeLimits[money.currency].min,
      (money) => ({
        message: `Fee must be at least ${feeLimits[money.currency].min} ${money.currency}`,
      }),
    )
    .refine(
      (money) => money.amount <= feeLimits[money.currency].max,
      (money) => ({
        message: `Fee must be at most ${feeLimits[money.currency].max} ${money.currency}`,
      }),
    ),
})
export type EditBatchForm = z.infer<typeof $EditBatchForm>

export const $AddBatchForm = $EditBatchForm.extend({
  batchId: $UUID,
  courseId: $UUID,
})
export type AddBatchForm = z.infer<typeof $AddBatchForm>

export const $EditBatchEventForm = z.object({
  days: z
    .array($WeekDay)
    .min(1)
    .max(7)
    .transform((days) => [...new Set(days)]), // remove duplicates
  at: $Time,
  durationMinutes: z.number().min(1).max(MAX_SESSION_DURATION_MINUTES),
})
export type EditBatchEventForm = z.infer<typeof $EditBatchEventForm>

const $EventType = z.enum(['meet']) // 'zoom' is not supported yet
export type EventType = z.infer<typeof $EventType>

export const $AddBatchEventForm = $EditBatchEventForm.extend({
  id: $UUID,
  batchId: $UUID,
  eventType: $EventType,
})
export type AddBatchEventForm = z.infer<typeof $AddBatchEventForm>

export const MAX_BATCHES_PER_COURSE = 1000

export const batchPath = (course: { id: UUID; name: string }, batchId: string) =>
  `${coursePath(course)}/batches/${batchId}`

export const $AddBatchStudentPaymentForm = z.object({
  cycle: z.number().min(1),
})
export type AddBatchStudentPaymentForm = z.infer<typeof $AddBatchStudentPaymentForm>

export const $PayStudentFeePaymentForm = z.object({
  paidRemarks: z.string().max(MAX_PAID_REMARKS_LENGTH).optional(),
})
export type PayStudentFeePaymentForm = z.infer<typeof $PayStudentFeePaymentForm>

export const $StudentFeePaymentOfAcademySearch = z.object({
  statuses: z.union([$PaymentStatus, z.array($PaymentStatus).min(1).max(PAYMENT_STATUSES.length)]).transform((val) => {
    return Array.isArray(val) ? val : [val]
  }),
  studentEmail: $OptionalEmail,
})

export type StudentFeePaymentOfAcademySearch = z.infer<typeof $StudentFeePaymentOfAcademySearch>

export const $AddBatchAttachmentForm = z.object({
  attachmentId: $UUID,
})
export type AddBatchAttachmentForm = z.infer<typeof $AddBatchAttachmentForm>

export const $CourseBatchesSearch = $PageSearch.extend({
  fromStartDate: $Date.optional(), // for cursor-based pagination
  fromCreatedAt: z.string().datetime().optional(), // for cursor-based pagination
  beyondId: $UUID.optional(), // for cursor-based pagination
})

export type CourseBatchesSearch = z.infer<typeof $CourseBatchesSearch>

export const $StudentFeePaymentsSearch = $PageSearch.extend({
  fromAcademyId: $UUID.optional(), // for cursor-based pagination
  fromCreatedAt: z.string().datetime().optional(), // for cursor-based pagination
  beyondId: $UUID.optional(), // for cursor-based pagination
})

export type StudentFeePaymentsSearch = z.infer<typeof $StudentFeePaymentsSearch>

import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Logger } from 'pino'
import { z } from 'zod'

import { ContextProfile, ContextUser } from '@/server/common/auth-context-types.server'

export type HonoVars = {
  Bindings: Record<string, unknown>
  Variables: {
    log: Logger
    user?: ContextUser
    profile?: ContextProfile
  }
}

export const queryFull = zValidator('query', z.object({ full: z.enum(['true', 'false']).optional() }))

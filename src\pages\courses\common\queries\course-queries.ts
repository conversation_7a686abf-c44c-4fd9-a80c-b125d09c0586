import { UUID } from 'crypto'

import { useMutation, useQuery, useQueryClient, useSuspenseQuery } from '@tanstack/react-query'
import { useDeferredValue } from 'react'

import { CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { CourseItem } from '@/server/courses/courses/list/listCourses.server'
import { bool2str, isClient } from '@/shared/common/common-utils.shared'
import { S3_DOWNLOAD_URL_EXPIRY_SECONDS } from '@/shared/common/s3-utils.shared'
import { useServerQuery } from '@/shared/common/serverQueries.shared'
import {
  AddCourseAttachmentForm,
  AddCourseForm,
  CoursesSearch,
  EditCourseAttachmentForm,
  EditCourseForm,
  MoveCourseAttachmentForm,
} from '@/shared/course/course-utils.shared'
import { api, withError } from '@ui/api-client'
import { showNotification } from '@ui/common/notification/notification-store'
import { useInvalidateDetail } from '@ui/common/query-helper'
import { dateConverter } from '@ui/common/utils/dateConverter'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

// Define date fields once to maintain consistency
const courseDateFields = ['publishedAt']

export const courseKeys = {
  all: ['course'] as const,
  lists: () => [...courseKeys.all, 'list'] as const,
  list: (viewerId: UUID | undefined, search: CoursesSearch) => [...courseKeys.lists(), viewerId, search] as const,
  details: () => [...courseKeys.all, 'detail'] as const,
  detail: (id: UUID) => [...courseKeys.details(), id] as const,
  detailFull: (id: UUID, full: boolean, viewerId: UUID | undefined) =>
    [...courseKeys.detail(id), full, viewerId] as const,
  attachment: (id: UUID) => ['course-attachment', id] as const,
  attachmentDetail: (id: UUID) => [...courseKeys.attachment(id), 'detail'] as const,
  attachmentUrl: (id: UUID) => [...courseKeys.attachment(id), 'url'] as const,
} as const

export const useAddCourseMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (form: AddCourseForm) =>
      api()
        .courses.$post({ json: form })
        .then((res) => res.json()),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: courseKeys.lists() })
    },
  })
}

export const useCourseSuspenseQuery = (id: UUID, full = false) => {
  const { currentProfile } = useCurrentProfile()
  const getCourse = useServerQuery('getCourse')
  const { data } = useSuspenseQuery({
    queryKey: courseKeys.detailFull(id, full, currentProfile?.id), // viewing unpublishes courses and restricted attachments depend on current profile
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .courses[':id'].$get({ param: { id }, query: { full: bool2str(full) } })
            .then((res) => res.json())
            .then(dateConverter<CourseWithoutDescr>(courseDateFields)),
        )
      : getCourse(id, full),
  })
  return useDeferredValue(data)
}

export const useCoursesSuspenseQuery = (search: CoursesSearch) => {
  const { currentProfile } = useCurrentProfile()
  const listCourses = useServerQuery('listCourses')
  const { data } = useSuspenseQuery({
    queryKey: courseKeys.list(currentProfile?.id, search),
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .courses.$get({ query: search })
            .then((res) => res.json())
            .then((courses) => courses.rows)
            .then(dateConverter<CourseItem[]>(courseDateFields)),
        )
      : listCourses(search, currentProfile?.id),
  })
  return useDeferredValue(data)
}

export const useUpdateCourseMutation = (id: UUID) => {
  const invalidateCourse = useInvalidateDetail(courseKeys)

  return useMutation({
    mutationFn: (form: EditCourseForm) => api().courses[':id'].$put({ param: { id }, json: form }),
    onSuccess: () => {
      invalidateCourse(id)
    },
  })
}

export const usePublishCourseMutation = (id: UUID) => {
  const invalidateCourse = useInvalidateDetail(courseKeys)

  return useMutation({
    mutationFn: () => api().courses[':id'].publish.$put({ param: { id } }),
    onSuccess: () => {
      invalidateCourse(id)
    },
  })
}

export const useAddTagToCourseMutation = (id: UUID) => {
  const invalidateCourse = useInvalidateDetail(courseKeys)
  return useMutation({
    mutationFn: (tagId: UUID) => api().courses[':id'].tags.$post({ param: { id }, json: { tagId } }),
    onSuccess: () => {
      invalidateCourse(id)
    },
  })
}

export const useRemoveTagFromCourseMutation = (id: UUID) => {
  const invalidateCourse = useInvalidateDetail(courseKeys)

  return useMutation({
    mutationFn: (tagId: UUID) => api().courses[':id'].tags[':tagId'].$delete({ param: { id, tagId } }),
    onSuccess: () => {
      invalidateCourse(id)
    },
  })
}

export const useAddCourseAttachmentMutation = (courseId: UUID) => {
  return useMutation({
    mutationFn: (json: AddCourseAttachmentForm) =>
      api()
        .courses[':id'].attachments.$post({ param: { id: courseId }, json })
        .then((res) => res.json()),
  })
}

export const useMarkUploadedCourseAttachmentMutation = (courseId: UUID) => {
  const invalidateCourse = useInvalidateDetail(courseKeys)
  return useMutation({
    mutationFn: (attachmentId: UUID) =>
      api()['course-attachments'][':id'].uploaded.$put({ param: { id: attachmentId } }),
    onSuccess: () => {
      invalidateCourse(courseId)
    },
    onError: (error) => {
      showNotification({
        heading: 'Error marking attachment as uploaded',
        message: getErrorMessage(error),
        type: 'error',
      })
    },
  })
}

export const useRemoveCourseAttachmentMutation = (courseId: UUID) => {
  const invalidateCourse = useInvalidateDetail(courseKeys)
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (attachmentId: UUID) => api()['course-attachments'][':id'].$delete({ param: { id: attachmentId } }),
    onSuccess: (_, attachmentId: UUID) => {
      invalidateCourse(courseId)
      void queryClient.removeQueries({ queryKey: courseKeys.attachment(attachmentId) })
    },
    onError: (error) => {
      showNotification({
        heading: 'Error removing attachment',
        message: getErrorMessage(error),
        type: 'error',
      })
    },
  })
}

export const useUpdateCourseAttachmentMutation = (courseId: UUID, attachmentId: UUID) => {
  const invalidateCourse = useInvalidateDetail(courseKeys)
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (form: EditCourseAttachmentForm) =>
      api()['course-attachments'][':id'].$put({ param: { id: attachmentId }, json: form }),
    onSuccess: () => {
      invalidateCourse(courseId)
      void queryClient.invalidateQueries({ queryKey: courseKeys.attachmentDetail(attachmentId) })
    },
  })
}

export const useMoveCourseAttachmentMutation = (courseId: UUID, attachmentId: UUID) => {
  const invalidateCourse = useInvalidateDetail(courseKeys)
  return useMutation({
    mutationFn: (form: MoveCourseAttachmentForm) =>
      api()['course-attachments'][':id'].position.$put({ param: { id: attachmentId }, json: form }),
    onSuccess: () => {
      invalidateCourse(courseId)
    },
  })
}

export const useCourseAttachmentQuery = (attachmentId: UUID) => {
  return useQuery({
    queryKey: courseKeys.attachmentDetail(attachmentId),
    queryFn: () =>
      api()
        ['course-attachments'][':id'].$get({ param: { id: attachmentId } })
        .then((res) => res.json()),
  })
}

export const useCourseAttachmentUrlQuery = (attachmentId: UUID, enabled: boolean) => {
  return useQuery({
    queryKey: courseKeys.attachmentUrl(attachmentId),
    queryFn: () =>
      api()
        ['course-attachments'][':id'].url.$get({ param: { id: attachmentId } })
        .then((res) => res.json()),
    staleTime: S3_DOWNLOAD_URL_EXPIRY_SECONDS,
    enabled,
  })
}

import { UseMutationResult } from '@tanstack/react-query'
import { useMemo } from 'react'

import { ShowData } from '@/pages/common/ShowData'
import { Protected } from '@/pages/users/auth/Protected'
import { masked } from '@/shared/common/string-utils.shared'
import { SuccessAlert } from '@ui/common/alerts/SuccessAlert'
import { VerifyMobile } from '@ui/common/sms/VerifyMobile'
import { useSendUserMobileOtpMutation, useUserQuery, useVerifyUserMobileMutation } from '@ui/users/common/user-queries'

import type { FullUserData } from '@/server/users/get-user/getUser.server'

export const VerifyUserMobilePage = () => {
  // User data
  const { data: user, isPending: userIsPending, error: userError } = useUserQuery(true)
  const userData = user as FullUserData

  // Mutations
  const sendOtpMutation = useSendUserMobileOtpMutation()
  const verifyMutation = useVerifyUserMobileMutation()

  // Compute masked mobile number - keep memoized as masked() might be expensive
  const maskedMobile = useMemo(() => {
    if (!userData) return ''
    return `+${userData.mobileCountry.phonePrefix}-${masked(userData.mobile)}`
  }, [userData])

  return (
    <Protected>
      <ShowData isPending={userIsPending} error={userError} spinnerSize="1.25rem">
        {userData?.mobileVerified ?
          <SuccessAlert>
            Your mobile number {maskedMobile} is verified! Create a{' '}
            <a className="link" href="/profiles/add">
              student profile
            </a>
            , or visit{' '}
            <a className="link" href="/">
              home page
            </a>
            .
          </SuccessAlert>
        : <VerifyMobile
            title="Verify Mobile"
            description={`Let's verify your WhatsApp number ${maskedMobile}`}
            sendOtpMutation={sendOtpMutation as UseMutationResult}
            verifyMutation={verifyMutation as UseMutationResult}
          />
        }
      </ShowData>
    </Protected>
  )
}

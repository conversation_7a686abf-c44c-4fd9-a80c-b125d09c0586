import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars, queryFull } from '@/server/common/hono-utils.server'

import { getUser } from './getUser.server'

export const getUserHandler = new Hono<HonoVars>().get('/', queryFull, async (c) => {
  const query = c.req.valid('query')
  const full = query.full === 'true'
  const user = await getUser(getCtx(c), full)
  return c.json(user, 200)
})

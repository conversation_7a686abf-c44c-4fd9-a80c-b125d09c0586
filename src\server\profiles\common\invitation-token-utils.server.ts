import { sign, verify } from 'hono/jwt'
import { DateTime } from 'luxon'

import { ensure } from '@/server/common/error/ensure.server'
import { InviteProfileForm } from '@/shared/profiles/profile-utils.shared'

import type { UUID } from 'crypto'

const AUDIENCE = 'invitation'

export const createInvitationToken = (jwtSecret: string, invitorProfileId: UUID, invitee: InviteProfileForm) =>
  sign(
    {
      aud: AUDIENCE,
      sub: invitee.email,
      iat: DateTime.now().startOf('second').toSeconds(),
      exp: DateTime.now().plus({ days: 1 }).startOf('second').toSeconds(),
      inviteeRole: invitee.role,
      invitorProfileId,
    },
    jwtSecret,
  )

export const parseInvitationToken = async (jwtSecret: string, token: string) => {
  const payload = await verify(token, jwtSecret)
  ensure(payload.aud && payload.aud === AUDIENCE, {
    logMessage: `Invalid audience in invitation token`,
    issueMessage: `Invalid audience in invitation token`,
    statusCode: 401,
  })
  return payload
}

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { removeOtp, verifyOtp } from '@/server/common/sms/otp/verifyOtp'
import { db, Transaction } from '@/server/db/db'
import { userTable } from '@/server/db/schema/user-schema'
import { phoneNumber } from '@/shared/common/common-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

const findUserDaf = (log: Logger, userId: UUID) =>
  withLog(log, 'findUserDaf', () =>
    db.query.userTable.findFirst({
      columns: {
        mobile: true,
        mobileVerified: true,
      },
      with: {
        mobileCountry: {
          columns: {
            phonePrefix: true,
          },
        },
      },
      where: eq(userTable.id, userId),
    }),
  )

const setUserMobileVerified = (log: Logger, db: Transaction, userId: UUID) =>
  withLog(log, 'setUserMobileVerified', () =>
    db.update(userTable).set({ mobileVerified: true }).where(eq(userTable.id, userId)),
  )

export const verifyMobile = async (c: Ctx, otp: string) => {
  ensureUser(c.user, { ignoreEmailVerification: true, ignoreMobileVerification: true })
  const userId = c.user.id

  // Get user details
  const user = await findUserDaf(c.log, userId)
  ensureExists(user, userId, 'User', 422)

  if (user.mobileVerified) return

  // Get OTP details
  const mobile = phoneNumber(user.mobileCountry.phonePrefix, user.mobile)
  await verifyOtp(c.log, mobile, otp)

  // Update user and delete OTP
  await db.transaction(async (tx) => {
    await setUserMobileVerified(c.log, tx, userId)
    await removeOtp(c.log, tx, mobile)
  })
}

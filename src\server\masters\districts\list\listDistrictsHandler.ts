import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'
import { z } from 'zod'

import { HonoVars } from '@/server/common/hono-utils.server'
import { $UUID } from '@/shared/common/common-utils.shared'

import { listDistricts } from './listDistricts'

export const listDistrictsHandler = new Hono<HonoVars>().get(
  '/',
  zValidator(
    'query',
    z.object({
      stateId: $UUID,
    }),
  ),
  async (c) => {
    const query = c.req.valid('query')
    const districts = await listDistricts(c.get('log'), query.stateId)
    return c.json({ rows: districts }, 200)
  },
)

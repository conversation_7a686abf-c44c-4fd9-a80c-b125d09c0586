import { ShoppingCartIcon } from '@heroicons/react/24/outline'

import { profileOk } from '@/shared/profiles/profile-check-utils.shared'
import { useStudentFeePaymentItemCountQuery } from '@ui/courses/@id/batches/common/student-fee-payment-queries'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

export const ViewDraftStudentPaymentsTrigger = () => {
  const { currentProfile } = useCurrentProfile()
  return profileOk(currentProfile, { roleAnyOf: ['student'] }) ? <DraftStudentPaymentsTrigger /> : null
}

const DraftStudentPaymentsTrigger = () => {
  const { data } = useStudentFeePaymentItemCountQuery()

  if (!data || data.count === 0) return null
  return (
    <a href="/students/me/payments" className="group -m-2 flex items-center p-2">
      <ShoppingCartIcon aria-hidden="true" className="size-6 shrink-0 text-gray-400 group-hover:text-gray-500" />
      <span className="ml-1 text-sm font-medium text-gray-700 group-hover:text-gray-800">{data.count}</span>
      <span className="sr-only">items in cart, view bag</span>
    </a>
  )
}

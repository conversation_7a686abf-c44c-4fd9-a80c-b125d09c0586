import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { db } from '@/server/db/db'
import { courseTable } from '@/server/db/schema/course-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { markdown2Html } from '@/shared/common/markdown-utils.shared'
import { with<PERSON>og } from '@/shared/common/withLog.shared'
import { EditCourseForm } from '@/shared/course/course-utils.shared'

import { ensureCanUpdateCourse } from '../common/ensureCanUpdateCourse'

const updateCourseDaf = (log: pino.Logger, courseId: UUID, form: EditCourseForm, profileId: UUID) =>
  withLog(log, 'updateCourseDaf', () =>
    db
      .update(courseTable)
      .set({
        name: form.name,
        descr: form.descr ? markdown2Html(form.descr) : null,
        updatedAt: utc().toJSDate(),
        updateRemarks: `${profileId} profile updated this course`,
      })
      .where(eq(courseTable.id, courseId)),
  )

export const updateCourse = async (c: Ctx, courseId: UUID, form: EditCourseForm) => {
  await ensureCanUpdateCourse(c, courseId)
  await updateCourseDaf(c.log, courseId, form, c.profile!.id)
}

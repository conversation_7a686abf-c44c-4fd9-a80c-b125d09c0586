import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { HonoVars } from '@/server/common/hono-utils.server'
import { $CourseBatchesSearch } from '@/shared/batch/batch-utils.shared'
import { $HasId } from '@/shared/common/common-utils.shared'

import { listCourseBatches } from './listCourseBatches.server'

export const listCourseBatchesHandler = new Hono<HonoVars>().get(
  '/',
  zValidator('param', $HasId),
  zValidator('query', $CourseBatchesSearch),
  async (c) => {
    const { id: courseId } = c.req.valid('param')
    const search = c.req.valid('query')
    const profile = c.get('profile')
    const batches = await listCourseBatches(c.get('log'), courseId, search, profile?.id)
    return c.json({ rows: batches }, 200)
  },
)

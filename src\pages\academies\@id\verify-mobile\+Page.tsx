import { UUID } from 'crypto'

import { usePageContext } from 'vike-react/usePageContext'

import { AcademyDetail } from '@/server/academies/get/getAcademy.server'
import { extractUuid } from '@/shared/common/common-utils.shared'
import { useMyAcademyQuery } from '@ui/academies/common/academy-queries'
import { useAcademyWithContact } from '@ui/academies/common/useAcademyWithContact'
import { ShowData } from '@ui/common/ShowData'

import { VerifyAcademyMobile } from './VerifyAcademyMobile'

export default () => {
  const pageContext = usePageContext()
  const id = extractUuid(pageContext.routeParams.id)

  const { data: myAcademy, isPending: myAcademyIsPending, error: myAcademyError } = useMyAcademyQuery()

  return (
    <ShowData isPending={myAcademyIsPending} error={myAcademyError}>
      {myAcademy?.id === id ?
        <VerifyAcademyMobilePage academyId={id} />
      : <div>You are not authorized to verify this academy's mobile number</div>}
    </ShowData>
  )
}

const VerifyAcademyMobilePage = ({ academyId }: { academyId: UUID }) => {
  const { academy, academyMobile, error } = useAcademyWithContact(academyId, true)
  return (
    <ShowData error={error}>
      <VerifyAcademyMobile academy={academy as AcademyDetail} academyMobile={academyMobile!} />
    </ShowData>
  )
}

# Database Resilience Configuration

This document explains the enhanced database configuration implemented to handle brief database outages and maintain application availability during platform maintenance updates, node failovers, or other brief disruptions.

## Problem Addressed

Database platforms occasionally experience brief outages (5-10 seconds) due to:
- Platform maintenance updates
- Node failover operations  
- Network connectivity issues
- Connection pool exhaustion

Without proper reconnection handling, applications may fail during these brief interruptions even when the database comes back online quickly.

## Solution: Leveraging postgres.js Built-in Resilience

**Good news**: [postgres.js](https://github.com/porsager/postgres) already handles automatic reconnection! The library automatically tries to reconnect the next time a query is made when connections are lost.

Our configuration focuses on **preventing connection issues** rather than handling them after they occur:

### 1. Proactive Connection Management
- **Idle Timeout**: Close idle connections before the server terminates them
- **Smart Lifetime**: Use postgres.js intelligent randomized connection refresh (30-60 minutes)

### 2. Health Monitoring
- **Health Check Endpoint**: `/api/health` for monitoring database connectivity
- **Connection Event Logging**: Tracks connection status for debugging
- **Graceful Shutdown**: Proper cleanup when application terminates

## Configuration

### Minimal Production Configuration

Our streamlined configuration leverages postgres.js defaults:

```javascript
const sql = postgres(env.DATABASE_URL, {
  ssl: 'prefer',
  max: 10,
  connect_timeout: 30,
  prepare: true,
  connection: {
    application_name: `tui_${env.APP_ENV}`,
    idle_in_transaction_session_timeout: 20000,
  },
  
  // Essential resilience: prevent "Connection terminated unexpectedly"
  idle_timeout: 20,
  // max_lifetime: postgres.js uses intelligent default (30-60min randomized)
  
  transform: { undefined: null },
  timeout: 0, // Let postgres.js handle timeouts
})
```

### Why We Don't Configure `max_lifetime`

According to the [postgres.js documentation](https://github.com/porsager/postgres?tab=readme-ov-file#all-postgres-options):

> **Default: `max_lifetime = 60 * (30 + Math.random() * 30)`**
> 
> This resolves to an interval between 30 and 60 minutes to optimize for the benefits of prepared statements and working nicely with Linux's OOM killer.

The randomization is **crucial** because:
- ✅ **Prevents thundering herd**: Connections don't all refresh simultaneously
- ✅ **OOM killer friendly**: Works better with Linux memory management  
- ✅ **Prepared statement optimization**: Balances freshness with performance

**Setting a fixed value defeats this intelligent behavior!**

## Automatic Reconnection

postgres.js handles reconnection automatically:

> "Since connections are lazy it'll automatically try to reconnect the next time a query is made. The benefit of this is no weird generic 'onerror' handler that tries to get things back to normal, and also simpler application code since you don't have to handle errors out of context."

This means:
- ✅ **5-10 second outages**: Handled transparently
- ✅ **Node failovers**: Automatic reconnection on next query
- ✅ **No application code changes**: Reconnection is invisible to your app

## Health Check Endpoint

Monitor database connectivity:

```bash
curl http://localhost:3000/api/health
```

**Healthy Response (200 OK):**
```json
{
  "status": "healthy",
  "database": "connected", 
  "latency": 15,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

**Unhealthy Response (503 Service Unavailable):**
```json
{
  "status": "unhealthy",
  "database": "disconnected",
  "error": "Connection timeout",
  "latency": 5000,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Docker Health Check

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s
```

## What This Configuration Provides

✅ **Automatic Reconnection**: postgres.js handles this out-of-the-box  
✅ **Proactive Prevention**: `idle_timeout: 20` prevents connection termination  
✅ **Intelligent Refresh**: Randomized `max_lifetime` (30-60min) prevents thundering herd  
✅ **Health Monitoring**: `/api/health` endpoint for observability  
✅ **Graceful Shutdown**: Proper cleanup during deployments  
✅ **Production Ready**: Minimal configuration, maximum reliability

## Key Benefits

- **Zero Configuration**: Works great with postgres.js defaults
- **Battle Tested**: Leverages postgres.js proven reconnection logic
- **Resource Efficient**: Intelligent connection lifecycle management
- **Observable**: Health checks and logging for monitoring
- **Simple**: No complex retry logic or error handling needed

The configuration specifically addresses brief outages (5-10 seconds) mentioned in your platform maintenance notice, while letting postgres.js handle the complex reconnection logic automatically.

## Monitoring and Logging

The application logs connection events for monitoring:

- **Connection Established**: When database connection is successful
- **Connection Lost**: When connection is dropped (will attempt to reconnect)
- **Connection Error**: When connection errors occur

These logs help identify patterns in connection issues and tune configuration accordingly.

## Best Practices

1. **Monitor Health Endpoint**: Set up monitoring on `/api/health` to track database connectivity
2. **Tune Timeouts**: Adjust `DB_IDLE_TIMEOUT` based on your traffic patterns
3. **Set Appropriate Retries**: Configure `DB_MAX_ATTEMPTS` based on typical outage duration
4. **Use Load Balancers**: Implement health checks at the load balancer level
5. **Database Monitoring**: Monitor database metrics to identify infrastructure issues

## Troubleshooting

### Connection Issues Persist
1. Check database server health
2. Verify network connectivity  
3. Review database connection limits
4. Increase `DB_MAX_ATTEMPTS` or `DB_RETRY_INTERVAL`

### Frequent Reconnections
1. Check `DB_IDLE_TIMEOUT` setting
2. Review application query patterns
3. Monitor database server resource usage
4. Consider increasing `DB_MAX_LIFETIME`

### High Latency
1. Monitor database server performance
2. Check network connectivity
3. Review connection pool configuration
4. Consider database query optimization

## Benefits

✅ **Automatic Recovery**: Application automatically reconnects after brief outages  
✅ **No Data Loss**: Transactions are properly handled during reconnection  
✅ **Minimal Downtime**: Only brief interruption during actual outage period  
✅ **Production Ready**: Handles real-world infrastructure maintenance scenarios  
✅ **Configurable**: Tune behavior based on your infrastructure characteristics  
✅ **Observable**: Health checks and logging for monitoring and debugging 
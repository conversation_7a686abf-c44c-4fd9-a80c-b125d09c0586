import { UUID } from 'crypto'

import { ContentfulStatusCode } from 'hono/utils/http-status'
import { ZodError } from 'zod'

export type MyIssue = {
  path: (string | number)[]
  code: string
  message: string
  data?: Record<string, unknown>
}

export type MyError = {
  issues: MyIssue[]
}

export type OmniError = MyError | ZodError

/* Aligned with response of https://www.npmjs.com/package/@hono/zod-validator
{
    "success": false,
    "error": {
        "issues": [
            {
                "code": "too_small",
                "minimum": 1,
                "type": "string",
                "inclusive": true,
                "exact": false,
                "message": "String must contain at least 1 character(s)",
                "path": [
                    "addressLine1"
                ]
            },
            {
                "code": "too_small",
                "minimum": 6,
                "type": "string",
                "inclusive": true,
                "exact": false,
                "message": "String must contain at least 6 character(s)",
                "path": [
                    "postalCode"
                ]
            }
        ],
        "name": "ZodError"
    }
}
*/
export type ErrorPayload = {
  id: UUID
  status: ContentfulStatusCode
  error: OmniError
}

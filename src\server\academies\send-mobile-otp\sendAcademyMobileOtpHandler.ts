import { UUID } from 'crypto'

import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'

import { sendAcademyMobileOtp } from './sendAcademyMobileOtp'

export const sendAcademyMobileOtpHandler = new Hono<HonoVars>().post('/', async (c) => {
  const academyId = c.req.param('id') as UUID
  await sendAcademyMobileOtp(getCtx(c), academyId)
  return c.body(null, 201)
})

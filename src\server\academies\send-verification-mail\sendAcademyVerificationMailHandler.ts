import { UUID } from 'crypto'

import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'

import { sendAcademyVerificationMail } from './sendAcademyVerificationMail'

export const sendAcademyVerificationMailHandler = new Hono<HonoVars>().post('/', async (c) => {
  const academyId = c.req.param('id') as UUID
  await sendAcademyVerificationMail(getCtx(c), academyId)
  return c.body(null, 201)
})

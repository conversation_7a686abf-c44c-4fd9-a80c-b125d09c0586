import { relations, sql } from 'drizzle-orm'
import { boolean, check, index, pgTable, text } from 'drizzle-orm/pg-core'

import { Currency } from '@/shared/common/common-utils.shared'

import { auditColumns, suspensionCheck, suspensionColumns, timestampz, uid } from './helpers'
import { countryTable, districtTable } from './master-schema'
import { profileTable } from './profile-schema'

export const academyTable = pgTable(
  'academy',
  {
    id: uid().primaryKey(),
    name: text().notNull().unique(),
    tncsAcceptedAt: timestampz(),
    approvedAt: timestampz(),
    descr: text(),

    email: text().notNull(),
    emailVerified: boolean().notNull().default(false),
    mobileCountryCode: text()
      .notNull()
      .references(() => countryTable.code),
    mobile: text().notNull(),
    mobileVerified: boolean().notNull().default(false),

    currency: text().$type<Currency>().notNull(),
    upiId: text().notNull(),

    tradeName: text(),
    gstin: text(),

    districtId: uid()
      .references(() => districtTable.id)
      .notNull(),
    pincode: text().notNull(),
    area: text().notNull(),
    addressLine1: text(),
    addressLine2: text(),

    ...auditColumns,
    ...suspensionColumns,
  },
  (t) => [
    check('chk_name_len', sql`char_length(${t.name}) <= 255`),
    check('descr_len', sql`char_length(${t.descr}) <= 10000`),
    check('chk_currency_len', sql`char_length(${t.currency}) <= 10`),
    check('chk_upi_id_len', sql`char_length(${t.upiId}) <= 255`),
    ...suspensionCheck(t),
    index('idx_academy_name').on(t.name),
  ],
)

export const academyStaffTable = pgTable(
  'academy_staff',
  {
    profileId: uid()
      .primaryKey()
      .references(() => profileTable.id, { onDelete: 'cascade' }),
    academyId: uid()
      .notNull()
      .references(() => academyTable.id),
    ...auditColumns,
    ...suspensionColumns,
  },
  (t) => [index('idx_academy_employee_academy_id').on(t.academyId)],
)

export const academyRelations = relations(academyTable, ({ many, one }) => ({
  staffs: many(academyStaffTable),
  mobileCountry: one(countryTable, {
    fields: [academyTable.mobileCountryCode],
    references: [countryTable.code],
  }),
}))

export const academyStaffRelations = relations(academyStaffTable, ({ one }) => ({
  profile: one(profileTable, {
    fields: [academyStaffTable.profileId],
    references: [profileTable.id],
  }),
  academy: one(academyTable, {
    fields: [academyStaffTable.academyId],
    references: [academyTable.id],
  }),
}))

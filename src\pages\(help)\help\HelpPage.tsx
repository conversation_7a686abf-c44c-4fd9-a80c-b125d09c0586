import {
  AcademicCapIcon,
  BookOpenIcon,
  CalendarIcon,
  ClockIcon,
  CurrencyDollarIcon,
  GlobeAltIcon,
  UserGroupIcon,
  ArrowPathIcon,
  PuzzlePieceIcon,
} from '@heroicons/react/24/outline'

const studentBenefits = [
  {
    name: 'Personalized Learning',
    description: 'Small batch sizes enable individualized attention and targeted feedback on MCQs and assignments.',
    icon: UserGroupIcon,
  },
  {
    name: 'Convenient Access',
    description:
      'Attend quality classes from anywhere, eliminating commute time and transportation costs associated with physical tuition centers.',
    icon: GlobeAltIcon,
  },
  {
    name: 'Geographic Freedom',
    description:
      'Access premier academies regardless of location, breaking down geographical barriers to quality education.',
    icon: GlobeAltIcon,
  },
  {
    name: 'Organized Learning Schedule',
    description:
      'Stay organized with structured class schedules and batch timings that help you maintain a consistent learning routine.',
    icon: CalendarIcon,
  },
  {
    name: 'Cost-Effective Education',
    description:
      'Reduced overhead costs for academies translate to more affordable tuition fees without sacrificing quality.',
    icon: CurrencyDollarIcon,
  },
  {
    name: 'Progressive Learning Resources',
    description:
      'Access to course materials that are released strategically as the batch progresses, enhancing the learning experience.',
    icon: BookOpenIcon,
  },
]

const howItWorks = [
  {
    title: 'Create Your Student Profile',
    description: 'Sign up using Google login and create your student profile.',
    icon: AcademicCapIcon,
  },
  {
    title: 'Browse Academies & Courses',
    description:
      'Explore a variety of academies and their course offerings to find what matches your educational needs.',
    icon: BookOpenIcon,
  },
  {
    title: 'Join a Batch',
    description: 'Join a course batch that fits your schedule and educational goals.',
    icon: UserGroupIcon,
  },
  {
    title: 'Attend Live Classes',
    description: 'Participate in scheduled live classes through online learning sessions.',
    icon: ClockIcon,
  },
  {
    title: 'Complete Assignments & MCQs (Coming Soon)',
    description: 'Enhance your learning by completing assigned tasks and receiving personalized feedback.',
    icon: PuzzlePieceIcon,
  },
  {
    title: 'Track Your Progress (Coming Soon)',
    description:
      'Monitor your progress and access gradually released learning resources as you advance through the batch.',
    icon: ArrowPathIcon,
  },
]

export const HelpPage = () => {
  return (
    <div className="bg-white">
      {/* Hero section */}
      <div className="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8 bg-indigo-50">
        <div className="mx-auto max-w-2xl text-center">
          <h1 className="text-4xl font-semibold tracking-tight text-balance text-gray-900 sm:text-5xl">
            Student Help Center
          </h1>
          <p className="mt-6 text-lg font-medium text-pretty text-gray-600 sm:text-xl/8">
            Everything you need to know about making the most of your online tuition experience.
          </p>
        </div>
      </div>

      {/* How it works section */}
      <div className="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="text-base/7 font-semibold text-indigo-600">Getting Started</h2>
          <p className="mt-2 text-3xl font-semibold tracking-tight text-pretty text-gray-900 sm:text-4xl">
            How TuitionLance Works
          </p>
          <p className="mt-6 text-lg/8 text-pretty text-gray-600">
            TuitionLance makes online learning simple and effective. Here's how to get started and make the most of your
            experience.
          </p>
        </div>
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-5xl">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 sm:grid-cols-2 lg:max-w-none lg:grid-cols-3 lg:gap-y-16">
            {howItWorks.map((item) => (
              <div key={item.title} className="relative pl-16">
                <dt className="text-base/7 font-semibold text-gray-900">
                  <div className="absolute top-0 left-0 flex size-10 items-center justify-center rounded-lg bg-indigo-600">
                    <item.icon className="size-6 text-white" aria-hidden="true" />
                  </div>
                  {item.title}
                </dt>
                <dd className="mt-2 text-base/7 text-gray-600">{item.description}</dd>
              </div>
            ))}
          </dl>
        </div>
      </div>

      {/* Benefits section */}
      <div className="bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base/7 font-semibold text-indigo-600">Student Benefits</h2>
            <p className="mt-2 text-3xl font-semibold tracking-tight text-pretty text-gray-900 sm:text-4xl">
              Why Choose TuitionLance
            </p>
            <p className="mt-6 text-lg/8 text-pretty text-gray-600">
              Our platform offers numerous advantages to enhance your educational journey.
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16">
              {studentBenefits.map((benefit) => (
                <div key={benefit.name} className="relative pl-16">
                  <dt className="text-base/7 font-semibold text-gray-900">
                    <div className="absolute top-0 left-0 flex size-10 items-center justify-center rounded-lg bg-indigo-600">
                      <benefit.icon className="size-6 text-white" aria-hidden="true" />
                    </div>
                    {benefit.name}
                  </dt>
                  <dd className="mt-2 text-base/7 text-gray-600">{benefit.description}</dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="bg-indigo-50">
        <div className="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-semibold tracking-tight text-balance text-gray-900 sm:text-4xl">
              Still have questions?
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg/8 text-pretty text-gray-600">
              We are here to help you make the most of your online learning experience.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <a
                href="/contact"
                className="rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Contact Us
              </a>
              <a href="/courses" className="text-sm/6 font-semibold text-gray-900">
                Browse Courses <span aria-hidden="true">→</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

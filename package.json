{"scripts": {"dev": "cross-env NODE_ENV=development vike dev", "clean": "rimraf ./dist", "build": "pnpm clean && vike build", "preview": "cross-env NODE_ENV=production tsx ./src/hono-entry.node.ts", "start": "pnpm drizzle:migrate && tsx ./src/hono-entry.node.ts", "drizzle:generate": "drizzle-kit generate", "drizzle:migrate": "drizzle-kit migrate", "drizzle:studio": "drizzle-kit studio", "lint": "eslint . --fix", "tsc": "tsc --noEmit", "check": "run-s lint tsc", "test": "drizzle-kit generate && cross-env DATABASE_URL=postgres://test-user:test-pass@localhost:5432/np-tuition-test-db drizzle-kit migrate && vitest", "dockerize": "git rev-parse --short HEAD > .git-commit && powershell -Command \"$tag = Get-Content .git-commit; docker build -t np-tuition-vr:$tag -t np-tuition-vr:latest .\"", "push": "powershell -Command \"$tag = Get-Content .git-commit; docker tag np-tuition-vr:$tag registry.digitalocean.com/np-blr-registry-1/np-tuition-vr:$tag; docker tag np-tuition-vr:latest registry.digitalocean.com/np-blr-registry-1/np-tuition-vr:latest; docker push registry.digitalocean.com/np-blr-registry-1/np-tuition-vr:$tag; docker push registry.digitalocean.com/np-blr-registry-1/np-tuition-vr:latest\""}, "dependencies": {"@aws-sdk/client-s3": "^3.777.0", "@aws-sdk/client-ses": "^3.743.0", "@aws-sdk/s3-request-presigner": "^3.777.0", "@epic-web/remember": "^1.1.0", "@googleapis/calendar": "^9.8.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hono/node-server": "^1.13.8", "@hono/zod-validator": "^0.4.2", "@logtail/pino": "^0.5.4", "@mdxeditor/editor": "^3.32.3", "@react-oauth/google": "^0.12.1", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "@tanstack/react-store": "^0.7.0", "@types/turndown": "^5.0.5", "@vitejs/plugin-react": "^4.4.1", "accept-language-parser": "^1.5.0", "clsx": "^2.1.1", "dotenv": "^16.4.7", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.2", "framer-motion": "^12.5.0", "fuse.js": "^7.1.0", "gaxios": "^6.7.1", "google-auth-library": "^9.15.1", "handlebars": "^4.7.8", "hono": "^4.6.20", "http-graceful-shutdown": "^3.1.14", "isomorphic-dompurify": "^2.21.0", "luxon": "^3.5.0", "markdown-it": "^14.1.0", "nodemailer": "^7.0.3", "ofetch": "^1.4.1", "p-retry": "^6.2.1", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "postgres": "^3.4.7", "qs": "^6.14.0", "quill": "^2.0.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^5.0.0", "react-quilljs": "^2.0.5", "react-secure-storage": "^1.3.2", "slugify": "^1.6.6", "statuses": "^2.0.1", "tsx": "^4.19.2", "turndown": "^7.2.0", "vike": "0.4.227", "vike-react": "^0.6.1", "vike-react-query": "^0.1.3", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.19.0", "@hono/vite-dev-server": "^0.18.1", "@modelcontextprotocol/server-filesystem": "^2025.1.14", "@tailwindcss/vite": "^4.0.3", "@tanstack/eslint-plugin-query": "^5.66.0", "@types/accept-language-parser": "^1.5.7", "@types/luxon": "^3.4.2", "@types/markdown-it": "^14.1.2", "@types/node": "^18.19.75", "@types/nodemailer": "^6.4.17", "@types/qs": "^6.9.18", "@types/quill": "^2.0.14", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@types/statuses": "^2.0.5", "@vitest/coverage-v8": "^3.0.5", "cross-env": "^7.0.3", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-boundaries": "^5.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.14.0", "msw": "^2.7.0", "npm-run-all2": "^7.0.2", "prettier": "^3.4.2", "rimraf": "^6.0.1", "tailwindcss": "^4.0.3", "typescript": "^5.7.3", "typescript-eslint": "^8.23.0", "vite": "^6.3.5", "vite-plugin-mkcert": "^1.17.8", "vitest": "^3.1.3"}, "type": "module"}
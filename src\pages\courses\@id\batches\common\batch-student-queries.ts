import { UUID } from 'node:crypto'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { BatchStudent } from '@/server/batches/batch-students/list/listStudentsOfBatch'
import { MyBatchStudent } from '@/server/batches/batch-students/my/getMyBatchStudent'
import { StudentBatch } from '@/server/batches/batches/list/student-batches/listStudentBatches'
import { Role } from '@/shared/profiles/role-utils.shared'
import { api } from '@ui/api-client'
import { useInvalidateDetail } from '@ui/common/query-helper'
import { dateConverter } from '@ui/common/utils/dateConverter'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

import { batchKeys } from './batch-queries'

export const batchStudentKeys = {
  all: ['batch-student'] as const,
  my: (batchId: UUID, profileId: UUID | undefined, role: Role | undefined) =>
    [...batchStudentKeys.all, batchId, profileId, role] as const,
  myBatches: (profileId: UUID | undefined) => [...batchStudentKeys.all, 'my-batches', profileId] as const,
  studentsOfBatch: (batchId: UUID, profileId: UUID | undefined) =>
    [...batchStudentKeys.all, 'students-of-batch', batchId, profileId] as const,
  studentEmailsOfBatch: (batchId: UUID, profileId: UUID | undefined) =>
    [...batchStudentKeys.all, 'student-emails-of-batch', batchId, profileId] as const,
}

const myBatchStudentDateFields = ['firstJoinedAt', 'leftAt']
const studentBatchesDateFields = ['leftAt', 'firstJoinedAt']

export const useJoinBatchMutation = () => {
  const { currentProfile } = useCurrentProfile()
  const invalidateBatch = useInvalidateDetail(batchKeys)
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (batchId: UUID) =>
      api().batches[':id'].students.$post({
        param: { id: batchId },
      }),
    onSuccess: (_, batchId) => {
      invalidateBatch(batchId)
      void queryClient.invalidateQueries({
        queryKey: batchStudentKeys.my(batchId, currentProfile?.id, currentProfile?.role),
      })
      void queryClient.invalidateQueries({
        queryKey: batchStudentKeys.myBatches(currentProfile?.id),
      })
      void queryClient.invalidateQueries({
        queryKey: batchStudentKeys.studentsOfBatch(batchId, currentProfile?.id),
      })
      void queryClient.invalidateQueries({
        queryKey: batchStudentKeys.studentEmailsOfBatch(batchId, currentProfile?.id),
      })
    },
  })
}

export const useLeaveBatchMutation = () => {
  const { currentProfile } = useCurrentProfile()
  const invalidateBatch = useInvalidateDetail(batchKeys)
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (batchId: UUID) =>
      api().batches[':id'].students.$delete({
        param: { id: batchId },
      }),
    onSuccess: (_, batchId) => {
      invalidateBatch(batchId)
      void queryClient.invalidateQueries({
        queryKey: batchStudentKeys.my(batchId, currentProfile?.id, currentProfile?.role),
      })
      void queryClient.invalidateQueries({
        queryKey: batchStudentKeys.myBatches(currentProfile?.id),
      })
      void queryClient.invalidateQueries({
        queryKey: batchStudentKeys.studentsOfBatch(batchId, currentProfile?.id),
      })
      void queryClient.invalidateQueries({
        queryKey: batchStudentKeys.studentEmailsOfBatch(batchId, currentProfile?.id),
      })
    },
  })
}

export const useMyBatchStudentQuery = (batchId: UUID) => {
  const { currentProfile } = useCurrentProfile()

  return useQuery({
    queryKey: batchStudentKeys.my(batchId, currentProfile?.id, currentProfile?.role),
    queryFn: () =>
      currentProfile?.role === 'student' ?
        api()
          .batches[':id'].students.me.$get({ param: { id: batchId } })
          .then((res) => (res.status == 204 ? null : res.json()))
          .then(dateConverter<MyBatchStudent>(myBatchStudentDateFields))
      : null,
  })
}

export const useBatchStudentsQuery = (batchId: UUID) => {
  const { currentProfile } = useCurrentProfile()

  return useQuery({
    queryKey: batchStudentKeys.studentsOfBatch(batchId, currentProfile?.id),
    queryFn: () =>
      api()
        .batches[':id'].students.$get({ param: { id: batchId } })
        .then((res) => res.json())
        .then((batches) => batches.rows)
        .then(dateConverter<BatchStudent[]>(studentBatchesDateFields)),
  })
}

export const useBatchStudentEmailsQuery = (batchId: UUID) => {
  const { currentProfile } = useCurrentProfile()

  return useQuery({
    queryKey: batchStudentKeys.studentEmailsOfBatch(batchId, currentProfile?.id),
    queryFn: () =>
      api()
        .batches[':id']['student-emails'].$get({ param: { id: batchId } })
        .then((res) => res.json())
        .then((batches) => batches.rows),
  })
}

export const useStudentBatchesQuery = (enabled = true) => {
  const { currentProfile, profileIsPending } = useCurrentProfile()
  return useQuery({
    queryKey: batchStudentKeys.myBatches(currentProfile?.id),
    queryFn: () =>
      currentProfile?.id ?
        api()
          .students.me.batches.$get()
          .then((res) => res.json())
          .then((batches) => batches.rows)
          .then(dateConverter<StudentBatch[]>(studentBatchesDateFields))
      : [],
    enabled: enabled && !profileIsPending,
  })
}

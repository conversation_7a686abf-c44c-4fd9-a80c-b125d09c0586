import crypto, { UUID } from 'crypto'

import { aliasedTable, and, eq, isNull, lt, notExists, sql } from 'drizzle-orm'
import { DateTime } from 'luxon'
import { pino } from 'pino'

import { db, Transaction } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { appCommandTable } from '@/server/db/schema'
import { sleep } from '@/shared/common/common-utils.shared'
import { getLogger } from '@/shared/common/logger.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { isDLockError, runDLocked } from '../distributed-lock/runDLocked'

const MAX_MINUTES_TO_TRY_A_COMMAND = 2

let toProcess = true
const { promise: processingStopped, resolve: notifyProcessingStopped } = Promise.withResolvers<undefined>()

export const stopProcessingCommands = async () => {
  toProcess = false
  await processingStopped
}

const createCmdLog = (loopId: UUID) => {
  const log = getLogger()
  try {
    return log.child({
      cmd: {
        loopId,
      },
    })
  } catch (e) {
    log.error(e, 'Could not create child log for processing command')
    return log
  }
}

export const processCommands = async () => {
  const log = getLogger()
  log.info('Processing commands ...')
  while (toProcess) {
    const loopId = crypto.randomUUID()
    const cmdLog = createCmdLog(loopId)
    try {
      if (await existsNextExecutableCommandDaf()) {
        // WARNING: on hot-code replacement (development mode),
        //          this loop may seem to execute twice, generating app_lock insertion error
        //          Don't panic! That's only on hot-code replacement
        await runDLocked(cmdLog, ['command', 'any'], () => processNextCommand(cmdLog, loopId), {
          lockRetries: 0,
        })
      }
    } catch (err) {
      if (isDLockError(err)) cmdLog.debug(err)
      else cmdLog.error(err)
    } finally {
      await sleep(100)
    }
  }
  log.info('Stopped processing commands ...')
  notifyProcessingStopped(undefined)
}

export type Command<T> = {
  command: string
  data: T
}

type CommandRow<T> = Command<T> & {
  id: number
  logBindings: pino.Bindings
  firstAttemptedAt: Date | null
}

const preceeding = aliasedTable(appCommandTable, 'preceeding')
const nextCommandFetchingCondition = and(
  isNull(appCommandTable.abortedAt),
  notExists(
    db
      .select({ dummy: sql`1` })
      .from(preceeding)
      .where(and(eq(preceeding.flock, appCommandTable.flock), lt(preceeding.id, appCommandTable.id))),
  ),
)

const existsNextExecutableCommandDaf = async () => {
  const rows = await db
    .select(dummyColumn.extras)
    .from(appCommandTable)
    .where(nextCommandFetchingCondition)
    .orderBy(appCommandTable.id)
    .limit(1)

  return rows.length > 0
}

const fetchNextExecutableCommandDaf = (log: pino.Logger) =>
  withLog(log, 'fetchNextExecutableCommandDaf', () =>
    db
      .select({
        id: appCommandTable.id,
        command: appCommandTable.command,
        logBindings: appCommandTable.logBindings,
        data: appCommandTable.data,
        firstAttemptedAt: appCommandTable.firstAttemptedAt,
      })
      .from(appCommandTable)
      .where(nextCommandFetchingCondition)
      .orderBy(appCommandTable.id)
      .limit(1),
  )

const deleteCommandDaf = (log: pino.Logger, id: number) =>
  withLog(log, 'deleteCommandDaf', () => db.delete(appCommandTable).where(eq(appCommandTable.id, id)))

const createNextLog = (log: pino.Logger, loopId: UUID, nextCommand: CommandRow<unknown>) => {
  const childBindings = {
    ...nextCommand.logBindings,
    cmd: {
      loopId,
      id: nextCommand.id,
      command: nextCommand.command,
    },
  }
  return log.child(childBindings)
}

const fillFirstAttemptedAtIfNeededDaf = (log: pino.Logger, commandId: number) =>
  withLog(log, 'fillFirstAttemptedAtIfNeededDaf', () =>
    db
      .update(appCommandTable)
      .set({ firstAttemptedAt: sql`now()` })
      .where(and(eq(appCommandTable.id, commandId), isNull(appCommandTable.firstAttemptedAt))),
  )

const setAbortedAtDaf = (log: pino.Logger, commandId: number) =>
  withLog(log, 'setAbortedAtDaf', () =>
    db.update(appCommandTable).set({ abortedAt: new Date() }).where(eq(appCommandTable.id, commandId)),
  )

const tryRun = async (runner: CommandRunnable<unknown>, log: pino.Logger, command: CommandRow<unknown>) => {
  log.info(`Processing command ${command.command}`)
  await fillFirstAttemptedAtIfNeededDaf(log, command.id)
  try {
    await runner(log, command)
    log.info(`Processed command ${command.command}`)
    await deleteCommandDaf(log, command.id)
  } catch (ex: unknown) {
    const minutesSinceFirstAttempt = DateTime.now().diff(
      DateTime.fromJSDate(command.firstAttemptedAt ?? new Date()),
      'minutes',
    ).minutes
    if (minutesSinceFirstAttempt < MAX_MINUTES_TO_TRY_A_COMMAND) {
      log.warn(ex, `Error processing command ${command.command}`)
      throw ex
    }
    log.error(ex, `Aborting command ${command.command}`)
    await setAbortedAtDaf(log, command.id)
  }
}

const processNextCommand = async (cmdLog: pino.Logger, loopId: UUID) => {
  const [command] = await fetchNextExecutableCommandDaf(cmdLog)
  if (!command) return

  const log = createNextLog(cmdLog, loopId, command)
  const runner = commands[command.command]
  await tryRun(runner, log, command)
}

type CommandRunnable<T> = (log: pino.Logger, command: Command<T>) => Promise<void>
const commands: Record<string, CommandRunnable<unknown>> = {}

export const appendCommandDaf = async <T>(db: Transaction, log: pino.Logger, flock: string, command: Command<T>) => {
  await withLog(
    log,
    'appendCommandDaf',
    () =>
      db.insert(appCommandTable).values({
        flock,
        command: command.command,
        logBindings: log.bindings(),
        data: command.data,
      }),
    {
      data: {
        command: command.command,
      },
    },
  )
}

export const addCommand = <T>(command: string, runnable: CommandRunnable<T>) => {
  commands[command] = runnable as CommandRunnable<unknown>
}

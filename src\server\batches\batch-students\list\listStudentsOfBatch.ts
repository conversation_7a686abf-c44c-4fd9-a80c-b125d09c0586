import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { db } from '@/server/db/db'
import { batchStudentTable, profileTable } from '@/server/db/schema'
import { withLog } from '@/shared/common/withLog.shared'

import { ensureCanUpdateBatch } from '../../batches/common/ensureCanUpdateBatch'

const findBatchStudentsOfBatchDaf = (log: pino.Logger, batchId: UUID) =>
  withLog(log, 'findBatchStudentsOfBatchDaf', () =>
    db
      .select({
        studentId: batchStudentTable.studentId,
        studentName: profileTable.displayName,
        firstJoinedAt: batchStudentTable.firstJoinedAt,
        leftAt: batchStudentTable.leftAt,
        paidTillCycle: batchStudentTable.paidTillCycle,
        lastReminderType: batchStudentTable.lastReminderType,
      })
      .from(batchStudentTable)
      .innerJoin(profileTable, eq(batchStudentTable.studentId, profileTable.id))
      .where(eq(batchStudentTable.batchId, batchId))
      .orderBy(batchStudentTable.studentId),
  )

export const listStudentsOfBatch = async (c: Ctx, batchId: UUID) => {
  await ensureCanUpdateBatch(c, batchId)
  return findBatchStudentsOfBatchDaf(c.log, batchId)
}

export type BatchStudent = Awaited<ReturnType<typeof findBatchStudentsOfBatchDaf>>[number]

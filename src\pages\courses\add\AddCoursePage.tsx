import { useEffect, useState } from 'react'
import { navigate } from 'vike/client/router'

import { type OmniError } from '@/shared/common/error-utils.shared'
import { $AddCourseForm, AddCourseForm } from '@/shared/course/course-utils.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'
import { useMyAcademyQuery } from '@ui/academies/common/academy-queries'
import { WarningAlert } from '@ui/common/alerts/WarningAlert'
import { FormButtons, PageContent, PageLayout } from '@ui/common/form/page-layout'
import { ShowData } from '@ui/common/ShowData'
import { WithProfile } from '@ui/profiles/common/WithProfile'

import { CourseCommonEditor } from '../common/CourseCommonEditor'
import { useAddCourseMutation } from '../common/queries/course-queries'

export const AddCoursePage = () => {
  const { data: myAcademy, isPending: myAcademyIsPending, error: myAcademyError } = useMyAcademyQuery()

  // Form state
  const [formData, setFormData] = useState<Required<Omit<AddCourseForm, 'academyId'>>>({
    name: '',
    descr: '',
  })

  // Error state
  const [omniError, setOmniError] = useState<OmniError | undefined>()

  const { mutate: addCourse, isPending: addCourseIsPending } = useAddCourseMutation()

  // Validate form data when it changes
  useEffect(() => {
    const parsed = $AddCourseForm.safeParse({ ...formData, academyId: myAcademy?.id })
    setOmniError(parsed.error)
  }, [formData])

  // Form submission handler
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    addCourse(
      { ...formData, academyId: myAcademy!.id },
      {
        onSuccess: (course) => {
          void navigate(`/courses/${course.id}`)
        },
        onError: (error) => {
          setOmniError(error.error)
        },
      },
    )
  }

  return (
    <WithProfile roleAnyOf={ACADEMY_STAFF}>
      <ShowData isPending={myAcademyIsPending} error={myAcademyError} spinnerSize="1.25rem">
        {myAcademy ?
          <PageLayout title="Add a Course" description="Please fill in the details to create a new course">
            <form onSubmit={handleSubmit}>
              <PageContent>
                <CourseCommonEditor
                  name={formData.name}
                  descr={formData.descr}
                  omniError={omniError}
                  onNameChange={(value) => setFormData((prev) => ({ ...prev, name: value }))}
                  onDescrChange={(value) => setFormData((prev) => ({ ...prev, descr: value }))}
                />
                <FormButtons omniError={omniError} onCancel={() => navigate('/')} isSubmitting={addCourseIsPending} />
              </PageContent>
            </form>
          </PageLayout>
        : <WarningAlert>You are not a staff of any academy.</WarningAlert>}
      </ShowData>
    </WithProfile>
  )
}

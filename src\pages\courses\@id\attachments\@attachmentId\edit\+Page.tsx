import { usePageContext } from 'vike-react/usePageContext'

import { extractUuid } from '@/shared/common/common-utils.shared'

import { EditAttachmentPage } from './EditAttachmentPage'

export default () => {
  const pageContext = usePageContext()
  return (
    <EditAttachmentPage
      courseParam={pageContext.routeParams.id}
      attachmentId={extractUuid(pageContext.routeParams.attachmentId)}
    />
  )
}

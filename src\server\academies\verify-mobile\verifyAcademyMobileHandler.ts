import { UUID } from 'crypto'

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $VerifyMobileForm } from '@/shared/common/common-utils.shared'

import { verifyAcademyMobile } from './verifyAcademyMobile'

export const verifyAcademyMobileHandler = new Hono<HonoVars>().put(
  '/',
  zValidator('json', $VerifyMobileForm),
  async (c) => {
    const academyId = c.req.param('id') as UUID
    const form = c.req.valid('json')

    await verifyAcademyMobile(getCtx(c), academyId, form.otp)
    return c.body(null, 204)
  },
)

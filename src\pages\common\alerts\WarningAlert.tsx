import { ExclamationTriangleIcon } from '@heroicons/react/20/solid'

export const WarningAlert = ({ children }: { children: React.ReactNode }) => (
  <div className="rounded-md bg-yellow-50 p-4">
    <div className="flex items-center">
      <div className="shrink-0">
        <ExclamationTriangleIcon aria-hidden="true" className="size-5 text-yellow-400" />
      </div>
      <div className="ml-3">
        <p className="text-sm text-yellow-700">{children}</p>
      </div>
    </div>
  </div>
)

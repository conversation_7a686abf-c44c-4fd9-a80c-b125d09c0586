import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { getMyBatchStudent } from './getMyBatchStudent'

export const getMyBatchStudentHandler = new Hono<HonoVars>().get('/', zValidator('param', $HasId), async (c) => {
  const { id: batchId } = c.req.valid('param')
  const batchStudent = await getMyBatchStudent(getCtx(c), batchId)
  return batchStudent ? c.json(batchStudent, 200) : c.body(null, 204)
})

import { DateTime, DurationUnit } from 'luxon'
import { z } from 'zod'

import { utc } from '../date-utils-basic.shared'

export const PAYMENT_STATUSES = ['draft', 'pending', 'received', 'failed'] as const
export const $PaymentStatus = z.enum(PAYMENT_STATUSES)
export type PaymentStatus = z.infer<typeof $PaymentStatus>

export const paymentStatuses: Record<PaymentStatus, { label: string; descr: string; badgeColor: string }> = {
  draft: {
    label: 'Draft',
    descr: 'Student is still adding batches to the payment',
    badgeColor: 'text-yellow-800 bg-yellow-50 ring-yellow-600/20',
  },
  pending: {
    label: 'Pending',
    descr: 'Student has paid and is waiting for the payment to be received',
    badgeColor: 'text-purple-800 bg-purple-50 ring-purple-600/20',
  },
  received: {
    label: 'Received',
    descr: 'Academy has received the payment',
    badgeColor: 'text-green-800 bg-green-50 ring-green-600/20',
  },
  failed: {
    label: 'Failed',
    descr: 'Payment was rejected by the payment gateway',
    badgeColor: 'text-red-800 bg-red-50 ring-red-600/10',
  },
} as const

export const $PaymentMethod = z.enum(['manual'])
export type PaymentMethod = z.infer<typeof $PaymentMethod>

/**
 * blocked - already blocked
 * urgent - due date + grace period exhausts next day
 * last - due date has arrived
 * prudent - due date arriving in a day
 * gentle - due date arriving in 3 days
 */
export type PaymentReminderType = 'blocked' | 'urgent' | 'last' | 'prudent' | 'gentle'

export const paymentReminderTypes: Record<PaymentReminderType, { label: string; descr: string; badgeColor: string }> = {
  blocked: {
    label: 'Blocked',
    descr: 'Is blocked',
    badgeColor: 'text-red-700 bg-red-50 ring-red-600/10',
  },
  urgent: {
    label: 'Urgent',
    descr: 'Going to be blocked',
    badgeColor: 'text-pink-700 bg-pink-50 ring-pink-600/20',
  },
  last: {
    label: 'Last',
    descr: 'Payment due date has arrived',
    badgeColor: 'text-yellow-700 bg-yellow-50 ring-yellow-600/20',
  },
  prudent: {
    label: 'Prudent',
    descr: 'Due date is arriving',
    badgeColor: 'text-purple-700 bg-purple-50 ring-purple-600/20',
  },
  gentle: {
    label: 'Gentle',
    descr: 'Due date is arriving in 3 days',
    badgeColor: 'text-blue-700 bg-blue-50 ring-blue-600/20',
  },
} as const

/**
 * Get the payment reminder type to send based on the next due date and grace period.
 * @param nextDueOn - DateTime object with time set to 00:00:00 in the **batch's timezone**.
 * @param gracePeriod - The grace period in days.
 * @returns The payment reminder type to send or null if fully paid.
 */
export const getPaymentReminderTypeToSend = (nextDueOn: DateTime | null, gracePeriod: number) => {
  if (!nextDueOn) return null // fully paid

  // Calculates the precise number of days until the next due date.
  // - `nextDueOn` is expected to be 00:00:00 in the batch's local timezone (e.g., invoice due date at the start of that day).
  // - `utc()` provides the current instant.
  // - `daysToDue` will be a potentially fractional number representing the exact time difference in days.
  //   - Positive: due in the future.
  //   - Zero: due at this exact instant (if nextDueOn was also at current time, unlikely given local midnight).
  //   - Negative: past due.
  // The subsequent logic uses this precise `daysToDue` value to categorize reminder urgency.
  const daysToDue = nextDueOn.diff(utc(), 'days').days

  if (daysToDue + gracePeriod <= 0) return 'blocked'
  if (daysToDue + gracePeriod <= 1) return 'urgent'
  if (daysToDue <= 0) return 'last'
  if (daysToDue <= 1) return 'prudent'
  if (daysToDue <= 3) return 'gentle'
  return null
}

/**
 * Add a number of cycles to a date.
 * E.g. plusCycles(DateTime.fromISO('2024-01-01'), 'months', 1) => DateTime.fromISO('2024-02-01')
 * See plusCycles.test.ts for examples.
 */
export const plusCycles = (date: DateTime, duration: DurationUnit, count: number) => date.plus({ [duration]: count })

/**
 * Calculate the current cycle number based on a start date and duration type.
 * See getCurrentCycle.test.ts for examples.
 */
export const getCurrentCycle = (startDate: string, duration: 'months' | 'weeks' | 'days') => {
  const start = DateTime.fromISO(startDate, { zone: 'utc' })
  const now = utc().startOf('day')

  const diff = now.diff(start, duration)
  const value = Math.floor(diff.toObject()[duration] || 0)
  return value + 1 // Add 1 because we want 1-based indexing for cycles
}

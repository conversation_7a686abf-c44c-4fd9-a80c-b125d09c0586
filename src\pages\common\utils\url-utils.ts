const getQueryString = (searchParams: Record<string, unknown>) => {
  return Object.entries(searchParams)
    .filter((entry) => entry[1])
    .map((entry) => `${entry[0]}=${String(entry[1])}`)
    .join('&')
}

export const getUrl = (rawUrl: string, searchParams: Record<string, unknown>) => {
  const queryString = getQueryString(searchParams)
  return queryString.length > 0 ? rawUrl + '?' + queryString : rawUrl
}

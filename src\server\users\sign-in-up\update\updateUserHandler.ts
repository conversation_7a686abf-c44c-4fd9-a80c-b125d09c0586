import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $UserForm } from '@/shared/users/user-utils.shared'

import { updateUser } from './updateUser'

export const updateUserHandler = new Hono<HonoVars>().put('/', zValidator('json', $UserForm), async (c) => {
  const formData = c.req.valid('json')
  const acceptLanguage = c.req.header('accept-language')
  await updateUser(getCtx(c), formData, acceptLanguage)
  return c.body(null, 204)
})

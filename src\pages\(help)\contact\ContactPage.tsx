import { BuildingLibraryIcon, IdentificationIcon, InformationCircleIcon, UserIcon } from '@heroicons/react/24/outline'
import { useEffect, useState } from 'react'

export const ContactPage = () => {
  const [email, setEmail] = useState('Loading...')

  useEffect(() => {
    // Delay email display to prevent spam bots from easily scraping it
    const timer = setTimeout(() => {
      setEmail('<EMAIL>')
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="isolate bg-white p-6 lg:p-8">
      <div className="mx-auto max-w-2xl sm:text-center">
        <h2 className="text-4xl font-semibold tracking-tight text-balance text-gray-900 sm:text-5xl">Contact Us</h2>
        <p className="mt-2 text-lg/8 text-gray-600">
          Find assistance or reach out to us for any inquiries you may have.
        </p>
      </div>
      <div className="mx-auto mt-20 max-w-lg space-y-16">
        <div className="flex gap-x-6">
          <div className="flex size-10 shrink-0 items-center justify-center rounded-lg bg-indigo-600">
            <UserIcon aria-hidden="true" className="size-6 text-white" />
          </div>
          <div>
            <h3 className="text-base/7 font-semibold text-gray-900">For Students</h3>
            <p className="mt-2 text-base/7 text-gray-600">
              <span className="font-medium">Subject-specific questions:</span> Reach out to your teacher directly
              through contact details available on your batch page.
            </p>
            <p className="mt-2 text-base/7 text-gray-600">
              <span className="font-medium">Other issues, such as website usage or payment issues:</span> Contact your
              academy through details provided on the academy page.
            </p>
          </div>
        </div>

        <div className="flex gap-x-6">
          <div className="flex size-10 shrink-0 items-center justify-center rounded-lg bg-indigo-600">
            <IdentificationIcon aria-hidden="true" className="size-6 text-white" />
          </div>
          <div>
            <h3 className="text-base/7 font-semibold text-gray-900">For Prospective Teachers</h3>
            <p className="mt-2 text-base/7 text-gray-600">
              Interested in joining an academy as a teacher or mentor? Connect directly with your preferred academy
              using the contact information available on their academy page.
            </p>
          </div>
        </div>

        <div className="flex gap-x-6">
          <div className="flex size-10 shrink-0 items-center justify-center rounded-lg bg-indigo-600">
            <BuildingLibraryIcon aria-hidden="true" className="size-6 text-white" />
          </div>
          <div>
            <h3 className="text-base/7 font-semibold text-gray-900">Academy Registration</h3>
            <p className="mt-2 text-base/7 text-gray-600">
              Want to create a new academy or bring your existing institution online? While we're currently not
              onboarding new academies, we're happy to register your interest for future opportunities.
            </p>
            <p className="mt-2 text-base/7 text-gray-600">
              Send us a detailed email at{' '}
              <a href={`mailto:${email}`} className="text-indigo-600 font-medium">
                {email}
              </a>{' '}
              with information about your academy.
            </p>
          </div>
        </div>

        <div className="flex gap-x-6">
          <div className="flex size-10 shrink-0 items-center justify-center rounded-lg bg-indigo-600">
            <InformationCircleIcon aria-hidden="true" className="size-6 text-white" />
          </div>
          <div>
            <h3 className="text-base/7 font-semibold text-gray-900">General Inquiries</h3>
            <p className="mt-2 text-base/7 text-gray-600">
              Have questions not covered above? Our team is here to help with any other inquiries you might have.
            </p>
            <p className="mt-2 text-base/7 text-gray-600">
              Reach out to us at{' '}
              <a href={`mailto:${email}`} className="text-indigo-600 font-medium">
                {email}
              </a>{' '}
              with the details of your query.
            </p>
            <p className="mt-5 text-sm/5 text-gray-600">
              TuitionLance is owned and operated by <b>Natural Programmer</b> operating at 13AA Mani Tribhuvan,
              Kalarahanga, Bhubaneswar, Odisha, India &ndash; 751024
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

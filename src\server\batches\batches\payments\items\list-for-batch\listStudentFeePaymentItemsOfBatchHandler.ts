import { zValida<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { listStudentFeePaymentItemsOfBatch } from './listStudentFeePaymentItemsOfBatch'

export const listStudentFeePaymentItemsOfBatchHandler = new Hono<HonoVars>().get(
  '/',
  zValidator('param', $HasId),
  async (c) => {
    const { id: batchId } = c.req.valid('param')
    const batchStudentPayments = await listStudentFeePaymentItemsOfBatch(getCtx(c), batchId)
    return c.json({ rows: batchStudentPayments }, 200)
  },
)

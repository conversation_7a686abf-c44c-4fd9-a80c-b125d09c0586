import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $BatchPaymentsSearch } from '@/shared/batch/batch-utils.shared'
import { $HasId } from '@/shared/common/common-utils.shared'

import { listStudentFeePaymentItemsOfBatch } from './listStudentFeePaymentItemsOfBatch'

export const listStudentFeePaymentItemsOfBatchHandler = new Hono<HonoVars>().get(
  '/',
  zValidator('param', $HasId),
  zValidator('query', $BatchPaymentsSearch),
  async (c) => {
    const { id: batchId } = c.req.valid('param')
    const search = c.req.valid('query')
    const batchStudentPayments = await listStudentFeePaymentItemsOfBatch(getCtx(c), batchId, search)
    return c.json({ rows: batchStudentPayments }, 200)
  },
)

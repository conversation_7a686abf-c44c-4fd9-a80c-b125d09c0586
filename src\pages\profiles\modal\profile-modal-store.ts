import { Store, useStore } from '@tanstack/react-store'

const SHOW_AT_STARTUP = 'showProfileModalAtStartup'

export const showProfileModalAtStartup = (value: boolean) => {
  localStorage.setItem(SHOW_AT_STARTUP, value.toString())
}

export const isProfileModalShownAtStartup = () => localStorage.getItem(SHOW_AT_STARTUP) !== 'false'

const profileModalStore = new Store({
  visible: isProfileModalShownAtStartup(),
})

export const useProfileModelVisible = () => useStore(profileModalStore, (state) => state.visible)
export const setProfileModalVisibility = (visibility: boolean) =>
  profileModalStore.setState((state) => {
    return {
      ...state,
      visible: visibility,
    }
  })

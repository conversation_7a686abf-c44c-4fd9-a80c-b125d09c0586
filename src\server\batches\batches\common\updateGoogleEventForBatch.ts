/* eslint-disable */
import { UUID } from 'crypto'

import pino from 'pino'

import { appendCommandDaf } from '@/server/common/command/processCommands'
import { calendarFlock } from '@/server/common/google/calendar/calendar-utils'
import { Transaction } from '@/server/db/db'

import {
  UpdateGoogleEventForBatchEventCommandData,
  UPDATE_GOOGLE_EVENT_FOR_BATCH_COMMAND,
} from '../events/update/updateGoogleEventForBatchEventCommand'

export const updateGoogleEventForBatch = async (
  db: Transaction,
  log: pino.Logger,
  teacherUserId: UUID,
  events: {
    id: UUID
  }[],
) => {
  for (const event of events) {
    // await appendCommandDaf<UpdateGoogleEventForBatchEventCommandData>(db, log, calendarFlock(teacherUserId), {
    //   command: UPDATE_GOOGLE_EVENT_FOR_BATCH_COMMAND,
    //   data: {
    //     eventId: event.id,
    //     teacherUserId,
    //   },
    // })
  }
}

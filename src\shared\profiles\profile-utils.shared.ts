import { UUID } from 'crypto'

import { z } from 'zod'

import { $UUID } from '../common/common-utils.shared'
import { slugOf } from '../common/string-utils.shared'
import { DISPLAY_NAME_MAX_LEN, EMAIL_MAX_LEN } from '../users/user-utils.shared'

import { $Role, Role } from './role-utils.shared'

export const PROFILE_HEADER = 'TL-Profile'
export const PROFILE_DESCR_MAX_LEN = 5000

const $Name = z.string().trim().min(3).max(DISPLAY_NAME_MAX_LEN)

export const $EditProfileForm = z.object({
  displayName: $Name,
  descr: z.string().trim().max(PROFILE_DESCR_MAX_LEN).optional(),
})
export type EditProfileForm = z.infer<typeof $EditProfileForm>

export const $AddProfileForm = $EditProfileForm.extend({
  role: $Role,
  invitationToken: z.string().optional(),
  tncsAccepted: z.boolean().refine((val) => val === true, {
    message: 'Please accept the TnCs',
  }),
})

export type AddProfileForm = z.infer<typeof $AddProfileForm>
export const MAX_PROFILES_PER_USER = 10

export const $InviteProfileForm = z.object({
  email: z.string().trim().max(EMAIL_MAX_LEN).email(),
  role: $Role,
})
export type InviteProfileForm = z.infer<typeof $InviteProfileForm>

export const profilePath = (profile: { id: UUID; displayName: string; role: Role }) =>
  `/profiles/${slugOf(profile.displayName)}-${profile.role}-${profile.id}`

export const $ProfilesSearch = z.object({
  profileId: $UUID.optional(),
  academyId: $UUID.optional(),
  role: $Role.optional(),
})

export type ProfilesSearch = z.infer<typeof $ProfilesSearch>

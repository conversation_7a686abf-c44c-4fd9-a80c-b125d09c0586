import crypto from 'crypto'

import { remember } from '@epic-web/remember'

import { env } from './env.server'

const algorithm = 'aes-256-cbc'
const ivLength = 16 // For AES, this is always 16 bytes.

const getSecretKey = () => Buffer.from(env.ENCRYPTION_SECRET_KEY, 'hex')

export const encrypt = (str: string) => {
  const secretKey = remember('enc-secret-key', getSecretKey)
  const iv = crypto.randomBytes(ivLength)
  const cipher = crypto.createCipheriv(algorithm, secretKey, iv)

  const encrypted = Buffer.concat([cipher.update(str, 'utf8'), cipher.final()])
  return iv.toString('hex') + ':' + encrypted.toString('hex') // Concatenate iv and encrypted str
}

export const decrypt = (encryptedStr: string) => {
  const secretKey = remember('enc-secret-key', getS<PERSON><PERSON><PERSON>ey)
  const parts = encryptedStr.split(':')

  const iv = Buffer.from(parts.shift()!, 'hex')
  const cipherText = Buffer.from(parts.join(':'), 'hex')
  const decipher = crypto.createDecipheriv(algorithm, secretKey, iv)

  const decrypted = Buffer.concat([decipher.update(cipherText), decipher.final()])
  return decrypted.toString()
}

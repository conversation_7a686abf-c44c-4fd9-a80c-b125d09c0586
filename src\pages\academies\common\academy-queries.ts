import { UUID } from 'crypto'

import { useMutation, useQuery, useQueryClient, useSuspenseQuery } from '@tanstack/react-query'
import { useDeferredValue } from 'react'

import { GetAcademyReturnType } from '@/server/academies/get/getAcademy.server'
import { AcademyItem } from '@/server/academies/list/listAcademies'
import { AcademySearch, AddAcademyForm, EditAcademyForm } from '@/shared/academies/academy-utils.shared'
import { isClient, VerifyEmailForm, VerifyMobileForm } from '@/shared/common/common-utils.shared'
import { useServerQuery } from '@/shared/common/serverQueries.shared'
import { api, withError } from '@ui/api-client'
import { useInvalidateDetail } from '@ui/common/query-helper'
import { dateConverter } from '@ui/common/utils/dateConverter'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

// Define date fields for academies
const academyDateFields = ['approvedAt', 'suspendedAt']

const academyKeys = {
  all: ['academy'] as const,
  lists: () => [...academyKeys.all, 'list'] as const,
  list: (search: AcademySearch) => [...academyKeys.lists(), search] as const,
  details: () => [...academyKeys.all, 'detail'] as const,
  detail: (id: UUID) => [...academyKeys.details(), id] as const,
  detailForViewer: (viewerId: UUID | undefined, id: UUID, includeDetail: boolean) =>
    [...academyKeys.detail(id), viewerId, includeDetail] as const,
  my: (profileId: UUID | null) => [...academyKeys.details(), 'my', profileId] as const,
}

export const useAddAcademyMutation = () => {
  const queryClient = useQueryClient()
  const { currentProfile } = useCurrentProfile()

  return useMutation({
    mutationFn: (form: AddAcademyForm) =>
      api()
        .academies.$post({ json: form })
        .then((res) => res.json()),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: academyKeys.lists() })
      void queryClient.invalidateQueries({ queryKey: academyKeys.my(currentProfile?.id ?? null) })
    },
  })
}

export const useMyAcademyQuery = () => {
  const { currentProfile, profileIsPending } = useCurrentProfile()

  return useQuery({
    queryKey: academyKeys.my(currentProfile?.id ?? null),
    queryFn: () =>
      currentProfile?.id ?
        api()
          .academies.$get({
            query: {
              profileId: currentProfile.id,
            },
          })
          .then((res) => res.json())
          .then((payload) => (payload.rows.length > 0 ? payload.rows[0] : null))
          .then(dateConverter<AcademyItem>(academyDateFields))
      : null,
    enabled: !profileIsPending, // Only run query once profile data is available
  })
}

export const useAcademySuspenseQuery = (id: UUID, includeDetail: boolean) => {
  const getAcademy = useServerQuery('getAcademy')
  const { currentProfile } = useCurrentProfile()

  const { data } = useSuspenseQuery({
    queryKey: academyKeys.detailForViewer(currentProfile?.id, id, includeDetail),
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .academies[':id'].$get({ param: { id }, query: { includeDetail: includeDetail.toString() } })
            .then((res) => res.json())
            .then(dateConverter<GetAcademyReturnType>(academyDateFields)),
        )
      : getAcademy(id, includeDetail),
  })

  return useDeferredValue(data)
}

export const useListAcademiesSuspenseQuery = (search: AcademySearch) => {
  const listAcademies = useServerQuery('listAcademies')

  const { data } = useSuspenseQuery({
    queryKey: academyKeys.list(search),
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .academies.$get({ query: search })
            .then((res) => res.json())
            .then((payload) => payload.rows)
            .then(dateConverter<AcademyItem[]>(academyDateFields)),
        )
      : listAcademies(search),
  })

  return useDeferredValue(data)
}

export const useUpdateAcademyMutation = (id: UUID) => {
  const queryClient = useQueryClient()
  const invalidateAcademy = useInvalidateDetail(academyKeys)
  const { currentProfile } = useCurrentProfile()

  return useMutation({
    mutationFn: (form: EditAcademyForm) => api().academies[':id'].$put({ param: { id }, json: form }),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: academyKeys.my(currentProfile?.id ?? null) })
      invalidateAcademy(id)
    },
  })
}

export const useApproveAcademyMutation = (id: UUID) => {
  const invalidateAcademy = useInvalidateDetail(academyKeys)

  return useMutation({
    mutationFn: () => api().academies[':id'].approve.$put({ param: { id } }),
    onSuccess: () => {
      invalidateAcademy(id)
    },
  })
}

export const useSendAcademyMobileOtpMutation = (id: UUID) => {
  return useMutation({
    mutationFn: () => api().academies[':id']['mobile-otp'].$post({ param: { id } }),
  })
}

export const useVerifyAcademyMobileMutation = (id: UUID) => {
  const queryClient = useQueryClient()
  const { currentProfile } = useCurrentProfile()

  return useMutation({
    mutationFn: (json: VerifyMobileForm) =>
      api().academies[':id']['mobile-verification'].$put({
        param: { id },
        json,
      }),
    onSuccess: () => {
      void queryClient.invalidateQueries({
        queryKey: academyKeys.detail(id),
      })
      void queryClient.invalidateQueries({ queryKey: academyKeys.my(currentProfile?.id ?? null) })
    },
  })
}

export const useSendAcademyEmailVerificationMailMutation = (id: UUID) => {
  return useMutation({
    mutationFn: () => api().academies[':id']['verification-mail'].$post({ param: { id } }),
  })
}

export const useVerifyAcademyEmailMutation = (id: UUID) => {
  const queryClient = useQueryClient()
  const { currentProfile } = useCurrentProfile()

  return useMutation({
    mutationFn: (json: VerifyEmailForm) =>
      api().academies[':id']['email-verification'].$put({
        param: { id },
        json,
      }),
    onSuccess: () => {
      void queryClient.invalidateQueries({
        queryKey: academyKeys.detail(id),
      })
      void queryClient.invalidateQueries({ queryKey: academyKeys.my(currentProfile?.id ?? null) })
    },
  })
}

import { Store, useStore } from '@tanstack/react-store'

import { updateStore } from '../utils/updateStore'

type NotificationType = 'info' | 'success' | 'warning' | 'error'

const notificationStore = new Store<{
  type: NotificationType
  heading?: string
  message?: string
}>({
  type: 'info',
})

const defaultMillis = (type?: NotificationType) => (type === 'error' || type === 'warning' ? 5000 : 3000)

export const showNotification = (notification: {
  type?: NotificationType
  heading?: string
  message?: string
  forMillis?: number
}) => {
  const showForMillis = notification.forMillis ?? defaultMillis(notification.type)
  updateStore(notificationStore, {
    type: notification.type || 'info',
    heading: notification.heading,
    message: notification.message,
  })
  setTimeout(clearNotification, showForMillis)
}

export const clearNotification = () => {
  updateStore(notificationStore, {
    heading: undefined,
    message: undefined,
  })
}

export const useNotification = () => useStore(notificationStore, (state) => state)

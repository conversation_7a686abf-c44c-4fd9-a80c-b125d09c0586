import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { appendCommandDaf } from '@/server/common/command/processCommands'
import { env } from '@/server/common/env.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { s3Flock } from '@/server/common/s3/s3-utils.server'
import { db, Transaction } from '@/server/db/db'
import { courseAttachmentTable } from '@/server/db/schema/course-schema'
import { withLog } from '@/shared/common/withLog.shared'
import { courseAttachmentS3Path } from '@/shared/course/course-utils.shared'

import { ensureCanUpdateAttachment } from '../common/ensureCanUpdateAttachment'

import { REMOVE_S3OBJECT_COMMAND, RemoveS3ObjectCommandData } from './removeS3ObjectCommand'
const removeCourseAttachmentDaf = async (log: pino.Logger, db: Transaction, attachmentId: UUID) =>
  withLog(log, 'removeCourseAttachmentDaf', () =>
    db.delete(courseAttachmentTable).where(eq(courseAttachmentTable.id, attachmentId)).returning({
      uploaded: courseAttachmentTable.uploaded,
    }),
  )

export const removeCourseAttachment = async (c: Ctx, attachmentId: UUID) => {
  await ensureCanUpdateAttachment(c, attachmentId)
  await db.transaction(async (tx) => {
    const [attachment] = await removeCourseAttachmentDaf(c.log, tx, attachmentId)
    ensureExists(attachment, attachmentId, 'Attachment')
    if (attachment.uploaded) {
      await appendCommandDaf<RemoveS3ObjectCommandData>(tx, c.log, s3Flock(), {
        command: REMOVE_S3OBJECT_COMMAND,
        data: {
          path: courseAttachmentS3Path(env.APP_ENV, attachmentId),
        },
      })
    }
  })
}

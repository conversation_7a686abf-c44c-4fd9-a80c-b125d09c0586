import { usePageContext } from 'vike-react/usePageContext'

import { extractUuid } from '@/shared/common/common-utils.shared'

import { CoursePage } from './CoursePage'

export default () => {
  const pageContext = usePageContext()
  const id = extractUuid(pageContext.routeParams.id)

  return <CoursePage courseId={id} />
}

// If your query, e.g. useCourseSuspenseQuery, doesn't catch error when being called from client,
// the you can use the code below instead. Actually, if SSR would be respecting fallbacks, we can just
// don't catch the error either on server or client, and use the code below uniformly.
// But maybe the current way is cleaner nontheless.
//
// import { UUID } from 'crypto'
// import { usePageContext } from 'vike-react/usePageContext'
// import { withFallback } from 'vike-react-query'
// import { extractUuid } from '@/shared/common/common-utils.shared'
// import { useMyAcademyQuery } from '@ui/academies/common/academy-queries'
// import { ShowData } from '@ui/common/ShowData'
// import { Spinner } from '@ui/common/Spinner'
// import { ErrorFallback } from '@ui/common/ErrorFallback'
// import { useCourseSuspenseQuery } from '../common/queries/course-queries'
// import { CoursePage } from './CoursePage'

// export default () => {
//   const pageContext = usePageContext()
//   const id = extractUuid(pageContext.routeParams.id)
//   const { data: myAcademy } = useMyAcademyQuery()

//   return <CoursePageWithFallback key={`${id}-${myAcademy?.id}`} id={id} myAcademyId={myAcademy?.id} />
// }

// const CoursePageWithFallback = withFallback(
//   ({ id, myAcademyId }: { id: UUID; myAcademyId?: UUID }) => {
//     const { data } = useCourseSuspenseQuery(id, myAcademyId)
//     return (
//       <ShowData error={data.error}>
//         <CoursePage course={data.data!} />
//       </ShowData>
//     )
//   },
//   () => <Spinner />,
//   ({ error }) => <ErrorFallback error={error} />,
// )

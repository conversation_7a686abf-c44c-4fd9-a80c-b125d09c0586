import { UUID } from 'crypto'

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $EditAcademyForm } from '@/shared/academies/academy-utils.shared'

import { updateAcademy } from './updateAcademy'

export const updateAcademyHandler = new Hono<HonoVars>().put('/', zValidator('json', $EditAcademyForm), async (c) => {
  const form = c.req.valid('json')
  const academyId = c.req.param('id') as UUID

  await updateAcademy(getCtx(c), academyId, form)
  return c.body(null, 204)
})

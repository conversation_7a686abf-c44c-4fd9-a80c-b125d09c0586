import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { getDistrict } from './getDistrict'

export const getDistrictHandler = new Hono<HonoVars>().get('/', zValidator('param', $HasId), async (c) => {
  const params = c.req.valid('param')
  const district = await getDistrict(c.get('log'), params.id)
  return c.json(district, 200)
})

import { UUID } from 'crypto'

import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'

import { removeTagFromCourse } from './removeTagFromCourse'

export const removeTagFromCourseHandler = new Hono<HonoVars>().delete('/', async (c) => {
  const courseId = c.req.param('id') as UUID
  const tagId = c.req.param('tagId') as UUID
  await removeTagFromCourse(getCtx(c), courseId, tagId)
  return c.body(null, 204)
})

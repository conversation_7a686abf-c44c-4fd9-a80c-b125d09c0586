import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'

import { ensureCanUpdateAcademy } from '@/server/academies/common/ensureCanUpdateAcademy'
import { Ctx } from '@/server/common/auth-context-types.server'
import { db } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { courseTable } from '@/server/db/schema'
import { academyStaffTable } from '@/server/db/schema/academy-schema'
import { UNKNOWN_UUID } from '@/shared/common/common-utils.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'

const findAcademyStaff = (profileId: UUID, courseId: UUID) =>
  db
    .select(dummyColumn.extras)
    .from(academyStaffTable)
    .innerJoin(courseTable, eq(academyStaffTable.academyId, courseTable.academyId))
    .where(and(eq(academyStaffTable.profileId, profileId), eq(courseTable.id, courseId)))
    .limit(1)

export const ensureCanUpdateCourse = async (c: Ctx, courseId: UUID) =>
  ensureCanUpdateAcademy(c, findAcademyStaff(c.profile?.id ?? UNKNOWN_UUID, courseId), ACADEMY_STAFF)

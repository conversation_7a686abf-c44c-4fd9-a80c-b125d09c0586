import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId, $UUID } from '@/shared/common/common-utils.shared'

import { removeBatchAttachment } from './removeBatchAttachment'

const $Params = $HasId.extend({
  attachmentId: $UUID,
})
export const removeBatchAttachmentHandler = new Hono<HonoVars>().delete(
  '/',
  zValidator('param', $Params),
  async (c) => {
    const { id, attachmentId } = c.req.valid('param')
    const ctx = getCtx(c)

    await removeBatchAttachment(ctx, id, attachmentId)
    return c.body(null, 204)
  },
)

import { serve } from '@hono/node-server'
import { serveStatic } from '@hono/node-server/serve-static'
import { config } from 'dotenv'
import { Hono } from 'hono'
import { compress } from 'hono/compress'
import gracefulShutdown from 'http-graceful-shutdown'

import app from './hono-entry.js'
import { stopProcessingCommands } from './server/common/command/processCommands.js'
import { env } from './server/common/env.server'
import { closeDb } from './server/db/db'
import { getLogger } from './shared/common/logger.shared.js'
// Load environment variables at the start
config()

const nodeApp = new Hono()

nodeApp.use(compress())

nodeApp.use(
  '/*',
  serveStatic({
    root: `./dist/client/`,
  }),
)

nodeApp.route('/', app!)

const port = env.PORT ? parseInt(env.PORT, 10) : 3000

console.log(`Server listening on http://localhost:${port}`)

// Start the server
const server = serve({
  fetch: nodeApp.fetch,
  port: port,
})

// Basic onShutdown function
const onShutdown = async () => {
  const log = getLogger()
  try {
    console.log('CONSOLE: Stopping processing commands ...')
    log.info('Stopping processing commands ...')
    await stopProcessingCommands()
    console.log('CONSOLE: Closing db ...')
    log.info('Closing db ...')
    await closeDb()
    console.log('CONSOLE: Server stopped')
    log.info('Server stopped')
  } catch (e: unknown) {
    console.error('CONSOLE: Error during shutdown:', e)
    log.error(e, 'Error during shutdown:')
  }
}

// Setup graceful shutdown
console.log('CONSOLE: Setting up graceful shutdown')
gracefulShutdown(server, {
  signals: 'SIGINT SIGTERM',
  timeout: 30000,
  development: false, // Force graceful shutdown even in development
  onShutdown,
  finally: () => {
    console.log('CONSOLE: Server gracefully shut down')
  },
})

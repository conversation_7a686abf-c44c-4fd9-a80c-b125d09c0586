import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { DateTime } from 'luxon'
import { http, HttpResponse } from 'msw'
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'

import app from '@/hono-entry'
import { env } from '@/server/common/env.server'
import { db } from '@/server/db/db'
import { countryTable } from '@/server/db/schema/master-schema'
import { userTable, userTncAcceptedTable, userTncSignTable } from '@/server/db/schema/user-schema'
import { initDb, TNC_PRIVACY_V1_ID, TNC_TOS_V1_ID } from '@/server/test/initDb'
import { mockServer } from '@/server/test/mockServer'
import { createAccessToken, parseAccessToken } from '@/server/users/common/auth-token-utils.server'
import { UUID_REGEX } from '@/shared/common/common-utils.shared'
import { googleOauthScopes } from '@/shared/users/googleOauthScopes.shared'
import { AuthTokenData } from '@/shared/users/user-utils.shared'

import { GOOGLE_OPENID_ENDPOINT } from '../getGoogleUserInfo'
import { fallbackLocale } from '../signin-up-utils.server'

const GOOGLE_USERINFO_ENDPOINT = 'https://openidconnect.googleapis.com/v1/userinfo'
const oauthCode = 'google-oauth-code'

const googleUserInfo = {
  sub: 'google-id',
  name: 'Sanjay Patel',
  picture: 'https://google.com/my-picture',
  email: '<EMAIL>',
  email_verified: true,
  language: 'userInfoLanguage' as string | undefined,
} as const

type GoogleUserInfo = typeof googleUserInfo

const googleTokens = {
  access_token: 'google_access_token',
  refresh_token: 'google_refresh_token',
} as const

type GoogleTokens = typeof googleTokens

const mocked = {
  now: DateTime.now(),
} as const

const existingUser = {
  id: crypto.randomUUID(),
  googleId: googleUserInfo.sub,
  googleRefreshToken: 'old-refresh-token',
  name: 'Old Name',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/old-picture',
  language: 'oldLanguage',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '1234567890',
  mobileVerified: false,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

const validSignupForm = {
  mobileCountryCode: 'IN',
  mobile: '9876543210',
  acceptedTnCs: [TNC_TOS_V1_ID, TNC_PRIVACY_V1_ID],
  authCode: oauthCode,
  legalAgeDeclaration: true,
  informationAccuracyDeclaration: true,
} as const

// Mock date utilities to have consistent test results
vi.mock(import('@/shared/common/date-utils-basic.shared'), async (importOriginal) => {
  const mod = await importOriginal()
  return {
    ...mod,
    utc: () => mocked.now,
  }
})

const mockGoogleCalls = (userInfo: GoogleUserInfo, tokens: GoogleTokens) => {
  mockServer.use(
    http.get(
      GOOGLE_OPENID_ENDPOINT,
      () =>
        HttpResponse.json({
          userinfo_endpoint: GOOGLE_USERINFO_ENDPOINT,
        }),
      { once: true },
    ),
  )

  mockServer.use(http.get(GOOGLE_USERINFO_ENDPOINT, () => HttpResponse.json(userInfo), { once: true }))
  mockServer.use(http.post('https://oauth2.googleapis.com/token', () => HttpResponse.json(tokens), { once: true }))

  mockServer.use(
    http.post(
      'https://oauth2.googleapis.com/tokeninfo',
      () => HttpResponse.json({ scope: googleOauthScopes.join(' ') }),
      { once: true },
    ),
  )
}

const signUpUser = async (body?: string, acceptLanguage?: string) => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  }

  if (acceptLanguage) {
    headers['Accept-Language'] = acceptLanguage
  }

  return app.request(
    '/api/users/signup',
    {
      method: 'POST',
      body: body || JSON.stringify(validSignupForm),
      headers,
    },
    {},
  )
}

const mockGoogleCallsWithError = () => {
  mockServer.use(http.post('https://oauth2.googleapis.com/token', () => new HttpResponse(null, { status: 401 })))
}

const mockGoogleCallsWithMissingScopes = () => {
  mockServer.use(
    http.post(
      'https://oauth2.googleapis.com/tokeninfo',
      () => HttpResponse.json({ scope: 'email' }), // Missing required scopes
    ),
  )
}

const assertValidTokenResponse = async (token: AuthTokenData, validityDays = 30) => {
  const expectedValidUntil = mocked.now.plus({ days: validityDays }).startOf('second')
  expect(token.userId, 'Response userId check').toMatch(UUID_REGEX)
  expect(token.accessTokenValidUntil, 'Access token validity').toBe(expectedValidUntil.toISO())

  const accessToken = await parseAccessToken(env.JWT_SECRET_KEY, token.accessToken)
  expect(accessToken.sub, 'Access token sub').toBe(token.userId)
  expect(accessToken.aud, 'Access token aud').toBe('auth')
  expect(accessToken.exp, 'Access token exp lower boundary').toBe(expectedValidUntil.toSeconds())
}

const assertValidUserRecord = async (
  userInfo: GoogleUserInfo,
  tokens: GoogleTokens,
  form: typeof validSignupForm,
  language = fallbackLocale,
) => {
  const users = await db.select().from(userTable)
  expect(users.length, 'User count').toBe(1)

  const user = users[0]
  expect(user.id, 'User id').toMatch(UUID_REGEX)
  expect(user.googleId, 'User googleId').toBe(userInfo.sub)
  expect(user.googleRefreshToken, 'User googleRefreshToken').toBeNull()
  expect(user.name, 'User name').toBe(userInfo.name)
  expect(user.email, 'User email').toBe(userInfo.email)
  expect(user.emailVerified, 'User emailVerified').toBe(userInfo.email_verified)
  expect(user.googlePictureUrl, 'User googlePictureUrl').toBe(userInfo.picture)
  expect(user.language, 'User language').toBe(language)
  expect(user.mobileCountryCode, 'User mobileCountryCode').toBe(form.mobileCountryCode)
  expect(user.mobile, 'User mobile').toBe(form.mobile)
  expect(user.mobileVerified, 'User mobileVerified').toBe(false)
  expect(user.suspendedAt, 'User suspendedAt').toBeNull()
  expect(user.legalAgeDeclaredAt, 'User legalAgeDeclaredAt').toBeDefined()
  expect(user.informationAccuracyDeclaredAt, 'User informationAccuracyDeclaredAt').toBeDefined()
}

const assertTnCRecords = async (userId: UUID, acceptedTnCs: readonly UUID[]) => {
  // Check TnC sign records
  const signRecords = await db.select().from(userTncSignTable).where(eq(userTncSignTable.userId, userId))
  expect(signRecords.length, 'TnC sign records count').toBe(acceptedTnCs.length)

  for (const tncId of acceptedTnCs) {
    const signRecord = signRecords.find((r) => r.tncVersionId === tncId)
    expect(signRecord, `TnC sign record for ${tncId}`).toBeDefined()
    expect(signRecord!.accepted, `TnC sign record accepted for ${tncId}`).toBe(true)
  }

  // Check TnC acceptance records
  const acceptanceRecords = await db.select().from(userTncAcceptedTable).where(eq(userTncAcceptedTable.userId, userId))
  expect(acceptanceRecords.length, 'TnC acceptance records count').toBe(acceptedTnCs.length)

  for (const tncId of acceptedTnCs) {
    const acceptanceRecord = acceptanceRecords.find((r) => r.tncVersionId === tncId)
    expect(acceptanceRecord, `TnC acceptance record for ${tncId}`).toBeDefined()
  }
}

describe('signupHandler', () => {
  beforeEach(async () => {
    await initDb()
  })

  afterEach(() => {
    vi.clearAllMocks()
    mockServer.resetHandlers()
  })

  test('should successfully sign up a new user', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)

    // when
    const res = await signUpUser()

    // then
    expect(res.status, 'Status check').toBe(201)
    const token = (await res.json()) as AuthTokenData
    await assertValidTokenResponse(token)
    await assertValidUserRecord(googleUserInfo, googleTokens, validSignupForm, googleUserInfo.language)
    await assertTnCRecords(token.userId, validSignupForm.acceptedTnCs)
  })

  test('should use acceptLanguage header when Google does not provide language', async () => {
    // given
    mockGoogleCalls({ ...googleUserInfo, language: undefined }, googleTokens)
    const frontendLanguage = 'frontendLanguage'

    // when
    const res = await signUpUser(undefined, frontendLanguage)

    // then
    expect(res.status, 'Status check').toBe(201)
    await assertValidUserRecord(googleUserInfo, googleTokens, validSignupForm, frontendLanguage)
  })

  test('should use fallback locale when no language is provided', async () => {
    // given
    mockGoogleCalls({ ...googleUserInfo, language: undefined }, googleTokens)

    // when
    const res = await signUpUser()

    // then
    expect(res.status, 'Status check').toBe(201)
    await assertValidUserRecord(googleUserInfo, googleTokens, validSignupForm, fallbackLocale)
  })

  test('should return 409 when user with same Google ID already exists', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)

    // Pre-create user with same Google ID
    await db.insert(userTable).values(existingUser)

    // when
    const res = await signUpUser()

    // then
    expect(res.status, 'Status check').toBe(409)
    await expect(res).toBeError(409, [
      {
        code: 'custom',
        path: [],
        message: 'A user with this Google account already exists. Please sign in instead.',
      },
    ])
  })

  test('should return 409 when user is already signed in', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)

    // Create an existing user in the database
    const signedInUser = {
      ...existingUser,
      id: crypto.randomUUID(),
      googleId: 'signed-in-google-id', // Different Google ID to avoid conflicts
      email: '<EMAIL>',
    }
    await db.insert(userTable).values(signedInUser)

    // Create a valid access token for the signed-in user
    const accessToken = await createAccessToken(env.JWT_SECRET_KEY, signedInUser.id)

    // when
    const res = await app.request(
      '/api/users/signup',
      {
        method: 'POST',
        body: JSON.stringify(validSignupForm),
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`, // This will trigger auth middleware
        },
      },
      {},
    )

    // then
    expect(res.status, 'Status check').toBe(409)
    await expect(res).toBeError(409, [
      {
        code: 'custom',
        path: [],
        message: 'Please sign out before trying to sign up.',
      },
    ])
  })

  test('should return 422 when mobile country code is invalid', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)
    const invalidForm = {
      ...validSignupForm,
      mobileCountryCode: 'INVALID',
    }

    // when
    const res = await signUpUser(JSON.stringify(invalidForm))

    // then
    expect(res.status, 'Status check').toBe(400)
    await expect(res).toBeError(400, [
      {
        code: 'too_big',
        path: ['mobileCountryCode'],
        message: 'String must contain at most 2 character(s)',
      },
    ])
  })

  test('should return 422 when required TnCs are not accepted', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)
    const invalidForm = {
      ...validSignupForm,
      acceptedTnCs: [], // No TnCs accepted
    }

    // when
    const res = await signUpUser(JSON.stringify(invalidForm))

    // then
    expect(res.status, 'Status check').toBe(422)
    await expect(res).toBeError(422, [
      {
        code: 'custom',
        path: [],
        message: 'All required terms and conditions must be accepted.',
      },
    ])
  })

  test('should handle invalid Google token', async () => {
    // given
    mockGoogleCallsWithError()

    // when
    const res = await signUpUser()

    // then
    expect(res.status, 'Status check').toBe(500)
    await expect(res).toBeError(500, [
      {
        path: [],
        code: 'custom',
        message: 'Failed to authenticate with Google. Please try again.',
      },
    ])
  })

  test('should handle missing required Google scopes', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)
    mockGoogleCallsWithMissingScopes()

    // when
    const res = await signUpUser()

    // then
    expect(res.status, 'Status check').toBe(422)
    await expect(res).toBeError(422, [
      {
        path: [],
        code: 'custom',
        message: 'Please allow us to access your email, profile, calendar and events',
      },
    ])
  })

  test('should handle malformed request body', async () => {
    // when
    const res = await signUpUser('invalid-json')

    // then
    expect(res.status, 'Status check').toBe(400)
  })

  test('should handle missing required fields', async () => {
    // given
    const incompleteForm = {
      mobileCountryCode: 'IN',
      // Missing other required fields
    }

    // when
    const res = await signUpUser(JSON.stringify(incompleteForm))

    // then
    expect(res.status, 'Status check').toBe(400)
    await expect(res).toBeError(400, [
      {
        code: 'invalid_type',
        path: ['mobile'],
        message: 'Required',
      },
      {
        code: 'invalid_type',
        path: ['acceptedTnCs'],
        message: 'Required',
      },
      {
        code: 'invalid_type',
        path: ['authCode'],
        message: 'Required',
      },
      {
        code: 'invalid_type',
        path: ['legalAgeDeclaration'],
        message: 'Required',
      },
      {
        code: 'invalid_type',
        path: ['informationAccuracyDeclaration'],
        message: 'Required',
      },
    ])
  })

  test('should handle invalid mobile number format', async () => {
    // given
    const invalidForm = {
      ...validSignupForm,
      mobile: 'invalid-mobile',
    }

    // when
    const res = await signUpUser(JSON.stringify(invalidForm))

    // then
    expect(res.status, 'Status check').toBe(400)
    await expect(res).toBeError(400, [
      {
        code: 'invalid_string',
        path: ['mobile'],
        message: 'Mobile number must be 10 digits',
      },
    ])
  })

  test('should handle false legal age declaration', async () => {
    // given
    const invalidForm = {
      ...validSignupForm,
      legalAgeDeclaration: false,
    }

    // when
    const res = await signUpUser(JSON.stringify(invalidForm))

    // then
    expect(res.status, 'Status check').toBe(400)
    await expect(res).toBeError(400, [
      {
        code: 'custom',
        path: ['legalAgeDeclaration'],
        message: 'Please accept',
      },
    ])
  })

  test('should handle false information accuracy declaration', async () => {
    // given
    const invalidForm = {
      ...validSignupForm,
      informationAccuracyDeclaration: false,
    }

    // when
    const res = await signUpUser(JSON.stringify(invalidForm))

    // then
    expect(res.status, 'Status check').toBe(400)
    await expect(res).toBeError(400, [
      {
        code: 'custom',
        path: ['informationAccuracyDeclaration'],
        message: 'Please accept',
      },
    ])
  })

  test('should handle suspended mobile country', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)

    // Suspend the IN country
    await db.update(countryTable).set({ suspendedAt: new Date() }).where(eq(countryTable.code, 'IN'))

    // when
    const res = await signUpUser()
    // console.log(JSON.stringify(await res.json(), null, 2))

    // then
    expect(res.status, 'Status check').toBe(422)
    await expect(res).toBeError(422, [
      {
        code: 'custom',
        path: ['mobileCountryCode'],
        message: 'Mobile country not found or suspended.',
      },
    ])
  })

  test('should create user with encrypted refresh token', async () => {
    // given
    mockGoogleCalls(googleUserInfo, googleTokens)

    // when
    const res = await signUpUser()

    // then
    expect(res.status, 'Status check').toBe(201)

    const users = await db.select().from(userTable)
    const user = users[0]
    expect(user.googleRefreshToken, 'Refresh token should be encrypted').toBeNull()
    // expect(decrypt(user.googleRefreshToken!), 'Decrypted refresh token').toBe(googleTokens.refresh_token)
  })

  test('should handle missing refresh token from Google', async () => {
    // given
    const tokensWithoutRefresh = {
      ...googleTokens,
      refresh_token: undefined,
    }
    mockGoogleCalls(googleUserInfo, tokensWithoutRefresh as unknown as GoogleTokens)

    // when
    const res = await signUpUser()

    // then
    expect(res.status, 'Status check').toBe(429)
    await expect(res).toBeError(429, [
      {
        path: [],
        code: 'custom',
        message: 'Failed to obtain refresh token from google. Please try again',
      },
    ])
  })
})

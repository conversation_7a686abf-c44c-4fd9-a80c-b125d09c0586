import { WarningAlert } from '@ui/common/alerts/WarningAlert'

import { clearAuth, useSignedIn } from './auth-token-store'

export const PublicOnly = ({ pageName, children }: { pageName: string; children: React.ReactNode }) => {
  const signedIn = useSignedIn()
  if (signedIn) {
    return (
      <WarningAlert>
        You are on the {pageName} page. Please{' '}
        <a className="link" onClick={() => clearAuth()}>
          sign out
        </a>{' '}
        to view this page, or{' '}
        <a className="link" href="/">
          click here
        </a>{' '}
        to go to home page.
      </WarningAlert>
    )
  }
  return children
}

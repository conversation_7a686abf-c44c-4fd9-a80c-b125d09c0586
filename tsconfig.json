{"compilerOptions": {"strict": true, "allowJs": true, "checkJs": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "module": "ESNext", "noEmit": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "target": "ES2022", "lib": ["DOM", "DOM.Iterable", "ESNext"], "types": ["vite/client", "vike-react"], "jsx": "preserve", "jsxImportSource": "react", "paths": {"@ui/*": ["./src/pages/*"], "@/*": ["./src/*"]}}, "exclude": ["dist"]}
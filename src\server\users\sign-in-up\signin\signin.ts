import { and, eq, isNull, or, sql } from 'drizzle-orm'

import { encrypt } from '@/server/common/encryption-utils.server'
import { env } from '@/server/common/env.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { profileTable } from '@/server/db/schema/profile-schema'
import { userTable } from '@/server/db/schema/user-schema'
import { withLog } from '@/shared/common/withLog.shared'
import { SignInForm } from '@/shared/users/user-utils.shared'

import { getGoogleUserInfo, GoogleUserInfo } from '../getGoogleUserInfo'
import { createAuthTokenForUser, fallbackLocale } from '../signin-up-utils.server'

import type { UUID } from 'crypto'
import type { Logger } from 'pino'

export const signin = async (log: Logger, form: SignInForm, acceptLanguage?: string) => {
  const { refreshToken, userInfo } = await withLog(log, 'getGoogleUserInfo', () =>
    getGoogleUserInfo(log, form.code, acceptLanguage),
  )

  const user = await findUserByGoogleIdDaf(log, userInfo.sub)

  ensureExists(user, userInfo.sub, 'User')
  await updateUserDaf(log, user.id, userInfo, refreshToken ? encrypt(refreshToken) : null)
  if (form.forUserId) {
    // get token for another user
    await ensureUserIsAdminOrManager(log, user)
    await ensureUserIsNotAdminOrManager(log, form.forUserId)
    return createAuthTokenForUser(env.JWT_SECRET_KEY, form.forUserId, 1) // 1 day validity
  }

  return createAuthTokenForUser(env.JWT_SECRET_KEY, user.id, form.validityDays)
}

const findUserByGoogleIdDaf = (log: Logger, sub: string) =>
  withLog(log, 'findUserByGoogleIdDaf', () =>
    db.query.userTable.findFirst({
      columns: {
        id: true,
        emailVerified: true,
        mobileVerified: true,
      },
      where: eq(userTable.googleId, sub),
    }),
  )

type User = NonNullable<Awaited<ReturnType<typeof findUserByGoogleIdDaf>>>

const updateUserDaf = (log: Logger, userId: UUID, userInfo: GoogleUserInfo, encryptedRefreshToken: string | null) =>
  withLog(log, 'updateUserDaf', () =>
    db
      .update(userTable)
      .set({
        name: userInfo.name,
        email: userInfo.email,
        emailVerified: userInfo.email_verified,
        googleRefreshToken: sql`COALESCE(${encryptedRefreshToken}, google_refresh_token)`,
        googlePictureUrl: userInfo.picture,
        language: sql`COALESCE(${userInfo.language ?? fallbackLocale}, language)`,
      })
      .where(eq(userTable.id, userId)),
  )

const ensureUserIsAdminOrManager = async (log: Logger, user: User) => {
  ensureUser(user)
  const profile = await findAdminOrManagerProfileByUserIdDaf(log, user.id)
  ensure(profile && profile.approvedAt, {
    issueMessage: `You are not an approved admin or manager`,
    logMessage: `User having id ${user.id} is not an approved admin or manager`,
    statusCode: 403,
  })
}

const findAdminOrManagerProfileByUserIdDaf = (log: Logger, userId: UUID, activeOnly = true) =>
  withLog(log, 'findAdminOrManagerProfileByUserIdDaf', () =>
    db.query.profileTable.findFirst({
      columns: {
        approvedAt: true,
      },
      where: and(
        eq(profileTable.userId, userId),
        or(eq(profileTable.role, 'admin'), eq(profileTable.role, 'manager')),
        activeOnly ? isNull(profileTable.suspendedAt) : undefined,
      ),
    }),
  )

const ensureUserIsNotAdminOrManager = async (log: Logger, userId: UUID) => {
  const profile = await findAdminOrManagerProfileByUserIdDaf(log, userId, false)
  ensure(!profile, {
    issueMessage: `Target user is an admin or manager`,
    logMessage: `Target user having id ${userId} is an admin or manager`,
    statusCode: 403,
  })
}

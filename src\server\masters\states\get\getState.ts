import { eq } from 'drizzle-orm'

import { ensureExists } from '@/server/common/error/ensureExists'
import { db } from '@/server/db/db'
import { stateTable } from '@/server/db/schema/master-schema'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'

import type { UUID } from 'crypto'
import type { Logger } from 'pino'

const findStateDaf = (log: Logger, stateId: UUID) =>
  withLog(log, 'findStateDaf', () =>
    db.query.stateTable.findFirst({
      columns: {
        id: true,
        name: true,
      },
      with: {
        country: {
          columns: {
            code: true,
            name: true,
          },
        },
      },
      where: eq(stateTable.id, stateId),
    }),
  )

export const getState = async (log: Logger, stateId: UUID) => {
  const state = await findStateDaf(log, stateId)
  ensureExists(state, stateId, 'State')
  return state
}

export type GetState = typeof getState
export type State = Awaited<ReturnType<GetState>>
setQuery('getState', getState)

import { Hono } from 'hono'

import { addAcademyHandler } from './academies/add/addAcademyHandler'
import { approveAcademyHandler } from './academies/approve/approveAcademyHandler'
import { getAcademyHandler } from './academies/get/getAcademyHandler'
import { listAcademiesHandler } from './academies/list/listAcademiesHandler'
import { sendAcademyMobileOtpHandler } from './academies/send-mobile-otp/sendAcademyMobileOtpHandler'
import { sendAcademyVerificationMailHandler } from './academies/send-verification-mail/sendAcademyVerificationMailHandler'
import { updateAcademyHandler } from './academies/update/updateAcademyHandler'
import { verifyAcademyEmailHandler } from './academies/verify-email/verifyAcademyEmailHandler'
import { verifyAcademyMobileHandler } from './academies/verify-mobile/verifyAcademyMobileHandler'
import { getHealthHandler } from './actuator/health/healthHandler'
import { listStudentEmailsOfBatchHandler } from './batches/batch-students/emails/listStudentsEmailsOfBatchHandler'
import { listStudentsOfBatchHandler } from './batches/batch-students/list/listStudentsOfBatchHandler'
import { getMyBatchStudentHandler } from './batches/batch-students/my/getMyBatchStudentHandler'
import { addBatchHandler } from './batches/batches/add/addBatchHandler'
import { addBatchAttachmentHandler } from './batches/batches/attachments/add/addBatchAttachmentHandler'
import { listBatchAttachmentsHandler } from './batches/batches/attachments/list/listBatchAttachmentsHandler'
import { removeBatchAttachmentHandler } from './batches/batches/attachments/remove/removeBatchattachmentHandler'
import { addBatchEventHandler } from './batches/batches/events/add/addBatchEventHandler'
import { removeBatchEventHandler } from './batches/batches/events/remove/removeBatchEventHandler'
import { updateBatchEventHandler } from './batches/batches/events/update/updateBatchEventHandler'
import { getBatchHandler } from './batches/batches/get/getBatchHandler'
import { joinBatchHandler } from './batches/batches/join/joinBatchHandler'
import { leaveBatchHandler } from './batches/batches/leave/leaveBatchHandler'
import { listCourseBatchesHandler } from './batches/batches/list/course-batches/listCourseBatchesHandler'
import { listStudentBatchesHandler } from './batches/batches/list/student-batches/listStudentBatchesHandler'
import { listTeacherBatchesHandler } from './batches/batches/list/teacher-batches/listTeacherBatchesHandler'
import { addStudentFeePaymentHandler } from './batches/batches/payments/add/addStudentFeePaymentHandler'
import { getStudentFeePaymentHandler } from './batches/batches/payments/get/getStudentFeePaymentHandler'
import { countStudentFeePaymentItemsHandler } from './batches/batches/payments/item-count/countStudentFeePaymentItemsHandler'
import { listStudentFeePaymentItemsOfBatchHandler } from './batches/batches/payments/items/list-for-batch/listStudentFeePaymentItemsOfBatchHandler'
import { listStudentFeePaymentsHandler } from './batches/batches/payments/list/listStudentFeePaymentsHandler'
import { listStudentFeePaymentsOfAcademyHandler } from './batches/batches/payments/list-for-academy/listStudentFeePaymentsOfAcademyHandler'
import { payStudentFeePaymentHandler } from './batches/batches/payments/pay/payStudentFeePaymentHandler'
import { processStudentBillingHandler } from './batches/batches/payments/process/processStudentBillingHandler'
import { receiveStudentFeePaymentHandler } from './batches/batches/payments/receive/receiveStudentFeePaymentHandler'
import { removeStudentFeePaymentItemHandler } from './batches/batches/payments/remove/removeStudentFeePaymentItemHandler'
import { recommendBatchHandler } from './batches/batches/recommend/recommendBatchHandler'
import { removeBatchHandler } from './batches/batches/remove/removeBatchHandler'
import { updateBatchHandler } from './batches/batches/update/updateBatchHandler'
import { addCourseHandler } from './courses/courses/add/addCourseHandler'
import { addCourseAttachmentHandler } from './courses/courses/attachments/add/addCourseAttachmentHandler'
import { getCourseAttachmentHandler } from './courses/courses/attachments/get/getCourseAttachmentHandler'
import { getCourseAttachmentUrlHandler } from './courses/courses/attachments/get-url/getCourseAttachmentUrlHandler'
import { moveCourseAttachmentHandler } from './courses/courses/attachments/move/moveCourseAttachmentHandler'
import { removeCourseAttachmentHandler } from './courses/courses/attachments/remove/removeCourseAttachmentHandler'
import { updateCourseAttachmentHandler } from './courses/courses/attachments/update/updateCourseAttachmentHandler'
import { markUploadedCourseAttachmentHandler } from './courses/courses/attachments/uploaded/markUploadedCourseAttachmentHandler'
import { getCourseHandler } from './courses/courses/get/getCourseHandler'
import { listCoursesHandler } from './courses/courses/list/listCoursesHandler'
import { publishCourseHandler } from './courses/courses/publish/publishCourseHandler'
import { addTagToCourseHandler } from './courses/courses/tag-assignments/add/addTagToCourseHandler'
import { removeTagFromCourseHandler } from './courses/courses/tag-assignments/remove/removeTagFromCourseHandler'
import { updateCourseHandler } from './courses/courses/update/updateCourseHandler'
import { getCourseTagHandler } from './courses/tags/get/getCourseTagHandler'
import { listCourseTagsHandler } from './courses/tags/list/listCourseTagsHandler'
import { listCountriesHandler } from './masters/countries/list/listCountriesHandler'
import { getDistrictHandler } from './masters/districts/get/getDistrictHandler'
import { listDistrictsHandler } from './masters/districts/list/listDistrictsHandler'
import { getCommonEnvHandler } from './masters/env/getCommonEnvHandler'
import { getStateHandler } from './masters/states/get/getStateHandler'
import { listStatesHandler } from './masters/states/list/listStatesHandler'
import { addProfileHandler } from './profiles/add/addProfileHandler'
import { approveProfileHandler } from './profiles/approve/approveProfileHandler'
import { getProfileHandler } from './profiles/get/getProfileHandler'
import { getProfileContactHandler } from './profiles/get-contact/getProfileContactHandler'
import { getMyProfilesHandler } from './profiles/get-my/getMyProfilesHandler'
import { getProfilesToAppproveHandler } from './profiles/get-profiles-to-approve/getProfilesToAppproveHandler'
import { inviteProfileHandler } from './profiles/invite/inviteProfileHandler'
import { listProfilesHandler } from './profiles/list/listProfilesHandler'
import { removeProfileHandler } from './profiles/remove/removeProfileHandler'
import { submitProfileForApprovalHandler } from './profiles/submit-for-approval/submitProfileForApprovalHandler'
import { updateProfileHandler } from './profiles/update/updateProfileHandler'
import { getUserHandler } from './users/get-user/getUserHandler'
import { invalidateTokensHandler } from './users/invalidate-tokens/invalidateTokensHandler'
import { revokeAllGoogleTokensHandler } from './users/revoke-all-google-tokens/revokeAllGoogleTokensHandler'
import { sendMobileOtpHandler } from './users/send-mobile-otp/sendMobileOtpHandler'
import { signInHandler } from './users/sign-in-up/signin/signinHandler'
import { signupHandler } from './users/sign-in-up/signup/signupHandler'
import { updateUserHandler } from './users/sign-in-up/update/updateUserHandler'
import { getUserTnCsHandler } from './users/tncs/get-user-tncs/getUserTnCsHandler'
import { verifyMobileHandler } from './users/verify-mobile/verifyMobileHandler'

export const apiRoutes = new Hono()

  // Actuator endpoints
  .route('/actuator/health', getHealthHandler)

  // masters
  .route('/masters/env', getCommonEnvHandler)
  .route('/masters/countries', listCountriesHandler)
  .route('/masters/states', listStatesHandler)
  .route('/masters/states/:id', getStateHandler)
  .route('/masters/districts', listDistrictsHandler)
  .route('/masters/districts/:id', getDistrictHandler)

  // users
  .route('/users/signup', signupHandler)
  .route('/users/signin', signInHandler)
  .route('/users/tncs', getUserTnCsHandler)

  // current user
  .route('/users/me', getUserHandler)
  .route('/users/me', updateUserHandler)
  .route('/users/me/mobile-otp', sendMobileOtpHandler)
  .route('/users/me/mobile-verification', verifyMobileHandler)
  .route('/users/me/invalidate-tokens', invalidateTokensHandler)

  // profiles
  .route('/profiles', addProfileHandler)
  .route('/profiles', listProfilesHandler)
  .route('/users/me/profiles', getMyProfilesHandler)
  .route('/profiles/:id', getProfileHandler)
  .route('/profiles/:id', updateProfileHandler)
  .route('/profiles/:id', removeProfileHandler)
  .route('/profiles/:id/submit-for-approval', submitProfileForApprovalHandler)
  .route('/profiles/:id/to-approve', getProfilesToAppproveHandler)
  .route('/profiles/:id/approve', approveProfileHandler)
  .route('/profiles/invite', inviteProfileHandler)
  .route('/profiles/:id/batches', listTeacherBatchesHandler)
  .route('/profiles/:id/contact', getProfileContactHandler)

  // Academies
  .route('/academies', addAcademyHandler)
  .route('/academies', listAcademiesHandler)
  .route('/academies/:id', getAcademyHandler)
  .route('/academies/:id', updateAcademyHandler)
  .route('/academies/:id/approve', approveAcademyHandler)
  .route('/academies/:id/mobile-otp', sendAcademyMobileOtpHandler)
  .route('/academies/:id/mobile-verification', verifyAcademyMobileHandler)
  .route('/academies/:id/verification-mail', sendAcademyVerificationMailHandler)
  .route('/academies/:id/email-verification', verifyAcademyEmailHandler)
  .route('/academies/:id/payments', listStudentFeePaymentsOfAcademyHandler)

  // courses
  .route('/course-tags', listCourseTagsHandler)
  .route('/course-tags/:id', getCourseTagHandler)
  .route('/courses', listCoursesHandler)
  .route('/courses', addCourseHandler)
  .route('/courses/:id', getCourseHandler)
  .route('/courses/:id', updateCourseHandler)
  .route('/courses/:id/publish', publishCourseHandler)
  .route('/courses/:id/tags', addTagToCourseHandler)
  .route('/courses/:id/tags/:tagId', removeTagFromCourseHandler)
  .route('/courses/:id/attachments', addCourseAttachmentHandler)
  .route('/course-attachments/:id/uploaded', markUploadedCourseAttachmentHandler)
  .route('/course-attachments/:id', getCourseAttachmentHandler)
  .route('/course-attachments/:id', removeCourseAttachmentHandler)
  .route('/course-attachments/:id', updateCourseAttachmentHandler)
  .route('/course-attachments/:id/position', moveCourseAttachmentHandler)
  .route('/course-attachments/:id/url', getCourseAttachmentUrlHandler)

  // batches
  .route('/batches', addBatchHandler)
  .route('/batches/:id', getBatchHandler)
  .route('/courses/:id/batches', listCourseBatchesHandler)
  .route('/batches/:id', updateBatchHandler)
  .route('/batches/:id', removeBatchHandler)
  .route('/batches/:id/recommend', recommendBatchHandler)
  .route('/batches/:id/students', joinBatchHandler)
  .route('/batches/:id/students', leaveBatchHandler)
  .route('/batches/:id/students', listStudentsOfBatchHandler)
  .route('/batches/:id/student-emails', listStudentEmailsOfBatchHandler)
  .route('/batches/:id/students/me', getMyBatchStudentHandler)
  .route('/batches/:id/payments', addStudentFeePaymentHandler)
  .route('/batches/:id/payments', listStudentFeePaymentItemsOfBatchHandler)
  .route('/batches/:id/attachments', listBatchAttachmentsHandler)
  .route('/batches/:id/attachments', addBatchAttachmentHandler)
  .route('/batches/:id/attachments/:attachmentId', removeBatchAttachmentHandler)
  .route('/batch-events', addBatchEventHandler)
  .route('/batch-events/:id', updateBatchEventHandler)
  .route('/batch-events/:id', removeBatchEventHandler)
  .route('/students/me/batches', listStudentBatchesHandler)
  .route('/students/me/payments', listStudentFeePaymentsHandler)
  .route('/students/me/payments/item-count', countStudentFeePaymentItemsHandler)
  .route('/schedule/student-billing', processStudentBillingHandler)
  .route('/students-fee-payments/:id', getStudentFeePaymentHandler)
  .route('/students-fee-payments/:id/pay', payStudentFeePaymentHandler)
  .route('/students-fee-payments/:id/receive', receiveStudentFeePaymentHandler)
  .route('/students-fee-payment-items/:id', removeStudentFeePaymentItemHandler)

  // Admin
  .route('/admin/revoke-all-google-tokens', revokeAllGoogleTokensHandler)

export type ApiType = typeof apiRoutes

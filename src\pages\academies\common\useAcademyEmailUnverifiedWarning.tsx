import clsx from 'clsx'

import { UNKNOWN_UUID } from '@/shared/common/common-utils.shared'

import { useMyAcademyQuery } from './academy-queries'
import { useSendAcademyVerificationMail } from './useSendAcademyVerificationMail'

export const useAcademyEmailUnverifiedWarning = () => {
  const { data: academy } = useMyAcademyQuery()
  const { sendAcademyVerificationMail, isPending } = useSendAcademyVerificationMail(academy?.id ?? UNKNOWN_UUID)

  if (!academy || academy.emailVerified) return null
  return () => (
    <span>
      Please{' '}
      <button
        onClick={sendAcademyVerificationMail}
        className={clsx('link', isPending && 'cursor-wait')}
        disabled={isPending}
      >
        verify
      </button>{' '}
      the email of your academy.
    </span>
  )
}

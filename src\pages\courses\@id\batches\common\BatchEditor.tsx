import { UUID } from 'crypto'

import { Combobox, ComboboxButton, ComboboxInput, ComboboxOption, ComboboxOptions, Label } from '@headlessui/react'
import { ChevronDownIcon } from '@heroicons/react/16/solid'
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/react/20/solid'
import Fuse from 'fuse.js'
import { useEffect, useState } from 'react'

import { TinyProfile } from '@/server/profiles/list/listProfiles'
import { $EditBatchForm, BILLING_CYCLES, BillingCycle, EditBatchForm } from '@/shared/batch/batch-utils.shared'
import { UNKNOWN_UUID } from '@/shared/common/common-utils.shared'
import { OmniError } from '@/shared/common/error-utils.shared'
import { useMyAcademyQuery } from '@ui/academies/common/academy-queries'
import { ErrorAlert } from '@ui/common/alerts/ErrorAlert'
import { CheckBox } from '@ui/common/form/CheckBox'
import { Grid6 } from '@ui/common/form/page-layout'
import { ShowErrors } from '@ui/common/ShowErrors'
import { useProfilesSuspenseQuery } from '@ui/profiles/common/profile-queries'

export const BatchEditor = ({
  formData,
  setFormData,
  error,
  setError,
  courseAcademyId,
}: {
  formData: EditBatchForm
  setFormData: (form: EditBatchForm) => void
  error?: OmniError
  setError: (error: OmniError | undefined) => void
  courseAcademyId: UUID | undefined
}) => {
  const { data: myAcademy } = useMyAcademyQuery()

  // Validate form data when it changes
  useEffect(() => {
    const parsed = $EditBatchForm.safeParse(formData)
    setError(parsed.error)
  }, [formData])

  useEffect(() => {
    if (myAcademy) {
      setFormData({
        ...formData,
        fee: {
          currency: myAcademy.currency,
          amount: formData.fee.amount,
        },
      })
    }
  }, [myAcademy])

  if (myAcademy?.id !== courseAcademyId) {
    return <ErrorAlert>You must be a part of the academy to edit this batch.</ErrorAlert>
  }

  return (
    <Grid6>
      <div className="sm:col-span-6">
        <TeacherSelect
          teacherId={formData.teacherId}
          setTeacherId={(teacherId) => setFormData({ ...formData, teacherId: teacherId })}
          error={error}
        />
      </div>
      <div className="sm:col-span-3">
        <label htmlFor="seat-count" className="form-label">
          Number of seats
        </label>
        <div className="mt-2">
          <input
            id="seat-count"
            name="seat-count"
            type="number"
            className="form-input"
            value={formData.seatCount}
            onChange={(e) => setFormData({ ...formData, seatCount: Number(e.target.value) })}
          />
        </div>
        <ShowErrors error={error} path={['seatCount']} />
      </div>
      <div className="sm:col-span-3">
        <div className="sm:mt-9">
          <CheckBox
            id="admission-open"
            label="Admission open"
            checked={formData.admissionOpen}
            onChange={(e) => setFormData({ ...formData, admissionOpen: e.target.checked })}
          />
        </div>
      </div>
      <div className="sm:col-span-3">
        <label htmlFor="start-date" className="form-label">
          Start date
        </label>
        <div className="mt-2">
          <input
            id="start-date"
            name="start-date"
            type="date"
            className="form-input"
            value={formData.startDate}
            onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
          />
        </div>
        <ShowErrors error={error} path={['startDate']} />
      </div>

      <div className="sm:col-span-3">
        <div>
          <label htmlFor="price" className="block text-sm/6 font-medium text-gray-900">
            Duration
          </label>
          <div className="mt-2">
            <div className="flex items-center rounded-md bg-white pl-3 outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline-2 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-600">
              <input
                id="cycle-count"
                name="cycle-count"
                type="number"
                placeholder="10"
                className="block min-w-0 grow py-1.5 pr-3 pl-1 text-base text-gray-900 placeholder:text-gray-400 focus:outline-none sm:text-sm/6"
                value={formData.cycleCount}
                onChange={(e) => setFormData({ ...formData, cycleCount: Number(e.target.value) })}
              />
              <div className="grid shrink-0 grid-cols-1 focus-within:relative">
                <select
                  id="billing-cycle"
                  name="billing-cycle"
                  aria-label="Billing cycle"
                  className="col-start-1 row-start-1 w-full appearance-none rounded-md py-1.5 pr-7 pl-3 text-base text-gray-500 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                  value={formData.billingCycle}
                  onChange={(e) => setFormData({ ...formData, billingCycle: e.target.value as BillingCycle })}
                >
                  {BILLING_CYCLES.map((cycle) => (
                    <option key={cycle} value={cycle}>
                      {cycle}
                    </option>
                  ))}
                </select>
                <ChevronDownIcon
                  aria-hidden="true"
                  className="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 sm:size-4"
                />
              </div>
            </div>
          </div>
        </div>{' '}
        <ShowErrors error={error} path={['cycleCount']} />
      </div>

      <div className="sm:col-span-3">
        <label htmlFor="fee" className="block text-sm/6 font-medium text-gray-900">
          Fee per {formData.billingCycle}
        </label>
        <div className="mt-2">
          <div className="flex items-center rounded-md bg-white pl-3 outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline-2 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-600">
            <input
              id="fee"
              name="fee"
              type="text"
              placeholder="0.00"
              className="block min-w-0 grow py-1.5 pr-3 pl-1 text-base text-gray-900 placeholder:text-gray-400 focus:outline-none sm:text-sm/6"
              value={formData.fee.amount}
              onChange={(e) => setFormData({ ...formData, fee: { ...formData.fee, amount: Number(e.target.value) } })}
            />
            <div id="price-currency" className="shrink-0 text-base text-gray-500 select-none sm:text-sm/6 pr-3">
              {formData.fee.currency}
            </div>
          </div>
          <ShowErrors error={error} path={['fee']} />
        </div>
      </div>
      <div className="sm:col-span-3">
        <label htmlFor="grace-days" className="form-label">
          Grace days
        </label>
        <div className="mt-2">
          <input
            id="grace-days"
            name="grace-days"
            type="number"
            className="form-input"
            value={formData.graceDays}
            onChange={(e) => setFormData({ ...formData, graceDays: Number(e.target.value) })}
          />
          <p className="form-descr">Days a student can delay paying fee before getting restricted.</p>
        </div>
        <ShowErrors error={error} path={['graceDays']} />
      </div>
    </Grid6>
  )
}

const TeacherSelect = ({
  teacherId,
  setTeacherId,
  error,
}: {
  teacherId: UUID
  setTeacherId: (id: UUID) => void
  error?: OmniError
}) => {
  const [query, setQuery] = useState('')

  const { data: myAcademy } = useMyAcademyQuery()
  const { data } = useProfilesSuspenseQuery({ academyId: myAcademy?.id, role: 'teacher' })
  const teachers = data || []
  const [selectedTeacher, setSelectedTeacher] = useState<TinyProfile | null>(
    teachers.find((t) => t.id === teacherId) ?? null,
  )

  const fuse = new Fuse(teachers, {
    includeScore: true,
    threshold: 0.2,
    keys: ['displayName'],
  })

  const filteredTeachers = fuse.search(query, { limit: 10 })

  return (
    <Combobox
      as="div"
      value={selectedTeacher}
      onChange={(teacher) => {
        setQuery('')
        setSelectedTeacher(teacher)
        setTeacherId(teacher?.id ?? UNKNOWN_UUID)
      }}
    >
      <Label className="block text-sm/6 font-medium text-gray-900">Teacher</Label>
      <div className="relative mt-2">
        <div className="relative">
          <ComboboxInput
            className="w-full rounded-md bg-white py-1.5 pr-10 pl-3 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
            onChange={(event) => setQuery(event.target.value)}
            onBlur={() => setQuery('')}
            displayValue={(teacher: TinyProfile) => teacher?.displayName ?? ''}
          />
          <ComboboxButton className="absolute inset-y-0 right-0 flex items-center pr-2">
            <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
          </ComboboxButton>
        </div>

        {filteredTeachers.length > 0 && (
          <ComboboxOptions className="absolute z-10 mt-1 max-h-56 w-full overflow-auto rounded-md bg-white py-1 text-base ring-1 shadow-lg ring-black/5 focus:outline-hidden sm:text-sm">
            {filteredTeachers.map((teacher) => (
              <ComboboxOption
                key={teacher.item.id}
                value={teacher.item}
                className="group relative cursor-default py-2 pr-9 pl-3 text-gray-900 select-none data-focus:bg-indigo-600 data-focus:text-white data-focus:outline-hidden"
              >
                <div className="flex items-center">
                  <img src={teacher.item.googlePictureUrl} alt="" className="size-6 shrink-0 rounded-full" />
                  <span className="ml-3 truncate group-data-selected:font-semibold">{teacher.item.displayName}</span>
                </div>

                <span className="absolute inset-y-0 right-0 hidden items-center pr-4 text-indigo-600 group-data-focus:text-white group-data-selected:flex">
                  <CheckIcon className="size-5" aria-hidden="true" />
                </span>
              </ComboboxOption>
            ))}
          </ComboboxOptions>
        )}
        <ShowErrors error={error} path={['teacherId']} />
      </div>
    </Combobox>
  )
}

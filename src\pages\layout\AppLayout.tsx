import { Dialog, DialogBackdrop, DialogPanel, TransitionChild } from '@headlessui/react'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import { GoogleOAuthProvider } from '@react-oauth/google'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import '@ui/common/assets/app.css'
import { lazy, Suspense } from 'react'
import { clientOnly } from 'vike-react/clientOnly'

import { ShowData } from '@ui/common/ShowData'

import { useCommonEnvSuspenseQuery } from '../masters/common/master-queries'

import { Content } from './Content'
import { setSidebarOpen, useSidebarOpen } from './layout-store'
import { LayoutFooter } from './LayoutFooter'
import { LayoutHeader } from './LayoutHeader'
import { LayoutSidebar } from './LayoutSidebar'

const BlankComponent = lazy(() => import('@ui/common/BlankComponent'))
const GlobalAlerts = clientOnly(async () => (await import('./GlobalAlerts')).GlobalAlerts)
const GlobalDialogs = clientOnly(async () => (await import('./GlobalDialogs')).GlobalDialogs)

const CookieConsentBanner = clientOnly(
  async () => (await import('@ui/common/cookie-consent/CookieConsentModal')).CookieConsentModal,
)

export const AppLayout = ({ children }: { children: React.ReactNode }) => {
  const { data: env, error } = useCommonEnvSuspenseQuery()
  return (
    <ShowData error={error}>
      <Suspense fallback={<div>Loading...</div>}>
        <BlankComponent />
      </Suspense>

      <GoogleOAuthProvider clientId={env!.GOOGLE_CLIENT_ID}>
        <RawAppLayout children={children} />
      </GoogleOAuthProvider>
    </ShowData>
  )
}

const RawAppLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      {/*
        This example requires updating your template (in +config.ts):

        ```
        <html class="h-full bg-white">
        <body class="h-full">
        ```
      */}
      <ReactQueryDevtools />
      <div className="min-h-screen flex flex-col">
        <SidebarDialog />

        {/* Static sidebar for desktop */}
        <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-67 lg:flex-col">
          <LayoutSidebar />
        </div>

        <div className="lg:pl-67 flex flex-col min-h-screen">
          <div className="flex h-22 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-xs sm:gap-x-6 sm:px-6 lg:px-8">
            <button type="button" onClick={() => setSidebarOpen(true)} className="-m-2.5 p-2.5 text-gray-700 lg:hidden">
              <span className="sr-only">Open sidebar</span>
              <Bars3Icon aria-hidden="true" className="size-6" />
            </button>

            {/* Separator */}
            <div aria-hidden="true" className="h-6 w-px bg-gray-900/10 lg:hidden" />
            <LayoutHeader />
          </div>

          <main className="flex-grow p-4 sm:p-6 lg:p-8">
            <GlobalAlerts />
            <Content>{children}</Content>
          </main>
          <LayoutFooter />
        </div>
      </div>
      <GlobalDialogs />
      <CookieConsentBanner />
    </>
  )
}

const SidebarDialog = () => {
  const sidebarOpen = useSidebarOpen()
  return (
    <Dialog open={sidebarOpen} onClose={setSidebarOpen} className="relative z-50 lg:hidden">
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-gray-900/80 transition-opacity duration-300 ease-linear data-closed:opacity-0"
      />
      <div className="fixed inset-0 flex">
        <DialogPanel
          transition
          className="relative mr-16 flex w-full max-w-67 flex-1 transform transition duration-300 ease-in-out data-closed:-translate-x-full"
        >
          <TransitionChild>
            <div className="absolute top-0 left-full flex w-16 justify-center pt-5 duration-300 ease-in-out data-closed:opacity-0">
              <button type="button" onClick={() => setSidebarOpen(false)} className="-m-2.5 p-2.5">
                <span className="sr-only">Close sidebar</span>
                <XMarkIcon aria-hidden="true" className="size-6 text-white" />
              </button>
            </div>
          </TransitionChild>
          <LayoutSidebar />
        </DialogPanel>
      </div>
    </Dialog>
  )
}

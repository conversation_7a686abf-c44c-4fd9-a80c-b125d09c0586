# Deployment

## Install Docker locally

Install Docker Desktop from [here](https://www.docker.com/products/docker-desktop/)

## Create image

Create image by using command `pnpm dockerize`

Run image locally by using command
```bash
docker run -p 3000:3000 --env-file .env -e NODE_ENV="production" -e DATABASE_URL="postgresql://api:<EMAIL>:5432/np-tuition-vi" np-tuition-vr
```

## Deploy to DigitalOcean

DigitalOcean has two App platforms created for this project. Both use the Dockerfile in the root of the project. The staging app targets the `main` branch, whereas the production app targets the `production` branch.

To deploy to production, in VSCode

1. Switch to the `production` branch
1. In the Git panel, in more actions ..., go to `Branch` -> `Rebash Branch` and choose `main`
1. Once rebased, push the changes to the remote repository
1. Switch back to main

### Environment variables

Environment variables are created in the DigitalOcean control panel, by referring to LastPass.

# Database

## Database Seeding

After the database is created, and first user is created, you can make the user an ADMIN by running the following SQL command:

```sql
INSERT INTO profile (id, user_id, display_name, role, submitted_for_approval_at, approved_at, tncs_accepted_at, created_at)
VALUES ('ce80c215-5efc-4108-8028-d845b6426a42', (SELECT id FROM usr WHERE email = '<EMAIL>'), 'Sanjay Patel', 'admin', NOW(), NOW(), NOW(), NOW());
```

# Logging

View logs here: https://telemetry.betterstack.com/team/309382/tail?s=1279445&rf=now-30m&pt=overview

# Cron Jobs

Cron jobs are configured at https://console.cron-job.org/jobs/folders/46625

# Google Search Console

https://search.google.com/search-console?resource_id=sc-domain%3Atuitionlance.com


import pino from 'pino'

import { env } from '@/server/common/env.server'
import { setQuery } from '@/shared/common/serverQueries.shared'

export const getCommonEnv = (log: pino.Logger) => {
  const commonEnv = {
    APP_ENV: env.APP_ENV,
    GOOGLE_CLIENT_ID: env.GOOGLE_CLIENT_ID,
  }
  log.info({ e: commonEnv }, 'Got common env')
  return commonEnv
}

export type GetCommonEnv = typeof getCommonEnv
export type CommonEnv = ReturnType<GetCommonEnv>
setQuery('getCommonEnv', getCommonEnv)

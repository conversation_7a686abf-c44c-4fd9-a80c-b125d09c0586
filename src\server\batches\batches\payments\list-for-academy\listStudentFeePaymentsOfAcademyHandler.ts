import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $StudentFeePaymentOfAcademySearch } from '@/shared/batch/batch-utils.shared'
import { $HasId } from '@/shared/common/common-utils.shared'

import { listStudentFeePaymentsOfAcademy } from './listStudentFeePaymentsOfAcademy'

export const listStudentFeePaymentsOfAcademyHandler = new Hono<HonoVars>().get(
  '/',
  zValidator('param', $HasId),
  zV<PERSON>dator('query', $StudentFeePaymentOfAcademySearch),
  async (c) => {
    const { id: academyId } = c.req.valid('param')
    const search = c.req.valid('query')
    const batchStudentPayments = await listStudentFeePaymentsOfAcademy(getCtx(c), academyId, search)
    return c.json({ rows: batchStudentPayments }, 200)
  },
)

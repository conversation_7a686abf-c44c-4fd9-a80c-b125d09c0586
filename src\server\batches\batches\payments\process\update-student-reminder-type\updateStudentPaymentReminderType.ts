import pino from 'pino'

import { appendCommandDaf } from '@/server/common/command/processCommands'
import { mailFlock } from '@/server/common/mail/mail'
import { smsFlock } from '@/server/common/sms/sms'
import { Transaction } from '@/server/db/db'
import { getNextPaymentDueOn } from '@/shared/batch/batch-due-utils'
import { getPaymentReminderTypeToSend } from '@/shared/common/payment-utils/payment-utils.shared'

import { updateGoogleEventForBatch } from '../../../common/updateGoogleEventForBatch'
import {
  MAIL_STUDENT_PAYMENT_REMINDER_COMMAND,
  MailStudentPaymentReminderCommandData,
} from '../mailStudentPaymentReminderCommand'
import {
  SMS_STUDENT_PAYMENT_REMINDER_COMMAND,
  SmsStudentPaymentReminderCommandData,
} from '../smsStudentPaymentReminderCommand'

import { Batch, BatchStudent, updateLastReminderTypeDaf } from './updateStudentPaymentReminderTypeHelper'

export const updateStudentPaymentReminderType = async (
  log: pino.Logger,
  db: Transaction,
  batch: Batch,
  batchStudent: BatchStudent,
) => {
  const nextDueOn = getNextPaymentDueOn(batch, batchStudent)
  const reminderType = getPaymentReminderTypeToSend(nextDueOn, batch.graceDays)
  if (reminderType === batchStudent.lastReminderType) return // no change from last time
  await updateLastReminderTypeDaf(log, db, batchStudent, reminderType)
  if (!reminderType) return // no need to send any reminder

  await appendCommandDaf<MailStudentPaymentReminderCommandData>(db, log, mailFlock(), {
    command: MAIL_STUDENT_PAYMENT_REMINDER_COMMAND,
    data: {
      batchStudentId: batchStudent.id,
      reminderType,
      dueDate: nextDueOn!.toISODate()!,
    },
  })
  await appendCommandDaf<SmsStudentPaymentReminderCommandData>(db, log, smsFlock(), {
    command: SMS_STUDENT_PAYMENT_REMINDER_COMMAND,
    data: {
      batchStudentId: batchStudent.id,
      reminderType,
      dueDate: nextDueOn!.toISODate()!,
    },
  })
  if (reminderType === 'blocked') {
    await updateGoogleEventForBatch(db, log, batch.teacher.user.id, batch.events)
  }
}

import { UUID } from 'crypto'

import { ArrowDownIcon, ArrowUpIcon } from '@heroicons/react/20/solid'
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { Fragment, useMemo } from 'react'

import { useAcademySuspenseQuery } from '@/pages/academies/common/academy-queries'
import { ShowData } from '@/pages/common/ShowData'
import { StudentFeePayment } from '@/server/batches/batches/payments/list/listStudentFeePayments'
import { academyPath } from '@/shared/academies/academy-utils.shared'
import { StudentFeePaymentsSearch } from '@/shared/batch/batch-utils.shared'
import { formatJsDate2Timestamp } from '@/shared/common/date-utils.shared'
import { DividerLink } from '@ui/common/DividerLink'
import { PaymentStatusBadge } from '@ui/common/payment/PaymentStatusBadge'
import { getUrl } from '@ui/common/utils/url-utils'
import { useStudentFeePaymentsQuery } from '@ui/courses/@id/batches/common/student-fee-payment-queries'

export const StudentFeePaymentsPage = ({ search }: { search: StudentFeePaymentsSearch }) => {
  const { data: payments, isPending, error } = useStudentFeePaymentsQuery(search)

  return (
    <ShowData isPending={isPending} error={error} spinnerSize="1.25rem">
      <ViewPayments payments={payments!} search={search} />
    </ShowData>
  )
}

const ViewPayments = ({ payments, search }: { payments: StudentFeePayment[]; search: StudentFeePaymentsSearch }) => {
  // Group payments by academyId
  const paymentsByAcademy = useMemo(() => {
    const grouped: Record<string, StudentFeePayment[]> = {}
    payments.forEach((payment) => {
      if (!grouped[payment.academyId]) {
        grouped[payment.academyId] = []
      }
      grouped[payment.academyId].push(payment)
    })
    return grouped
  }, [payments])

  // Calculate previous and next page search parameters
  const prevSearch: StudentFeePaymentsSearch = { ...search, previous: 'true' }
  const nextSearch: StudentFeePaymentsSearch = { ...search, previous: undefined }
  if (payments.length > 0) {
    // Set cursor for previous page (first item)
    prevSearch.fromAcademyId = payments[0].academyId
    prevSearch.fromCreatedAt = payments[0].createdAt.toISOString()
    prevSearch.beyondId = payments[0].id

    // Set cursor for next page (last item)
    nextSearch.fromAcademyId = payments[payments.length - 1].academyId
    nextSearch.fromCreatedAt = payments[payments.length - 1].createdAt.toISOString()
    nextSearch.beyondId = payments[payments.length - 1].id
  } else {
    // No payments case
    prevSearch.fromAcademyId = undefined
    prevSearch.fromCreatedAt = undefined
    prevSearch.beyondId = undefined
    nextSearch.fromAcademyId = undefined
    nextSearch.fromCreatedAt = undefined
    nextSearch.beyondId = undefined
  }

  return (
    <div>
      <PaymentDueWarning payments={payments} />
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <h2 className="mx-auto max-w-2xl text-base font-semibold text-gray-900 lg:mx-0 lg:max-w-none">My Payments</h2>
        {payments.some((payment) => payment.status === 'draft') && (
          <p className="mt-2 text-sm text-gray-700">Please complete the draft payments.</p>
        )}
      </div>

      {/* Previous page link */}
      <DividerLink href={getUrl('/students/me/payments', prevSearch)}>
        <ArrowUpIcon aria-hidden="true" className="-mr-0.5 -ml-1 size-5 text-gray-400" />
        Previous
      </DividerLink>

      <div className="mt-6 overflow-hidden border-t border-gray-100">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:mx-0 lg:max-w-none">
            <table className="w-full text-left">
              <tbody>
                {Object.keys(paymentsByAcademy).length > 0 ?
                  Object.entries(paymentsByAcademy).map(([academyId, academyPayments]) => (
                    <AcademyPaymentsRows key={academyId} academyId={academyId as UUID} payments={academyPayments} />
                  ))
                : <tr>
                    <td colSpan={3} className="py-4 text-center text-gray-500">
                      No payments on this page.
                    </td>
                  </tr>
                }
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Next page link */}
      <DividerLink href={getUrl('/students/me/payments', nextSearch)}>
        <ArrowDownIcon aria-hidden="true" className="-mr-0.5 -ml-1 size-5 text-gray-400" />
        Next
      </DividerLink>
    </div>
  )
}

const PaymentDueWarning = ({ payments }: { payments: StudentFeePayment[] }) => {
  const draftPayments = payments.filter((payment) => payment.status === 'draft')

  if (draftPayments.length === 0) return null

  return (
    <div className="rounded-md bg-yellow-50 p-4 mb-6">
      <div className="flex">
        <div className="flex-shrink-0">
          <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" aria-hidden="true" />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-yellow-800">Payment Due</h3>
          <div className="mt-2 text-sm text-yellow-700">
            <p>
              You have {draftPayments.length} draft payment{draftPayments.length !== 1 ? 's' : ''} that need to be
              completed.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

const AcademyPaymentsRows = ({ academyId, payments }: { academyId: UUID; payments: StudentFeePayment[] }) => {
  const { data: academy } = useAcademySuspenseQuery(academyId, false)

  if (!academy) return null

  return (
    <Fragment>
      <tr className="text-sm/6 text-gray-900 border-t border-gray-200">
        <th scope="colgroup" colSpan={3} className="py-2 font-semibold bg-gray-50 px-4">
          <a href={academyPath(academy)} className="text-indigo-600 hover:text-indigo-700">
            {academy.name}
          </a>
        </th>
      </tr>
      {payments.map((payment) => (
        <tr key={payment.id}>
          <td className="relative py-5 pr-6 pl-4">
            <div className="flex gap-x-6">
              <div className="flex-auto">
                <div className="text-sm/6 font-medium text-gray-900">{formatJsDate2Timestamp(payment.createdAt)}</div>
              </div>
            </div>
            <div className="absolute bottom-0 left-0 h-px w-screen bg-gray-100" />
          </td>
          <td className="py-5 px-3">
            <div className="flex items-start gap-x-3">
              <div className="text-sm/6 text-gray-900">
                {payment.currency} {payment.cents / 100}
              </div>
              <PaymentStatusBadge status={payment.status} />
            </div>
          </td>
          <td className="py-5 pl-3 pr-4 text-right">
            <div className="flex justify-end">
              <a
                href={`/student-fee-payments/${payment.id}`}
                className="text-sm/6 font-medium text-indigo-600 hover:text-indigo-500"
              >
                View &rarr;
                <span className="sr-only">, payment #{payment.id}</span>
              </a>
            </div>
          </td>
        </tr>
      ))}
    </Fragment>
  )
}

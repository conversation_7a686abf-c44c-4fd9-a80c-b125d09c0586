import { navigate } from 'vike/client/router'

import { showNotification } from '@/pages/common/notification/notification-store'
import { ShowData } from '@/pages/common/ShowData'
import { getErrorMessage } from '@/pages/common/utils/error-utils.ui'
import { AcademyDetail } from '@/server/academies/get/getAcademy.server'
import { profileOk } from '@/shared/profiles/profile-check-utils.shared'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

import { useApproveAcademyMutation, useMyAcademyQuery } from '../common/academy-queries'

export const AcademyActions = ({ academy }: { academy: AcademyDetail }) => {
  const { currentProfile, profileIsPending, profileError } = useCurrentProfile()
  const { data: myAcademy, isPending: myAcademyIsPending, error: myAcademyError } = useMyAcademyQuery()

  const approveAcademyMutation = useApproveAcademyMutation(academy.id)

  const handleApprove = async () => {
    try {
      await approveAcademyMutation.mutateAsync()
      showNotification({
        heading: 'Academy approved',
        type: 'success',
      })
    } catch (error) {
      showNotification({
        heading: getErrorMessage(error),
        type: 'error',
      })
    }
  }

  // Principals of the academy can edit the academy
  const canEdit = myAcademy?.id === academy.id && profileOk(currentProfile, { roleAnyOf: ['principal'] })
  const canApprove = academy.approvedAt === null && profileOk(currentProfile, { roleAnyOf: ['admin'] })

  return (
    <ShowData isPending={profileIsPending || myAcademyIsPending} error={profileError || myAcademyError}>
      {(canEdit || canApprove) && (
        <div className="flex flex-row sm:flex-col gap-x-3 text-sm link underline items-start">
          {canApprove && (
            <button type="button" disabled={approveAcademyMutation.isPending} onClick={handleApprove}>
              Approve
            </button>
          )}
          {canEdit && (
            <>
              <button type="button" onClick={() => navigate(`/academies/${academy.id}/edit`)}>
                Edit
              </button>
              <button type="button" onClick={() => navigate(`/courses/add`)}>
                Add a Course
              </button>
            </>
          )}
        </div>
      )}
    </ShowData>
  )
}

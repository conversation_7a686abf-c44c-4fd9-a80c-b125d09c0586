import { UUID } from 'crypto'

import clsx from 'clsx'
import { navigate } from 'vike/client/router'

import { MyBatchStudent } from '@/server/batches/batch-students/my/getMyBatchStudent'
import { Batch } from '@/server/batches/batches/get/getBatch.server'
import { CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { batchPath } from '@/shared/batch/batch-utils.shared'
import { coursePath } from '@/shared/course/course-utils.shared'
import { confirmAndRun } from '@ui/common/confirmation/confirmation-store'
import { showNotification } from '@ui/common/notification/notification-store'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'

import { useRemoveBatchMutation } from '../common/batch-queries'
import { useJoinBatchMutation, useLeaveBatchMutation } from '../common/batch-student-queries'

export const BatchActions = ({
  course,
  batch,
  isStaff,
  isStudent,
  batchStudent,
}: {
  course: CourseWithoutDescr
  batch: Batch
  isStaff: boolean
  isStudent: boolean
  batchStudent: MyBatchStudent | null | undefined
}) => {
  return (
    <div className="flex flex-row sm:flex-col gap-x-2 text-sm items-start mt-4 sm:mt-2 link underline">
      {isStaff && (
        <>
          <a href={`${batchPath(course, batch.id)}/edit`}>Edit</a>
          <RemoveBatchLink course={course} batchId={batch.id} />
          <ViewStudentsLink course={course} batchId={batch.id} />
          <ViewPaymentsLink course={course} batchId={batch.id} />
        </>
      )}
      {isStudent && <BatchStudentActions course={course} batch={batch} batchStudent={batchStudent} />}
    </div>
  )
}

const RemoveBatchLink = ({ course, batchId }: { course: CourseWithoutDescr; batchId: UUID }) => {
  const removeBatch = useRemoveBatchMutation(batchId)

  const handleRemove = () => {
    confirmAndRun(
      async () => {
        try {
          await removeBatch.mutateAsync()
          showNotification({
            heading: 'Batch removed',
            type: 'success',
          })
          void navigate(`${coursePath(course)}/batches`)
        } catch (error) {
          showNotification({
            heading: 'Error removing batch',
            message: getErrorMessage(error),
            type: 'error',
          })
        }
      },
      {
        heading: 'Remove Batch',
        message: `Are you sure you want to remove the batch? This action cannot be undone.`,
        okLabel: 'Remove',
      },
    )
  }

  return (
    <a onClick={handleRemove} className={clsx(removeBatch.isPending && 'cursor-wait')}>
      Remove
    </a>
  )
}

const BatchStudentActions = ({
  course,
  batch,
  batchStudent,
}: {
  course: CourseWithoutDescr
  batch: Batch
  batchStudent: MyBatchStudent | null | undefined
}) => {
  return (
    <>
      {batchStudent ?
        <>
          {batchStudent.leftAt ?
            <JoinBatchButton batch={batch} label="Rejoin" />
          : <LeaveBatchButton batch={batch} />}
          <ViewPaymentsLink course={course} batchId={batch.id} />
        </>
      : <JoinBatchButton batch={batch} label="Join" />}
    </>
  )
}

const JoinBatchButton = ({ batch, label }: { batch: Batch; label: string }) => {
  const joinBatch = useJoinBatchMutation()

  const handleJoin = () => {
    joinBatch.mutate(batch.id, {
      onSuccess: () => {
        showNotification({
          heading: 'Batch joined',
          type: 'success',
        })
      },
      onError: (error) => {
        showNotification({
          heading: 'Error joining batch',
          message: getErrorMessage(error),
          type: 'error',
        })
      },
    })
  }

  return (
    <button
      type="button"
      onClick={handleJoin}
      disabled={joinBatch.isPending}
      className={clsx(
        'rounded-md bg-indigo-600 px-2.5 py-1.5 text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600',
        joinBatch.isPending && 'cursor-wait',
      )}
    >
      {label}
    </button>
  )
}

const LeaveBatchButton = ({ batch }: { batch: Batch }) => {
  const leaveBatch = useLeaveBatchMutation()

  const handleLeave = () => {
    confirmAndRun(
      () => {
        leaveBatch.mutate(batch.id, {
          onSuccess: () => {
            showNotification({
              heading: 'Batch left',
              type: 'success',
            })
          },
          onError: (error) => {
            showNotification({
              heading: 'Error leaving batch',
              message: getErrorMessage(error),
              type: 'error',
            })
          },
        })
      },
      {
        heading: 'Leaving Batch',
        message: `Are you sure you want to leave the batch? You can re-join later if you change your mind.`,
        okLabel: 'Leave',
      },
    )
  }

  return (
    <button
      type="button"
      onClick={handleLeave}
      disabled={leaveBatch.isPending}
      className={clsx(leaveBatch.isPending && 'cursor-wait')}
    >
      Leave Batch
    </button>
  )
}

const ViewPaymentsLink = ({ course, batchId }: { course: CourseWithoutDescr; batchId: UUID }) => {
  return <a href={`${batchPath(course, batchId)}/payments`}>Payments</a>
}

const ViewStudentsLink = ({ course, batchId }: { course: CourseWithoutDescr; batchId: UUID }) => {
  return <a href={`${batchPath(course, batchId)}/students`}>Students</a>
}

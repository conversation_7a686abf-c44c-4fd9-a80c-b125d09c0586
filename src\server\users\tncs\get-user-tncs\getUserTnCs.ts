import { eq, isNull, sql } from 'drizzle-orm'

import { db } from '@/server/db/db'
import { userTncAcceptedTable, userTncTable, userTncVersionTable } from '@/server/db/schema/user-schema'
import { withLog } from '@/shared/common/withLog.shared'

import type { Logger } from 'pino'

const columns = {
  id: userTncVersionTable.id,
  version: userTncVersionTable.version,
  baseUrl: userTncTable.baseUrl,
  effectiveDate: userTncVersionTable.effectiveDate,
  expiryDate: userTncVersionTable.expiryDate,
  name: userTncTable.name,
  description: userTncTable.description,
}

const findLatestOrAcceptedTncsDaf = (log: Logger, userId?: string) =>
  withLog(log, 'findLatestOrAcceptedTncsDaf', () => {
    const latestTncsQuery = db
      .select(columns)
      .from(userTncVersionTable)
      .innerJoin(userTncTable, eq(userTncVersionTable.tncId, userTncTable.id))
      .where(isNull(userTncVersionTable.expiryDate))

    if (!userId) {
      return latestTncsQuery
    }

    return latestTncsQuery.union(
      db
        .select(columns)
        .from(userTncVersionTable)
        .innerJoin(userTncTable, eq(userTncVersionTable.tncId, userTncTable.id))
        .leftJoin(userTncAcceptedTable, eq(userTncAcceptedTable.tncVersionId, userTncVersionTable.id))
        .where(eq(userTncAcceptedTable.userId, sql`${userId}`)),
    )
  })

export const getUserTnCs = async (log: Logger, userId?: string) => {
  log.info({ userId }, `Getting applicable terms and conditions`)

  const tncs = await findLatestOrAcceptedTncsDaf(log, userId)

  return tncs.map((tnc) => ({
    id: tnc.id,
    name: tnc.name,
    version: tnc.version,
    description: tnc.description,
    url: `https://tuitionlance.s3.amazonaws.com${tnc.baseUrl}-v${tnc.version}.pdf?t=${Date.now()}`,
    effectiveDate: tnc.effectiveDate,
    expiryDate: tnc.expiryDate,
  }))
}

export type ApplicableUserTnCs = Awaited<ReturnType<typeof getUserTnCs>>

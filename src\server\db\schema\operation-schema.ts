import { jsonb, pgTable, serial, text, index, smallint } from 'drizzle-orm/pg-core'
import { Bindings } from 'pino'

import { timestampz } from './helpers'

/**
 * Table for managing application-level locks
 */
export const appLockTable = pgTable('app_lock', {
  subject: text('subject').primaryKey(),
  expiresAt: timestampz().notNull(),
})

/**
 * Table for storing and tracking application commands
 */
export const appCommandTable = pgTable(
  'app_command',
  {
    id: serial().primaryKey(),
    createdAt: timestampz().defaultNow(),
    creationRemarks: text(),
    flock: text().notNull(),
    command: text().notNull(),
    logBindings: jsonb().$type<Bindings>().notNull(),
    data: jsonb().notNull(),
    firstAttemptedAt: timestampz(),
    abortedAt: timestampz(),
  },
  (t) => [index('idx_app_command_aborted_at').on(t.abortedAt), index('idx_app_command_flock_id').on(t.flock, t.id)],
)

/**
 * Stores encrypted OTPs for mobile number verification
 * - Each mobile can have only one active OTP at a time (enforced by primary key)
 * - OTPs are encrypted before storage for security
 * - System tracks failed attempts to prevent brute force attacks
 * - OTPs expire after OTP_EXPIRY_INTERVAL_MINUTES
 * - OTPs are rate-limited to requesting new OTPs every OTP_SENDING_INTERVAL_MINUTES
 */
export const mobileOtpTable = pgTable('mobile_otp', {
  mobile: text().primaryKey(), // full mobile number, e.g. +919337888808
  otp: text().notNull(),
  createdAt: timestampz().notNull().defaultNow(),
  tryFailedAt: timestampz(),
  otpTryCount: smallint().notNull().default(0),
})

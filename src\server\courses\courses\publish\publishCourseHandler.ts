import { UUID } from 'crypto'

import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'

import { publishCourse } from './publishCourse'

export const publishCourseHandler = new Hono<HonoVars>().put('/', async (c) => {
  const courseId = c.req.param('id') as UUID

  await publishCourse(getCtx(c), courseId)
  return c.body(null, 204)
})

import { UUID } from 'crypto'

import { useCourseTagSuspenseQuery } from './queries/course-tag-queries'

/*
 * For tooltip, https://github.com/cosbgn/tailwindcss-tooltips is used
 */
export const CourseTagBadge = ({ tagId }: { tagId: UUID }) => {
  const { data } = useCourseTagSuspenseQuery(tagId)
  const tag = data.data
  return (
    <div className="has-tooltip inline-flex items-center rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
      {tag?.name}
      <span className="tooltip -mt-12 inline-flex items-center rounded-md bg-green-50 px-3 py-1 text-xs font-medium text-green-700 ring-1 ring-green-600/20 ring-inset">
        {tag?.descr}
      </span>
    </div>
  )
}

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { ensureCanUpdateBatch } from '@/server/batches/batches/common/ensureCanUpdateBatch'
import { Ctx } from '@/server/common/auth-context-types.server'
// import { appendCommandDaf } from '@/server/common/command/processCommands'
import { ensureExists } from '@/server/common/error/ensureExists'
// import { calendarFlock } from '@/server/common/google/calendar/calendar-utils'
import { db, Transaction } from '@/server/db/db'
import { batchEventTable } from '@/server/db/schema/batch-schema'
import { withLog } from '@/shared/common/withLog.shared'

// import { REMOVE_GOOGLE_EVENT_COMMAND, RemoveGoogleEventCommandData } from './removeGoogleEventCommand'

const findBatchEventDaf = (log: pino.Logger, eventId: UUID) =>
  withLog(log, 'findBatchEventDaf', () =>
    db.query.batchEventTable.findFirst({
      columns: {
        batchId: true,
        eventXid: true,
      },
      with: {
        batch: {
          with: {
            teacher: {
              with: {
                user: {
                  columns: {
                    id: true,
                  },
                },
              },
            },
          },
        },
      },
      where: eq(batchEventTable.id, eventId),
    }),
  )

const removeBatchEventDaf = (log: pino.Logger, db: Transaction, eventId: UUID) =>
  withLog(log, 'removeBatchEventDaf', () => db.delete(batchEventTable).where(eq(batchEventTable.id, eventId)))

export const removeBatchEvent = async (c: Ctx, eventId: UUID) => {
  // Find the event to get its batch ID
  const event = await findBatchEventDaf(c.log, eventId)
  ensureExists(event, eventId, 'Batch event')

  // Ensure the user has permission to update the batch
  await ensureCanUpdateBatch(c, event.batchId)

  // Remove the batch event from the database
  await db.transaction(async (tx) => {
    const result = await removeBatchEventDaf(c.log, tx, eventId)
    ensureExists(result.count === 1, eventId, 'Batch event')
    // await appendCommandDaf<RemoveGoogleEventCommandData>(tx, c.log, calendarFlock(event.batch.teacher.user.id), {
    //   command: REMOVE_GOOGLE_EVENT_COMMAND,
    //   data: {
    //     userId: event.batch.teacher.user.id,
    //     eventXid: event.eventXid,
    //   },
    // })
  })
}

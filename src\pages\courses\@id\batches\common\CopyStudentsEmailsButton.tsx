import { UUID } from 'crypto'

import { useState } from 'react'

import { showNotification } from '@ui/common/notification/notification-store'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'

import { useBatchStudentEmailsQuery } from './batch-student-queries'

export const CopyStudentsEmailsButton = ({ batchId }: { batchId: UUID }) => {
  const { data: students } = useBatchStudentEmailsQuery(batchId)
  const [isCopied, setIsCopied] = useState(false)

  if (!students) return null

  const studentEmails = students.map((student) => student.email).join(',')

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(studentEmails)
      setIsCopied(true)

      // Reset the state back to "Copy student emails" after 2 seconds
      setTimeout(() => {
        setIsCopied(false)
      }, 2000)
    } catch (error) {
      showNotification({
        heading: 'Error copying emails',
        message: getErrorMessage(error),
        type: 'error',
      })
    }
  }

  return (
    <a
      type="button"
      className="text-sm font-medium link hover:bg-gray-50 transition-colors duration-200"
      onClick={handleCopy}
    >
      {isCopied ?
        <span className="text-green-600">✓ Copied!</span>
      : `Copy student emails (${students.length})`}
    </a>
  )
}

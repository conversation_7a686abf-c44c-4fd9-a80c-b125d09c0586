import { EnvelopeIcon, InformationCircleIcon, PhoneIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { clientOnly } from 'vike-react/clientOnly'
import { Config } from 'vike-react/Config'
import { Head } from 'vike-react/Head'

import { useDistrictSuspenseQuery } from '@/pages/masters/common/master-queries'
import { AcademyDetail } from '@/server/academies/get/getAcademy.server'
import { academyPath } from '@/shared/academies/academy-utils.shared'
import { AcademyHeader } from '@ui/academies/common/AcademyHeader'
import '@ui/common/assets/rich-content.css'
import { useSignedIn } from '@ui/users/auth/auth-token-store'

import { useIsClient } from '../../common/useIsClient'
import { useMyAcademyQuery } from '../common/academy-queries'
import { useSendAcademyVerificationMail } from '../common/useSendAcademyVerificationMail'

const AcademyActions = clientOnly(async () => (await import('./AcademyActions')).AcademyActions)

export const AcademyPage = ({ academy, academyMobile }: { academy: AcademyDetail; academyMobile: string }) => {
  return (
    <>
      <Config title={`${academy.name}`} />
      <Head>
        <meta name="description" content={`${academy.name}`} />
      </Head>
      <div className="px-4 sm:px-0">
        {/* Header section with name and role */}
        <div className="flex flex-col sm:flex-row items-start justify-between">
          <AcademyHeader academy={academy} />
          <AcademyActions academy={academy} />
        </div>

        {/* About section */}
        <div className="mt-6">
          {academy.descr ?
            <div className="rich-content">
              <div dangerouslySetInnerHTML={{ __html: academy.descr }} />
            </div>
          : <div className="mt-6 rounded-lg bg-gray-50 px-6 py-12">
              <div className="text-center">
                <p className="text-sm text-gray-500 italic">No academy description available</p>
              </div>
            </div>
          }
        </div>
        <div className="mt-6 text-sm text-gray-500">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Courses Offered</h3>
          <a href={`/courses?academyId=${academy.id}&includeUnpublished=true`} className="text-sm underline link">
            Click here
          </a>{' '}
          to view all courses offered by us.
        </div>
        <ContactSection academy={academy} academyMobile={academyMobile} />
        <AddressSection academy={academy} />
        <LegalSection academy={academy} />
      </div>
    </>
  )
}

export const ContactSection = ({ academy, academyMobile }: { academy: AcademyDetail; academyMobile: string }) => {
  const isClient = useIsClient()
  const signedIn = useSignedIn()
  const { data: myAcademy } = useMyAcademyQuery()
  const { sendAcademyVerificationMail, isPending } = useSendAcademyVerificationMail(academy.id)

  const isMyAcademy = myAcademy?.id === academy.id

  return (
    <div className="mt-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Us</h3>
      {isClient && signedIn ?
        <>
          <div className="flex items-center gap-2 mt-2">
            <EnvelopeIcon className="h-5 w-5 text-blue-700" />
            <a href={`mailto:${academy.email}`} className="text-sm text-gray-500 hover:text-gray-700">
              {academy.email}
            </a>
            {!academy.emailVerified &&
              (isMyAcademy ?
                <button
                  onClick={sendAcademyVerificationMail}
                  className={clsx('text-sm underline link', isPending && 'cursor-wait')}
                  disabled={isPending}
                >
                  verify
                </button>
              : <span>(Unverified)</span>)}
          </div>
          <div className="flex items-center gap-2 mt-2">
            <PhoneIcon className="h-5 w-5 text-blue-700" />
            <span className="text-sm text-gray-500 hover:text-gray-700">{academyMobile}</span>
            {!academy.mobileVerified &&
              (isMyAcademy ?
                <a href={`${academyPath(academy)}/verify-mobile`} className="text-sm underline link">
                  verify
                </a>
              : <span>(Unverified)</span>)}
          </div>
        </>
      : <div className="rounded-md bg-blue-50 p-4">
          <div className="flex">
            <div className="shrink-0">
              <InformationCircleIcon aria-hidden="true" className="size-5 text-blue-400" />
            </div>
            <div className="ml-3 flex-1 md:flex md:justify-between">
              <p className="text-sm text-blue-700">Please sign in / sign up to view contact information</p>
            </div>
          </div>
        </div>
      }
    </div>
  )
}

export const AddressSection = ({ academy }: { academy: AcademyDetail }) => {
  const { data: district } = useDistrictSuspenseQuery(academy.districtId)

  if (!district) {
    return null
  }

  return (
    <div className="mt-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Address</h3>
      <div className="text-sm text-gray-500">
        {academy.addressLine1 && <div>{academy.addressLine1}</div>}
        {academy.addressLine2 && <div>{academy.addressLine2}</div>}
        <div>{academy.area}</div>
        <div>
          {district.name}, {district.state.name}
        </div>
        <div>
          {district.state.country.name} - {academy.pincode}
        </div>
      </div>
    </div>
  )
}

export const LegalSection = ({ academy }: { academy: AcademyDetail }) => {
  if (!academy.tradeName && !academy.gstin) {
    return null
  }

  return (
    <div className="mt-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Legal Information</h3>
      <div className="text-sm text-gray-500">
        {academy.tradeName && (
          <div>
            <strong>Trade Name:</strong> {academy.tradeName}
          </div>
        )}
        {academy.gstin && (
          <div>
            <strong>GSTIN:</strong> {academy.gstin}
          </div>
        )}
      </div>
    </div>
  )
}

import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { db } from '@/server/db/db'
import { courseAttachmentTable } from '@/server/db/schema/course-schema'
import { withLog } from '@/shared/common/withLog.shared'

import { ensureCanUpdateAttachment } from '../common/ensureCanUpdateAttachment'

const markUploadedCourseAttachmentDaf = async (log: pino.Logger, attachmentId: UUID) =>
  withLog(log, 'markUploadedCourseAttachmentDaf', () =>
    db
      .update(courseAttachmentTable)
      .set({
        uploaded: true,
      })
      .where(and(eq(courseAttachmentTable.id, attachmentId), eq(courseAttachmentTable.uploaded, false))),
  )

export const markUploadedCourseAttachment = async (c: Ctx, attachmentId: UUID) => {
  await ensureCanUpdateAttachment(c, attachmentId)
  await markUploadedCourseAttachmentDaf(c.log, attachmentId)
}

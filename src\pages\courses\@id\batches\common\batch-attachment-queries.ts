import { UUID } from 'crypto'

import { useMutation, useQueryClient, useSuspenseQuery } from '@tanstack/react-query'
import { useDeferredValue } from 'react'

import { AddBatchAttachmentForm } from '@/shared/batch/batch-utils.shared'
import { isClient } from '@/shared/common/common-utils.shared'
import { useServerQuery } from '@/shared/common/serverQueries.shared'
import { api, withError } from '@ui/api-client'
import { showNotification } from '@ui/common/notification/notification-store'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'

const batchAttachmentKeys = {
  all: ['batch-attachment'] as const,
  lists: () => [...batchAttachmentKeys.all, 'list'] as const,
  listsForBatch: (batchId: UUID) => [...batchAttachmentKeys.lists(), batchId] as const,
  listForProfile: (batchId: UUID, profileId: UUID | undefined) =>
    [...batchAttachmentKeys.listsForBatch(batchId), profileId] as const,
}

export const useAddBatchAttachmentMutation = (batchId: UUID) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (form: AddBatchAttachmentForm) =>
      api().batches[':id'].attachments.$post({ param: { id: batchId }, json: form }),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: batchAttachmentKeys.listsForBatch(batchId) })
    },
    onError: (error) => {
      showNotification({
        heading: 'Error',
        message: getErrorMessage(error),
        type: 'error',
      })
    },
  })
}

export const useRemoveBatchAttachmentMutation = (batchId: UUID) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (attachmentId: UUID) =>
      api().batches[':id'].attachments[':attachmentId'].$delete({ param: { id: batchId, attachmentId } }),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: batchAttachmentKeys.listsForBatch(batchId) })
    },
    onError: (error) => {
      showNotification({
        heading: 'Error',
        message: getErrorMessage(error),
        type: 'error',
      })
    },
  })
}

export const useBatchAttachmentsSuspenseQuery = (batchId: UUID, profileId: UUID | undefined) => {
  const listBatchAttachments = useServerQuery('listBatchAttachments')
  const { data } = useSuspenseQuery({
    queryKey: batchAttachmentKeys.listForProfile(batchId, profileId),
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .batches[':id'].attachments.$get({ param: { id: batchId } })
            .then((res) => res.json())
            .then((batchAttachments) => batchAttachments.rows),
        )
      : listBatchAttachments(batchId),
  })
  return useDeferredValue(data)
}

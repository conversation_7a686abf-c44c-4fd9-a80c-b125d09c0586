import { UUID } from 'crypto'

import { phoneNumber } from '@/shared/common/common-utils.shared'
import { useCountriesSuspenseQuery } from '@ui/masters/common/master-queries'

import { useAcademySuspenseQuery } from './academy-queries'

export const useAcademyWithContact = (academyId: UUID, includeDetails = false) => {
  const { data: academy, error: academyError } = useAcademySuspenseQuery(academyId, includeDetails)
  const { data: countries, error: countriesError } = useCountriesSuspenseQuery()

  if (academyError) return { academy: undefined, academyMobile: undefined, error: academyError }
  if (countriesError) return { academy: undefined, academyMobile: undefined, error: countriesError }
  const country = countries!.find((c) => c.code === academy!.mobileCountryCode)
  return { academy, academyMobile: phoneNumber(country?.phonePrefix ?? '', academy!.mobile), error: null }
}

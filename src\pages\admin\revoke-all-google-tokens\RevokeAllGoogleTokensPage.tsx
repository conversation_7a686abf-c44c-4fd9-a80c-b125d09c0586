import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { navigate } from 'vike/client/router'

import { showNotification } from '@ui/common/notification/notification-store'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'
import { WithProfile } from '@ui/profiles/common/WithProfile'

import { useRevokeAllGoogleTokensMutation } from '../common/admin-queries'

export const RevokeAllGoogleTokensPage = () => {
  const { mutate: revokeAllGoogleTokens, isPending: revokeAllGoogleTokensIsPending } =
    useRevokeAllGoogleTokensMutation()

  const handleInvalidate = () => {
    revokeAllGoogleTokens(undefined, {
      onSuccess: () => {
        showNotification({
          heading: 'Request Submitted',
          message: 'Request for revoking all Google tokens has been submitted',
          type: 'success',
        })
        void navigate('/')
      },
      onError: (error) => {
        showNotification({
          heading: 'Error revoking google tokens',
          message: getErrorMessage(error),
          type: 'error',
        })
      },
    })
  }

  return (
    <WithProfile roleAnyOf={['admin']}>
      <div className="max-w-2xl mx-auto">
        <div className="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
          <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
            <div className="flex items-center">
              <h3 className="text-lg font-semibold text-gray-900">Security Settings</h3>
            </div>
          </div>

          <div className="px-6 py-5">
            <div className="rounded-md bg-yellow-50 p-4 mb-6">
              <div className="flex">
                <div className="shrink-0">
                  <ExclamationTriangleIcon className="size-5 text-yellow-400" aria-hidden="true" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">Danger Zone</h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>
                      This is an irreversible action. Google refresh tokens of all users will be revoked immediately.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="sm:flex sm:items-center sm:justify-between">
              <div>
                <h4 className="text-base font-semibold text-gray-900">Invalidate all tokens</h4>
                <div className="mt-2 max-w-xl text-sm text-gray-500">
                  <p>This will revoke all Google refresh tokens of all users immediately.</p>
                </div>
              </div>
              <div className="mt-5 sm:mt-0 sm:ml-6 sm:flex sm:shrink-0 sm:items-center">
                <button
                  type="button"
                  className={clsx(
                    'inline-flex items-center rounded-md px-4 py-2 text-sm font-semibold text-white shadow-sm',
                    revokeAllGoogleTokensIsPending ? 'bg-gray-400 cursor-not-allowed' : 'bg-red-600 hover:bg-red-500',
                    'focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600',
                  )}
                  onClick={handleInvalidate}
                  disabled={revokeAllGoogleTokensIsPending}
                >
                  {revokeAllGoogleTokensIsPending ? 'Revoking...' : 'Revoke All Tokens'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </WithProfile>
  )
}

import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { runDLocked } from '@/server/common/distributed-lock/runDLocked'
import { ensureProfileWithRole } from '@/server/common/error/ensure-profile.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { studentFeePaymentTable } from '@/server/db/schema/batch-schema'
import { PayStudentFeePaymentForm } from '@/shared/batch/batch-utils.shared'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'

// Find batch student by ID
const findStudentFeePaymentDaf = (log: pino.Logger, studentFeePaymentId: UUID) =>
  withLog(log, 'findStudentFeePaymentDaf', () =>
    db.query.studentFeePaymentTable.findFirst({
      columns: {
        studentId: true,
        status: true,
      },
      where: eq(studentFeePaymentTable.id, studentFeePaymentId),
    }),
  )

const updateStudentFeePaymentDaf = (log: pino.Logger, studentFeePaymentId: UUID, form: PayStudentFeePaymentForm) => {
  const now = utc().toJSDate()
  return withLog(log, 'updateStudentFeePaymentDaf', () =>
    db
      .update(studentFeePaymentTable)
      .set({
        status: 'pending',
        paidAt: now,
        paidRemarks: form.paidRemarks,
        updatedAt: now,
        updateRemarks: 'paid',
      })
      .where(and(eq(studentFeePaymentTable.id, studentFeePaymentId), eq(studentFeePaymentTable.status, 'draft'))),
  )
}

export const payStudentFeePayment = async (c: Ctx, studentFeePaymentId: UUID, form: PayStudentFeePaymentForm) => {
  ensureUser(c.user)
  ensureProfileWithRole('student', c.profile)
  const studentId = c.profile.id

  await runDLocked(c.log, ['studentFeePayment', studentId], async () => {
    const studentFeePayment = await findStudentFeePaymentDaf(c.log, studentFeePaymentId)
    ensureExists(studentFeePayment, studentFeePaymentId, 'Student Fee Payment')
    ensure(studentFeePayment.studentId === studentId, {
      statusCode: 403,
      issueMessage: 'You are not allowed to pay for this student fee payment',
      logMessage: `Attempted to pay for student fee payment ${studentFeePaymentId} but you are not the student`,
    })

    await updateStudentFeePaymentDaf(c.log, studentFeePaymentId, form)
  })
}

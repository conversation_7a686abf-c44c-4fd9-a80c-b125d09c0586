import { ClockIcon, ShoppingCartIcon } from '@heroicons/react/24/outline'

import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

export const useStudentSidebarLinks = () => {
  const { currentProfile } = useCurrentProfile()

  return currentProfile?.role === 'student' ?
      [
        { name: 'My Batches', href: '/students/me/batches', icon: ClockIcon },
        { name: 'My Payments', href: '/students/me/payments', icon: ShoppingCartIcon },
      ]
    : []
}

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { ensureCanUpdateBatch } from '@/server/batches/batches/common/ensureCanUpdateBatch'
import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { db } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { batchTable } from '@/server/db/schema/batch-schema'
import { withLog } from '@/shared/common/withLog.shared'

const findBatchDaf = (log: pino.Logger, batchId: UUID) =>
  withLog(log, 'findBatchDaf', () =>
    db.query.batchTable.findFirst({
      ...dummyColumn,
      where: eq(batchTable.id, batchId),
    }),
  )

const removeBatchDaf = (log: pino.Logger, batchId: UUID) =>
  withLog(log, 'removeBatchDaf', () => db.delete(batchTable).where(eq(batchTable.id, batchId)))

export const removeBatch = async (c: Ctx, batchId: UUID) => {
  const batch = await findBatchDaf(c.log, batchId)
  ensureExists(batch, batchId, 'Batch')
  await ensureCanUpdateBatch(c, batchId)
  await removeBatchDaf(c.log, batchId)
}

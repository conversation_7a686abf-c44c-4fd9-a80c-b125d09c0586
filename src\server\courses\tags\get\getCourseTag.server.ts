import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { ensureExists } from '@/server/common/error/ensureExists'
import { courseTagColumns } from '@/server/courses/tags/common/course-tag-select-helper'
import { db } from '@/server/db/db'
import { courseTagTable } from '@/server/db/schema/course-schema'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'

const findCourseTagByIdDaf = (log: Logger, tagId: UUID) =>
  withLog(log, 'findCourseTagByIdDaf', () =>
    db.query.courseTagTable.findFirst({
      columns: courseTagColumns,
      where: eq(courseTagTable.id, tagId),
    }),
  )

export const getCourseTag = async (log: Logger, tagId: UUID) => {
  const tag = await findCourseTagByIdDaf(log, tagId)
  ensureExists(tag, tagId, 'Course tag')
  return tag
}

export type GetCourseTag = typeof getCourseTag
export type CourseTag = Awaited<ReturnType<GetCourseTag>>
setQuery('getCourseTag', getCourseTag)

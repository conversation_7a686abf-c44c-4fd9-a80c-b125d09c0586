import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { sendOtp } from '@/server/common/sms/otp/sendOtp'
import { db } from '@/server/db/db'
import { academyTable } from '@/server/db/schema/academy-schema'
import { phoneNumber } from '@/shared/common/common-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { ensureGoodStaff } from '../common/ensureGoodStaff'

const findAcademyDaf = (log: Logger, academyId: UUID) =>
  withLog(log, 'findAcademyDaf', () =>
    db.query.academyTable.findFirst({
      columns: {
        mobile: true,
        mobileVerified: true,
      },
      with: {
        mobileCountry: {
          columns: {
            phonePrefix: true,
          },
        },
      },
      where: eq(academyTable.id, academyId),
    }),
  )

export const sendAcademyMobileOtp = async (c: Ctx, academyId: UUID) => {
  await ensureGoodStaff(c, academyId)

  const academy = await findAcademyDaf(c.log, academyId)
  ensureExists(academy, academyId, 'Academy', 422)

  ensure(!academy.mobileVerified, {
    logMessage: `Mobile already verified for academy: ${academyId}`,
    issueMessage: 'Academy mobile number is already verified',
    statusCode: 422,
  })

  const mobile = phoneNumber(academy.mobileCountry.phonePrefix, academy.mobile)
  await sendOtp(c.log, mobile, c.user!.language)
}

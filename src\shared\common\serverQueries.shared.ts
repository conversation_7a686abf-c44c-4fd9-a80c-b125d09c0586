import { remember } from '@epic-web/remember'
import pino from 'pino'
import { usePageContext } from 'vike-react/usePageContext'

import { GetAcademy } from '@/server/academies/get/getAcademy.server'
import { ListAcademies } from '@/server/academies/list/listAcademies'
import { ListBatchAttachments } from '@/server/batches/batches/attachments/list/listBatchAttachments'
import { GetBatch } from '@/server/batches/batches/get/getBatch.server'
import { ListCourseBatches } from '@/server/batches/batches/list/course-batches/listCourseBatches.server'
import { ListTeacherBatches } from '@/server/batches/batches/list/teacher-batches/listTeacherBatches'
import { GetCourse } from '@/server/courses/courses/get/getCourse.server'
import { ListCourses } from '@/server/courses/courses/list/listCourses.server'
import { GetCourseTag } from '@/server/courses/tags/get/getCourseTag.server'
import { ListCountries } from '@/server/masters/countries/list/listCountries'
import { GetDistrict } from '@/server/masters/districts/get/getDistrict'
import { ListDistricts } from '@/server/masters/districts/list/listDistricts'
import { GetCommonEnv } from '@/server/masters/env/getCommonEnv'
import { GetState } from '@/server/masters/states/get/getState'
import { ListStates } from '@/server/masters/states/list/listStates'
import { GetProfile } from '@/server/profiles/get/getProfile.server'
import { ListProfiles } from '@/server/profiles/list/listProfiles'

import { ErrorPayload } from './error-utils.shared'

class Queries {
  getBatch!: GetBatch
  listBatchAttachments!: ListBatchAttachments
  listCourseBatches!: ListCourseBatches
  getCourseTag!: GetCourseTag
  getProfile!: GetProfile
  listProfiles!: ListProfiles
  getAcademy!: GetAcademy
  listAcademies!: ListAcademies
  getCourse!: GetCourse
  getCommonEnv!: GetCommonEnv
  listCourses!: ListCourses
  listCountries!: ListCountries
  listStates!: ListStates
  getState!: GetState
  listDistricts!: ListDistricts
  getDistrict!: GetDistrict
  listTeacherBatches!: ListTeacherBatches
}

console.log('Setting up queries')
const queries = remember('queries', () => new Queries())
console.log('Queries set up: getCourse', !!queries.getCourse)

export const setQuery = <K extends keyof Queries>(fnName: K, fn: Queries[K]) => {
  console.log(`Setting query ${fnName}`)
  queries[fnName] = fn
  console.log('Queries set up: getCourse', !!queries.getCourse)
}

export const useServerQuery = <K extends keyof Queries>(fnName: K) => {
  const pageContext = usePageContext()

  // Get the function type
  type Fn = Queries[K]
  // Get all parameters except the first one (logger)
  type Args = Parameters<Fn> extends [first: pino.Logger, ...rest: infer Rest] ? Rest : never
  // Type for the server function with logger
  type CallbackFn = (log: pino.Logger, ...args: Args) => ReturnType<Fn>

  return async (...args: Args) => {
    try {
      pageContext.log.info(`Calling function ${fnName} with args ${JSON.stringify(args)}. ${!!queries[fnName]}`)
      const fn = queries[fnName] as unknown as CallbackFn
      const data = await fn(pageContext.log, ...args)
      return { data: data as Awaited<ReturnType<Fn>>, error: null }
    } catch (error) {
      return { error: errorProcessor.toError(pageContext.log, error as Error) }
    }
  }
}

class ErrorProcessor {
  toError!: (log: pino.Logger, error: Error) => ErrorPayload
}
const errorProcessor = remember('errorProcessor', () => new ErrorProcessor())
export const setToError = (fn: (log: pino.Logger, error: Error) => ErrorPayload) => {
  errorProcessor.toError = fn
}

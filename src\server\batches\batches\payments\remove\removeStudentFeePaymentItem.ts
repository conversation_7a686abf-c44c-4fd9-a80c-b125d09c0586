import { UUID } from 'crypto'

import { and, eq, sql } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { runDLocked } from '@/server/common/distributed-lock/runDLocked'
import { ensureProfileWithRole } from '@/server/common/error/ensure-profile.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db, Transaction } from '@/server/db/db'
import { studentFeePaymentItemTable, studentFeePaymentTable } from '@/server/db/schema/batch-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'

const findStudentFeePaymentItemDaf = (log: pino.Logger, studentFeePaymentItemId: UUID) =>
  withLog(log, 'findStudentFeePaymentItemDaf', () =>
    db.query.studentFeePaymentItemTable.findFirst({
      columns: {
        fee: true,
      },
      with: {
        payment: {
          columns: {
            id: true,
            studentId: true,
            status: true,
          },
        },
      },
      where: eq(studentFeePaymentItemTable.id, studentFeePaymentItemId),
    }),
  )

const countStudentFeePaymentItems = (log: pino.Logger, studentFeePaymentId: UUID) =>
  withLog(log, 'countStudentFeePaymentItems', () =>
    db.$count(studentFeePaymentItemTable, eq(studentFeePaymentItemTable.studentFeePaymentId, studentFeePaymentId)),
  )

const updateStudentFeePaymentDaf = (log: pino.Logger, db: Transaction, studentFeePaymentId: UUID, fee: number) => {
  const now = utc().toJSDate()
  return withLog(log, 'updateStudentFeePaymentDaf', () =>
    db
      .update(studentFeePaymentTable)
      .set({
        cents: sql`studentFeePaymentTable.cents - ${fee * 100}`,
        updatedAt: now,
        updateRemarks: 'removed an item',
      })
      .where(and(eq(studentFeePaymentTable.id, studentFeePaymentId), eq(studentFeePaymentTable.status, 'draft'))),
  )
}

const removeStudentFeePaymentItemDaf = (log: pino.Logger, db: Transaction, studentFeePaymentItemId: UUID) =>
  withLog(log, 'removeStudentFeePaymentItemDaf', () =>
    db.delete(studentFeePaymentItemTable).where(eq(studentFeePaymentItemTable.id, studentFeePaymentItemId)),
  )

const removeStudentFeePaymentDaf = (log: pino.Logger, db: Transaction, studentFeePaymentId: UUID) =>
  withLog(log, 'removeStudentFeePaymentDaf', () =>
    db.delete(studentFeePaymentTable).where(eq(studentFeePaymentTable.id, studentFeePaymentId)),
  )

export const removeBatchStudentPaymentItem = async (c: Ctx, studentFeePaymentItemId: UUID) => {
  ensureUser(c.user)
  ensureProfileWithRole('student', c.profile)
  const profileId = c.profile.id

  await runDLocked(c.log, ['studentFeePayment', profileId], async () => {
    const studentFeePaymentItem = await findStudentFeePaymentItemDaf(c.log, studentFeePaymentItemId)
    ensureExists(studentFeePaymentItem, studentFeePaymentItemId, 'Student Fee Payment Item')
    ensure(studentFeePaymentItem.payment.studentId === profileId, {
      statusCode: 403,
      issueMessage: 'You are not allowed to remove this student fee payment item',
      logMessage: `Attempted to remove student fee payment item ${studentFeePaymentItemId} but you are not the student`,
    })
    ensure(studentFeePaymentItem.payment.status === 'draft', {
      statusCode: 409,
      issueMessage: 'Only items for draft student fee payment can be removed',
      logMessage: `Attempted to remove student fee payment item ${studentFeePaymentItemId} but the payment is not draft`,
    })

    const itemCount = await countStudentFeePaymentItems(c.log, studentFeePaymentItem.payment.id)
    await db.transaction(async (tx) => {
      const result = await removeStudentFeePaymentItemDaf(c.log, tx, studentFeePaymentItemId)
      if (result.count === 1) {
        if (itemCount === 1) {
          await removeStudentFeePaymentDaf(c.log, tx, studentFeePaymentItem.payment.id)
        } else {
          await updateStudentFeePaymentDaf(c.log, tx, studentFeePaymentItem.payment.id, studentFeePaymentItem.fee)
        }
      }
    })
  })
}

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { DateTime } from 'luxon'
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'

import app from '@/hono-entry'
import { env } from '@/server/common/env.server'
import { db } from '@/server/db/db'
import { academyStaffTable, academyTable } from '@/server/db/schema/academy-schema'
import { profileTable } from '@/server/db/schema/profile-schema'
import { userTable, userTncAcceptedTable, userTncSignTable } from '@/server/db/schema/user-schema'
import { initDb, SUNDARGARH_DISTRICT_ID, TNC_PRIVACY_V1_ID, TNC_TOS_V1_ID } from '@/server/test/initDb'
import { createAccessToken } from '@/server/users/common/auth-token-utils.server'
import { PROFILE_HEADER } from '@/shared/profiles/profile-utils.shared'
import { Role } from '@/shared/profiles/role-utils.shared'

const mocked = {
  now: DateTime.now(),
} as const

// Mock date utilities to have consistent test results
vi.mock(import('@/shared/common/date-utils-basic.shared'), async (importOriginal) => {
  const mod = await importOriginal()
  return {
    ...mod,
    utc: () => mocked.now,
  }
})

const testUser = {
  id: crypto.randomUUID() as UUID,
  googleId: 'test-google-id' as string,
  googleRefreshToken: null as string | null,
  name: 'Test Principal' as string,
  email: '<EMAIL>' as string,
  emailVerified: true as boolean,
  googlePictureUrl: 'https://google.com/picture' as string,
  language: 'en-US' as string,
  tokensValidFrom: new Date(2020, 4, 6) as Date,
  mobileCountryCode: 'IN' as string,
  mobile: '9876543210' as string,
  mobileVerified: true as boolean,
  legalAgeDeclaredAt: mocked.now.toJSDate() as Date,
  informationAccuracyDeclaredAt: mocked.now.toJSDate() as Date,
} as const

const testProfile = {
  id: crypto.randomUUID() as UUID,
  userId: testUser.id,
  role: 'principal' as Role,
  displayName: 'Test Principal' as string,
  approvedAt: mocked.now.toJSDate() as Date,
  tncsAcceptedAt: mocked.now.toJSDate() as Date,
} as const

const testAcademy = {
  id: crypto.randomUUID() as UUID,
  name: 'Test Academy',
  email: '<EMAIL>',
  emailVerified: true,
  mobileCountryCode: 'IN',
  mobile: '9876543210',
  mobileVerified: true,
  currency: 'INR' as const,
  upiId: 'test@upi',
  districtId: SUNDARGARH_DISTRICT_ID as UUID,
  pincode: '770001',
  area: 'Test Area',
  descr: 'Test Academy Description',
  tradeName: 'Test Trade Name',
  gstin: '27AAPFU0939F1ZV',
  addressLine1: 'Test Address Line 1',
  addressLine2: 'Test Address Line 2',
  tncsAcceptedAt: mocked.now.toJSDate(),
  approvedAt: mocked.now.toJSDate(),
  suspendedAt: null as Date | null,
} as const

const suspendedAcademy = {
  id: crypto.randomUUID() as UUID,
  name: 'Suspended Academy',
  email: '<EMAIL>',
  emailVerified: true,
  mobileCountryCode: 'IN',
  mobile: '9876543211',
  mobileVerified: true,
  currency: 'INR' as const,
  upiId: 'suspended@upi',
  districtId: SUNDARGARH_DISTRICT_ID as UUID,
  pincode: '770002',
  area: 'Suspended Area',
  tncsAcceptedAt: mocked.now.toJSDate(),
  approvedAt: mocked.now.toJSDate(),
  suspendedAt: mocked.now.toJSDate(),
} as const

const unapprovedAcademy = {
  id: crypto.randomUUID() as UUID,
  name: 'Unapproved Academy',
  email: '<EMAIL>',
  emailVerified: true,
  mobileCountryCode: 'IN',
  mobile: '9876543212',
  mobileVerified: true,
  currency: 'INR' as const,
  upiId: 'unapproved@upi',
  districtId: SUNDARGARH_DISTRICT_ID as UUID,
  pincode: '770003',
  area: 'Unapproved Area',
  tncsAcceptedAt: mocked.now.toJSDate(),
  // Note: no approvedAt date
} as const

// Internal role user for testing internal access
const internalUser = {
  id: crypto.randomUUID() as UUID,
  googleId: 'internal-google-id',
  googleRefreshToken: null,
  name: 'Internal Admin',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/picture',
  language: 'en-US',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '9876543213',
  mobileVerified: true,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

const internalProfile = {
  id: crypto.randomUUID() as UUID,
  userId: internalUser.id,
  role: 'admin' as const,
  displayName: 'Internal Admin',
  approvedAt: mocked.now.toJSDate(),
  tncsAcceptedAt: mocked.now.toJSDate(),
} as const

// External user not affiliated with any academy
const externalUser = {
  id: crypto.randomUUID() as UUID,
  googleId: 'external-google-id',
  googleRefreshToken: null,
  name: 'External User',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/picture',
  language: 'en-US',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '9876543214',
  mobileVerified: true,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

const externalProfile = {
  id: crypto.randomUUID() as UUID,
  userId: externalUser.id,
  role: 'student' as const,
  displayName: 'External Student',
  approvedAt: mocked.now.toJSDate(),
  tncsAcceptedAt: mocked.now.toJSDate(),
} as const

const makeAuthenticatedRequest = async (
  academyId: UUID,
  includeDetail?: boolean,
  userOverride?: typeof testUser,
  profileOverride?: typeof testProfile,
) => {
  const user = userOverride || testUser
  const profile = profileOverride || testProfile
  const token = await createAccessToken(env.JWT_SECRET_KEY, user.id)

  const searchParams = includeDetail ? '?includeDetail=true' : ''

  return app.request(
    `/api/academies/${academyId}${searchParams}`,
    {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
        [PROFILE_HEADER]: profile.id,
      },
    },
    {},
  )
}

const makeUnauthenticatedRequest = async (academyId: UUID, includeDetail?: boolean) => {
  const searchParams = includeDetail ? '?includeDetail=true' : ''

  return app.request(
    `/api/academies/${academyId}${searchParams}`,
    {
      method: 'GET',
    },
    {},
  )
}

const setupBasicTestData = async () => {
  // Create users and TnC acceptance records
  const users = [testUser, internalUser, externalUser]
  const profiles = [testProfile, internalProfile, externalProfile]

  for (const user of users) {
    await db.insert(userTable).values(user)

    // Add TnC acceptance records (required by authMiddleware for API endpoints)
    const acceptedTnCs = [TNC_TOS_V1_ID as UUID, TNC_PRIVACY_V1_ID as UUID]
    for (const tncId of acceptedTnCs) {
      await db.insert(userTncSignTable).values({
        id: crypto.randomUUID() as UUID,
        userId: user.id,
        tncVersionId: tncId,
        accepted: true,
      })

      await db.insert(userTncAcceptedTable).values({
        id: crypto.randomUUID() as UUID,
        userId: user.id,
        tncVersionId: tncId,
      })
    }
  }

  // Create profiles
  for (const profile of profiles) {
    await db.insert(profileTable).values(profile)
  }

  // Create academies
  await db.insert(academyTable).values([testAcademy, suspendedAcademy, unapprovedAcademy])

  // Create academy staff relationship (only for test academy and test profile)
  await db.insert(academyStaffTable).values({
    profileId: testProfile.id,
    academyId: testAcademy.id,
  })
}

const assertValidAcademyBriefResponse = (
  responseBody: Record<string, unknown>,
  expectedAcademy: typeof testAcademy,
) => {
  expect(responseBody).toMatchObject({
    id: expectedAcademy.id,
    name: expectedAcademy.name,
    email: expectedAcademy.email,
    mobileCountryCode: expectedAcademy.mobileCountryCode,
    mobile: expectedAcademy.mobile,
    currency: expectedAcademy.currency,
    upiId: expectedAcademy.upiId,
  })

  // Brief response should not include detailed fields
  expect(responseBody.descr).toBeUndefined()
  expect(responseBody.tradeName).toBeUndefined()
  expect(responseBody.gstin).toBeUndefined()
  expect(responseBody.approvedAt).toBeUndefined()
  expect(responseBody.suspendedAt).toBeUndefined()
}

const assertValidAcademyDetailResponse = (
  responseBody: Record<string, unknown>,
  expectedAcademy: typeof testAcademy,
  isStaffOrInternal = true,
) => {
  expect(responseBody).toMatchObject({
    id: expectedAcademy.id,
    name: expectedAcademy.name,
    email: expectedAcademy.email,
    mobileCountryCode: expectedAcademy.mobileCountryCode,
    mobile: expectedAcademy.mobile,
    currency: expectedAcademy.currency,
    upiId: expectedAcademy.upiId,
    descr: expectedAcademy.descr,
    emailVerified: expectedAcademy.emailVerified,
    mobileVerified: expectedAcademy.mobileVerified,
    districtId: expectedAcademy.districtId,
    pincode: expectedAcademy.pincode,
    area: expectedAcademy.area,
    addressLine1: expectedAcademy.addressLine1,
    addressLine2: expectedAcademy.addressLine2,
  })

  // Approve and suspension dates should be included
  if (expectedAcademy.approvedAt) {
    expect(responseBody.approvedAt).toBeDefined()
  }
  if (expectedAcademy.suspendedAt) {
    expect(responseBody.suspendedAt).toBeDefined()
  } else {
    expect(responseBody.suspendedAt).toBeNull()
  }

  // Trade name and GSTIN are only visible to staff and internal users
  if (isStaffOrInternal) {
    expect(responseBody.tradeName).toBe(expectedAcademy.tradeName)
    expect(responseBody.gstin).toBe(expectedAcademy.gstin)
  } else {
    expect(responseBody.tradeName).toBeUndefined()
    expect(responseBody.gstin).toBeUndefined()
  }
}

describe('getAcademyHandler', () => {
  beforeEach(async () => {
    await initDb()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Success scenarios', () => {
    test('should return academy brief data when not requesting details', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(testAcademy.id, false)

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()
      assertValidAcademyBriefResponse(responseBody, testAcademy)
    })

    test('should return academy detailed data when requesting details as staff', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(testAcademy.id, true)

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()
      assertValidAcademyDetailResponse(responseBody, testAcademy, true)
    })

    test('should return academy detailed data when requesting details as internal user', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(testAcademy.id, true, internalUser, internalProfile)

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()
      assertValidAcademyDetailResponse(responseBody, testAcademy, true)
    })

    test('should return academy detailed data without sensitive fields for authenticated external users', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(testAcademy.id, true, externalUser, externalProfile)

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()
      assertValidAcademyDetailResponse(responseBody, testAcademy, false)
    })

    test('should return academy brief data without sensitive fields for authenticated external users', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(testAcademy.id, false, externalUser, externalProfile)

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()
      assertValidAcademyBriefResponse(responseBody, testAcademy)
    })

    test('should handle includeDetail=false query parameter correctly', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await app.request(
        `/api/academies/${testAcademy.id}?includeDetail=false`,
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${await createAccessToken(env.JWT_SECRET_KEY, testUser.id)}`,
            [PROFILE_HEADER]: testProfile.id,
          },
        },
        {},
      )

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()
      assertValidAcademyBriefResponse(responseBody, testAcademy)
    })
  })

  describe('Unauthenticated access', () => {
    test('should return academy brief data for unauthenticated users', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeUnauthenticatedRequest(testAcademy.id)

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()

      // Should get basic academy data but without sensitive fields
      expect(responseBody).toMatchObject({
        id: testAcademy.id,
        name: testAcademy.name,
        email: testAcademy.email,
        mobileCountryCode: testAcademy.mobileCountryCode,
        mobile: testAcademy.mobile,
        currency: testAcademy.currency,
        upiId: testAcademy.upiId,
      })

      // Brief response should not include detailed fields
      expect(responseBody.descr).toBeUndefined()
      expect(responseBody.tradeName).toBeUndefined()
      expect(responseBody.gstin).toBeUndefined()
      expect(responseBody.approvedAt).toBeUndefined()
      expect(responseBody.suspendedAt).toBeUndefined()
    })

    test('should return academy detailed data for unauthenticated users when requesting details', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeUnauthenticatedRequest(testAcademy.id, true)

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()

      // Should get detailed academy data but without sensitive fields
      expect(responseBody).toMatchObject({
        id: testAcademy.id,
        name: testAcademy.name,
        email: testAcademy.email,
        mobileCountryCode: testAcademy.mobileCountryCode,
        mobile: testAcademy.mobile,
        currency: testAcademy.currency,
        upiId: testAcademy.upiId,
        descr: testAcademy.descr,
        emailVerified: testAcademy.emailVerified,
        mobileVerified: testAcademy.mobileVerified,
        districtId: testAcademy.districtId,
        pincode: testAcademy.pincode,
        area: testAcademy.area,
        addressLine1: testAcademy.addressLine1,
        addressLine2: testAcademy.addressLine2,
      })

      // Sensitive fields should be undefined for unauthenticated users
      expect(responseBody.tradeName).toBeUndefined()
      expect(responseBody.gstin).toBeUndefined()

      // Approval and suspension dates should be included
      expect(responseBody.approvedAt).toBeDefined()
      expect(responseBody.suspendedAt).toBeNull()
    })

    test('should return 404 for unauthenticated users trying to access unapproved academy', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeUnauthenticatedRequest(unapprovedAcademy.id)

      // then
      // console.log(JSON.stringify(await res.json(), null, 2))
      await expect(res).toBeError(404, [
        { code: 'custom', path: [], message: 'Academy not found, or you are not authorized to access it.' },
      ])
    })

    test('Unauthenticated users should be able to access suspended academy', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeUnauthenticatedRequest(suspendedAcademy.id)

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()
      assertValidAcademyBriefResponse(responseBody, suspendedAcademy as unknown as typeof testAcademy)
    })
  })

  describe('Authentication and authorization errors', () => {
    test('should return 401 when invalid token provided', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await app.request(
        `/api/academies/${testAcademy.id}`,
        {
          method: 'GET',
          headers: {
            Authorization: 'Bearer invalid-token',
            [PROFILE_HEADER]: testProfile.id,
          },
        },
        {},
      )

      // then
      expect(res.status, 'Status check').toBe(401)
    })

    test('should not return sensitive fields when profile header is missing', async () => {
      // given
      await setupBasicTestData()
      const token = await createAccessToken(env.JWT_SECRET_KEY, testUser.id)

      // when
      const res = await app.request(
        `/api/academies/${testAcademy.id}?includeDetail=true`,
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            // Missing PROFILE_HEADER
          },
        },
        {},
      )

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()
      assertValidAcademyDetailResponse(responseBody, testAcademy, false)
    })
  })

  describe('Error scenarios', () => {
    test('should return 404 when academy does not exist', async () => {
      // given
      await setupBasicTestData()
      const nonExistentAcademyId = crypto.randomUUID() as UUID

      // when
      const res = await makeAuthenticatedRequest(nonExistentAcademyId)

      // then
      await expect(res).toBeError(404, [
        { code: 'custom', path: [], message: 'Academy not found, or you are not authorized to access it.' },
      ])
    })

    test('should return 404 when trying to access unapproved academy as external user', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(unapprovedAcademy.id, false, externalUser, externalProfile)

      // then
      await expect(res).toBeError(404, [
        { code: 'custom', path: [], message: 'Academy not found, or you are not authorized to access it.' },
      ])
    })

    test('should allow internal users to access unapproved academies', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(unapprovedAcademy.id, false, internalUser, internalProfile)

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()
      expect(responseBody.id).toBe(unapprovedAcademy.id)
    })

    test('should allow academy staff to access their own academy even if unapproved', async () => {
      // given
      await setupBasicTestData()

      // Create staff relationship for unapproved academy
      await db
        .update(academyStaffTable)
        .set({ academyId: unapprovedAcademy.id })
        .where(eq(academyStaffTable.profileId, testProfile.id))

      // when
      const res = await makeAuthenticatedRequest(unapprovedAcademy.id)

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()
      expect(responseBody.id).toBe(unapprovedAcademy.id)
    })

    test('should handle invalid UUID parameter gracefully', async () => {
      // given
      await setupBasicTestData()
      const token = await createAccessToken(env.JWT_SECRET_KEY, testUser.id)

      // when
      const res = await app.request(
        '/api/academies/invalid-uuid',
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            [PROFILE_HEADER]: testProfile.id,
          },
        },
        {},
      )

      // then
      expect(res.status, 'Status check').toBe(400)
    })
  })

  describe('Edge cases', () => {
    test('should handle missing optional fields in academy data', async () => {
      // given
      await setupBasicTestData()

      // Create academy with minimal required fields
      const minimalAcademy = {
        id: crypto.randomUUID() as UUID,
        name: 'Minimal Academy',
        email: '<EMAIL>',
        emailVerified: true,
        mobileCountryCode: 'IN',
        mobile: '9876543215',
        mobileVerified: true,
        currency: 'INR' as const,
        upiId: 'minimal@upi',
        districtId: SUNDARGARH_DISTRICT_ID as UUID,
        pincode: '770004',
        area: 'Minimal Area',
        tncsAcceptedAt: mocked.now.toJSDate(),
        approvedAt: mocked.now.toJSDate(),
        // Note: Missing optional fields like descr, tradeName, gstin, etc.
      }

      await db.insert(academyTable).values(minimalAcademy)
      await db
        .update(academyStaffTable)
        .set({ academyId: minimalAcademy.id })
        .where(eq(academyStaffTable.profileId, testProfile.id))

      // when
      const res = await makeAuthenticatedRequest(minimalAcademy.id, true)

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()
      expect(responseBody.id).toBe(minimalAcademy.id)
      expect(responseBody.descr).toBeNull()
      expect(responseBody.tradeName).toBeNull()
      expect(responseBody.gstin).toBeNull()
    })
  })

  describe('Query parameter validation', () => {
    test('should treat any non-"true" includeDetail value as false', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await app.request(
        `/api/academies/${testAcademy.id}?includeDetail=invalid`,
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${await createAccessToken(env.JWT_SECRET_KEY, testUser.id)}`,
            [PROFILE_HEADER]: testProfile.id,
          },
        },
        {},
      )

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()
      assertValidAcademyBriefResponse(responseBody, testAcademy)
    })

    test('should handle multiple query parameters correctly', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await app.request(
        `/api/academies/${testAcademy.id}?includeDetail=true&someOtherParam=value`,
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${await createAccessToken(env.JWT_SECRET_KEY, testUser.id)}`,
            [PROFILE_HEADER]: testProfile.id,
          },
        },
        {},
      )

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseBody = await res.json()
      assertValidAcademyDetailResponse(responseBody, testAcademy, true)
    })
  })
})

import { UUID } from 'crypto'

import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'

import { approveProfile } from './approveProfile'

export const approveProfileHandler = new Hono<HonoVars>().put('/', async (c) => {
  const targetProfileId = c.req.param('id') as UUID

  await approveProfile(getCtx(c), targetProfileId)
  return c.body(null, 204)
})

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $InviteProfileForm } from '@/shared/profiles/profile-utils.shared'

import { inviteProfile } from './inviteProfile'

export const inviteProfileHandler = new Hono<HonoVars>().post(
  '/',
  zValidator('json', $InviteProfileForm),
  async (c) => {
    const form = c.req.valid('json')
    await inviteProfile(getCtx(c), form)
    return c.body(null, 204)
  },
)

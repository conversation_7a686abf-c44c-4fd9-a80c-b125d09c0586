import { XCircleIcon } from '@heroicons/react/20/solid'
import clsx from 'clsx'
import React from 'react'

import { getErrors } from '@/pages/common/utils/error-utils.ui'
import { type OmniError } from '@/shared/common/error-utils.shared'

type ShowErrorsProps = {
  error?: OmniError
  path?: (string | number)[]
}

/**
 * Displays error messages in either form or field variant.
 * Form variant shows errors in a red box with an icon.
 * Field variant shows errors in a simpler list format.
 *
 * @example
 * // Form level errors
 * <ShowErrors error={error} />
 *
 * // Field level errors
 * <ShowErrors error={error} path={['fieldName']} />
 */
export const ShowErrors = ({ error, path = [] }: ShowErrorsProps) => {
  const errorMessages = getErrors(error, path)
  const variant = path.length > 0 ? 'field' : 'form'

  if (errorMessages.length === 0) {
    return null
  }

  if (variant === 'form') {
    return <ShowErrorMessages messages={errorMessages} />
  }

  return (
    <ul
      className={clsx('text-sm text-red-600 space-y-1', {
        'mt-2': errorMessages.length > 1,
        'mt-1': errorMessages.length === 1,
      })}
    >
      {errorMessages.map((err) => (
        <li key={err} className="flex items-center">
          {err}
        </li>
      ))}
    </ul>
  )
}

/**
 * Consider using getMyError instead.
 */
export const ShowErrorMessages = ({ messages }: { messages: string[] }) => {
  return (
    <div className="rounded-md bg-red-50 p-4 my-2">
      <div className="flex">
        <div className="shrink-0">
          <XCircleIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
        </div>
        {messages.length > 1 ?
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">There are errors !</h3>
            <div className="mt-2 text-sm text-red-700">
              <ul role="list" className="list-disc space-y-1 pl-5">
                {messages.map((message) => (
                  <li key={message}>{message}</li>
                ))}
              </ul>
            </div>
          </div>
        : <div className="ml-3">
            <div className="text-sm text-red-700">
              <span className="sr-only">Error</span>
              {messages[0]}
            </div>
          </div>
        }
      </div>
    </div>
  )
}

import { UUID } from 'crypto'

import { useQueryClient } from '@tanstack/react-query'

type QueryKeys = {
  lists: () => readonly [...unknown[], 'list']
  detail: (id: UUID) => readonly [...unknown[], UUID]
}

export const useInvalidateDetail = (keys: QueryKeys) => {
  const queryClient = useQueryClient()
  return (id: UUID, removing = false) => {
    void queryClient.invalidateQueries({ queryKey: keys.lists() })
    if (removing) {
      void queryClient.removeQueries({ queryKey: keys.detail(id) })
    } else {
      void queryClient.invalidateQueries({ queryKey: keys.detail(id) })
    }
  }
}

import { DateTime } from 'luxon'
import { useRef, useState } from 'react'
import { navigate } from 'vike/client/router'

import { CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { batchPath, EditBatchForm } from '@/shared/batch/batch-utils.shared'
import { UNKNOWN_UUID } from '@/shared/common/common-utils.shared'
import { type OmniError } from '@/shared/common/error-utils.shared'
import { coursePath } from '@/shared/course/course-utils.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'
import { FormButtons, PageContent, PageLayout } from '@ui/common/form/page-layout'
import { WithProfile } from '@ui/profiles/common/WithProfile'

import { useAddBatchMutation } from '../common/batch-queries'
import { BatchEditor } from '../common/BatchEditor'

export const AddBatch = ({ course }: { course: CourseWithoutDescr }) => {
  const batchIdRef = useRef(crypto.randomUUID())

  const [formData, setFormData] = useState<EditBatchForm>({
    teacherId: UNKNOWN_UUID,
    startDate: DateTime.now().toISODate(),
    timezone: 'Asia/Kolkata',
    billingCycle: 'months',
    cycleCount: 9,
    graceDays: 3,
    seatCount: 25,
    admissionOpen: true,
    fee: { currency: 'INR', amount: 500 },
  })

  const [omniError, setOmniError] = useState<OmniError | undefined>()
  const { mutate: addBatch, isPending: addBatchIsPending } = useAddBatchMutation()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    addBatch(
      { ...formData, courseId: course.id, batchId: batchIdRef.current },
      {
        onSuccess: () => {
          void navigate(`${batchPath(course, batchIdRef.current)}`)
        },
        onError: (error) => {
          setOmniError(error.error)
        },
      },
    )
  }

  return (
    <WithProfile roleAnyOf={ACADEMY_STAFF}>
      <PageLayout title="Add a Batch" description={`${course.name}`} maxWidth="max-w-2xl">
        <form onSubmit={handleSubmit}>
          <PageContent>
            <BatchEditor
              formData={formData}
              setFormData={setFormData}
              error={omniError}
              setError={setOmniError}
              courseAcademyId={course.academyId}
            />
            <FormButtons
              omniError={omniError}
              onCancel={() => navigate(`${coursePath(course)}/batches`)}
              isSubmitting={addBatchIsPending}
              submitLabel="Next"
            />
          </PageContent>
        </form>
      </PageLayout>
    </WithProfile>
  )
}

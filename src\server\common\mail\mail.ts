import { Logger } from 'pino'

import { env } from '../env.server'
import { getTemplate } from '../getTemplate'

import { sendSesMail } from './ses/sendSesMail'
import { sendSmtpMail } from './smtp/sendSmtpMail'

type MailTemplate =
  | 'invitation'
  | 'tncReminder'
  | 'studentPaymentReminderGentle'
  | 'studentPaymentReminderPrudent'
  | 'studentPaymentReminderLast'
  | 'studentPaymentReminderUrgent'
  | 'studentPaymentReminderBlocked'
  | 'academyEmailVerification'
  | 'pleaseLoginAsap'

type MailDetail = {
  to: string
  data: Record<string, unknown>
}

export const mailFlock = () => 'mail'

export const mail = async (
  log: Logger,
  redact: boolean,
  template: MailTemplate,
  language: string,
  detail: MailDetail,
) => {
  const subjectTemplate = await getTemplate(log, `${template}Subject`, 'mail', language)
  const bodyTemplate = await getTemplate(log, `${template}Body`, 'mail', language)

  const subjectPrefix = env.APP_ENV === 'production' ? '' : `[${env.APP_ENV}]`
  const subject = subjectPrefix + subjectTemplate(detail.data)
  const body = bodyTemplate(detail.data)

  const hideBody = redact && env.AWS_SECRET_KEY
  log.info(
    { mail: { template, language, subject, detail: hideBody ? 'REDACTED' : detail } },
    `Mail content: ${hideBody ? 'REDACTED' : body}`,
  )

  if (env.SMTP_PASSWORD) await sendSmtpMail(log, detail.to, subject, body)
  else if (env.AWS_SECRET_KEY) await sendSesMail(log, detail.to, subject, body)
}

import { UUID } from 'crypto'

import { Store, useStore } from '@tanstack/react-store'
import { useEffect } from 'react'

import { updateStore } from '@ui/common/utils/updateStore'

import { useMyProfilesQuery } from './profile-queries'

const CURRENT_PROFILE_ID = 'currentProfileId'
const currentProfileIdStore = new Store({
  id: null as UUID | null,
})

let isCurrentProfileIdInitialized = false
const useCurrentProfileId = () => {
  useEffect(() => {
    if (!isCurrentProfileIdInitialized) {
      updateStore(currentProfileIdStore, { id: localStorage.getItem(CURRENT_PROFILE_ID) as UUID | null })
      isCurrentProfileIdInitialized = true
    }
  }, [])

  return useStore(currentProfileIdStore, (state) => state.id)
}

export const getCurrentProfileId = () => currentProfileIdStore.state.id

export const setCurrentProfileId = (id: UUID | null) => {
  if (id) localStorage.setItem(CURRENT_PROFILE_ID, id)
  else localStorage.removeItem(CURRENT_PROFILE_ID)
  updateStore(currentProfileIdStore, { id })
}

export const useCurrentProfile = () => {
  const currentProfileId = useCurrentProfileId()
  const { data: profiles, isPending, error } = useMyProfilesQuery()

  const currentProfile = profiles?.find((p) => p.id === currentProfileId)
  return { currentProfile, profileIsPending: isPending, profileError: error }
}

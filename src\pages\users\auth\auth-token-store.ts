import { Store, useStore } from '@tanstack/react-store'
import secureLocalStorage from 'react-secure-storage'

import { getLogger } from '@/shared/common/logger.shared'
import { AuthTokenData } from '@/shared/users/user-utils.shared'
import { updateStore } from '@ui/common/utils/updateStore'

import { getRememberMe } from '../signin/remember-me-store'

export const AUTH_TOKEN = 'authToken'

export const getTokenFromSecureStorage = () => {
  const log = getLogger()
  const token = secureLocalStorage.getItem(AUTH_TOKEN) as AuthTokenData | null

  if (!token) {
    log.info('Token not found in secure storage')
    return null
  }

  log.info(`Token found in secure storage, which is valid until ${token.accessTokenValidUntil}`)

  const now = new Date().getTime()
  const tokenValidUntil = Date.parse(token.accessTokenValidUntil)
  const hoursDifference = Math.abs(tokenValidUntil - now) / 36e5
  log.info(`Token found in secure storage, which is expiring in ${hoursDifference} hours`)

  if (hoursDifference <= 24) {
    return null
  }
  return token
}

export const tokenStore = new Store({
  token: null as AuthTokenData | null,
})

export const useUserId = () => useStore(tokenStore, (state) => state.token?.userId) ?? null
export const useSignedIn = () => useUserId() !== null

export const getAccessToken = () => tokenStore.state.token?.accessToken ?? null

export const setAuth = (token: AuthTokenData) => {
  updateStore(tokenStore, { token })

  if (getRememberMe()) {
    secureLocalStorage.setItem(AUTH_TOKEN, token)
  }
}

export const clearAuth = () => {
  updateStore(tokenStore, { token: null })
  secureLocalStorage.removeItem(AUTH_TOKEN)
}

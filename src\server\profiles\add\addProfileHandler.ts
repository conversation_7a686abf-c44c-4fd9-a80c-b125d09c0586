import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $AddProfileForm } from '@/shared/profiles/profile-utils.shared'

import { addProfile } from './addProfile'

export const addProfileHandler = new Hono<HonoVars>().post('/', zValidator('json', $AddProfileForm), async (c) => {
  const form = c.req.valid('json')

  const result = await addProfile(getCtx(c), form)
  return c.json(result, 201)
})

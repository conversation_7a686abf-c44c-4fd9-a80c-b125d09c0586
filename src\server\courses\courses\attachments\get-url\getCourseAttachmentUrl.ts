import { UUID } from 'crypto'

import { and, eq, or, exists, isNull, isNotNull } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { env } from '@/server/common/env.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { generateDownloadURL } from '@/server/common/s3/s3-utils.server'
import { db } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { academyStaffTable, academyTable } from '@/server/db/schema/academy-schema'
import { batchStudentTable, batchTable } from '@/server/db/schema/batch-schema'
import { courseAttachmentTable, courseTable } from '@/server/db/schema/course-schema'
import { withLog } from '@/shared/common/withLog.shared'
import { courseAttachmentS3Path } from '@/shared/course/course-utils.shared'
const findCourseAttachmentDaf = async (log: pino.Logger, attachmentId: UUID, profileId: UUID | undefined) =>
  withLog(log, 'findCourseAttachmentDaf', () =>
    db
      .select({
        id: courseAttachmentTable.id,
        name: courseAttachmentTable.name,
        contentType: courseAttachmentTable.contentType,
        uploaded: courseAttachmentTable.uploaded,
        free: courseAttachmentTable.free,
      })
      .from(courseAttachmentTable)
      .innerJoin(courseTable, eq(courseAttachmentTable.courseId, courseTable.id))
      .innerJoin(academyTable, eq(courseTable.academyId, academyTable.id))
      .leftJoin(academyStaffTable, eq(academyStaffTable.academyId, courseTable.academyId))
      .where(
        and(
          eq(courseAttachmentTable.id, attachmentId),
          or(
            eq(courseAttachmentTable.free, true),
            profileId ?
              or(
                and(isNotNull(academyStaffTable.profileId), eq(academyStaffTable.profileId, profileId)),
                exists(
                  db
                    .select(dummyColumn.extras)
                    .from(batchStudentTable)
                    .innerJoin(batchTable, eq(batchStudentTable.batchId, batchTable.id))
                    .where(
                      and(
                        eq(batchStudentTable.studentId, profileId),
                        eq(batchTable.courseId, courseTable.id),
                        isNull(batchStudentTable.leftAt),
                      ),
                    ),
                ),
              )
            : undefined,
          ),
        ),
      ),
  )

export const getCourseAttachmentUrl = async (c: Ctx, attachmentId: UUID) => {
  const [attachment] = await findCourseAttachmentDaf(c.log, attachmentId, c.profile?.id)

  ensureExists(attachment, attachmentId, 'Course attachment')

  ensure(attachment.uploaded, {
    statusCode: 409,
    issueMessage: 'Attachment was not uploaded',
    logMessage: `Attachment ${attachmentId} was not uploaded`,
  })

  ensure(!attachment.free, {
    statusCode: 409,
    issueMessage: 'Attachment is freely downloadable',
    logMessage: `Attachment ${attachmentId} is freely downloadable`,
  })

  const url = await generateDownloadURL(courseAttachmentS3Path(env.APP_ENV, attachmentId))
  return {
    url,
  }
}

export type CourseAttachmentUrl = Awaited<ReturnType<typeof getCourseAttachmentUrl>>

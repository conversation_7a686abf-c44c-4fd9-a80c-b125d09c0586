import { UUID } from 'crypto'

import { and, eq, isNull, lt, sql } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { runDLocked } from '@/server/common/distributed-lock/runDLocked'
import { ensureProfile } from '@/server/common/error/ensure-profile.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db, Transaction } from '@/server/db/db'
import { batchStudentTable, batchTable } from '@/server/db/schema/batch-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { updateGoogleEventForBatch } from '../common/updateGoogleEventForBatch'
import { updateStudentPaymentReminderType } from '../payments/process/update-student-reminder-type/updateStudentPaymentReminderType'

// Check if the batch exists and has available seats
const findBatchForJoiningDaf = (log: pino.Logger, batchId: UUID) =>
  withLog(log, 'findBatchForJoiningDaf', () =>
    db.query.batchTable.findFirst({
      columns: {
        id: true,
        seatCount: true,
        studentCount: true,
        admissionOpen: true,
        startDate: true,
        timezone: true,
        billingCycle: true,
        cycleCount: true,
        graceDays: true,
      },
      with: {
        teacher: {
          with: {
            user: {
              columns: {
                id: true,
              },
            },
          },
        },
        events: {
          columns: {
            id: true,
            eventXid: true,
          },
        },
      },
      where: eq(batchTable.id, batchId),
    }),
  )

// Check if the student has already joined this batch
const findExistingBatchStudentDaf = (log: pino.Logger, batchId: UUID, studentId: UUID) =>
  withLog(log, 'findExistingBatchStudentDaf', () =>
    db
      .select({
        id: batchStudentTable.id,
      })
      .from(batchStudentTable)
      .where(
        and(
          eq(batchStudentTable.batchId, batchId),
          eq(batchStudentTable.studentId, studentId),
          isNull(batchStudentTable.leftAt),
        ),
      )
      .limit(1),
  )

// Insert a new batch student record
const upsertBatchStudentDaf = (log: pino.Logger, db: Transaction, batchId: UUID, studentId: UUID) =>
  withLog(log, 'upsertBatchStudentDaf', () =>
    db
      .insert(batchStudentTable)
      .values({
        id: crypto.randomUUID(),
        batchId,
        studentId,
        firstJoinedAt: utc().toJSDate(),
      })
      .onConflictDoUpdate({
        target: [batchStudentTable.batchId, batchStudentTable.studentId],
        set: {
          leftAt: null,
          updatedAt: utc().toJSDate(),
          updateRemarks: 'rejoined',
        },
      })
      .returning({
        id: batchStudentTable.id,
        firstJoinedAt: batchStudentTable.firstJoinedAt,
        paidTillCycle: batchStudentTable.paidTillCycle,
        lastReminderType: batchStudentTable.lastReminderType,
      }),
  )

// Update the student count in the batch
const incrementBatchStudentCountDaf = (log: pino.Logger, db: Transaction, batchId: UUID) =>
  withLog(log, 'incrementBatchStudentCountDaf', () =>
    db
      .update(batchTable)
      .set({
        studentCount: sql`${batchTable.studentCount} + 1`,
      })
      .where(
        and(
          eq(batchTable.id, batchId),
          eq(batchTable.admissionOpen, true),
          lt(batchTable.studentCount, batchTable.seatCount),
        ),
      ),
  )

export const joinBatch = async (c: Ctx, batchId: UUID) => {
  ensureUser(c.user)
  ensureProfile(c.profile, { roleAnyOf: ['student'] })
  const studentId = c.profile.id

  // Ensure the batch exists and has available seats
  const batch = await findBatchForJoiningDaf(c.log, batchId)
  ensureExists(batch, batchId, 'Batch')

  // Ensure the batch is open for admission
  ensure(batch.admissionOpen, {
    statusCode: 409,
    issueMessage: 'Batch is not open for admission.',
    logMessage: `Attempted to join batch ${batchId} which is not open for admission.`,
  })

  await runDLocked(c.log, ['batch', batchId], async () => {
    // Ensure the batch has available seats
    ensure(batch.studentCount < batch.seatCount, {
      statusCode: 409,
      issueMessage: 'Batch is full.',
      logMessage: `Attempted to join batch ${batchId} which is full (${batch.studentCount}/${batch.seatCount}).`,
    })

    // Ensure the student has not already joined this batch
    const existingBatchStudent = await findExistingBatchStudentDaf(c.log, batchId, studentId)
    ensure(existingBatchStudent.length === 0, {
      statusCode: 409,
      issueMessage: 'Student has already joined this batch.',
      logMessage: `Student ${studentId} has already joined batch ${batchId}.`,
    })

    await db.transaction(async (tx) => {
      await incrementBatchStudentCountDaf(c.log, tx, batchId)
      const [batchStudent] = await upsertBatchStudentDaf(c.log, tx, batchId, studentId)
      await updateStudentPaymentReminderType(c.log, tx, batch, batchStudent)
      // Don't unblock the student if they are already blocked
      if (batchStudent.lastReminderType !== 'blocked') {
        await updateGoogleEventForBatch(tx, c.log, batch.teacher.user.id, batch.events)
      }
    })
  })
}

import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { profileTable } from '@/server/db/schema/profile-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { markdown2Html } from '@/shared/common/markdown-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { EditProfileForm } from '@/shared/profiles/profile-utils.shared'

const updateProfileDaf = (log: pino.Logger, userId: UUID, profileId: UUID, form: EditProfileForm) =>
  withLog(log, 'updateProfileDaf', () =>
    db
      .update(profileTable)
      .set({
        displayName: form.displayName,
        descr: form.descr ? markdown2Html(form.descr) : null,
        updatedAt: utc().toJSDate(),
      })
      .where(and(eq(profileTable.id, profileId), eq(profileTable.userId, userId))),
  )

export const updateProfile = async (c: Ctx, profileId: UUID, form: EditProfileForm) => {
  ensureUser(c.user, { ignoreEmailVerification: true, ignoreMobileVerification: true })

  // Update profile
  const result = await updateProfileDaf(c.log, c.user.id, profileId, form)

  // Ensure profile was found and updated
  ensure(result.count > 0, {
    statusCode: 404,
    issueMessage: 'Profile not found or does not belong to you.',
    logMessage: `User ${c.user.id} attempted to update profile ${profileId} but it was not found or does not belong to them.`,
  })
}

import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfile } from '@/server/common/error/ensure-profile.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { academyStaffTable, academyTable } from '@/server/db/schema/academy-schema'
import { courseAttachmentTable, courseTable } from '@/server/db/schema/course-schema'
import { withLog } from '@/shared/common/withLog.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'

const findCourseAttachmentDaf = async (log: pino.Logger, attachmentId: UUID, staffId: UUID) =>
  withLog(log, 'findCourseAttachmentDaf', () =>
    db
      .select({
        id: courseAttachmentTable.id,
        name: courseAttachmentTable.name,
        contentType: courseAttachmentTable.contentType,
        sizeBytes: courseAttachmentTable.sizeBytes,
        free: courseAttachmentTable.free,
      })
      .from(courseAttachmentTable)
      .innerJoin(courseTable, eq(courseAttachmentTable.courseId, courseTable.id))
      .innerJoin(academyTable, eq(courseTable.academyId, academyTable.id))
      .innerJoin(academyStaffTable, eq(academyStaffTable.academyId, courseTable.academyId))
      .where(and(eq(courseAttachmentTable.id, attachmentId), eq(academyStaffTable.profileId, staffId))),
  )

export const getCourseAttachment = async (c: Ctx, attachmentId: UUID) => {
  ensureUser(c.user)
  ensureProfile(c.profile, { roleAnyOf: ACADEMY_STAFF })

  const [attachment] = await findCourseAttachmentDaf(c.log, attachmentId, c.profile.id)
  ensureExists(attachment, attachmentId, 'Course attachment')
  return attachment
}

export type CourseAttachmentDetail = Awaited<ReturnType<typeof getCourseAttachment>>

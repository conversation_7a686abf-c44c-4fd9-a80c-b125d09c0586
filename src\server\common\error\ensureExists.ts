import { ContentfulStatusCode } from 'hono/utils/http-status'

import { ensure } from '@/server/common/error/ensure.server'

export function ensureExists<T>(
  entity: T,
  id: string,
  name: string,
  statusCode: ContentfulStatusCode = 404,
): asserts entity {
  ensure(entity, {
    statusCode,
    issueMessage: `${name} not found, or you are not authorized to access it.`,
    logMessage: `${name} ${id} not found.`,
    data: { id },
  })
}

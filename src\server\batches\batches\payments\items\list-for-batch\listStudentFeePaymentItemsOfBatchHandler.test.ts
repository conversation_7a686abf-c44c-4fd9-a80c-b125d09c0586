import { UUID } from 'crypto'

import { testClient } from 'hono/testing'

import { batchStudentTable, studentFeePaymentItemTable, studentFeePaymentTable } from '@/server/db/schema'
import { BatchPaymentsSearch } from '@/shared/batch/batch-utils.shared'

import { listStudentFeePaymentItemsOfBatchHandler } from './listStudentFeePaymentItemsOfBatchHandler'

import { createTestAcademy } from '@/server/academies/test-helpers/academy-test-helpers.server'
import { createTestBatch } from '@/server/batches/test-helpers/batch-test-helpers.server'
import { createTestCourse } from '@/server/courses/test-helpers/course-test-helpers.server'
import { testDb } from '@/server/db/test-db'
import { createTestProfile } from '@/server/profiles/test-helpers/profile-test-helpers.server'
import { createTestApp } from '@/server/test-helpers/app-test-helpers.server'
import { createTestUser } from '@/server/users/test-helpers/user-test-helpers.server'

const app = createTestApp().route('/test', listStudentFeePaymentItemsOfBatchHandler)

describe('listStudentFeePaymentItemsOfBatchHandler', () => {
  describe('Pagination scenarios', () => {
    const setupPaginationTestData = async () => {
      // Create test academy, course, and batch
      const { user: teacherUser } = await createTestUser()
      const { profile: teacherProfile } = await createTestProfile({ userId: teacherUser.id, role: 'teacher' })
      const { academy } = await createTestAcademy({ ownerId: teacherProfile.id })
      const { course } = await createTestCourse({ academyId: academy.id, teacherId: teacherProfile.id })
      const { batch } = await createTestBatch({ courseId: course.id, teacherId: teacherProfile.id })

      // Create 3 students with multiple payment items each
      const students = []
      const paymentItems = []

      for (let i = 1; i <= 3; i++) {
        const { user: studentUser } = await createTestUser()
        const { profile: studentProfile } = await createTestProfile({
          userId: studentUser.id,
          role: 'student',
        })
        students.push({ user: studentUser, profile: studentProfile })

        // Enroll student in batch
        await testDb.insert(batchStudentTable).values({
          batchId: batch.id,
          studentId: studentProfile.id,
          firstJoinedAt: new Date(),
          paidTillCycle: 2,
        })

        // Create payment for this student
        const [payment] = await testDb
          .insert(studentFeePaymentTable)
          .values({
            studentId: studentProfile.id,
            academyId: academy.id,
            currency: 'INR',
            cents: 100000, // 1000 INR
            status: 'paid',
            paidAt: new Date(),
          })
          .returning()

        // Create 2 payment items per student (cycles 1 and 2)
        for (let cycle = 1; cycle <= 2; cycle++) {
          const [item] = await testDb
            .insert(studentFeePaymentItemTable)
            .values({
              batchId: batch.id,
              studentFeePaymentId: payment.id,
              cycle,
              fee: 50000, // 500 INR per cycle
            })
            .returning()
          paymentItems.push(item)
        }
      }

      return {
        teacherUser,
        teacherProfile,
        academy,
        course,
        batch,
        students,
        paymentItems,
      }
    }

    it('should return first page with pageSize=2', async () => {
      const { batch, teacherUser } = await setupPaginationTestData()

      const search: BatchPaymentsSearch = { pageSize: '2' }
      const client = testClient(app, {
        Variables: {
          user: teacherUser,
          profile: null,
        },
      })

      const response = await client.test.$get({
        param: { id: batch.id },
        query: search,
      })

      expect(response.status).toBe(200)
      const responseBody = await response.json()
      expect(responseBody.rows).toHaveLength(2)

      // Should be ordered by studentId DESC, cycle DESC (first page)
      const payments = responseBody.rows
      expect(payments[0].studentId >= payments[1].studentId).toBe(true)
    })

    it('should return next page with cursor', async () => {
      const { batch, teacherUser, students } = await setupPaginationTestData()

      // First, get the first page
      const firstPageSearch: BatchPaymentsSearch = { pageSize: '2' }
      const client = testClient(app, {
        Variables: {
          user: teacherUser,
          profile: null,
        },
      })

      const firstResponse = await client.test.$get({
        param: { id: batch.id },
        query: firstPageSearch,
      })

      const firstPageBody = await firstResponse.json()
      const lastItem = firstPageBody.rows[firstPageBody.rows.length - 1]

      // Now get the next page using cursor
      const nextPageSearch: BatchPaymentsSearch = {
        pageSize: '2',
        fromStudentId: lastItem.studentId,
        fromCycle: lastItem.cycle,
        beyondStudentId: lastItem.studentId,
        beyondCycle: lastItem.cycle,
      }

      const nextResponse = await client.test.$get({
        param: { id: batch.id },
        query: nextPageSearch,
      })

      expect(nextResponse.status).toBe(200)
      const nextPageBody = await nextResponse.json()

      // Should have remaining items
      expect(nextPageBody.rows.length).toBeGreaterThan(0)
      expect(nextPageBody.rows.length).toBeLessThanOrEqual(2)

      // Items should be different from first page
      const nextPageIds = nextPageBody.rows.map((p: any) => `${p.studentId}-${p.cycle}`)
      const firstPageIds = firstPageBody.rows.map((p: any) => `${p.studentId}-${p.cycle}`)
      expect(nextPageIds).not.toEqual(firstPageIds)
    })

    it('should return previous page with cursor', async () => {
      const { batch, teacherUser } = await setupPaginationTestData()

      // Get a middle page first by getting next page
      const firstPageSearch: BatchPaymentsSearch = { pageSize: '2' }
      const client = testClient(app, {
        Variables: {
          user: teacherUser,
          profile: null,
        },
      })

      const firstResponse = await client.test.$get({
        param: { id: batch.id },
        query: firstPageSearch,
      })

      const firstPageBody = await firstResponse.json()
      const lastItem = firstPageBody.rows[firstPageBody.rows.length - 1]

      // Get next page
      const nextPageSearch: BatchPaymentsSearch = {
        pageSize: '2',
        fromStudentId: lastItem.studentId,
        fromCycle: lastItem.cycle,
        beyondStudentId: lastItem.studentId,
        beyondCycle: lastItem.cycle,
      }

      const nextResponse = await client.test.$get({
        param: { id: batch.id },
        query: nextPageSearch,
      })

      const nextPageBody = await nextResponse.json()

      if (nextPageBody.rows.length > 0) {
        const firstItemOfNextPage = nextPageBody.rows[0]

        // Now get previous page
        const prevPageSearch: BatchPaymentsSearch = {
          pageSize: '2',
          previous: 'true',
          fromStudentId: firstItemOfNextPage.studentId,
          fromCycle: firstItemOfNextPage.cycle,
          beyondStudentId: firstItemOfNextPage.studentId,
          beyondCycle: firstItemOfNextPage.cycle,
        }

        const prevResponse = await client.test.$get({
          param: { id: batch.id },
          query: prevPageSearch,
        })

        expect(prevResponse.status).toBe(200)
        const prevPageBody = await prevResponse.json()

        // Should return the first page items
        expect(prevPageBody.rows).toEqual(firstPageBody.rows)
      }
    })

    it('should handle empty result set', async () => {
      const { batch, teacherUser } = await setupPaginationTestData()

      // Search with cursor that should return no results
      const search: BatchPaymentsSearch = {
        pageSize: '10',
        fromStudentId: '00000000-0000-0000-0000-000000000000' as UUID,
        fromCycle: 999,
        beyondStudentId: '00000000-0000-0000-0000-000000000000' as UUID,
        beyondCycle: 999,
      }

      const client = testClient(app, {
        Variables: {
          user: teacherUser,
          profile: null,
        },
      })

      const response = await client.test.$get({
        param: { id: batch.id },
        query: search,
      })

      expect(response.status).toBe(200)
      const responseBody = await response.json()
      expect(responseBody.rows).toHaveLength(0)
    })
  })
})

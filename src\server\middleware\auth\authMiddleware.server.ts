import { UUID } from 'crypto'

import { and, eq, isNull, lte } from 'drizzle-orm'
import { Context } from 'hono'
import { bearerAuth } from 'hono/bearer-auth'
import { createMiddleware } from 'hono/factory'
import { Logger } from 'pino'

import { ContextProfile, ContextUser } from '@/server/common/auth-context-types.server'
import { env } from '@/server/common/env.server'
import { profileTable } from '@/server/db/schema/profile-schema'
import { parseAccessToken } from '@/server/users/common/auth-token-utils.server'
import { Email } from '@/shared/common/common-utils.shared'
import { today } from '@/shared/common/date-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { PROFILE_HEADER } from '@/shared/profiles/profile-utils.shared'
import { Role } from '@/shared/profiles/role-utils.shared'
import { whetherAllTnCsAccepted } from '@/shared/users/user-utils.shared'

import { ensure } from '../../common/error/ensure.server'
import { db } from '../../db/db'
import { userTncVersionTable } from '../../db/schema/user-schema'

import { findUserForAuthDaf, UserWithTnCsRow } from './findUserForAuthDaf'

export const authMiddleware = createMiddleware<{
  Variables: {
    user?: ContextUser
    profile?: ContextProfile
  }
}>(async (c, next) => {
  if (c.req.header('Authorization')) {
    await bearerAuth({ verifyToken: processBearerToken })(c, next)
  } else {
    await next() // Public request, no Authorization header
  }
})

const processBearerToken = async (token: string, c: Context) => {
  let log = c.get('log')
  const payload = await parseAccessToken(env.JWT_SECRET_KEY, token)
  const userId = payload.sub as UUID

  log.info(`User ${userId} logging in: Auth token has payload ${JSON.stringify(payload)}`)
  const toCheckTnCs = whetherToCheckTnCs(c)
  const user = await findUserForAuthDaf(log, userId, toCheckTnCs)

  ensure(user, {
    logMessage: `User ${userId} not found`,
    issueMessage: `User not found. Please login again`,
    statusCode: 401,
  })

  log = log.child(getUserLogBinding({ id: userId, email: user.email }))
  c.set('log', log)
  ensure(!user.suspendedAt, {
    logMessage: `User ${userId} suspended`,
    issueMessage: `User suspended. Please contact support`,
    statusCode: 401,
  })

  ensure(user.tokensValidFrom.getTime() <= payload.iat! * 1000, {
    logMessage: `Obsolete token used for user ${userId}`,
    issueMessage: `Obsolete token used. Please login again`,
    statusCode: 401,
  })

  if (toCheckTnCs) {
    const requiredTnCs = await findRequiredTnCsDaf(log)
    const userWithTnCs = user as UserWithTnCsRow
    ensure(
      whetherAllTnCsAccepted(
        requiredTnCs,
        userWithTnCs.tncAcceptances.map((row) => row.tncVersionId),
      ),
      {
        issueMessage: `Please first accept all TnCs`,
        logMessage: `User ${userId} has not accepted all applicable TnCs`,
        statusCode: 403,
      },
    )
  }

  c.set('user', {
    id: userId,
    email: user.email,
    emailVerified: user.emailVerified,
    mobileVerified: user.mobileVerified,
    suspendedAt: user.suspendedAt,
    language: user.language,
  } as ContextUser)

  const profileId = c.req.header(PROFILE_HEADER)
  if (profileId) {
    const profile = await findProfileDaf(log, userId, profileId as UUID)
    if (profile) {
      c.set('log', log.child(getProfileLogBinding(profile)))
      c.set('profile', {
        id: profile.id,
        displayName: profile.displayName,
        role: profile.role,
        suspendedAt: profile.suspendedAt,
        approvedAt: profile.approvedAt,
      } as ContextProfile)
    }
  }

  return true
}

const findRequiredTnCsDaf = (log: Logger) =>
  withLog(log, 'authMiddleware.findRequiredTnCsDaf', () =>
    db.query.userTncVersionTable.findMany({
      columns: {
        id: true,
      },
      // Latest TnCs effective from today or earlier
      where: and(isNull(userTncVersionTable.expiryDate), lte(userTncVersionTable.effectiveDate, today())),
    }),
  )

const whetherToCheckTnCs = (c: Context) => {
  const path = c.req.path
  const method = c.req.method

  // If path doesn't start with /api, i.e. SSR, don't check TnCs because they present public content
  if (!path.startsWith('/api')) return false

  // Don't check TnCs for endpoints that help users to accept TnCs
  if (
    (method === 'GET' && path === '/api/masters/countries') ||
    (method === 'POST' && path === '/api/users/signin') ||
    (method === 'POST' && path === '/api/users/signup') ||
    (method === 'GET' && path === '/api/users/tncs') ||
    (method === 'GET' && path === '/api/users/me') ||
    (method === 'PUT' && path === '/api/users/me')
  )
    return false

  // For all other cases, user must accept TnCs
  return true
}

const findProfileDaf = (log: Logger, userId: UUID, profileId: UUID) =>
  withLog(log, 'authMiddleware.findProfileDaf', () =>
    db.query.profileTable.findFirst({
      columns: {
        id: true,
        displayName: true,
        role: true,
        suspendedAt: true,
        approvedAt: true,
      },
      where: and(eq(profileTable.id, profileId), eq(profileTable.userId, userId)),
    }),
  )

const getUserLogBinding = (user: { id: UUID; email: Email }) => {
  return {
    user: {
      id: user.id,
      email: user.email,
    },
  }
}

const getProfileLogBinding = (profile: { id: UUID; role: Role }) => {
  return {
    profile: {
      id: profile.id,
      role: profile.role,
    },
  }
}

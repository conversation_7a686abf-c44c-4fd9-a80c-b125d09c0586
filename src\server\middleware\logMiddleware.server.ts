import { getConnInfo } from '@hono/node-server/conninfo'
import { Context } from 'hono'
import { createMiddleware } from 'hono/factory'
import pino from 'pino'

import { getLogger } from '@/shared/common/logger.shared'

import { env } from '../common/env.server'
// import { sleep } from '@/shared/common/common-utils.shared'

const getClientIp = (c: Context): string | undefined => {
  const doConnectingIp = c.req.header('do-connecting-ip')
  if (doConnectingIp) {
    return doConnectingIp
  }

  const xForwardedFor = c.req.header('x-forwarded-for')
  if (xForwardedFor) {
    // X-Forwarded-For can be a comma-separated list of IPs
    // The first IP is the original client IP
    return xForwardedFor.split(',')[0]?.trim()
  }

  const { remote } = getConnInfo(c)
  return remote?.address
}

const getReqLogBinding = (c: Context) => {
  return {
    req: {
      id: c.get('requestId'),
      path: c.req.path,
      method: c.req.method,
      query: c.req.query(),
      ip: ['development', 'test'].includes(env.APP_ENV) ? '127.0.0.1' : getClientIp(c),
      userAgent: c.req.header('User-Agent'),
    },
  }
}

export const logMiddleware = createMiddleware<{
  Variables: {
    log: pino.Logger
  }
}>(async (c, next) => {
  // await sleep(10000)
  const startedAt = Date.now()
  const log = getLogger().child(getReqLogBinding(c))
  log.info(`${c.req.method} ${c.req.path} started`)

  c.set('log', log)
  await next()

  const completedAt = Date.now()
  const ms = completedAt - startedAt
  log.info({ res: { status: c.res.status, ms } }, `${c.req.method} ${c.req.path} completed in ${ms} ms`)
})

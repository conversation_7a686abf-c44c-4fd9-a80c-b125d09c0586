import { UUID } from 'crypto'

import { and, desc, eq, isNotNull, sql } from 'drizzle-orm'
import pino, { Logger } from 'pino'

import { db } from '@/server/db/db'
import { batchTable, courseTable } from '@/server/db/schema'
import { isGoodProfile } from '@/server/profiles/common/profile-check.server'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { eventsQuery } from '../../common/batch-select-helper'
import { findBatchStaffByTeacherIdDaf } from '../../common/batch-teacher-staff-check'
import { BatchEvent } from '../../get/getBatch.server'

const findBatchesForTeacherDaf = (log: Logger, teacherId: UUID, goodStaff: boolean) => {
  return withLog(log, 'findBatchesForTeacherDaf', async () => {
    return db
      .select({
        id: batchTable.id,
        courseId: batchTable.courseId,
        startDate: batchTable.startDate,
        timezone: batchTable.timezone,
        fee: batchTable.fee,
        billingCycle: batchTable.billingCycle,
        cycleCount: batchTable.cycleCount,
        events: sql<BatchEvent[]>`(${eventsQuery})`,
        admissionOpen: batchTable.admissionOpen,
      })
      .from(batchTable)
      .innerJoin(courseTable, eq(courseTable.id, batchTable.courseId))
      .where(and(eq(batchTable.teacherId, teacherId), goodStaff ? undefined : isNotNull(courseTable.publishedAt)))
      .orderBy(desc(batchTable.startDate), desc(batchTable.createdAt))
  })
}

const isGoodStaff = async (log: pino.Logger, teacherId: UUID, currentProfileId: UUID | undefined) => {
  if (!currentProfileId) return false
  const staff = await findBatchStaffByTeacherIdDaf(log, teacherId, currentProfileId)
  return isGoodProfile(staff)
}

export const listTeacherBatches = async (log: Logger, teacherId: UUID, currentProfileId?: UUID) => {
  const goodStaff = await isGoodStaff(log, teacherId, currentProfileId)
  return findBatchesForTeacherDaf(log, teacherId, !!goodStaff)
}

export type ListTeacherBatches = typeof listTeacherBatches
export type TeacherBatch = Awaited<ReturnType<ListTeacherBatches>>[number]
setQuery('listTeacherBatches', listTeacherBatches)

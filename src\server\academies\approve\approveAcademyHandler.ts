import { UUID } from 'crypto'

import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'

import { approveAcademy } from './approveAcademy'

export const approveAcademyHandler = new Hono<HonoVars>().put('/', async (c) => {
  const academyId = c.req.param('id') as UUID

  await approveAcademy(getCtx(c), academyId)
  return c.body(null, 204)
})

import { UUID } from 'crypto'

import { and, desc, eq, lt } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { nextBetween } from '@/server/common/str-math/str-math.server'
import { db } from '@/server/db/db'
import { courseAttachmentTable } from '@/server/db/schema/course-schema'
import { withLog } from '@/shared/common/withLog.shared'
import { COURSE_ATTACHMENT_MIN_POSITION } from '@/shared/course/course-utils.shared'

const findCourseAttachmentDaf = (log: pino.Logger, attachmentId: UUID) =>
  withLog(log, 'findCourseAttachment', () =>
    db
      .select({ courseId: courseAttachmentTable.courseId, position: courseAttachmentTable.position })
      .from(courseAttachmentTable)
      .where(eq(courseAttachmentTable.id, attachmentId)),
  )

const findCourseAttachmentBeforePositionDaf = (log: pino.Logger, courseId: UUID, position: string) =>
  withLog(log, 'findCourseAttachmentBeforePosition', () =>
    db
      .select({ id: courseAttachmentTable.id, position: courseAttachmentTable.position })
      .from(courseAttachmentTable)
      .where(and(eq(courseAttachmentTable.courseId, courseId), lt(courseAttachmentTable.position, position)))
      .orderBy(desc(courseAttachmentTable.position))
      .limit(1),
  )

const updateCourseAttachmentDaf = (log: pino.Logger, attachmentId: UUID, newPosition: string) =>
  withLog(log, 'updateCourseAttachmentDaf', () =>
    db.update(courseAttachmentTable).set({ position: newPosition }).where(eq(courseAttachmentTable.id, attachmentId)),
  )

export const moveCourseAttachment = async (c: Ctx, attachmentId: UUID, beforePosition: string) => {
  const [attachment] = await findCourseAttachmentDaf(c.log, attachmentId)
  ensureExists(attachment, attachmentId, 'Course attachment')
  if (attachment.position === beforePosition) {
    // Move the attachment before itself!
    return
  }

  const [attachmentBefore] = await findCourseAttachmentBeforePositionDaf(c.log, attachment.courseId, beforePosition)
  if (attachmentBefore?.id === attachmentId) {
    // The attachment is already in the desired position!
    return
  }

  const afterPosition = attachmentBefore?.position ?? COURSE_ATTACHMENT_MIN_POSITION
  const newPosition = nextBetween(afterPosition, beforePosition)
  // Update the attachment with the new position
  await updateCourseAttachmentDaf(c.log, attachmentId, newPosition)
}

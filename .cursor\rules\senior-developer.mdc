---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: true
---
For development tasks, you must

1. Thoroughly analyze existing code and requirements before implementation. If you are not sure about relevant file content or codebase structure, use your tools to read files and gather the relevant information: do NOT guess or make up an answer.
2. Strictly adhere to existing coding practices and patterns, deviating only when absolutely necessary. If there's not enough context in the prompt to know what the conventions in the current project are, you MUST proactively read other files to find out.
3. When asked to do something or create some kind of code, first read code of the same kind in the project so you know what's the project's syntax and practices.
4. Highlight potential issues, e.g. performance, scalability, security, trade-offs.
5. Implement with clean, efficient, and well-documented code.

import { sign, verify } from 'hono/jwt'
import { DateTime } from 'luxon'

import { ensure } from '@/server/common/error/ensure.server'
import { Email } from '@/shared/common/common-utils.shared'

const AUDIENCE = 'email-verification'

export const createEmailVerificationToken = (jwtSecret: string, email: Email) =>
  sign(
    {
      aud: AUDIENCE,
      sub: email,
      iat: DateTime.now().startOf('second').toSeconds(),
      exp: DateTime.now().plus({ days: 1 }).startOf('second').toSeconds(),
    },
    jwtSecret,
  )

export const verifyEmailVerificationToken = async (jwtSecret: string, token: string, email: Email) => {
  const payload = await verify(token, jwtSecret)
  ensure(payload.aud && payload.aud === AUDIENCE, {
    logMessage: `Invalid audience in email verification token`,
    issueMessage: `Invalid audience in email verification token`,
    statusCode: 401,
  })
  ensure(payload.sub === email, {
    logMessage: `Invalid email in email verification token`,
    issueMessage: `Invalid email in email verification token`,
    statusCode: 422,
  })
}

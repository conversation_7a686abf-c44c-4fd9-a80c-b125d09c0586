import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { userTable } from '@/server/db/schema/user-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { nowTruncated } from '@/shared/common/date-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

export const invalidateTokens = async (c: Ctx) => {
  ensureUser(c.user, { ignoreEmailVerification: true, ignoreMobileVerification: true })
  await updatetokensValidFromDaf(c.log, c.user.id)
}

const updatetokensValidFromDaf = (log: Logger, userId: UUID) =>
  withLog(log, 'updatetokensValidFromDaf', () =>
    db
      .update(userTable)
      .set({ tokensValidFrom: nowTruncated(utc()) })
      .where(eq(userTable.id, userId)),
  )

import { UUID } from 'crypto'

import { ArrowDownIcon, ArrowUpIcon } from '@heroicons/react/20/solid'
import { AcademicCapIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { Config } from 'vike-react/Config'
import { Head } from 'vike-react/Head'

import { CourseBatch } from '@/server/batches/batches/list/course-batches/listCourseBatches.server'
import { CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { batchPath, CourseBatchesSearch } from '@/shared/batch/batch-utils.shared'
import { formatDate, timezoneOffset } from '@/shared/common/date-utils.shared'
import { coursePath } from '@/shared/course/course-utils.shared'
import { profilePath } from '@/shared/profiles/profile-utils.shared'
import { useAcademySuspenseQuery, useMyAcademyQuery } from '@ui/academies/common/academy-queries'
import { DividerLink } from '@ui/common/DividerLink'
import { ShowData } from '@ui/common/ShowData'
import { getUrl } from '@ui/common/utils/url-utils'
import { CourseHeader } from '@ui/courses/common/CourseHeader'
import { useCourseSuspenseQuery } from '@ui/courses/common/queries/course-queries'
import { useProfilesSuspenseQuery } from '@ui/profiles/common/profile-queries'

import { useCourseBatchesSuspenseQuery } from './common/batch-queries'
import { BatchEvents } from './common/BatchEvents'
import { RecommendBatchButton } from './common/RecommendBatchButton'

export const BatchesPage = ({ courseId, search }: { courseId: UUID; search: CourseBatchesSearch }) => {
  const { data: course, error: courseError } = useCourseSuspenseQuery(courseId)
  const { data: batchesData, error: batchesError } = useCourseBatchesSuspenseQuery(courseId, search)

  return (
    <ShowData error={courseError || batchesError} spinnerSize="1.25rem">
      <ViewBatches course={course!} batches={batchesData!} search={search} />
    </ShowData>
  )
}

const ViewBatches = ({
  course,
  batches,
  search,
}: {
  course: CourseWithoutDescr
  batches: CourseBatch[]
  search: CourseBatchesSearch
}) => {
  const { data: myAcademy } = useMyAcademyQuery()
  const isStaff = myAcademy?.id === course.academyId
  return (
    <>
      <Config title={`Batches for ${course.name}`} />
      <Head>
        <meta name="description" content={`Batches for ${course.name}`} />
      </Head>
      <div>
        <div className="sm:flex sm:items-start">
          <CourseHeader course={course} />
          {isStaff && (
            <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
              <a
                href={`${coursePath(course)}/batches/add`}
                className="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Add a Batch
              </a>
            </div>
          )}
        </div>
        <div className="mt-6">
          <BatchList course={course} batches={batches} search={search} isStaff={isStaff} />
        </div>
      </div>
    </>
  )
}

const BatchList = ({
  course,
  batches,
  search,
  isStaff,
}: {
  course: CourseWithoutDescr
  batches: CourseBatch[]
  search: CourseBatchesSearch
  isStaff: boolean
}) => {
  // Calculate previous and next page search parameters
  const prevSearch: CourseBatchesSearch = { ...search, previous: 'true' }
  const nextSearch: CourseBatchesSearch = { ...search, previous: undefined }
  if (batches.length > 0) {
    // Set cursor for previous page (first item)
    prevSearch.fromStartDate = batches[0].startDate ?? undefined
    prevSearch.fromCreatedAt = batches[0].createdAt.toISOString()
    prevSearch.beyondId = batches[0].id

    // Set cursor for next page (last item)
    nextSearch.fromStartDate = batches[batches.length - 1].startDate ?? undefined
    nextSearch.fromCreatedAt = batches[batches.length - 1].createdAt.toISOString()
    nextSearch.beyondId = batches[batches.length - 1].id
  } else {
    // No batches case
    prevSearch.fromStartDate = undefined
    prevSearch.fromCreatedAt = undefined
    prevSearch.beyondId = undefined
    nextSearch.fromStartDate = undefined
    nextSearch.fromCreatedAt = undefined
    nextSearch.beyondId = undefined
  }

  return (
    <>
      {/* Previous page link */}
      <DividerLink href={getUrl(`${coursePath(course)}/batches`, prevSearch)}>
        <ArrowUpIcon aria-hidden="true" className="-mr-0.5 -ml-1 size-5 text-gray-400" />
        Previous
      </DividerLink>

      {/* Batch list or empty state */}
      {batches.length > 0 ?
        <BatchesTable course={course} batches={batches} isStaff={isStaff} />
      : <div className="text-center py-12 my-6">
          <AcademicCapIcon className="mx-auto h-12 w-12 text-gray-400" aria-hidden="true" />
          <h3 className="mt-2 text-sm font-semibold text-gray-900">No batches on this page</h3>
          <p className="mt-1 text-sm text-gray-500">Try browsing &uarr; previous or &darr; next page.</p>
        </div>
      }

      {/* Next page link */}
      <DividerLink href={getUrl(`${coursePath(course)}/batches`, nextSearch)}>
        <ArrowDownIcon aria-hidden="true" className="-mr-0.5 -ml-1 size-5 text-gray-400" />
        Next
      </DividerLink>
    </>
  )
}

const BatchesTable = ({
  course,
  batches,
  isStaff,
}: {
  course: CourseWithoutDescr
  batches: CourseBatch[]
  isStaff: boolean
}) => {
  const { data: academy } = useAcademySuspenseQuery(course.academyId, false)

  return (
    <div className="space-y-6 my-6">
      {batches.map((batch) => (
        <div
          key={batch.id}
          className={clsx(
            'rounded-lg shadow overflow-hidden mb-1 border',
            batch.admissionOpen ? 'border-green-500' : 'border-gray-200',
            course.recommendedBatchId === batch.id ? 'border-green-500 bg-green-50' : '',
          )}
        >
          <div className="p-2 grid gap-2 grid-cols-1 sm:grid-cols-4">
            <div className="pb-4 sm:pb-0 sm:pr-4">
              <TeacherCell batch={batch} isStaff={isStaff} />
            </div>
            <a href={`${batchPath(course, batch.id)}`} className="pb-4 sm:pb-0 sm:px-4">
              <div className="text-sm text-gray-500 mb-1">From</div>
              <div className="font-medium text-gray-900">{formatDate(batch.startDate)}</div>
              <div className="text-xs text-gray-700">
                for {batch.cycleCount} {batch.billingCycle}
              </div>
            </a>
            <a href={`${batchPath(course, batch.id)}`} className="pb-4 sm:pb-0 sm:px-4">
              <div className="text-sm text-gray-500 mb-1">
                Schedule ({timezoneOffset(batch.startDate, batch.timezone)})
              </div>
              <div className="mt-1 text-gray-900">
                {batch.events.length > 0 ?
                  <BatchEvents
                    batchId={batch.id}
                    events={batch.events}
                    setEditingEvent={() => {}}
                    showActions={false}
                  />
                : 'No schedules fed yet'}
              </div>
            </a>
            <a href={`${batchPath(course, batch.id)}`} className="pb-4 sm:pb-0 sm:px-4">
              <div className="text-sm text-gray-500 mb-1">Pricing</div>
              <div className="text-gray-900">
                <span className="font-medium">Fee:</span> {academy?.currency} {batch.fee}
              </div>
              <div className="text-xs italic text-gray-500">
                {batch.admissionOpen ? 'admission open' : 'admission closed'}
              </div>
            </a>
          </div>
        </div>
      ))}
    </div>
  )
}

const TeacherCell = ({ batch, isStaff }: { batch: CourseBatch; isStaff: boolean }) => {
  const { data } = useProfilesSuspenseQuery({ profileId: batch.teacherId })
  const teacher = data?.[0]
  if (!teacher) return null
  return (
    <div className="flex justify-center items-center h-full">
      <div className="size-9 shrink-0">
        <img alt="Teacher picture" src={teacher.googlePictureUrl} className="size-9 rounded-full" />
      </div>
      <div className="ml-4">
        <div className="mt-1">
          <a href={profilePath(teacher)}>{teacher.displayName}</a>
        </div>
        <div className="text-xs text-gray-500">{batch.seatCount} seats</div>
        {batch.recommended && <div className="text-xs font-medium text-gray-900">Recommended</div>}
        {isStaff && !batch.recommended && <RecommendBatchButton batchId={batch.id} courseId={batch.courseId} />}
      </div>
    </div>
  )
}

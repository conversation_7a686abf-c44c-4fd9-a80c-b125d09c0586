import { usePageContext } from 'vike-react/usePageContext'

import { extractUuid } from '@/shared/common/common-utils.shared'

import { VerifyAcademyEmailPage } from './VerifyAcademyEmailPage'

export default () => {
  const pageContext = usePageContext()
  const id = extractUuid(pageContext.routeParams.id)
  const token = pageContext.urlParsed.search.token

  return <VerifyAcademyEmailPage academyId={id} token={token} />
}

import { usePageContext } from 'vike-react/usePageContext'

import { isPaymentDue } from '@ui/batches/common/batch-utils.ui'
import { useStudentBatchesQuery } from '@ui/courses/@id/batches/common/batch-student-queries'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

import { StudentPaymentDueWarning } from './StudentPaymentDueWarning'

export const useStudentPaymentDueWarning = () => {
  const pageContext = usePageContext()
  const { currentProfile } = useCurrentProfile()
  const { data: batches } = useStudentBatchesQuery(currentProfile?.role === 'student')

  if (
    pageContext.urlPathname.startsWith('/students/me/batches') ||
    pageContext.urlPathname.startsWith('/students/me/payments') ||
    pageContext.urlPathname.startsWith('/student-fee-payments')
  )
    return null
  if (!currentProfile || currentProfile.role !== 'student') return null
  if (!isPaymentDue(batches)) return null
  return StudentPaymentDueWarning
}

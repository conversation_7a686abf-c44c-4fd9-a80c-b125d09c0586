import { UUID } from 'crypto'

import { useEffect, useState } from 'react'

import { AcademyBrief } from '@/server/academies/get/getAcademy.server'
import { Batch } from '@/server/batches/batches/get/getBatch.server'
import {
  StudentFeePaymentDetail,
  StudentFeePaymentDetailItem,
} from '@/server/batches/batches/payments/get/getStudentFeePayment'
import { Profile } from '@/server/profiles/get/getProfile.server'
import { FullUserData } from '@/server/users/get-user/getUser.server'
import { academyPath } from '@/shared/academies/academy-utils.shared'
import {
  $PayStudentFeePaymentForm,
  batchPath,
  billingCycles,
  PayStudentFeePaymentForm,
} from '@/shared/batch/batch-utils.shared'
import { formatJsDate2Timestamp, formatDate, today } from '@/shared/common/date-utils.shared'
import { OmniError } from '@/shared/common/error-utils.shared'
import { profilePath } from '@/shared/profiles/profile-utils.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'
import { useAcademyWithContact } from '@ui/academies/common/useAcademyWithContact'
import { SubmitButton } from '@ui/common/form/page-layout'
import { showNotification } from '@ui/common/notification/notification-store'
import { PaymentStatusBadge } from '@ui/common/payment/PaymentStatusBadge'
import { ShowData } from '@ui/common/ShowData'
import { ShowErrors } from '@ui/common/ShowErrors'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'
import { useBatchSuspenseQuery } from '@ui/courses/@id/batches/common/batch-queries'
import {
  usePayStudentFeePaymentMutation,
  useReceiveStudentFeePaymentMutation,
  useRemoveStudentFeePaymentItemMutation,
  useStudentFeePaymentQuery,
} from '@ui/courses/@id/batches/common/student-fee-payment-queries'
import { useCourseSuspenseQuery } from '@ui/courses/common/queries/course-queries'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'
import { useProfileSuspenseQuery } from '@ui/profiles/common/profile-queries'
import { useUserQuery } from '@ui/users/common/user-queries'

export const StudentFeePaymentPage = ({ id }: { id: UUID }) => {
  const { data, isPending, error } = useStudentFeePaymentQuery(id)

  return (
    <ShowData error={error} isPending={isPending}>
      <StudentFeePayment payment={data!} />
    </ShowData>
  )
}

const StudentFeePayment = ({ payment }: { payment: StudentFeePaymentDetail }) => {
  const { currentProfile, profileIsPending, profileError } = useCurrentProfile()
  const { data: student, error: studentError } = useProfileSuspenseQuery(payment.studentId)
  const { academy, academyMobile, error: academyError } = useAcademyWithContact(payment.academyId, false)
  const { mutate, isPending } = useReceiveStudentFeePaymentMutation(payment.id)
  const isStudent = currentProfile?.id === payment.studentId

  return (
    <ShowData isPending={profileIsPending} error={profileError || studentError || academyError}>
      {student && academy && (
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-start justify-between">
            <div className="sm:flex-auto">
              <div className="flex items-center gap-2">
                <h1 className="text-base font-semibold text-gray-900">Payment</h1>
                <PaymentStatusBadge status={payment.status} />
              </div>
              <p className="mt-2 text-sm text-gray-700">
                From <a href={profilePath(student)}>{student.displayName}</a>
              </p>
              <p className="mt-1 text-sm text-gray-700">
                To <a href={academyPath(academy)}>{academy.name}</a>
              </p>
            </div>
            <div className="flex flex-col items-end text-right ml-4">
              <p className="text-sm text-gray-700">Created at {formatJsDate2Timestamp(payment.createdAt)}</p>
              {payment.paidAt && (
                <p className="text-sm text-gray-700">Paid at {formatJsDate2Timestamp(payment.paidAt)}</p>
              )}
              {payment.paidRemarks && <p className="mt-2 text-sm text-gray-700">{payment.paidRemarks}</p>}
            </div>
          </div>
          <div className="-mx-4 mt-8 flow-root sm:mx-0">
            <table className="min-w-full">
              <colgroup>
                <col className="w-full sm:w-1/2" />
                <col className="sm:w-1/6" />
              </colgroup>
              <thead className="border-b border-gray-300 text-gray-900">
                <tr>
                  <th scope="col" className="py-3.5 pr-3 pl-4 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                    Course
                  </th>
                  <th scope="col" className="py-3.5 pr-4 pl-3 text-right text-sm font-semibold text-gray-900 sm:pr-0">
                    {payment.currency}
                  </th>
                </tr>
              </thead>
              <tbody>
                {payment.items.map((item) => (
                  <tr key={item.id} className="border-b border-gray-200">
                    <PaymentItem payment={payment} item={item} />
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr>
                  <th scope="row" className="pt-6 pr-3 pl-4 text-left text-sm font-normal text-gray-500">
                    Subtotal
                  </th>
                  <td className="pt-6 pr-4 pl-3 text-right text-sm text-gray-500 sm:pr-0">{payment.cents / 100}</td>
                </tr>
                <tr>
                  <th scope="row" className="pt-4 pr-3 pl-4 text-left text-sm font-normal text-gray-500">
                    Tax
                  </th>
                  <td className="pt-4 pr-4 pl-3 text-right text-sm text-gray-500 sm:pr-0">0</td>
                </tr>
                <tr>
                  <th scope="row" className="pt-4 pr-3 pl-4 text-left text-sm font-semibold text-gray-900">
                    Total
                  </th>
                  <td className="pt-4 pr-4 pl-3 text-right text-sm font-semibold text-gray-900 sm:pr-0">
                    {payment.cents / 100}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
          {isStudent && ['draft', 'pending'].includes(payment.status) && (
            <PaymentGuide student={student} academy={academy} academyMobile={academyMobile} payment={payment} />
          )}
          {isStudent && payment.status === 'draft' && <PaymentForm payment={payment} />}
          {currentProfile && ACADEMY_STAFF.includes(currentProfile.role) && payment.status === 'pending' && (
            <div className="flex justify-end mt-4 mb-2">
              <button
                type="button"
                className="rounded-sm bg-indigo-600 px-2 py-1 text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                onClick={() => mutate()}
                disabled={isPending}
              >
                Receive payment
              </button>
            </div>
          )}
        </div>
      )}
    </ShowData>
  )
}

const PaymentItem = ({ payment, item }: { payment: StudentFeePaymentDetail; item: StudentFeePaymentDetailItem }) => {
  const { data: batch, error: batchError } = useBatchSuspenseQuery(item.batchId)

  if (batchError)
    return (
      <td colSpan={2} className="text-error">
        {getErrorMessage(batchError)}
      </td>
    )

  return batch && <PaymentItemForBatch payment={payment} item={item} batch={batch} />
}

const PaymentItemForBatch = ({
  payment,
  item,
  batch,
}: {
  payment: StudentFeePaymentDetail
  item: StudentFeePaymentDetailItem
  batch: Batch
}) => {
  const { data: course, error: courseError } = useCourseSuspenseQuery(batch.courseId)
  const { mutate: removeItem, isPending } = useRemoveStudentFeePaymentItemMutation(item.id)
  const { currentProfile } = useCurrentProfile()

  if (courseError)
    return (
      <td colSpan={2} className="text-error">
        {getErrorMessage(courseError)}
      </td>
    )

  return (
    course && (
      <>
        <td className="py-5 pr-3 pl-4 text-sm sm:pl-0">
          <div className="font-medium text-gray-900">
            <a href={batchPath(course, item.batchId)}>{course.name}</a>
          </div>
          <div className="truncate text-gray-500 text-xs">
            {batch.startDate < today() ? 'Started' : 'Starting'} on {formatDate(batch.startDate)}
          </div>
          <div className="truncate text-gray-500">
            Fee for {billingCycles[batch.billingCycle].singular} {item.cycle}
          </div>
          {currentProfile?.role === 'student' && payment.status === 'draft' && (
            <button type="button" className="text-xs link mt-1" disabled={isPending} onClick={() => removeItem()}>
              Remove this item
            </button>
          )}
        </td>
        <td className="py-5 pr-4 pl-3 text-right text-sm text-gray-500 sm:pr-0 align-top">{item.fee}</td>
      </>
    )
  )
}

const PaymentGuide = ({
  student,
  academy,
  academyMobile,
  payment,
}: {
  student: Profile
  academy: AcademyBrief
  academyMobile: string
  payment: StudentFeePaymentDetail
}) => {
  const { data, isPending: userIsPending, error: userError } = useUserQuery(true)
  const user = data as FullUserData

  // Generate UPI deep link
  const generateUpiLink = () => {
    // Format: upi://pay?pa=UPI_ID&pn=PAYEE_NAME&am=AMOUNT&cu=CURRENCY&tn=TRANSACTION_NOTE
    const amount = (payment.cents / 100).toString()
    const note = `${payment.id} ${student.displayName}`

    return `upi://pay?pa=${academy.upiId}&pn=${encodeURIComponent(academy.name)}&am=${amount}&cu=INR&tn=${encodeURIComponent(note)}`
  }

  // Handle QR code click
  const handleQrCodeClick = () => {
    window.location.href = generateUpiLink()
  }

  return (
    <ShowData isPending={userIsPending} error={userError}>
      {user && (
        <div className="bg-gray-100 rounded-lg p-6 my-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Instructions</h3>

          <div className="grid sm:grid-cols-2 gap-6">
            {/* QR Code Section */}
            <div className="bg-white p-4 rounded-lg shadow-sm flex flex-col items-center">
              <div className="text-sm font-medium text-gray-900 mb-3">Scan to pay via any UPI app</div>
              <div className="relative cursor-pointer" onClick={handleQrCodeClick} title="Click to open UPI app">
                <img
                  src="/images/priya-scanner.jpg"
                  alt="QR Code for payment"
                  className="w-48 h-48 object-contain mb-3"
                />
                <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200 bg-black bg-opacity-10 rounded-lg">
                  <div className="bg-indigo-600 text-white text-xs py-1 px-2 rounded">Click to open UPI app</div>
                </div>
              </div>
              <div className="text-sm text-gray-600 mt-1 mb-3">
                Or send to: <span className="font-medium text-indigo-600">{academy.upiId}</span>
              </div>
              <button
                type="button"
                onClick={handleQrCodeClick}
                className="w-full bg-indigo-600 text-white py-2 px-4 rounded hover:bg-indigo-700 transition flex items-center justify-center gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
                Pay with UPI
              </button>
            </div>

            {/* Payment Details Section */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <p className="text-sm font-medium text-gray-900 mb-3">After payment, send the following details:</p>
              <ul className="text-sm space-y-3 text-gray-700">
                <li className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-gray-400 mr-2 flex-shrink-0 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                  <span>
                    Name: <span className="font-medium">{student.displayName}</span>
                  </span>
                </li>
                <li className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-gray-400 mr-2 flex-shrink-0 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  <span>
                    Email: <span className="font-medium">{user.email}</span>
                  </span>
                </li>
                <li className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-gray-400 mr-2 flex-shrink-0 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                  <span>Attach screenshot of payment</span>
                </li>
              </ul>

              <div className="mt-4 pt-3 border-t border-gray-200">
                <p className="text-sm font-medium text-gray-900 mb-2">Send details to:</p>
                <div className="flex items-center mb-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 448 512"
                    className="h-4 w-4 text-green-500 mr-2 flex-shrink-0"
                    fill="currentColor"
                  >
                    <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z" />
                  </svg>
                  <a
                    href={`https://wa.me/${academyMobile.replace(/\+/g, '').replace(/[-\s]/g, '')}`}
                    className="text-sm text-indigo-600 font-medium hover:underline"
                  >
                    {academyMobile}
                  </a>
                </div>
                <div className="flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                  <a href={`mailto:${academy.email}`} className="text-sm text-indigo-600 font-medium hover:underline">
                    {academy.email}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </ShowData>
  )
}

const PaymentForm = ({ payment }: { payment: StudentFeePaymentDetail }) => {
  const { mutate: pay, isPending } = usePayStudentFeePaymentMutation(payment.id)
  const [formData, setFormData] = useState<PayStudentFeePaymentForm>({
    paidRemarks: payment.paidRemarks ?? '',
  })
  const [omniError, setOmniError] = useState<OmniError | undefined>()

  // Validate form data when it changes
  useEffect(() => {
    const parsed = $PayStudentFeePaymentForm.safeParse(formData)
    setOmniError(parsed.error)
  }, [formData])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    pay(formData, {
      onSuccess: () => {
        showNotification({
          heading: 'Payment done',
          type: 'success',
        })
      },
    })
  }

  return (
    <form onSubmit={handleSubmit}>
      <div className="mt-6">
        <label htmlFor="paid-remarks" className="form-descr">
          Any other details you'd like to share
        </label>
        <input
          id="paid-remarks"
          name="paid-remarks"
          type="text"
          className="form-input mt-1"
          value={formData.paidRemarks}
          placeholder="Ref number etc. (optional)"
          onChange={(e) => setFormData({ ...formData, paidRemarks: e.target.value })}
        />
        <ShowErrors error={omniError} path={['paidRemarks']} />
      </div>
      <div className="mt-6">
        <SubmitButton hasError={!!omniError} isSubmitting={isPending} label="Payment Done" />
      </div>
    </form>
  )
}

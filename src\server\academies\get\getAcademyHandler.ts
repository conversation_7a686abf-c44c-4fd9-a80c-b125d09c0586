import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'
import { z } from 'zod'

import { ensureProfile } from '@/server/common/error/ensure-profile.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { getAcademy } from './getAcademy.server'

const $Query = z.object({
  includeDetail: z.string().optional(),
})

export const getAcademyHandler = new Hono<HonoVars>().get(
  '/',
  zValidator('param', $HasId),
  zValidator('query', $Query),
  async (c) => {
    const profile = c.get('profile')
    if (profile) {
      // If logged in, must be good user and profile
      ensureUser(c.get('user'))
      ensureProfile(c.get('profile'))
    }
    const { id: academyId } = c.req.valid('param')
    const { includeDetail } = c.req.valid('query')
    const academy = await getAcademy(c.get('log'), academyId, includeDetail === 'true', profile)
    return c.json(academy, 200)
  },
)

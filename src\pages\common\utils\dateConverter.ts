/**
 * Convert an ISO date string to a Date object
 * @param value - The ISO date string to convert
 * @returns Date object or null if invalid/null input
 */
const strToDate = (value: string | null | undefined): Date | null | undefined => {
  if (!value) return value as undefined | null
  const date = new Date(value)
  return isNaN(date.getTime()) ? null : date
}

/**
 * Convert a date string or an array of date strings to Date objects
 * @param value - The date string, array of strings, or any other value
 * @returns Date object, array of Dates, or the original value if not convertible
 * @example
 * toDates("2024-03-20") // => Date object
 * toDates(["2024-03-20", "2024-03-21"]) // => [Date, Date]
 * toDates(null) // => null
 */
const toDates = (value: unknown): Date | Date[] | unknown => {
  if (typeof value === 'string') return strToDate(value)
  if (Array.isArray(value)) return value.map(strToDate)
  return value
}

/**
 * Convert all specified date fields in an object to Date objects
 * Recursively processes nested objects and arrays
 * @param something - The object or array to process
 * @param fieldsToConvert - Array of field names to convert to Date objects
 * @example
 * convertDates({ publishedAt: "2024-03-20", nested: { date: "2024-03-21" } }, ["publishedAt", "date"])
 */
const convertDates = <T>(something: T, fieldsToConvert: string[]) => {
  if (!something) return
  if (Array.isArray(something)) {
    for (const item of something) {
      convertDates(item, fieldsToConvert)
    }
  } else if (typeof something === 'object') {
    const obj = something as Record<string, unknown>
    for (const key in obj) {
      const value = obj[key]
      if (fieldsToConvert.includes(key)) {
        obj[key] = toDates(value)
      } else {
        convertDates(value, fieldsToConvert)
      }
    }
  }
}

/**
 * @Deprecated('This is not type-safe.Use getDatesConverted or convertDates instead')
 *
 * Creates a converter function that transforms ISO date strings to Date objects
 * Use this to process API responses that contain date strings
 * @param fieldsToConvert - Array of field names that should be converted to Date objects
 * @returns A function that takes any object/array and converts specified fields to Dates
 * @example
 * const converter = dateConverter(["publishedAt", "createdAt"])
 * const processed = converter(apiResponse)
 *
 */

export const dateConverter =
  <T>(fieldsToConvert: string[]) =>
  (something: unknown): T => {
    convertDates(something, fieldsToConvert)
    return something as T
  }

import React from 'react'

type SpinnerProps = {
  width?: string
  height?: string
  size?: string
  border?: string
}

/**
 * A loading spinner component that can be customized with different sizes and dimensions.
 *
 * @example
 * // Default size
 * <Spinner />
 *
 * // Larger spinner
 * <Spinner size="2rem" />
 *
 * // Centered in a specific area
 * <Spinner width="100%" height="200px" />
 */
export const Spinner = ({
  width = 'auto',
  height = 'auto',
  size = '1.25rem', // equivalent to h-5/w-5
  border = '3px',
}: SpinnerProps) => (
  <div className="flex items-center justify-center" style={{ width, height }}>
    <div
      className="animate-spin rounded-full border-gray-300 border-t-indigo-600"
      style={{
        width: size,
        height: size,
        borderWidth: border,
      }}
    />
    <span className="sr-only">Loading...</span>
  </div>
)

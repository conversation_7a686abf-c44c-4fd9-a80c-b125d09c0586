import { UUID } from 'crypto'

import { useMemo } from 'react'
import { ArrowDownIcon, ArrowUpIcon } from '@heroicons/react/20/solid'

import { useProfileSuspenseQuery } from '@/pages/profiles/common/profile-queries'
import { StudentFeePaymentItemOfBatch } from '@/server/batches/batches/payments/items/list-for-batch/listStudentFeePaymentItemsOfBatch'
import { BatchPaymentsSearch, billingCycles } from '@/shared/batch/batch-utils.shared'
import { formatJsDate2Timestamp } from '@/shared/common/date-utils.shared'
import { profilePath } from '@/shared/profiles/profile-utils.shared'
import { DividerLink } from '@ui/common/DividerLink'
import { PaymentStatusBadge } from '@ui/common/payment/PaymentStatusBadge'
import { ShowData } from '@ui/common/ShowData'
import { getUrl } from '@ui/common/utils/url-utils'
import { useCourseSuspenseQuery } from '@ui/courses/common/queries/course-queries'

import { useBatchSuspenseQuery } from '../../common/batch-queries'
import { BatchHeader } from '../../common/BatchHeader'
import { useStudentFeePaymentsOfBatchQuery } from '../../common/student-fee-payment-queries'

export const BatchPaymentsPage = ({ courseId, batchId, search }: { courseId: UUID; batchId: UUID; search: BatchPaymentsSearch }) => {
  const { data: course, error: courseError } = useCourseSuspenseQuery(courseId)
  const { data: batch, error: batchError } = useBatchSuspenseQuery(batchId)
  const { data: studentFeePayments, isPending, error } = useStudentFeePaymentsOfBatchQuery(batchId, search)

  // Group payments by studentId
  const groupedPayments = useMemo(() => {
    if (!studentFeePayments) return []

    const grouped: Record<UUID, StudentFeePaymentItemOfBatch[]> = {}
    studentFeePayments.forEach((payment) => {
      if (!grouped[payment.studentId]) {
        grouped[payment.studentId] = []
      }
      grouped[payment.studentId].push(payment)
    })

    return Object.entries(grouped).map(([studentId, payments]) => ({
      studentId,
      payments,
    }))
  }, [studentFeePayments])

  return (
    <ShowData isPending={isPending} error={error || courseError || batchError} spinnerSize="1.25rem">
      <div className="px-4 sm:px-6 lg:px-8">
        {course && batch && <BatchHeader course={course} batch={batch} h1="Payments for" />}
        <PaymentsList payments={studentFeePayments || []} search={search} courseId={courseId} batchId={batchId} />
      </div>
    </ShowData>
  )
}

const PaymentsList = ({ payments, search, courseId, batchId }: {
  payments: StudentFeePaymentItemOfBatch[];
  search: BatchPaymentsSearch;
  courseId: UUID;
  batchId: UUID
}) => {
  // Group payments by studentId
  const groupedPayments = useMemo(() => {
    if (!payments) return []

    const grouped: Record<UUID, StudentFeePaymentItemOfBatch[]> = {}
    payments.forEach((payment) => {
      if (!grouped[payment.studentId]) {
        grouped[payment.studentId] = []
      }
      grouped[payment.studentId].push(payment)
    })

    return Object.entries(grouped).map(([studentId, payments]) => ({
      studentId,
      payments,
    }))
  }, [payments])

  // Calculate previous and next page search parameters
  const prevSearch: BatchPaymentsSearch = { ...search, previous: 'true' }
  const nextSearch: BatchPaymentsSearch = { ...search, previous: undefined }

  if (payments.length > 0) {
    // Set cursor for previous page (first item)
    prevSearch.fromStudentId = payments[0].studentId
    prevSearch.fromCycle = payments[0].cycle
    prevSearch.beyondStudentId = payments[0].studentId
    prevSearch.beyondCycle = payments[0].cycle

    // Set cursor for next page (last item)
    const lastPayment = payments[payments.length - 1]
    nextSearch.fromStudentId = lastPayment.studentId
    nextSearch.fromCycle = lastPayment.cycle
    nextSearch.beyondStudentId = lastPayment.studentId
    nextSearch.beyondCycle = lastPayment.cycle
  } else {
    // No payments case
    prevSearch.fromStudentId = undefined
    prevSearch.fromCycle = undefined
    prevSearch.beyondStudentId = undefined
    prevSearch.beyondCycle = undefined
    nextSearch.fromStudentId = undefined
    nextSearch.fromCycle = undefined
    nextSearch.beyondStudentId = undefined
    nextSearch.beyondCycle = undefined
  }

  const basePath = `/courses/${courseId}/batches/${batchId}/payments`

  return (
    <>
      {/* Previous page link */}
      <DividerLink href={getUrl(basePath, prevSearch)}>
        <ArrowUpIcon aria-hidden="true" className="-mr-0.5 -ml-1 size-5 text-gray-400" />
        Previous
      </DividerLink>

      {/* Payments list or empty state */}
      {payments.length > 0 ?
        <div className="mt-8 flow-root">
          <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
              {groupedPayments.map(({ studentId, payments }) => (
                <StudentPaymentGroup key={studentId} studentId={studentId as UUID} payments={payments} />
              ))}
            </div>
          </div>
        </div>
      : <div className="text-center py-12 my-6">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-semibold text-gray-900">No payments on this page</h3>
          <p className="mt-1 text-sm text-gray-500">Try browsing ↑ previous or ↓ next page.</p>
        </div>
      }

      {/* Next page link */}
      <DividerLink href={getUrl(basePath, nextSearch)}>
        <ArrowDownIcon aria-hidden="true" className="-mr-0.5 -ml-1 size-5 text-gray-400" />
        Next
      </DividerLink>
    </>
  )
}

type StudentPaymentGroupProps = {
  studentId: UUID
  payments: StudentFeePaymentItemOfBatch[]
}

const StudentPaymentGroup = ({ studentId, payments }: StudentPaymentGroupProps) => {
  const { data: profile } = useProfileSuspenseQuery(studentId)

  return (
    <div className="mb-8">
      <div className="border-b border-gray-200 pb-2 mb-4">
        <h2 className="text-lg font-semibold text-gray-900">
          {profile && (
            <a href={profilePath(profile)} className="hover:text-indigo-600">
              {profile.displayName}
            </a>
          )}
          <span className="ml-1 text-sm text-gray-500">
            ({payments.length} payment{payments.length !== 1 ? 's' : ''})
          </span>
        </h2>
      </div>

      <table className="min-w-full divide-y divide-gray-300">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="py-3.5 pr-3 pl-4 text-left text-sm font-semibold text-gray-900 sm:pl-0">
              Billing Cycle
            </th>
            <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
              Fee
            </th>
            <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
              Paid At
            </th>
            <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
              Status
            </th>
            <th scope="col" className="relative py-3.5 pr-4 pl-3 sm:pr-0">
              <span className="sr-only">Details</span>
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white">
          {payments.map((payment) => (
            <tr key={`${payment.studentId}-${payment.cycle}`}>
              <td className="py-4 pr-3 pl-4 text-sm whitespace-nowrap sm:pl-0">
                <div className="font-medium text-gray-900">
                  {billingCycles[payment.billingCycle].singular} {payment.cycle}
                </div>
              </td>
              <td className="px-3 py-4 text-sm whitespace-nowrap text-gray-500">
                <div className="text-gray-900">
                  {payment.currency} {payment.fee}
                </div>
              </td>
              <td className="px-3 py-4 text-sm whitespace-nowrap text-gray-500">
                {payment.paidAt ?
                  formatJsDate2Timestamp(payment.paidAt)
                : <span className="text-yellow-500">Not paid yet</span>}
              </td>
              <td className="px-3 py-4 text-sm whitespace-nowrap text-gray-500">
                <PaymentStatusBadge status={payment.status} />
              </td>
              <td className="relative py-4 pr-4 pl-3 text-right text-sm font-medium whitespace-nowrap sm:pr-0">
                <a
                  href={`/student-fee-payments/${payment.studentFeePaymentId}`}
                  className="text-indigo-600 hover:text-indigo-900"
                >
                  View<span className="sr-only">, payment for cycle {payment.cycle}</span>
                </a>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

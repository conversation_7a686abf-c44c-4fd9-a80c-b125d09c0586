import { UUID } from 'crypto'

import { and, eq, gt, isNotNull, isNull, lt, or, sql } from 'drizzle-orm'
import pino, { Logger } from 'pino'

import { findAcademyStaffDaf } from '@/server/academies/common/academy-staff-check'
import { db } from '@/server/db/db'
import { academyTable, batchRecommendationTable, batchTable, courseTable } from '@/server/db/schema'
import { isGoodProfile } from '@/server/profiles/common/profile-check.server'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { CoursesSearch } from '@/shared/course/course-utils.shared'

import { courseItemColumns } from '../common/course-select-helper'

const DEFAULT_COURSES_PAGE_SIZE = 30

const pageFilter = (search: CoursesSearch) => {
  const firstOrLastPage = !search.beyondId // if no beyondId and `previous` is true, its the last page
  if (firstOrLastPage) return undefined
  if (search.previous === 'true') {
    // Backward pagination - get rows that come before the current cursor
    // search.fromStartDate will have been set by frontend as the batch.startDate of the first row
    // It could be null when there are no batches of a course
    // When it's not null, we need to get rows with
    //   1. batch.startDate is not null, and
    //   2. either batch.startDate = search.fromStartDate and course id < beyondId, or batch.startDate > search.fromStartDate
    // When it's null, we need to get rows with either batch.startDate null and course id < beyondId, or batch.startDate not null
    if (search.fromStartDate) {
      return and(
        isNotNull(batchTable.startDate),
        or(
          and(eq(batchTable.startDate, search.fromStartDate), lt(courseTable.id, search.beyondId!)),
          gt(batchTable.startDate, search.fromStartDate),
        ),
      )
    } else {
      return or(
        and(isNull(batchTable.startDate), lt(courseTable.id, search.beyondId!)),
        isNotNull(batchTable.startDate),
      )
    }
  }
  // Forward pagination - get rows that come after the current cursor
  // search.fromStartDate will have been set by frontend as the batch.startDate of the last row
  // It could be null when there are no batches of a course
  // When it's not null: get rows with either of
  //   1. batch.startDate is not null and
  //      1.1. batch.startDate = search.fromStartDate and course id > beyondId, or
  //      1.2. batch.startDate < search.fromStartDate
  //  2. batch.startDate is null
  // When it's null: get rows with batch.startDate null and course id > beyondId
  if (search.fromStartDate) {
    return or(
      and(
        isNotNull(batchTable.startDate),
        or(
          and(eq(batchTable.startDate, search.fromStartDate), gt(courseTable.id, search.beyondId!)),
          lt(batchTable.startDate, search.fromStartDate),
        ),
      ),
      isNull(batchTable.startDate),
    )
  } else {
    return and(isNull(batchTable.startDate), gt(courseTable.id, search.beyondId!))
  }
}

const findCoursesDaf = (log: Logger, search: CoursesSearch, goodStaff: boolean) => {
  const orderBy =
    search.previous === 'true' ?
      sql`${batchTable.startDate} nulls first, ${courseTable.id} desc`
    : sql`${batchTable.startDate} desc nulls last, ${courseTable.id}`

  const publishedFilter =
    search.includeUnpublished === 'true' && search.academyId && goodStaff ?
      undefined
    : isNotNull(courseTable.publishedAt)
  const academyFilter = search.academyId ? eq(courseTable.academyId, search.academyId) : undefined

  return withLog(log, 'findCoursesDaf', async () =>
    db
      .select({ ...courseItemColumns, startDate: batchTable.startDate })
      .from(courseTable)
      .innerJoin(academyTable, eq(academyTable.id, courseTable.academyId))
      .leftJoin(batchRecommendationTable, eq(batchRecommendationTable.courseId, courseTable.id))
      .leftJoin(batchTable, eq(batchTable.id, batchRecommendationTable.batchId))
      .where(and(publishedFilter, academyFilter, pageFilter(search)))
      .orderBy(orderBy) // https://github.com/drizzle-team/drizzle-orm/issues/1699
      .limit(search.pageSize ? parseInt(search.pageSize) : DEFAULT_COURSES_PAGE_SIZE),
  )
}

const isGoodAcademyStaff = async (log: pino.Logger, academyId: UUID | undefined, profileId: UUID | undefined) => {
  if (!academyId) return false
  if (!profileId) return false
  const staff = await findAcademyStaffDaf(log, academyId, profileId)
  return isGoodProfile(staff)
}

export const listCourses = async (log: Logger, search: CoursesSearch, profileId?: UUID) => {
  const goodStaff = await isGoodAcademyStaff(log, search.academyId, profileId)

  const courses = await findCoursesDaf(log, search, !!goodStaff)
  if (search.previous === 'true') courses.reverse()
  return courses
}
export type ListCourses = typeof listCourses
export type CourseItem = Awaited<ReturnType<ListCourses>>[0]

setQuery('listCourses', listCourses)

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { env } from '@/server/common/env.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { verifyEmailVerificationToken } from '@/server/common/mail/email-verification-token-utils.server'
import { db } from '@/server/db/db'
import { academyTable } from '@/server/db/schema/academy-schema'
import { withLog } from '@/shared/common/withLog.shared'

import { ensureGoodStaff } from '../common/ensureGoodStaff'

const findAcademyDaf = (log: Logger, academyId: UUID) =>
  withLog(log, 'findAcademyDaf', () =>
    db.query.academyTable.findFirst({
      columns: {
        email: true,
        emailVerified: true,
      },
      where: eq(academyTable.id, academyId),
    }),
  )

const setAcademyEmailVerified = (log: Logger, academyId: UUID) =>
  withLog(log, 'setAcademyEmailVerified', () =>
    db.update(academyTable).set({ emailVerified: true }).where(eq(academyTable.id, academyId)),
  )

export const verifyAcademyEmail = async (c: Ctx, academyId: UUID, token: string) => {
  await ensureGoodStaff(c, academyId)

  const academy = await findAcademyDaf(c.log, academyId)
  ensureExists(academy, academyId, 'Academy', 422)

  if (academy.emailVerified) return

  await verifyEmailVerificationToken(env.JWT_SECRET_KEY, token, academy.email)
  await setAcademyEmailVerified(c.log, academyId)
}

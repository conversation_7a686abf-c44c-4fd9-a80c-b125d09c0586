import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { listBatchAttachments } from './listBatchAttachments'

export const listBatchAttachmentsHandler = new Hono<HonoVars>().get('/', zValidator('param', $HasId), async (c) => {
  const { id } = c.req.valid('param')
  const batchAttachments = await listBatchAttachments(c.get('log'), id, c.get('profile'))
  return c.json({ rows: batchAttachments }, 200)
})

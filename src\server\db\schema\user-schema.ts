import { relations, sql } from 'drizzle-orm'
import { boolean, check, date, index, jsonb, pgTable, smallint, text, uniqueIndex } from 'drizzle-orm/pg-core'

import { TnCReminderType } from '@/shared/users/user-utils.shared'

import { auditColumns, suspensionCheck, suspensionColumns, timestampz, uid } from './helpers'
import { countryTable } from './master-schema'

/**
 * User table stores the core user information including:
 * - Google OAuth data
 * - Personal information
 * - Contact information
 */
export const userTable = pgTable(
  'usr',
  {
    id: uid().primaryKey(),
    googleId: text().unique().notNull(),
    googleRefreshToken: text(), // unused currently, after ditching google event creation
    name: text().notNull(),
    email: text().notNull(),
    emailVerified: boolean().notNull(),
    googlePictureUrl: text().notNull(),
    language: text().notNull(),
    tokensValidFrom: timestampz().notNull(),

    // Personal Information
    mobileCountryCode: text()
      .notNull()
      .references(() => countryTable.code),
    mobile: text().notNull(),
    mobileVerified: boolean().notNull().default(false),
    legalAgeDeclaredAt: timestampz().notNull().defaultNow(),
    informationAccuracyDeclaredAt: timestampz().notNull().defaultNow(),

    tncReminderMailedAt: timestampz(), // when was the last reminder mailed. null means not mailed yet
    tncReminderType: text().$type<TnCReminderType>(), // what type of reminder was it? null means not mailed yet

    ...auditColumns,
    ...suspensionColumns,
  },
  (t) => [
    check('name_len', sql`char_length(${t.name}) <= 255`),
    check('email_len', sql`char_length(${t.email}) <= 255`),
    check('google_picture_url_len', sql`char_length(${t.googlePictureUrl}) <= 2000`),
    check('language_len', sql`char_length(${t.language}) <= 100`),
    check('mobile_len', sql`char_length(${t.mobile}) <= 20`),
    ...suspensionCheck(t),
    index('idx_user_mobile_country_code').on(t.mobileCountryCode),
    // Index optimized for TnC reminder scanning
    // - tncReminderType first as it's the primary filter
    // - id for pagination and uniqueness
    index('idx_user_tnc_reminder_scan').on(t.tncReminderType, t.id),
    index('idx_user_google_refresh_token').on(t.googleRefreshToken),
  ],
)

export const userTableRelations = relations(userTable, ({ one, many }) => ({
  mobileCountry: one(countryTable, {
    fields: [userTable.mobileCountryCode],
    references: [countryTable.code],
  }),
  tncSigns: many(userTncSignTable),
  tncAcceptances: many(userTncAcceptedTable),
}))

export const userSnapshotTable = pgTable(
  'user_snapshot',
  {
    id: uid().primaryKey(),
    userId: uid()
      .notNull()
      .references(() => userTable.id, { onDelete: 'cascade' }),
    snapshotAt: timestampz().notNull().defaultNow(),
    data: jsonb('data').notNull(),
  },
  (t) => [index('idx_user_snapshot_user_id').on(t.userId)],
)

export const userTncTable = pgTable('user_tnc', {
  id: uid().primaryKey(),
  name: text().notNull(),
  baseUrl: text().notNull().default('/legal-agreements/terms-of-service'),
  description: text().notNull(),
})

/**
 * When adding a new new version,
 * 1. Set the expiry date of the previous version to one day before the new version's effective date.
 * 2. Set the tncReminderMailedAt and tncReminderTypeof all users to null
 */
export const userTncVersionTable = pgTable(
  'user_tnc_version',
  {
    id: uid().primaryKey(),
    tncId: uid()
      .notNull()
      .references(() => userTncTable.id),
    version: smallint().notNull().default(1),
    effectiveDate: date().notNull().defaultNow(), // Effective from 00 hours of the day in UTC
    expiryDate: date(), // Effective until 00 hours of next day in UTC. null means latest
  },
  (t) => [
    index('user_tnc_version_tnc_id_idx').on(t.tncId),
    // Partial index for active TnC versions with effective date for MIN aggregation
    index('idx_user_tnc_version_active')
      .on(t.id, t.effectiveDate)
      .where(sql`${t.expiryDate} IS NULL`),
  ],
)

/**
 * Stores TNC versions that a user has signed. All signatures are logged here.
 */
export const userTncSignTable = pgTable(
  'user_tnc_sign',
  {
    id: uid().primaryKey(),
    userId: uid()
      .notNull()
      .references(() => userTable.id, { onDelete: 'cascade' }),
    tncVersionId: uid()
      .notNull()
      .references(() => userTncVersionTable.id),
    signedAt: timestampz().notNull().defaultNow(),
    accepted: boolean().notNull().default(true),
    ...auditColumns,
  },
  (t) => [
    index('idx_user_tnc_sign_user_id').on(t.userId),
    index('idx_user_tnc_sign_tnc_version_id').on(t.tncVersionId),
  ],
)

/**
 * Stores TNC versions that a user has ultimtely accepted as of now
 */
export const userTncAcceptedTable = pgTable(
  'user_tnc_accepted',
  {
    id: uid().primaryKey(),
    userId: uid()
      .notNull()
      .references(() => userTable.id, { onDelete: 'cascade' }),
    tncVersionId: uid()
      .notNull()
      .references(() => userTncVersionTable.id),
    ...auditColumns,
  },
  (t) => [
    index('idx_user_tnc_accepted_user_id').on(t.userId),
    index('idx_user_tnc_accepted_tnc_version_id').on(t.tncVersionId),
    uniqueIndex('idx_user_tnc_accepted_user_tnc_version').on(t.userId, t.tncVersionId),
  ],
)

export const userTncAcceptedRelations = relations(userTncAcceptedTable, ({ one }) => ({
  user: one(userTable, {
    fields: [userTncAcceptedTable.userId],
    references: [userTable.id],
  }),
  version: one(userTncVersionTable, {
    fields: [userTncAcceptedTable.tncVersionId],
    references: [userTncVersionTable.id],
  }),
}))

export const userTncRelations = relations(userTncTable, ({ many }) => ({
  versions: many(userTncVersionTable),
}))

export const userTncVersionRelations = relations(userTncVersionTable, ({ one, many }) => ({
  tnc: one(userTncTable, {
    fields: [userTncVersionTable.tncId],
    references: [userTncTable.id],
  }),
  signs: many(userTncSignTable),
  acceptances: many(userTncAcceptedTable),
}))

export const userTncSignRelations = relations(userTncSignTable, ({ one }) => ({
  user: one(userTable, {
    fields: [userTncSignTable.userId],
    references: [userTable.id],
  }),
  version: one(userTncVersionTable, {
    fields: [userTncSignTable.tncVersionId],
    references: [userTncVersionTable.id],
  }),
}))

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { HonoVars } from '@/server/common/hono-utils.server'
import { $AcademySearch } from '@/shared/academies/academy-utils.shared'

import { listAcademies } from './listAcademies'

export const listAcademiesHandler = new Hono<HonoVars>().get('/', zValidator('query', $AcademySearch), async (c) => {
  const search = c.req.valid('query')
  const academies = await listAcademies(c.get('log'), search)
  return c.json({ rows: academies }, 200)
})

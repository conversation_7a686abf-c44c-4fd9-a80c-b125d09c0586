import { usePageContext } from 'vike-react/usePageContext'

import { WithProfile } from '@/pages/profiles/common/WithProfile'
import { $StudentFeePaymentsSearch } from '@/shared/batch/batch-utils.shared'

import { StudentFeePaymentsPage } from './StudentFeePaymentsPage'

export default function Page() {
  const pageContext = usePageContext()
  const search = $StudentFeePaymentsSearch.parse(pageContext.urlParsed.search)

  return (
    <WithProfile roleAnyOf={['student']}>
      <StudentFeePaymentsPage search={search} />
    </WithProfile>
  )
}

import slugify from 'slugify'

export const masked = (str: string) =>
  str.length <= 5 ?
    str[0] + 'X'.repeat(str.length - 2) + str[str.length - 1]
  : str[0] + str[1] + 'X'.repeat(str.length - 4) + str[str.length - 2] + str[str.length - 1]

export const slugOf = (str: string) =>
  slugify(str, {
    lower: true, // Convert to lowercase
    strict: true, // Strip special characters except replacement
    trim: true, // Trim leading and trailing replacement chars
    locale: 'en', // Language code for locale-specific rules
    remove: /[*+~.()'"!:@]/g, // Remove special characters
  })

import { ShowData } from '@/pages/common/ShowData'
import { Protected } from '@/pages/users/auth/Protected'
import { phoneNumber } from '@/shared/common/common-utils.shared'
import { PageContent, PageLayout, PageSection } from '@ui/common/form/page-layout'
import { useUserQuery } from '@ui/users/common/user-queries'

import type { FullUserData } from '@/server/users/get-user/getUser.server'

export const UserPage = () => {
  const { data: user, isPending, error } = useUserQuery(true)
  const userData = user as FullUserData | undefined

  return (
    <Protected>
      <ShowData isPending={isPending} error={error} spinnerSize="1.25rem">
        <PageLayout maxWidth="max-w-screen-sm">
          <PageContent>
            <PageSection>
              {userData?.googlePictureUrl && (
                <div className="flex flex-col items-center space-y-2">
                  <img
                    src={userData.googlePictureUrl}
                    alt={userData.name}
                    className="rounded-full h-32 w-32 object-cover border-2 border-gray-200"
                  />
                  <div className="text-sm text-gray-500">(via Google)</div>
                  <div className="text-2xl font-bold leading-7 text-gray-900">{userData.name}</div>
                  <a href="/users/me/edit" className="link texm-sm">
                    Edit
                  </a>
                </div>
              )}
            </PageSection>
            <PageSection>
              {userData && (
                <div className="px-4 py-5 sm:w-2/3">
                  <dl className="grid grid-cols-1 gap-y-4">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Name</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {userData.name}
                        <span className="text-sm text-gray-500 ml-2">(via Google)</span>
                      </dd>
                    </div>

                    <div>
                      <dt className="text-sm font-medium text-gray-500">Email</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {userData.email}
                        <span className="text-sm text-gray-500 ml-2">(via Google)</span>
                      </dd>
                    </div>

                    <div>
                      <dt className="text-sm font-medium text-gray-500">Mobile Number</dt>
                      <dd className="mt-1 text-sm text-gray-900 flex items-center">
                        {phoneNumber(userData.mobileCountry.phonePrefix, userData?.mobile)}
                        {userData.mobileVerified ?
                          <span className="ml-2 inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                            Verified
                          </span>
                        : <span className="ml-2 inline-flex items-center rounded-full bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-700 ring-1 ring-inset ring-yellow-600/20">
                            Unverified
                          </span>
                        }
                      </dd>
                    </div>
                  </dl>
                </div>
              )}
            </PageSection>
          </PageContent>
        </PageLayout>
      </ShowData>
    </Protected>
  )
}

import { ChevronDownIcon } from '@heroicons/react/24/outline'

import { ShowData } from '@ui/common/ShowData'
import { useUserQuery } from '@ui/users/common/user-queries'

import { useCurrentProfile } from '../common/current-profile-store'
import { ProfileRole } from '../common/ProfileRole'
import { RoleIcon } from '../common/RoleIcon'
import { SuspendedBadge } from '../common/SuspendedBadge'
import { UnapprovedBadge } from '../common/UnapprovedBadge'

export const ProfileMenuTrigger = () => {
  const { data: user, isPending: usersIsPending, error: usersError } = useUserQuery()
  const { currentProfile: profile, profileIsPending, profileError } = useCurrentProfile()

  return (
    <ShowData isPending={usersIsPending || profileIsPending} error={usersError || profileError}>
      <span className="sr-only">Open user menu</span>
      {profile ?
        <RoleIcon role={profile.role} type="outline24" />
      : <img className="rounded-full size-8 bg-gray-50" src={user?.googlePictureUrl} alt="User Picture" />}
      <span className="flex flex-col items-start ml-4">
        <span className="font-semibold text-gray-900 text-sm/6" aria-hidden="true">
          {profile?.displayName ?? user?.name}
        </span>
        {profile && (
          <>
            <ProfileRole role={profile.role} />
            <span className="flex items-center gap-x-1">
              <UnapprovedBadge approvedAt={profile.approvedAt} />
              <SuspendedBadge suspendedAt={profile.suspendedAt} />
            </span>
          </>
        )}
      </span>
      <ChevronDownIcon className="ml-2 text-gray-400 size-5" aria-hidden="true" />
    </ShowData>
  )
}

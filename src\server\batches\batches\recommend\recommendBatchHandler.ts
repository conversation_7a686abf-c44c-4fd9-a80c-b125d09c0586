import { UUID } from 'crypto'

import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'

import { recommendBatch } from './recommendBatch'

export const recommendBatchHandler = new Hono<HonoVars>().put('/', async (c) => {
  const batchId = c.req.param('id') as UUID

  await recommendBatch(getCtx(c), batchId)
  return c.body(null, 204)
})

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { db } from '@/server/db/db'
import { academyStaffTable, academyTable } from '@/server/db/schema/academy-schema'
import { AcademySearch } from '@/shared/academies/academy-utils.shared'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'

export const listAcademies = async (log: Logger, search: AcademySearch) =>
  findAcademiesByProfildIdDaf(log, search.profileId)

const findAcademiesByProfildIdDaf = (log: Logger, profileId: UUID) =>
  withLog(log, 'findAcademyByProfildIdDaf', () =>
    db
      .select({
        id: academyTable.id,
        name: academyTable.name,
        approvedAt: academyTable.approvedAt,
        suspendedAt: academyTable.suspendedAt,
        emailVerified: academyTable.emailVerified,
        mobileVerified: academyTable.mobileVerified,
        currency: academyTable.currency,
      })
      .from(academyTable)
      .innerJoin(academyStaffTable, eq(academyStaffTable.academyId, academyTable.id))
      .where(eq(academyStaffTable.profileId, profileId)),
  )

export type ListAcademies = typeof listAcademies
export type AcademyItem = Awaited<ReturnType<ListAcademies>>[0]

setQuery('listAcademies', listAcademies)

@import "tailwindcss";

@layer components {
  .editor-wrapper {
    @apply min-h-[150px] px-3 py-2;
  }

  .editor-wrapper:focus-within {
    @apply outline-2 outline-offset-0 outline-indigo-600;
  }

  /* Base rich content configuration */
  .rich-content {
    @apply text-sm text-gray-900 max-w-none;
  }

  /* Typography - Headers */
  .rich-content h1 {
    @apply text-3xl font-bold tracking-tight text-gray-900 mt-8;
  }

  .rich-content h2 {
    @apply text-2xl font-semibold text-gray-900 mt-7;
  }

  .rich-content h3 {
    @apply text-xl font-semibold text-gray-900 mt-6;
  }

  .rich-content h4 {
    @apply text-lg font-medium text-gray-900 mt-5;
  }

  /* Paragraphs and spacing */
  .rich-content p {
    @apply text-sm text-gray-600 leading-6 mt-3;
  }

  /* Lists */
  .rich-content ul,
  .rich-content ol {
    @apply mt-4 ml-6 text-gray-600;
  }

  .rich-content ul {
    @apply list-disc;
  }

  .rich-content ol {
    @apply list-decimal;
  }

  .rich-content li + li {
    @apply mt-2 leading-6;
  }

  /* Links */
  .rich-content a {
    @apply text-indigo-600 hover:text-indigo-500 hover:underline font-medium;
  }

  /* Blockquotes */
  .rich-content blockquote {
    @apply border-l-4 border-gray-200 pl-4 py-2 mt-4 text-gray-600 italic;
  }

  /* Horizontal rule */
  .rich-content hr {
    @apply mt-8 border-t border-gray-200;
  }

  /* MDX Editor toolbar */
  .mdxeditor-toolbar {
    @apply border-b border-gray-200 px-3 py-1;
  }

  .mdxeditor-toolbar button {
    @apply hover:bg-gray-100 rounded p-1;
  }
}


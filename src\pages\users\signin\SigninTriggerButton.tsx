import React from 'react'

import { GoogleIcon } from '../common/GoogleIcon'

import { setSigninModalVisibility } from './signin-modal-store'
import './SigninTriggerButton.css'

export const SigninTriggerButton = () => (
  <button className="gsi-material-button" onClick={() => setSigninModalVisibility(true)}>
    <div className="gsi-material-button-state"></div>
    <div className="gsi-material-button-content-wrapper">
      <GoogleIcon />
      <span className="gsi-material-button-contents">Sign in</span>

      <span style={{ display: 'none' }}>Sign in with Google</span>
    </div>
  </button>
)

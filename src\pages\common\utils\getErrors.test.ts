import { describe, expect, it } from 'vitest'
import { z } from 'zod'

import { getErrors } from './error-utils.ui'

describe('getErrors', () => {
  const schema = z.object({
    name: z.string().min(3),
    address: z.object({
      street: z.string(),
      postalCode: z.string().length(6),
    }),
    hobbies: z.array(z.string()),
  })

  it('returns empty array when error is undefined', () => {
    expect(getErrors(undefined)).toEqual([])
  })

  it('returns empty array when error has no matching path', () => {
    const result = schema.safeParse({
      name: '<PERSON>',
      address: {
        street: '123 Main St',
        postalCode: '12345', // Invalid postal code
      },
      hobbies: ['reading'],
    })

    if (result.success) throw new Error('Schema should have failed')

    expect(getErrors(result.error, ['nonexistent'])).toEqual([])
  })

  it('returns no errors when path is empty', () => {
    const result = schema.safeParse({
      name: '<PERSON>', // Too short
      address: {
        street: '123 Main St',
        postalCode: '12345', // Wrong length
      },
      hobbies: ['reading'],
    })

    if (result.success) throw new Error('Schema should have failed')

    const errors = getErrors(result.error)
    expect(errors).toHaveLength(0)
  })

  it('returns only errors matching the exact path', () => {
    const result = schema.safeParse({
      name: 'Jo', // Too short
      address: {
        street: '123 Main St',
        postalCode: '12345', // Wrong length
      },
      hobbies: ['reading'],
    })

    if (result.success) throw new Error('Schema should have failed')

    const nameErrors = getErrors(result.error, ['name'])
    expect(nameErrors).toHaveLength(1)
    expect(nameErrors[0]).toBe('String must contain at least 3 character(s)')

    const postalErrors = getErrors(result.error, ['address', 'postalCode'])
    expect(postalErrors).toHaveLength(1)
    expect(postalErrors[0]).toBe('String must contain exactly 6 character(s)')
  })

  it('handles array indices in paths', () => {
    const arraySchema = z.object({
      items: z.array(z.string().min(3)),
    })

    const result = arraySchema.safeParse({
      items: ['a', 'abc', 'b'],
    })

    if (result.success) throw new Error('Schema should have failed')

    const itemErrors = getErrors(result.error, ['items', 0])
    expect(itemErrors).toHaveLength(1)
    expect(itemErrors[0]).toBe('String must contain at least 3 character(s)')
  })
})

import { clearDiagnosticLogs, useDiagnosticLogs } from './diagnostic-store'

export const DiagnosticPage = () => {
  const logs = useDiagnosticLogs()

  const handleCopyToClipboard = async () => {
    if (logs.length > 0) {
      try {
        await navigator.clipboard.writeText(logs.join('\n'))
      } catch (error) {
        console.error('Failed to copy logs to clipboard:', error)
      }
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <h1 className="text-3xl font-semibold text-gray-900">Diagnostic Logs</h1>
        <div className="flex space-x-4 mt-4 sm:mt-0">
          <button
            type="button"
            onClick={handleCopyToClipboard}
            disabled={logs.length === 0}
            className="rounded-sm bg-indigo-600 px-2 py-1 text-xs font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Copy to Clipboard
          </button>
          <button
            type="button"
            onClick={clearDiagnosticLogs}
            disabled={logs.length === 0}
            className="rounded-sm bg-red-600 px-2 py-1 text-xs font-semibold text-white shadow-xs hover:bg-red-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
          >
            Clear
          </button>
        </div>
      </div>

      <div className="rounded-lg bg-white shadow-sm ring-1 ring-gray-900/5">
        {logs.length === 0 ?
          <div className="p-6 text-center text-gray-500">No diagnostic logs available.</div>
        : <div className="max-h-[70vh] overflow-y-auto p-6">
            <ul className="space-y-3">
              {logs.map((log, index) => (
                <li key={index} className="text-sm/6 text-gray-500 py-2 border-b border-gray-100 last:border-0">
                  <span className="font-medium text-gray-900 mr-2">-</span>
                  {log}
                </li>
              ))}
            </ul>
          </div>
        }
      </div>
    </div>
  )
}

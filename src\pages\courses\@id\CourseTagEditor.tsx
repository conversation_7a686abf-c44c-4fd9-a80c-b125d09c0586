import { UUID } from 'crypto'

import { XMarkIcon } from '@heroicons/react/20/solid'
import { useEffect } from 'react'

import { CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { CourseTag } from '@/server/courses/tags/get/getCourseTag.server'
import { CheckBox } from '@ui/common/form/CheckBox'
import { PageContent, PageSection } from '@ui/common/form/page-layout'
import { showNotification } from '@ui/common/notification/notification-store'
import { ShowData } from '@ui/common/ShowData'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'

import { useAddTagToCourseMutation, useRemoveTagFromCourseMutation } from '../common/queries/course-queries'
import { useCourseTagsQuery } from '../common/queries/course-tag-queries'

export const CourseTagEditorPanel = ({
  course,
  showTagEditor,
  setShowTagEditor,
}: {
  course: CourseWithoutDescr
  showTagEditor: boolean
  setShowTagEditor: (show: boolean) => void
}) => (showTagEditor ? <CourseTagEditor course={course} setShowTagEditor={setShowTagEditor} /> : null)

export const CourseTagEditor = ({
  course,
  setShowTagEditor,
}: {
  course: CourseWithoutDescr
  setShowTagEditor: (show: boolean) => void
}) => {
  const { data: tagsGroups, error: tagsGroupsError } = useCourseTagsQuery()
  const toShowCloseButton = (course.tagIds?.length ?? 0) > 0

  const handleClose = () => {
    setShowTagEditor(false)
  }

  return (
    <ShowData error={tagsGroupsError}>
      <PageContent divide={false}>
        {toShowCloseButton && (
          <div className="relative">
            <button
              onClick={handleClose}
              className="absolute -top-[-15px] -right-1 rounded-full p-1 hover:bg-gray-100"
              aria-label="Close tag editor"
            >
              <XMarkIcon className="h-7 w-7 text-gray-400" />
            </button>
          </div>
        )}
        <h2 className="mt-3 mb-1 text-lg font-medium">Select Tags</h2>
        {tagsGroups?.map((tagGroup) => (
          <PageSection key={tagGroup.id} title={tagGroup.name} className="py-3">
            <TagSelector course={course} tags={tagGroup.tags} />
          </PageSection>
        ))}
      </PageContent>
    </ShowData>
  )
}

const TagSelector = ({ course, tags }: { course: CourseWithoutDescr; tags: CourseTag[] }) => {
  const addTag = useAddTagToCourseMutation(course.id)
  const removeTag = useRemoveTagFromCourseMutation(course.id)

  const isTagSelected = (tagId: UUID) => course.tagIds.includes(tagId) ?? false
  const toggleTag = (tagId: UUID) => {
    if (isTagSelected(tagId)) {
      removeTag.mutate(tagId)
    } else {
      addTag.mutate(tagId)
    }
  }

  useEffect(() => {
    if (addTag.error || removeTag.error) {
      showNotification({
        type: 'error',
        heading: 'Error',
        message: getErrorMessage(addTag.error || removeTag.error),
      })
    }
  }, [addTag.error, removeTag.error])

  return (
    <div>
      <ul role="list" className="mt-3 grid grid-cols-1 gap-2 sm:grid-cols-2 sm:gap-3 lg:grid-cols-4">
        {tags.map((tag) => (
          <li key={tag.id} className="col-span-1 flex rounded-md shadow-xs">
            <CheckBox
              label={tag.name}
              description={tag.descr}
              checked={isTagSelected(tag.id)}
              onChange={() => toggleTag(tag.id)}
              disabled={addTag.isPending || removeTag.isPending}
            />
          </li>
        ))}
      </ul>
    </div>
  )
}

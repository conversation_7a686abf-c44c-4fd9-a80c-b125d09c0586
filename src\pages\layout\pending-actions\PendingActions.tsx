import { ExclamationTriangleIcon } from '@heroicons/react/20/solid'

import { useAcademyEmailUnverifiedWarning } from '@ui/academies/common/useAcademyEmailUnverifiedWarning'
import { useAcademyMobileUnverifiedWarning } from '@ui/academies/common/useAcademyMobileUnverifiedWarning'
import { useCreateAProfileTeaser } from '@ui/profiles/common/useCreateAProfileTeaser'
import { useProfilesToApprove } from '@ui/profiles/common/useProfilesToApprove'
import { useStudentPaymentDueWarning } from '@ui/students/common/student-payment-due-warning/useStudentPaymentDueWarning'
import { useMobileUnverifiedWarning } from '@ui/users/common/useMobileUnverifiedWarning'
import { useTnCWarning } from '@ui/users/common/useTnCWarning'

import { PendingAction } from './PendingAction'
export const PendingActions = () => {
  const warnings = [
    useTnCWarning(),
    useMobileUnverifiedWarning(),
    useCreateAProfileTeaser(),
    useAcademyMobileUnverifiedWarning(),
    useAcademyEmailUnverifiedWarning(),
    useProfilesToApprove(),
    useStudentPaymentDueWarning(),
  ]
  const toShow = warnings.some((warning) => warning)

  if (!toShow) return null
  return (
    <div className="rounded-md bg-yellow-50 p-4 mb-4">
      <div className="flex">
        <div className="shrink-0">
          <ExclamationTriangleIcon className="size-5 text-yellow-400" aria-hidden="true" />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-yellow-800">Attention needed</h3>
          <div className="mt-2 text-sm text-yellow-700">
            <ul className="list-none space-y-1 pl-1">
              {warnings.map(
                (Warning, index) =>
                  Warning && (
                    <PendingAction key={index}>
                      <Warning />
                    </PendingAction>
                  ),
              )}
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

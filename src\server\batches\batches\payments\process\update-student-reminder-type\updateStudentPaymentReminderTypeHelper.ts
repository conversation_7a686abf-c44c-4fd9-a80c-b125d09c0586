import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { Transaction } from '@/server/db/db'
import { batchStudentTable } from '@/server/db/schema'
import { BillingCycle } from '@/shared/batch/batch-utils.shared'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { PaymentReminderType } from '@/shared/common/payment-utils/payment-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

export type Batch = {
  id: UUID
  startDate: string
  timezone: string
  billingCycle: BillingCycle
  graceDays: number
  cycleCount: number
  teacher: {
    user: {
      id: UUID
    }
  }
  events: {
    id: UUID
  }[]
}

export type BatchStudent = {
  id: UUID
  firstJoinedAt: Date
  paidTillCycle: number
  lastReminderType: PaymentReminderType | null
}

export const updateLastReminderTypeDaf = async (
  log: pino.Logger,
  db: Transaction,
  batchStudent: BatchStudent,
  lastReminderType: PaymentReminderType | null,
) => {
  await withLog(log, 'updateLastReminderTypeDaf', () =>
    db
      .update(batchStudentTable)
      .set({ lastReminderType, updatedAt: utc().toJSDate(), updateRemarks: 'updated last reminder type' })
      .where(eq(batchStudentTable.id, batchStudent.id)),
  )
}

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { getBatch } from './getBatch.server'

export const getBatchHandler = new Hono<HonoVars>().get('/', zValidator('param', $HasId), async (c) => {
  const profile = c.get('profile')
  const { id: batchId } = c.req.valid('param')
  const batch = await getBatch(c.get('log'), batchId, profile?.id)
  return c.json(batch, 200)
})

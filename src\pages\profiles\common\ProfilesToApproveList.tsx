import { profilePath } from '@/shared/profiles/profile-utils.shared'
import { ERoles } from '@/shared/profiles/role-utils.shared'

import type { ProfilesToApprove } from '@/server/profiles/get-profiles-to-approve/getProfilesToApprove'

export const ProfilesToApproveList = ({ profilesToApprove }: { profilesToApprove: ProfilesToApprove }) => {
  return (
    <div>
      Profiles needing your approval
      <ul>
        {profilesToApprove.map((profile) => (
          <li key={profile.id} className="text-xs">
            <span className="mr-2">-</span>
            <span>
              <a href={profilePath(profile)} className="link">
                {profile.displayName}
              </a>
              , {ERoles[profile.role].name}
            </span>
          </li>
        ))}
      </ul>
    </div>
  )
}

import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'

import { getProfilesToApprove } from './getProfilesToApprove'

export const getProfilesToAppproveHandler = new Hono<HonoVars>().get('/', async (c) => {
  const profiles = await getProfilesToApprove(getCtx(c))
  return c.json({ rows: profiles }, 200)
})

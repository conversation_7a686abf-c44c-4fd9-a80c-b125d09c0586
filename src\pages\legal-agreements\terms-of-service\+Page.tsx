export default () => (
  <div className="max-w-4xl mx-auto p-6 space-y-4">
    <p className="text-center">
      <strong className="text-2xl">Terms of Service</strong>
    </p>
    <p className="text-center">
      <strong>Last Updated: 30 May, 2025</strong>
      <br />
      <a
        className="link text-sm"
        target="_blank"
        href={`https://tuitionlance.s3.amazonaws.com/legal-agreements/terms-of-service-v1.pdf?t=${Date.now()}`}
      >
        View or Download PDF
      </a>
    </p>
    <p>
      These Terms of Service ("Terms") govern your access to and use of the TuitionLance platform (referred to as "we,"
      "us," "our," or the "platform"), which includes the website www.tuitionlance.com, mobile applications, and related
      services. The brand and platform TuitionLance is owned and operated by the proprietorship{' '}
      <strong>Natural Programmer</strong> operating at 13AA Mani Tribhuvan, Kalarahanga, Bhubaneswar, Odisha, India, PIN
      – 751024.
    </p>
    <p>
      TuitionLance is an aggregator platform of teaching entities—it does not provide instruction or process Student
      payments, but offers a platform for Academies and Students to discover each other, connect, assist in delivering
      coursework (including calendar integration), and maintain transparency via public feedback. Collectively, these
      shall be referred to as the "Services". Academies pay a subscription fee to Natural Programmer for marketing,
      displaying, and managing their information on the Platform, including but not limited to subjects taught, courses
      offered, instructor profiles, and scheduling features. All course fees and other contractual terms between each
      Student and the Academy are negotiated and agreed directly between them.
    </p>
    <p>
      <strong>
        As a user or registered user, by browsing our website or interacting with our platform, you automatically agree
        to Section 2 of the Terms of Service.
      </strong>
    </p>
    <p>
      <strong>1.</strong> <strong>Definitions:</strong>
    </p>
    <p>
      <strong>a)</strong> <strong>Academy</strong>
    </p>
    <p>
      An educational entity registered on the Platform, comprising one or more profiles of Principals, Mentors, or
      Teachers, offering educational courses or coaching.
    </p>
    <p>
      <strong>b)</strong> <strong>Academy Owner</strong>
    </p>
    <p>The Principal who creates the Academy.</p>
    <p>
      <strong>c)</strong> <strong>Principal, Mentor, Teacher</strong>
    </p>
    <p>
      The Academy staff with individual profiles, responsible for the administration and delivery of educational
      content.
    </p>
    <p>
      <strong>d)</strong> <strong>Student</strong>
    </p>
    <p>Any individual who registers to receive educational services via the Platform.</p>
    <p>
      <strong>e)</strong> <strong>Profile</strong>
    </p>
    <p>
      A registered user account associated with one of the roles above, as well as employees or contractors engaged by
      Natural Programmer.
      <span id="_Hlk199442696">
        Each registered user may have multiple profiles and must have the legal capacity to enter into binding
        agreements
      </span>
      .
    </p>
    <p>
      <strong>f)</strong> <strong>Registered User</strong>
    </p>
    <p>
      Any individual who has successfully signed up on the TuitionLance Platform by providing a valid email address and
      mobile number. Each registered user must have the legal capacity to enter into binding agreements.
    </p>
    <p>
      <strong>g)</strong> <strong>User</strong>
    </p>
    <p>
      Any individual or entity that accesses, browses, or otherwise interacts with the TuitionLance Platform, whether or
      not registered or holding a Profile.
    </p>
    <p className="text-center">
      <strong>SECTION 1 – APPLICABLE TO PROFILE OWNERS</strong>
    </p>
    <p>
      <strong>2.</strong> <strong>For Students</strong>
    </p>
    <p>
      <strong>2.1</strong> <strong>Profile Creation</strong>
    </p>
    <p>
      A registered user may create a Student profile without an invitation. Note that minor students who do not have
      legal capacity to enter into a binding contract can't become registered users. Consequently, their parent or legal
      guardian may register, create profile and use the platform for them.
    </p>
    <p>
      <strong>2.2</strong> <strong>Connection</strong>
    </p>
    <p>
      We facilitate introductions and recommendations of Academies based on Students' preferences and help them connect
      and negotiate the terms of the teaching arrangement with each other.
    </p>
    <p>
      <strong>2.3</strong> <strong>Payment and Tuition Terms</strong>
    </p>
    <p>
      All payment terms (fees, billing cycles), payment methods (online, bank transfer), class schedules, course
      structures, course material and teacher assignments are governed by the individual agreement between each Student
      and their chosen Academy. We act solely as an intermediary and do not set or receive any educational fees from
      students or provide direct instruction or course materials.
    </p>
    <p>
      <strong>2.4 </strong>
      <strong>Verification of Academy and Academy Owner</strong>
    </p>
    <p>
      We verify the Academy and the Academy Owner - legal status, registration, educational qualifications, and
      certifications, to guard against fraudulent or impostor entities.
    </p>
    <p>
      <strong>2.5 </strong>
      <strong>Verification of Teachers, Mentors and Additional Principals</strong>
    </p>
    <p>
      Academy Principals independently invite and manage mentors and teachers, and are responsible for verifying their
      identities, educational qualifications or credentials. We do not pre-screen all individual instructor profiles;
      however, upon any report of discrepancy or fraud, we will promptly investigate and may suspend or terminate the
      relevant profile.
    </p>
    <p>
      <strong>2.6</strong> <strong>Student Feedback</strong>
    </p>
    <p>
      We monitor Academy performance by tracking Student satisfaction. Feedback submitted by Students may be published
      publicly, subject to the owner's consent, to help future Students make informed choices.
    </p>
    <p>
      <strong>2.7</strong> <strong>Additional Features</strong>
    </p>
    <p>
      Students may opt into calendar integration (e.g., Google Calendar notifications) and other platform capabilities
      to streamline scheduling and coursework reminders. We shall be implementing new features such as internal chat and
      payment systems in the platform in the near future.
    </p>
    <p>
      <strong>2.8</strong> <strong>Caution</strong>
    </p>
    <p>
      a) Students or their parents are advised to proceed with any internal agreements at their own discretion and
      exercise due diligence when entering into contracts. As an intermediary, we disclaim all responsibility for the
      quality, accuracy, completeness, or efficacy of teaching, curricula, or materials provided by any Academy, and any
      refunds.
    </p>
    <p>
      b) Academies may employ third-party tools (e.g., video conferencing, payment gateways) for classes and
      communications. Each such service has its own terms, conditions, and privacy policies, for which we bear no
      responsibility.
    </p>
    <p>
      <strong>2.9 </strong>
      <strong>Indemnification</strong>
    </p>
    <p>
      Each Student shall similarly indemnify Natural Programmer, including its affiliates, officers, directors,
      employees, agents, contractors and licensors (collectively, the "Indemnitees") against any claims arising from (a)
      the Student's breach of these Terms; (b) disputes with Academies; or (c) misuse of Platform content.
    </p>
    <p>
      <strong>3.</strong> <strong>For Academies</strong>
    </p>
    <p>
      <strong>3.1</strong> <strong>Creation</strong>
    </p>
    <p>
      We invite a registered user who may wish to create and manage an Academy to create a Principal profile. On our
      approval of the profile, the Principal can proceed to create the Academy. Newly created Academies must be approved
      by us before onboarding students. <strong></strong>
    </p>
    <p>
      <strong>3.2</strong> <strong>Academy Management</strong>
    </p>
    <p>
      Principals retain full control over course creation, scheduling, batch management and other information displayed
      on our platform, such as the academy's achievements and teachers' qualifications. Teachers and Mentors may also
      perform some of these functions, but only upon authorisation from the Principals.
    </p>
    <p>
      <strong>3.3 </strong>
      <strong>Principal, Mentor, and Teacher Profile Creation</strong>
    </p>
    <p>
      a) Principal, Mentor, and Teacher profiles are Academy-specific. Teaching at multiple Academies requires separate
      Profile registrations for each.
    </p>
    <p>
      b) Only a Principal may invite and authorise the creation of Mentor, Teacher, or additional Principal profiles
      within their Academy.
    </p>
    <p>
      <strong>3.4 </strong>
      <strong>Credential Verification &amp; Liability</strong>
    </p>
    <p>
      Principals are solely liable for verifying and onboarding qualified staff. Discovery of forged or fraudulent
      credentials may result in the suspension or termination of any profile, and in case of repeated violations, the
      suspension or termination of the Academy itself.
    </p>
    <p>
      <strong>3.5</strong> <strong>Negative Feedback</strong>
    </p>
    <p>
      Upon receipt of verifiable evidence of an Academy's or its staff's breach of an agreement with a Student, or
      multiple verifiable complaints regarding unsatisfactory course delivery, Natural Programmer may, at its sole
      discretion, suspend or terminate the relevant Academy or the specific staff's profile.
    </p>
    <p>
      <strong>3.6</strong> <strong>Platform Fees</strong>
    </p>
    <p>
      Academies pay a monthly subscription fee (e.g. 10% of the Academy's revenue). These fees may be negotiated and
      designated in a counterpart agreement. We reserve the right to adjust fees with 30 days' prior written notice.{' '}
      <strong></strong>
    </p>
    <p>
      <strong>3.7</strong> <strong>Relationship</strong>
    </p>
    <p>
      The relationship between you and Natural Programmer is that of independent contractors. Nothing in these Terms
      shall be construed to create a partnership, joint venture, agency, or employment relationship between the parties.
    </p>
    <p>
      <strong>3.8</strong> <strong>Refund Policy</strong>
    </p>
    <p>
      Except in the event that we materially breach these Terms, all subscription and service fees paid by Academies to
      Natural Programmer for access to the Platform are non-refundable. This includes matters of Academy profile
      suspension or termination, due to various breaches of these Terms, as described in clauses 2.5, 3.5, 3.6 and 5 -
      20 herein.
    </p>
    <p>
      <strong>3.9</strong> <strong>Code of Conduct</strong>
    </p>
    <p>In addition to the General Code of Conduct (Section 5), Academies and their staff profiles must:</p>
    <p>a) Maintain fairness and impartiality in grading and assessment.</p>
    <p>b) Disclose all course objectives, curricula, and assessment methods transparently.</p>
    <p>c) Avoid discriminatory practices in teaching or evaluation.</p>
    <p>
      <strong>3.10</strong> <strong>Caution</strong>
    </p>
    <p>
      Ensure that when providing services to a Student, they have the legal capacity to engage with you, as many of the
      Students shall be minors.
    </p>
    <p>
      <strong>3.11</strong> <strong>Indemnification</strong>
    </p>
    <p>
      Each Academy (including its Principals, Mentors, Teachers, and other staff) shall defend, indemnify, and hold
      harmless Natural Programmer, including its affiliates, officers, directors, employees, agents, contractors and
      licensors (collectively, the "Indemnitees") from and against any and all losses, liabilities, damages, fines,
      penalties, costs, and expenses (including reasonable attorneys' fees and costs of investigation) arising out of or
      relating to:
    </p>
    <p>
      a) Any breach by the Academy or its staff of these Terms (including any misrepresentation regarding credentials,
      course content, or fees);
    </p>
    <p>
      b) Any dispute between the Academy and a Student (or third party) concerning course quality, delivery, payment, or
      contractual obligations;
    </p>
    <p>
      c) Any claim that materials provided by the Academy (including curricula, presentations, videos, or other content)
      infringe the intellectual property, privacy, or other rights of any third party;
    </p>
    <p>
      d) Any negligent or willfully wrongful act or omission by the Academy or its staff in connection with the use of
      the Platform or delivery of educational services.
    </p>
    <p>
      The Academy's indemnification obligation shall apply regardless of whether we were notified in advance of, or
      participated in, the underlying dispute or proceeding.
    </p>
    <p>
      <strong>4.</strong> <strong>For Internal Profiles</strong>
    </p>
    <p>
      <strong>4.1</strong> <strong>Profile Creation</strong>
    </p>
    <p>
      Profiles designated as Admin, Manager, or Executive etc. shall solely be for internal operational, support, and
      administrative functions on the Platform. They shall be created only upon invitation from any of TuitionLance's
      ADMINs or other internal profiles.
    </p>
    <p>
      <strong>4.2</strong> <strong>Separate Engagement Terms</strong>
    </p>
    <p>
      Compensation, termination, scope of work and other Employment or Contractor Agreement clauses for internal
      profiles are governed by separate agreements executed between the individual and our Natural Programmer.
    </p>
    <p>
      <strong>4.3</strong> <strong>Default Applicability of these Terms</strong>
    </p>
    <p>
      Unless explicitly overridden by the terms of a separately executed employment or contractor agreement, internal
      profiles shall be bound by and required to comply with the obligations in clauses 5–20 of these Terms, and the
      Privacy Policy.
    </p>
    <p className="text-center">
      <strong>SECTION 2 - APPLY TO ALL USERS</strong>
    </p>
    <p className="text-center">
      <strong></strong>
    </p>
    <p>
      <strong>5.</strong> <strong>General Code of Conduct</strong>
    </p>
    <p>
      Violation of the following codes may lead to suspension or termination of profiles or bans from social media
      channels.
    </p>
    <p>
      <strong>5.1 </strong>All users, including unregistered visitors, must conduct themselves professionally. Obscene
      remarks, harassment or defamatory behaviour—whether in chats or social media are strictly prohibited.
    </p>
    <p>
      <strong>5.2 </strong>Discrimination on the basis of sex, religion, nationality, disability, or any protected
      characteristic is strictly prohibited.
    </p>
    <p>
      <strong>5.3 </strong>Users must not use the Platform to disseminate spam, malware, or content that infringes
      intellectual property rights or violates applicable law.
    </p>
    <p>
      <strong>5.4 </strong>We reserve the right to take appropriate enforcement actions, including legal action, where
      conduct constitutes abuse, fraud, or criminal behaviour.
    </p>
    <p>
      <strong>6.</strong> <strong>Third-Party Services</strong>
    </p>
    <p>
      The Platform may integrate or interoperate with third-party products, services, or websites ("Third-Party
      Services"). Your use of such services is subject to their respective terms and privacy policies. We are not
      responsible for, and makes no representations or warranties regarding, such Third-Party Services. Users are solely
      responsible for reviewing and complying with the terms of any third-party tools used in conjunction with the
      Platform, including conferencing or payment tools.
    </p>
    <p>
      <strong>7.</strong> <strong>Term and Termination</strong>
    </p>
    <p>
      <strong>7.1 </strong>The term of service in the case of Students is indefinite.
    </p>
    <p>
      <strong>7.2 </strong>The term of service for Academies renews monthly for an indefinite period. For discontinuing
      renewal, an Academy must inform us if they wish to pause or permanently withdraw from our services before the
      billing for the next renewal.
    </p>
    <p>
      <strong>7.3 </strong>Any profile may be suspended, and personal data shall be deleted post six (06) years of
      inactivity. A Student, Principal, Teacher or Mentor may request their account and personal data to be deleted at
      any point in time, which shall be followed, subject to identity verification and no pending contractual
      obligations. An internal profile's data may be deleted only upon termination of engagement with Natural
      Programmer. Please refer to the Privacy Policy for more details in this regard.
    </p>
    <p>
      <strong>7.4 </strong>In the absence of any pending obligations, we reserve the right to modify, suspend or
      discontinue the Services (or any part thereof) with ten (10) days' notice.
    </p>
    <p>
      <strong>7.5 </strong>Services may be terminated or suspended immediately in cases of breaches of these Terms, as
      described in clauses 2.5, 3.5, 3.6 and 5 - 20 herein.
    </p>
    <p>
      <strong>7.6 </strong>Profiles which are terminated for cause (including breach of these Terms) shall not be
      entitled to refunds or reactivation, unless required by law or expressly agreed in writing.
    </p>
    <p>
      <strong>8.</strong> <strong>Intellectual Property</strong>
    </p>
    <p>
      <strong>8.1 </strong>All Platform Content (designs, logos, text, code, media) is owned by Natural Programmer or
      licensors, except where attributed to Academies.
    </p>
    <p>
      <strong>8.2 </strong>Users get a revocable, non-exclusive license to access and use Platform Content strictly for
      personal or internal business purposes in connection with the Services.
    </p>
    <p>
      <strong>8.3 </strong>No copying, derivative works, decompilation, scraping, or misuse of Platform Content is
      allowed.
    </p>
    <p>
      <strong>8.4 </strong>Any suggestions, ideas, or feedback you submit regarding the Platform shall be deemed
      non-confidential and may be used by us without restriction or compensation.
    </p>
    <p>
      <strong>9.</strong> <strong>Force Majeure</strong>
    </p>
    <p>
      <strong>9.1 </strong>Neither party shall be liable for any delay or failure to perform its obligations under these
      Terms due to causes beyond its reasonable control, including acts of God, war, terrorism, labour disputes,
      pandemics, governmental acts, or failures of telecommunications or hosting infrastructure.
    </p>
    <p>
      <strong>9.2 </strong>The affected party shall notify the other in writing within ten (10) days of the force
      majeure event and shall resume performance as soon as reasonably practicable after the event ends.
    </p>
    <p>
      <strong>9.3 </strong>If the Force Majeure situation continues for over a period of sixty (60) days, services and
      profile shall automatically stand suspended or terminated.
    </p>
    <p>
      <strong>10.</strong> <strong>Assignment</strong>
    </p>
    <p>
      Neither party may assign or transfer its rights or obligations under these Terms without the prior written consent
      of the other party, except that we may assign these Terms in the event of a merger, acquisition, or sale of
      assets.
    </p>
    <p>
      <strong></strong>
    </p>
    <p>
      <strong>11.</strong> <strong>Survival</strong>
    </p>
    <p>
      Any provisions of these Terms which by their nature should survive termination shall survive, including but not
      limited to Sections 2.10 and 3.11 (Indemnification), Sections 5 (Code of Conduct), 6 (Third-Party Services), 8
      (Intellectual Property), 9 (Force Majeure), 16 (Limitation of Liability), 17 (Record Keeping and Dispute
      Facilitation), and 18 (Governing Law &amp; Dispute Resolution).
    </p>
    <p>
      <strong>12.</strong> <strong>Severability</strong>
    </p>
    <p>
      If any provision or portion of these Terms is held to be invalid, illegal, or unenforceable by a court of
      competent jurisdiction, such provision shall be struck and the remaining provisions shall remain in full force and
      effect.
    </p>
    <p>
      <strong>13.</strong> <strong>Counterparts</strong>
    </p>
    <p>
      These Terms may be agreed to electronically. In case of any separate agreement or amendment between the parties,
      such documents may be executed in counterparts and transmitted electronically, each of which shall be deemed an
      original and all of which shall constitute one and the same instrument.
    </p>
    <p>
      <strong>14.</strong> <strong>Modifications to Terms</strong>
    </p>
    <p>
      We reserve the right to update or modify these Terms at any time with ten (10) days' notice, including by posting
      an updated version on the Platform. Continued use of the Platform after such notice constitutes acceptance of the
      modified Terms.
    </p>
    <p>
      <strong></strong>
    </p>
    <p>
      <strong>15.</strong> <strong>Entire Agreement</strong>
    </p>
    <p>
      These Terms constitute the entire agreement between you and Natural Programmer regarding the use of the Services
      and supersede all prior oral or written understandings. Any additional terms agreed directly between a Student and
      an Academy shall be valid only if in writing and signed by both parties. In the event of a conflict, the specific
      agreement shall take precedence to the extent of the inconsistency.
    </p>
    <p>
      <strong></strong>
    </p>
    <p>
      <strong>16.</strong> <strong>Limitation of Liability</strong>
    </p>
    <p>
      <strong>16.1 </strong>The platform and services are provided "as is" and "as available," without any warranty,
      express or implied, including merchantability or fitness for a particular purpose.
    </p>
    <p>
      <strong>16.2 </strong>To the maximum extent permitted, we are not liable for any indirect, special, incidental,
      punitive, or consequential damages (including loss of profits, loss of business, or loss of goodwill), even if
      advised of the possibility of such damages. This includes any loss or damage resulting from unauthorised access to
      or alteration of your transmissions or data.
    </p>
    <p>
      <strong>16.3 </strong>Our total liability shall not exceed the fees paid to us in the three (03) months prior to
      the claim.
    </p>
    <p>
      <strong>16.4 </strong>We are not liable for the acts or omissions of Academies or third-party services.
    </p>
    <p>
      <strong>16.5 </strong>Where local law prohibits these limitations, liability shall be limited to the maximum
      extent permitted.
    </p>
    <p>
      <strong>17. </strong>
      <strong>Record‑Keeping and Dispute Facilitation</strong>
    </p>
    <p>
      We will maintain records of payment receipts to assist in dispute resolution. We will make reasonable efforts to
      facilitate dialogue, but do not arbitrate or guarantee outcomes. Legal recourse rests with the contracting
      parties. <strong></strong>
    </p>
    <p>
      <strong></strong>
    </p>
    <p>
      <strong>18. </strong>
      <strong>Governing Law &amp; Dispute Resolution</strong>
    </p>
    <p>
      <strong>18.1 </strong>In the event of any dispute, controversy, or claim arising out of or relating to these
      Terms, the parties shall first attempt in good faith to resolve the matter through direct negotiation for a period
      not less than thirty (30) days after one party provides written notice of the dispute to the other.{' '}
      <strong></strong>
    </p>
    <p>
      <strong>18.2</strong> <strong>If Negotiation Fails</strong>
    </p>
    <p>a) For Indian Users:</p>
    <p>
      i. For users who are Indian citizens or ordinarily resident in India, any dispute not resolved by negotiation
      shall be subject to the exclusive jurisdiction of the courts in Bhubaneswar, Odisha, India.
    </p>
    <p>
      ii. The courts of Bhubaneswar shall have exclusive venue and personal jurisdiction, and the parties irrevocably
      waive any objections based on inconvenient forum.
    </p>
    <p>b) International Users:</p>
    <p>
      i. For users residing outside India, any dispute not resolved by negotiation shall be finally resolved by binding
      arbitration under a mutually agreed neutral venue.
    </p>
    <p>ii. The number of arbitrators shall be one, unless otherwise agreed.</p>
    <p>iii. The language of the arbitration shall be English.</p>
    <p>
      iv. The arbitral award shall be final, binding, and enforceable in any court of competent jurisdiction, and the
      parties waive any right of appeal or review except as permitted under the Arbitration and Conciliation Act, 1996
      and applicable international conventions.
    </p>
    <p>
      v. Each party shall bear its own costs and expenses, including attorneys' fees, incurred in connection with any
      dispute resolution.
    </p>
    <p>
      <strong>19.</strong> <strong>Notices</strong>
    </p>
    <p>
      All notices under these Terms shall be in writing and sent to the other party's email address provided during
      Registration. Notices shall be deemed given after a lapse of three (3) business days after an email is sent
      (unless a delivery failure is indicated).
    </p>
    <p>
      <strong>20.</strong> <strong>Contact Information:</strong>
    </p>
    <p>Email: <EMAIL></p>
    <p>Address: Natural Programmer, 13AA Mani Tribhuvan, Kalarahanga, Bhubaneswar, Odisha, 751024, India</p>
    <p className="text-center mt-5 italic">
      By creating a profile and availing our services, you agree to Section 1 of the Terms of Service.
    </p>
  </div>
)

// @ts-nocheck

import eslint from "@eslint/js";
import prettier from "eslint-plugin-prettier/recommended";
import react from "eslint-plugin-react/configs/recommended.js";
import globals from "globals";
import tseslint from "typescript-eslint";
import importPlugin from 'eslint-plugin-import';
import boundaries from "eslint-plugin-boundaries";
import pluginQuery from "@tanstack/eslint-plugin-query"
import reactRefresh from "eslint-plugin-react-refresh";

export default tseslint.config(
  // -----------------------------------------------
  // Base Configuration and Ignores
  // -----------------------------------------------
  {
    ignores: [
      "dist/*",
      // Temporary compiled files
      "**/*.ts.build-*.mjs",

      // JS files at the root of the project
      "*.js",
      "*.cjs",
      "*.mjs",
      "src/pages/common/catalyst/*"
    ],
  },
  eslint.configs.recommended,
  ...tseslint.configs.recommended,
  {
    languageOptions: {
      parserOptions: {
        warnOnUnsupportedTypeScriptVersion: false,
        sourceType: "module",
        ecmaVersion: "latest",
        project: "./tsconfig.json",
        tsconfigRootDir: ".",
      },
    },
  },
  // -----------------------------------------------
  // Common Rules (for all files)
  // -----------------------------------------------
  {
    rules: {
      "consistent-return": 'error',
      "@typescript-eslint/no-unused-vars": [
        1,
        {
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_",
        },
      ],
      "@typescript-eslint/no-namespace": 'off',
      // Ensure promises are properly handled
      "no-return-await": "off", // Turn off base rule (recommended when using typescript-eslint)
    },
  },
  // -----------------------------------------------
  // TypeScript-specific rules (requiring type info)
  // -----------------------------------------------
  {
    files: ["**/*.ts", "**/*.tsx"],
    rules: {
      "@typescript-eslint/no-floating-promises": "error",
      "@typescript-eslint/return-await": "error", // Better alternative to no-return-await
      // Consider adding these useful type-aware rules (commented out by default)
      // "@typescript-eslint/no-unnecessary-condition": "warn",
      // "@typescript-eslint/no-unnecessary-type-assertion": "warn",
    },
  },
  // -----------------------------------------------
  // Default export rules exceptions
  // -----------------------------------------------
  {
    ignores: ["**/+Page.tsx", "**/+Head.tsx", "**/+Wrapper.tsx", "**/*config.ts", "src/hono-entry.ts", "docs/**"],
    rules: {
      'import/no-default-export': 'error',
    },
  },
  // -----------------------------------------------
  // React Configuration
  // -----------------------------------------------
  {
    files: ["**/*.{js,mjs,cjs,jsx,mjsx,ts,tsx,mtsx}"],
    ...react,
    languageOptions: {
      ...react.languageOptions,
      globals: {
        ...globals.serviceworker,
        ...globals.browser,
      },
    },

    settings: {
      react: {
        version: "detect",
        runtime: "automatic"
      },
    },
    rules: {
      "react/react-in-jsx-scope": "off", // Disabled for React 17+ auto import
      "react/jsx-uses-react": "off", // Disabled for new JSX transform
      "@typescript-eslint/no-unused-vars": [
        "warn",
        { 
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_"
        }
      ],
    }
  },

  // React Refresh rules for non-+Page files only
  {
    ignores: ["**/+Page.tsx", "**/+Head.tsx", "**/+Wrapper.tsx"],
    ...reactRefresh.configs.recommended,
  },
  
  // -----------------------------------------------
  // Styling and Formatting
  // -----------------------------------------------
  prettier,
  ...pluginQuery.configs['flat/recommended'],
  
  // -----------------------------------------------
  // Import Rules
  // -----------------------------------------------
  importPlugin.flatConfigs.recommended,
  {
    rules: {
      "import/order": ["error", {
        "groups": ["builtin", "external", "internal", "parent", "sibling", "index", "object", "type"],
        "newlines-between": "always", // Add separation between import groups
        "alphabetize": { "order": "asc", "caseInsensitive": true } // Sort imports alphabetically
      }],
    },
    settings: {
      "import/resolver": {
        // You will also need to install and configure the TypeScript resolver
        // See also https://github.com/import-js/eslint-import-resolver-typescript#configuration
        "typescript": true,
        "node": true,
      },
    },
  },
  // -----------------------------------------------
  // Module Boundaries
  // -----------------------------------------------
  {
    plugins: {
      boundaries,
    },
    settings: {
      "boundaries/elements": [
        {
          type: "ui:common",
          pattern: "src/pages/common"
        },
        {
          type: "ui:users",
          pattern: "src/pages/users"
        },
        {
          type: "ui:profiles",
          pattern: "src/pages/profiles"
        },
        {
          type: "server:common",
          pattern: "src/server/common"
        },
        {
          type: "server:masters",
          pattern: "src/server/masters"
        },
        {
          type: "server:users",
          pattern: "src/server/users"
        },
        {
          type: "server:profiles",
          pattern: "src/server/profiles"
        },
      ]
    },
    rules: {
      ...boundaries.configs.recommended.rules,
      "boundaries/element-types": ["error", {
        default: "disallow",
        rules: [
          {
            from: "ui:users",
            allow: ["ui:common", "server:users"]
          },
          {
            from: "ui:profiles",
            allow: ["ui:common", "ui:users", "server:profiles", "server:common"]
          },
          {
            from: "server:masters",
            allow: ["server:common"]
          },
          {
            from: "server:users",
            allow: ["server:common"]
          },
          {
            from: "server:profiles",
            allow: ["server:common", "server:users"]
          },
        ]
      }]
     }
  }
);

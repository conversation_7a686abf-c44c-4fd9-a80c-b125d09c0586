import { UUID } from 'crypto'

import { useMutation, useQueryClient, useSuspenseQuery } from '@tanstack/react-query'
import { useDeferredValue } from 'react'

import { CourseBatch } from '@/server/batches/batches/list/course-batches/listCourseBatches.server'
import {
  AddBatchEventForm,
  AddBatchForm,
  CourseBatchesSearch,
  EditBatchEventForm,
  EditBatchForm,
} from '@/shared/batch/batch-utils.shared'
import { isClient } from '@/shared/common/common-utils.shared'
import { useServerQuery } from '@/shared/common/serverQueries.shared'
import { useMyAcademyQuery } from '@ui/academies/common/academy-queries'
import { api, withError } from '@ui/api-client'
import { showNotification } from '@ui/common/notification/notification-store'
import { useInvalidateDetail } from '@ui/common/query-helper'
import { dateConverter } from '@ui/common/utils/dateConverter'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'
import { courseKeys } from '@ui/courses/common/queries/course-queries'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

import { studentFeePaymentKeys } from './student-fee-payment-queries'

export const batchKeys = {
  all: ['batch'] as const,
  lists: () => [...batchKeys.all, 'list'] as const,
  listCourseBatches: (courseId: UUID, search: CourseBatchesSearch, profileId?: UUID) =>
    [...batchKeys.lists(), 'course-batches', courseId, search, profileId] as const,
  listStudentBatches: (profileId?: UUID) => [...batchKeys.lists(), 'student-batches', profileId] as const,
  listTeacherBatches: (teacherId: UUID, currentProfileId?: UUID) =>
    [...batchKeys.lists(), 'teacher-batches', teacherId, currentProfileId] as const,
  details: () => [...batchKeys.all, 'detail'] as const,
  detail: (id: UUID) => [...batchKeys.details(), id] as const,
}

export const useAddBatchMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (form: AddBatchForm) => api().batches.$post({ json: form }),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: batchKeys.lists() })
    },
  })
}

export const useCourseBatchesSuspenseQuery = (courseId: UUID, search: CourseBatchesSearch) => {
  const { currentProfile } = useCurrentProfile()
  const listBatches = useServerQuery('listCourseBatches')
  const { data } = useSuspenseQuery({
    queryKey: batchKeys.listCourseBatches(courseId, search, currentProfile?.id),
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .courses[':id'].batches.$get({ param: { id: courseId }, query: search })
            .then((res) => res.json())
            .then((batches) => batches.rows)
            .then(dateConverter<CourseBatch[]>(['createdAt'])),
        )
      : listBatches(courseId, search, currentProfile?.id),
  })
  return useDeferredValue(data)
}

export const useTeacherBatchesSuspenseQuery = (teacherId: UUID) => {
  const { currentProfile } = useCurrentProfile()
  const listBatches = useServerQuery('listTeacherBatches')
  const { data } = useSuspenseQuery({
    queryKey: batchKeys.listTeacherBatches(teacherId, currentProfile?.id),
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .profiles[':id'].batches.$get({ param: { id: teacherId } })
            .then((res) => res.json())
            .then((batches) => batches.rows),
        )
      : listBatches(teacherId),
  })
  return useDeferredValue(data)
}

export const useBatchSuspenseQuery = (id: UUID) => {
  const { data: myAcademy } = useMyAcademyQuery()

  const getBatch = useServerQuery('getBatch')
  const { data } = useSuspenseQuery({
    queryKey: [...batchKeys.detail(id), myAcademy?.id],
    queryFn: () =>
      isClient() ?
        withError(
          api()
            .batches[':id'].$get({ param: { id } })
            .then((res) => res.json()),
        )
      : getBatch(id),
  })
  return useDeferredValue(data)
}

export const useUpdateBatchMutation = (id: UUID) => {
  const queryClient = useQueryClient()
  const invalidateBatch = useInvalidateDetail(batchKeys)

  return useMutation({
    mutationFn: (form: EditBatchForm) => api().batches[':id'].$put({ param: { id }, json: form }),
    onSuccess: () => {
      invalidateBatch(id)
      void queryClient.invalidateQueries({ queryKey: studentFeePaymentKeys.all })
    },
  })
}

export const useRemoveBatchMutation = (id: UUID) => {
  const invalidateBatch = useInvalidateDetail(batchKeys)

  return useMutation({
    mutationFn: () => api().batches[':id'].$delete({ param: { id } }),
    onSuccess: () => {
      invalidateBatch(id, true)
    },
  })
}

export const useAddBatchEventMutation = () => {
  const invalidateBatch = useInvalidateDetail(batchKeys)

  return useMutation({
    mutationFn: (form: AddBatchEventForm) => api()['batch-events'].$post({ json: form }),
    onSuccess: (_, form) => {
      invalidateBatch(form.batchId)
    },
  })
}

export const useUpdateBatchEventMutation = (batchId: UUID, eventId: UUID) => {
  const invalidateBatch = useInvalidateDetail(batchKeys)

  return useMutation({
    mutationFn: (form: EditBatchEventForm) => api()['batch-events'][':id'].$put({ param: { id: eventId }, json: form }),
    onSuccess: () => {
      invalidateBatch(batchId)
    },
  })
}

export const useRemoveBatchEventMutation = (batchId: UUID) => {
  const invalidateBatch = useInvalidateDetail(batchKeys)

  return useMutation({
    mutationFn: (eventId: UUID) => api()['batch-events'][':id'].$delete({ param: { id: eventId } }),
    onSuccess: () => {
      invalidateBatch(batchId)
    },
  })
}

export const useRecommendBatchMutation = (batchId: UUID, courseId: UUID) => {
  const invalidateCourses = useInvalidateDetail(courseKeys)
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: () => api().batches[':id'].recommend.$put({ param: { id: batchId } }),
    onSuccess: () => {
      invalidateCourses(courseId)
      void queryClient.invalidateQueries({ queryKey: batchKeys.all })
    },
    onError: (error) => {
      showNotification({
        heading: 'Error recommending batch',
        message: getErrorMessage(error),
        type: 'error',
      })
    },
  })
}

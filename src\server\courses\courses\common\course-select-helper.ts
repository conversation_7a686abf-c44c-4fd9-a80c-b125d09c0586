import { UUID } from 'crypto'

import { and, eq, sql } from 'drizzle-orm'

import { db } from '@/server/db/db'
import {
  batchRecommendationTable,
  courseAttachmentTable,
  courseTable,
  courseTagAssignmentTable,
  courseTagGroupTable,
  courseTagTable,
} from '@/server/db/schema'

const tagIdsSubquery = db
  .select({ tagId: courseTagAssignmentTable.tagId })
  .from(courseTagAssignmentTable)
  .innerJoin(courseTagTable, eq(courseTagTable.id, courseTagAssignmentTable.tagId))
  .innerJoin(courseTagGroupTable, eq(courseTagGroupTable.id, courseTagTable.groupId))
  .where(eq(courseTagAssignmentTable.courseId, courseTable.id))
  .orderBy(courseTagGroupTable.position, courseTagTable.position)

const attachmentSubquery = (isStaff: boolean) =>
  db
    .select({
      id: courseAttachmentTable.id,
      name: courseAttachmentTable.name,
      contentType: courseAttachmentTable.contentType,
      sizeBytes: courseAttachmentTable.sizeBytes,
      free: courseAttachmentTable.free,
      position: courseAttachmentTable.position,
    })
    .from(courseAttachmentTable)
    .where(
      and(
        eq(courseAttachmentTable.courseId, courseTable.id),
        eq(courseAttachmentTable.uploaded, true),
        isStaff ? undefined : eq(courseAttachmentTable.free, true),
      ),
    )
    .orderBy(courseAttachmentTable.position)

export type CourseAttachment = Awaited<ReturnType<typeof attachmentSubquery>>[number]

export const courseItemColumns = {
  id: courseTable.id,
  name: courseTable.name,
  publishedAt: courseTable.publishedAt,
  academyId: courseTable.academyId,
  recommendedBatchId: batchRecommendationTable.batchId,
  tagIds: sql<UUID[]>`coalesce(array(${tagIdsSubquery}), '{}'::uuid[])`,
}

export const courseDetailColumns = (isStaff: boolean) => ({
  ...courseItemColumns,
  descr: courseTable.descr,
  attachments: sql<CourseAttachment[]>`coalesce((SELECT json_agg(
              json_build_object(
                'id', attachment.id,
                'name', attachment.name,
                'contentType', attachment.content_type,
                'sizeBytes', attachment.size_bytes,
                'free', attachment.free,
                'position', attachment.position
              )
            ) FROM (${attachmentSubquery(isStaff)}) AS attachment), '[]')`,
})

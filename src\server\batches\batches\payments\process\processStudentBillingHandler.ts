import { Hono } from 'hono'

import { env } from '@/server/common/env.server'
import { ensure } from '@/server/common/error/ensure.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { CRON_AUTH_KEY_HEADER } from '@/shared/common/common-utils.shared'

import { processStudentBilling } from './processStudentBilling'

export const processStudentBillingHandler = new Hono<HonoVars>().post('/', async (c) => {
  const authKey = c.req.header(CRON_AUTH_KEY_HEADER)
  ensure(authKey === env.CRON_AUTH_KEY, {
    statusCode: 401,
    issueMessage: 'Invalid cron auth key',
    logMessage: 'Invalid cron auth key',
  })

  void processStudentBilling(c.get('log'))
  return c.body(null, 202) // Accepted
})

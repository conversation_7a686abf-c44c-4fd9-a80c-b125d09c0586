import { DateTime } from 'luxon'
import { afterEach, describe, expect, test, vi } from 'vitest'

import * as dateUtils from '../date-utils-basic.shared'

import { getPaymentReminderTypeToSend } from './payment-utils.shared'

// Helper to create a DateTime object for mocking utc() with a specific date at T00:00:00Z
const createUtcDateTime = (dateStr: string): DateTime<true> => {
  return DateTime.fromISO(`${dateStr}T00:00:00Z`, { zone: 'utc' }) as DateTime<true>
}

describe('getPaymentReminderTypeToSend', () => {
  afterEach(() => {
    vi.restoreAllMocks()
  })

  test('should return null if nextDueOn is null (invoice fully paid)', () => {
    // utc() is not called if nextDueOn is null, so no mock needed here.
    const result = getPaymentReminderTypeToSend(null, 5) // gracePeriod value is irrelevant
    expect(result).toBeNull()
  })

  // Comprehensive test cases covering all logic paths and reminder types
  const testCases = [
    // 'blocked': daysToDue + gracePeriod <= 0
    { daysToDue: -4, gracePeriod: 3, expected: 'blocked', description: 'due 4 days ago, 3 days grace' },
    {
      daysToDue: -3,
      gracePeriod: 3,
      expected: 'blocked',
      description: 'due 3 days ago, 3 days grace (at blocked threshold)',
    },
    {
      daysToDue: 0,
      gracePeriod: 0,
      expected: 'blocked',
      description: 'due today, 0 days grace (at blocked threshold)',
    },
    { daysToDue: -1, gracePeriod: 0, expected: 'blocked', description: 'due 1 day ago, 0 days grace' },

    // 'urgent': 0 < daysToDue + gracePeriod <= 1
    {
      daysToDue: -2,
      gracePeriod: 3,
      expected: 'urgent',
      description: 'due 2 days ago, 3 days grace (at urgent threshold)',
    },
    {
      daysToDue: 1,
      gracePeriod: 0,
      expected: 'urgent',
      description: 'due in 1 day, 0 days grace (at urgent threshold)',
    },
    { daysToDue: -4, gracePeriod: 5, expected: 'urgent', description: 'due 4 days ago, 5 days grace' },

    // 'last': daysToDue <= 0 AND daysToDue + gracePeriod > 1
    { daysToDue: -1, gracePeriod: 3, expected: 'last', description: 'due 1 day ago, 3 days grace' },
    { daysToDue: 0, gracePeriod: 3, expected: 'last', description: 'due today, 3 days grace' },
    {
      daysToDue: 0,
      gracePeriod: 2,
      expected: 'last',
      description: 'due today, 2 days grace (at last threshold for daysToDue)',
    },

    // 'prudent': daysToDue = 1 AND gracePeriod > 0 (so daysToDue + gracePeriod > 1)
    { daysToDue: 1, gracePeriod: 3, expected: 'prudent', description: 'due in 1 day, 3 days grace' },
    {
      daysToDue: 1,
      gracePeriod: 1,
      expected: 'prudent',
      description: 'due in 1 day, 1 day grace (at prudent threshold for grace)',
    },

    // 'gentle': 1 < daysToDue <= 3 AND daysToDue + gracePeriod > 1
    { daysToDue: 2, gracePeriod: 3, expected: 'gentle', description: 'due in 2 days, 3 days grace' },
    {
      daysToDue: 3,
      gracePeriod: 3,
      expected: 'gentle',
      description: 'due in 3 days, 3 days grace (at gentle threshold for daysToDue)',
    },
    { daysToDue: 2, gracePeriod: 0, expected: 'gentle', description: 'due in 2 days, 0 days grace' },
    {
      daysToDue: 3,
      gracePeriod: 0,
      expected: 'gentle',
      description: 'due in 3 days, 0 days grace (at gentle threshold for daysToDue)',
    },

    // null (no reminder): daysToDue > 3 (AND other conditions not met)
    { daysToDue: 4, gracePeriod: 3, expected: null, description: 'due in 4 days, 3 days grace' },
    { daysToDue: 4, gracePeriod: 0, expected: null, description: 'due in 4 days, 0 days grace (at null threshold)' },
    { daysToDue: 10, gracePeriod: 5, expected: null, description: 'due in 10 days, 5 days grace' },

    // Cases demonstrating handling of fractional daysToDue, reflecting precise calculation
    { daysToDue: 0.5, gracePeriod: 0, expected: 'urgent', description: 'due in 0.5 days (12 hours), 0 grace' },
    { daysToDue: -0.5, gracePeriod: 2, expected: 'last', description: 'due 0.5 days (12 hours) ago, 2 days grace' },
    { daysToDue: 1.5, gracePeriod: 1, expected: 'gentle', description: 'due in 1.5 days (36 hours), 1 day grace' },
    {
      daysToDue: 3.5,
      gracePeriod: 0,
      expected: null,
      description: 'due in 3.5 days (e.g. 3 days and 12 hours), 0 grace',
    },
  ]

  // Test with a few different "today" dates to ensure logic is purely relative
  describe.each([
    { todayDate: '2024-07-15', context: 'standard date' },
    { todayDate: '2023-02-28', context: 'non-leap year end of Feb' },
    { todayDate: '2024-02-29', context: 'leap day' },
  ])('when today is $todayDate ($context)', ({ todayDate }) => {
    test.each(testCases)(
      'should return "$expected" for: $description (daysToDue: $daysToDue, grace: $gracePeriod)',
      ({ daysToDue, gracePeriod, expected }) => {
        const mockedCurrentUtc = createUtcDateTime(todayDate)
        vi.spyOn(dateUtils, 'utc').mockReturnValue(mockedCurrentUtc)

        const nextDueOn = mockedCurrentUtc.plus({ days: daysToDue })
        const result = getPaymentReminderTypeToSend(nextDueOn, gracePeriod)
        expect(result).toBe(expected)
      },
    )
  })
})

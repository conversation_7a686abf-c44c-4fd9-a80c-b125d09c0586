TuitionLance.com is a platform for academies to offer live online tuition to students.

# Why TuitionLance

## Academy Perspective

- **Seamless Online Learning**: TuitionLance bridges the gap between traditional and digital education with scheduled live classes and interactive learning sessions.

- **Role-Based Administration**: Comprehensive role management system allows academies to efficiently organize principals, mentors, and teachers with appropriate permissions.

- **Personalized Learning Experience**: Small batch sizes enable individualized attention and targeted feedback on MCQs and assignments.

- **Flexible Resource Management**: Gradually release course materials as batches progress, ensuring students access content at the right time in their learning journey.

- **Simplified Payments**: Automated fee collection with grace periods helps academies manage finances while ensuring fair access to education.

- **Accessible & Scalable**: Designed for academies of all sizes with an intuitive interface that can handle numerous courses, batches, and students without compromising performance.

- **Secure & Reliable**: Robust architecture ensures student data protection and consistent platform availability.

## Student Perspective

- **Personalized Learning**: Small batch sizes enable individualized attention and targeted feedback on MCQs and assignments.

- **Convenient Access**: Attend quality classes from anywhere, eliminating commute time and transportation costs associated with physical tuition centers.

- **Geographic Freedom**: Access premier academies regardless of location, breaking down geographical barriers to quality education.

- **Organized Learning Schedule**: Stay organized with structured class schedules and batch timings that help you maintain a consistent learning routine.

- **Cost-Effective Education**: Reduced overhead costs for academies translate to more affordable tuition fees without sacrificing quality.

- **Progressive Learning Resources**: Access to course materials that are released strategically as the batch progresses, enhancing the learning experience.

- **Interactive Experience**: Engage directly with teachers in real-time through live sessions, providing a more dynamic learning environment than pre-recorded content.

- **Social Learning**: Connect with peers in small batches, facilitating collaborative learning while maintaining individual attention from teachers.

- **Transparent Fee Structure**: Clear periodic payment system with grace periods ensures students understand financial commitments upfront.

# Key features

1. Users can sign up (using Google login) and create multiple profiles.
2. Each profile has a specific role. Roles include:
    1. Principal
    2. Mentor
    3. Teacher
    4. Student
    5. Admin
    6. Manager
    7. Executive
3. Each principal, mentor, and teacher is associated with one and only one academy. If a person works in multiple academies, they must create separate profiles for each academy.
4. Students are not associated with any academy.
5. Admin, manager, and executive profiles are not associated with any academy. They are employees of TuitionLance who help manage the platform.
6. Student profiles can be freely created by anyone. Other profiles require an invitation. Here is a table showing who can invite whom:
   | Role | Can Invite |
   | ---- | ---------- |
   | Principal | Principal, Mentor, Teacher |
   | Mentor | *None* |
   | Teacher | *None* |
   | Student | *None* (auto-approved) |
   | Admin | Principal, Admin, Manager, Executive |
   | Manager | Principal, Manager, Executive |
   | Executive | *None* |
7. After receiving an invitation, users create their desired profile, fill in details, and submit for approval.   
8. The invitor is responsible for approving the invitee and providing support.
9. The term "staff" refers to principals, mentors, or teachers of an academy.
10. Staff can create courses and batches. Each course can have zero or more batches.
11. Students can join and leave batches at any time.
12. Each batch has scheduled live classes and learning sessions. Students who join a batch can attend these sessions. When a student leaves a batch, they lose access to batch resources and sessions.
13. Each course has a list of MCQs and attachment files, called "resources." Some resources are publicly visible, while others are restricted.
14. As a batch progresses, academy staff will gradually add restricted resources to the batch. Students in the batch can then view and download these resources.
15. Each batch has a periodic fee paid by students to the academy. If a student hasn't paid the fee for a period even after the grace period has expired, the student is blocked from accessing the batch.

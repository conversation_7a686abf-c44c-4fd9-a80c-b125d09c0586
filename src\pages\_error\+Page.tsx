import { usePageContext } from 'vike-react/usePageContext'

import { getLogger } from '@/shared/common/logger.shared'
import { ErrorAlert } from '@ui/common/alerts/ErrorAlert'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'

export default () => {
  const { is404, abortReason, abortStatusCode } = usePageContext()
  const log = getLogger()
  log.error('Error loading page', {
    is404,
    abortReason,
    abortStatusCode,
  })
  if (is404) {
    return <ErrorAlert>Page not found.</ErrorAlert>
  }
  return <ErrorAlert>{getErrorMessage(abortReason)}</ErrorAlert>
}

import pino from 'pino'

import { masked } from '@/shared/common/string-utils.shared'

import { env } from '../../env.server'
import { ensure } from '../../error/ensure.server'

// https://developers.facebook.com/apps/679608021689255/whatsapp-business/wa-dev-console/?business_id=1911099383033726
const PHONE_NUMBER_ID = '599105303295873'

export type SmsDetail = {
  to: string
  headerParams?: Record<string, string>
  bodyParams?: Record<string, string>
  additionalComponents?: unknown[]
}

export const sendWhatsAppMessage = async (log: pino.Logger, template: string, detail: SmsDetail) => {
  const url = `https://graph.facebook.com/v22.0/${PHONE_NUMBER_ID}/messages`
  const bearerToken = env.META_ACCESS_TOKEN

  const headerParams = Object.entries(detail.headerParams ?? {}).map(([key, value]) => ({
    type: 'text',
    parameter_name: key,
    text: value,
  }))

  const bodyParams = Object.entries(detail.bodyParams ?? {}).map(([key, value]) => ({
    type: 'text',
    parameter_name: key,
    text: value,
  }))

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${bearerToken}`,
    },
    body: JSON.stringify({
      messaging_product: 'whatsapp',
      recipient_type: 'individual',
      to: detail.to,
      type: 'template',
      template: {
        name: template,
        language: {
          code: 'en_US',
        },
        components: [
          {
            type: 'header',
            parameters: headerParams,
          },
          {
            type: 'body',
            parameters: bodyParams,
          },
          ...(detail.additionalComponents ?? []),
        ],
      },
    }),
  })

  const responseData = await response.json()
  const whatsAppRespone = { status: response.status, payload: responseData }
  ensure(response.ok, {
    statusCode: 500,
    issueMessage: `Failed to send WhatsApp message to ${masked(detail.to)}. Please try again later.`,
    logMessage: `Failed to send WhatsApp message to ${detail.to}: ${JSON.stringify(whatsAppRespone)}`,
  })
  log.info({ whatsAppRespone }, 'WhatsApp message sent')
  return responseData
}

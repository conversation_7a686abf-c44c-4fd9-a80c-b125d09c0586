import { UUID } from 'crypto'

import { and, eq, or, sql } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfile } from '@/server/common/error/ensure-profile.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { academyStaffTable } from '@/server/db/schema'
import { studentFeePaymentItemTable, studentFeePaymentTable } from '@/server/db/schema/batch-schema'
import { withLog } from '@/shared/common/withLog.shared'

const itemSubquery = db
  .select({
    id: studentFeePaymentItemTable.id,
    batchId: studentFeePaymentItemTable.batchId,
    cycle: studentFeePaymentItemTable.cycle,
    fee: studentFeePaymentItemTable.fee,
  })
  .from(studentFeePaymentItemTable)
  .where(eq(studentFeePaymentItemTable.studentFeePaymentId, studentFeePaymentTable.id))
  .orderBy(studentFeePaymentItemTable.createdAt)

export type StudentFeePaymentDetailItem = Awaited<typeof itemSubquery>[number]

const findStudentFeePaymentByIdDaf = (log: pino.Logger, studentFeePaymentId: UUID, profileId: UUID) =>
  withLog(log, 'findStudentFeePaymentByIdDaf', () =>
    db
      .select({
        id: studentFeePaymentTable.id,
        studentId: studentFeePaymentTable.studentId,
        academyId: studentFeePaymentTable.academyId,
        method: studentFeePaymentTable.method,
        currency: studentFeePaymentTable.currency,
        cents: studentFeePaymentTable.cents,
        status: studentFeePaymentTable.status,
        createdAt: studentFeePaymentTable.createdAt,
        paidAt: studentFeePaymentTable.paidAt,
        paidRemarks: studentFeePaymentTable.paidRemarks,
        items: sql<StudentFeePaymentDetailItem[]>`coalesce((SELECT json_agg(
              json_build_object(
                'id', item.id,
                'batchId', item.batch_id,
                'cycle', item.cycle,
                'fee', item.fee
              )
            ) FROM (${itemSubquery}) AS item), '[]')`,
      })
      .from(studentFeePaymentTable)
      .innerJoin(academyStaffTable, eq(academyStaffTable.academyId, studentFeePaymentTable.academyId))
      .where(
        and(
          eq(studentFeePaymentTable.id, studentFeePaymentId),
          or(eq(studentFeePaymentTable.studentId, profileId), eq(academyStaffTable.profileId, profileId)),
        ),
      ),
  )

export const getStudentFeePayment = async (c: Ctx, studentFeePaymentId: UUID) => {
  ensureUser(c.user)
  ensureProfile(c.profile)

  const [studentFeePayment] = await findStudentFeePaymentByIdDaf(c.log, studentFeePaymentId, c.profile.id)
  ensureExists(studentFeePayment, studentFeePaymentId, 'Student Fee Payment')
  return studentFeePayment
}

export type StudentFeePaymentDetail = Awaited<ReturnType<typeof getStudentFeePayment>>

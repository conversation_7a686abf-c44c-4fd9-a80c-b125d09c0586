import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $AddAcademyForm } from '@/shared/academies/academy-utils.shared'

import { addAcademy } from './addAcademy'

export const addAcademyHandler = new Hono<HonoVars>().post('/', zValidator('json', $AddAcademyForm), async (c) => {
  const form = c.req.valid('json')

  const result = await addAcademy(getCtx(c), form)
  return c.json(result, 201)
})

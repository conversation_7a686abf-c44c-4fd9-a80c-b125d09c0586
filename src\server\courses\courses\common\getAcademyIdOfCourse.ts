import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { ensureExists } from '@/server/common/error/ensureExists'
import { db } from '@/server/db/db'
import { courseTable } from '@/server/db/schema/course-schema'
import { withLog } from '@/shared/common/withLog.shared'

const findCourseDaf = (log: pino.Logger, courseId: UUID) =>
  withLog(log, 'findCourseDaf', () =>
    db.query.courseTable.findFirst({
      columns: {
        academyId: true,
      },
      where: and(eq(courseTable.id, courseId)),
    }),
  )

export const getAcademyIdOfCourse = async (log: pino.Logger, courseId: UUID) => {
  const course = await findCourseDaf(log, courseId)
  ensureExists(course, courseId, 'Course')
  return course.academyId
}

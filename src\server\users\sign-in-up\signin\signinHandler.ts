import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { HonoVars } from '@/server/common/hono-utils.server'
import { $SignInForm } from '@/shared/users/user-utils.shared'

import { signin } from './signin'

export const signInHandler = new Hono<HonoVars>().post('/', zValidator('json', $SignInForm), async (c) => {
  const form = c.req.valid('json')
  const acceptLanguage = c.req.header('accept-language')
  const token = await signin(c.get('log'), form, acceptLanguage)
  c.status(200)
  return c.json(token)
})

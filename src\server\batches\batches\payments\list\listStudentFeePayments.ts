import { UUID } from 'crypto'

import { and, asc, desc, eq, gt, lt, or } from 'drizzle-orm'
import { DateTime } from 'luxon'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfileWithRole } from '@/server/common/error/ensure-profile.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { studentFeePaymentTable } from '@/server/db/schema'
import { StudentFeePaymentsSearch } from '@/shared/batch/batch-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

const DEFAULT_STUDENT_FEE_PAYMENTS_PAGE_SIZE = 20

const pageFilter = (studentId: UUID, search: StudentFeePaymentsSearch) => {
  const firstOrLastPage = !search.beyondId // if no beyondId and `previous` is true, its the last page
  if (firstOrLastPage) return eq(studentFeePaymentTable.studentId, studentId)

  // Since beyondId exists, fromAcademyId and fromCreatedAt must also exist (set by frontend)
  const cursorAcademyId = search.fromAcademyId!
  const cursorCreatedAt = DateTime.fromISO(search.fromCreatedAt!)

  if (search.previous === 'true') {
    // Backward pagination - get rows that come before the current cursor
    // With compound ordering academyId DESC, createdAt ASC, id DESC, get rows with:
    //   1. academyId < cursor.academyId, OR
    //   2. academyId = cursor.academyId AND createdAt > cursor.createdAt, OR
    //   3. academyId = cursor.academyId AND createdAt = cursor.createdAt AND id < cursor.id
    return or(
      lt(studentFeePaymentTable.academyId, cursorAcademyId),
      and(
        eq(studentFeePaymentTable.academyId, cursorAcademyId),
        // Note: postgres.js driver truncates JavaScript dates to milliseconds.
        // So, unless we add 1 millisecond, gt(studentFeePaymentTable.createdAt, cursorCreatedAt) will be true even for the same value.
        gt(studentFeePaymentTable.createdAt, cursorCreatedAt.plus({ millisecond: 1 }).toJSDate()),
      ),
      and(
        eq(studentFeePaymentTable.academyId, cursorAcademyId),
        eq(studentFeePaymentTable.createdAt, cursorCreatedAt.toJSDate()),
        lt(studentFeePaymentTable.id, search.beyondId!),
      ),
    )
  }

  // Forward pagination - get rows that come after the current cursor
  // With compound ordering academyId ASC, createdAt DESC, id ASC, get rows with:
  //   1. academyId > cursor.academyId, OR
  //   2. academyId = cursor.academyId AND createdAt < cursor.createdAt, OR
  //   3. academyId = cursor.academyId AND createdAt = cursor.createdAt AND id > cursor.id

  // Note: postgres.js driver truncates JavaScript dates to milliseconds.
  // So, unless we subtract 1 millisecond, lt(studentFeePaymentTable.createdAt, cursorCreatedAt) will be true even for the same value.
  const cursorCreatedAtMinus1 = cursorCreatedAt.plus({ millisecond: -1 }).toJSDate()

  return or(
    gt(studentFeePaymentTable.academyId, cursorAcademyId),
    and(
      eq(studentFeePaymentTable.academyId, cursorAcademyId),
      lt(studentFeePaymentTable.createdAt, cursorCreatedAtMinus1),
    ),
    and(
      eq(studentFeePaymentTable.academyId, cursorAcademyId),
      eq(studentFeePaymentTable.createdAt, cursorCreatedAtMinus1),
      gt(studentFeePaymentTable.id, search.beyondId!),
    ),
  )
}

const findStudentFeePaymentsDaf = (log: pino.Logger, studentId: UUID, search: StudentFeePaymentsSearch) => {
  const orderBy =
    search.previous === 'true' ?
      [desc(studentFeePaymentTable.academyId), asc(studentFeePaymentTable.createdAt), desc(studentFeePaymentTable.id)]
    : [asc(studentFeePaymentTable.academyId), desc(studentFeePaymentTable.createdAt), asc(studentFeePaymentTable.id)]

  return withLog(log, 'findStudentFeePaymentsDaf', () => {
    return db
      .select({
        id: studentFeePaymentTable.id,
        academyId: studentFeePaymentTable.academyId,
        method: studentFeePaymentTable.method,
        currency: studentFeePaymentTable.currency,
        cents: studentFeePaymentTable.cents,
        status: studentFeePaymentTable.status,
        createdAt: studentFeePaymentTable.createdAt,
      })
      .from(studentFeePaymentTable)
      .where(and(eq(studentFeePaymentTable.studentId, studentId), pageFilter(studentId, search)))
      .orderBy(...orderBy)
      .limit(search.pageSize ? parseInt(search.pageSize) : DEFAULT_STUDENT_FEE_PAYMENTS_PAGE_SIZE)
  })
}

export const listStudentFeePayments = async (c: Ctx, search: StudentFeePaymentsSearch) => {
  ensureUser(c.user)
  ensureProfileWithRole('student', c.profile)
  const payments = await findStudentFeePaymentsDaf(c.log, c.profile.id, search)
  if (search.previous === 'true') payments.reverse()
  return payments
}

export type StudentFeePayment = Awaited<ReturnType<typeof findStudentFeePaymentsDaf>>[number]

import { useEffect, useState } from 'react'
import { navigate } from 'vike/client/router'

import { AcademyCommonEditor } from '@/pages/academies/common/AcademyCommonEditor'
import { $AddAcademyForm, EditAcademyForm, type AddAcademyForm } from '@/shared/academies/academy-utils.shared'
import { UNKNOWN_UUID } from '@/shared/common/common-utils.shared'
import { type OmniError } from '@/shared/common/error-utils.shared'
import { WarningAlert } from '@ui/common/alerts/WarningAlert'
import { CheckBox } from '@ui/common/form/CheckBox'
import { FormButtons, PageContent, PageLayout, PageSection } from '@ui/common/form/page-layout'
import { ShowData } from '@ui/common/ShowData'
import { WithProfile } from '@ui/profiles/common/WithProfile'

import { useAddAcademyMutation, useMyAcademyQuery } from '../common/academy-queries'

export const AddAcademyPage = () => {
  const { data: myAcademy, isPending: myAcademyIsPending, error: myAcademyError } = useMyAcademyQuery()

  // Form state
  const [formData, setFormData] = useState<Required<AddAcademyForm>>({
    name: '',
    descr: '',
    email: '',
    mobileCountryCode: 'IN',
    mobile: '',
    currency: 'INR',
    upiId: '',
    tradeName: '',
    gstin: '',
    districtId: UNKNOWN_UUID,
    pincode: '',
    area: '',
    addressLine1: '',
    addressLine2: '',
    tncsAccepted: false,
  })

  // Error state
  const [omniError, setOmniError] = useState<OmniError | undefined>()

  // Add academy mutation
  const { mutate: addAcademy, isPending: addAcademyIsPending } = useAddAcademyMutation()

  // Validate form data when it changes
  useEffect(() => {
    const parsed = $AddAcademyForm.safeParse(formData)
    setOmniError(parsed.error)
  }, [formData])

  // Form submission handler
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    addAcademy(formData, {
      onSuccess: (academy) => {
        void navigate(`/academies/${academy.id}`)
      },
      onError: (error) => {
        setOmniError(error.error)
      },
    })
  }

  return (
    <WithProfile roleAnyOf={['principal']}>
      <ShowData isPending={myAcademyIsPending} error={myAcademyError} spinnerSize="1.25rem">
        {myAcademy ?
          <WarningAlert>You already have an academy.</WarningAlert>
        : <PageLayout title="Add an Academy" description="Please fill in the details to create a new academy">
            <form onSubmit={handleSubmit}>
              <PageContent>
                <AcademyCommonEditor
                  formData={formData}
                  setFormData={setFormData as (formData: Required<EditAcademyForm>) => void}
                  omniError={omniError}
                />

                <PageSection title="Terms and Conditions" description="Please review the terms and conditions">
                  <div className="mt-6 space-y-6">
                    <div className="text-sm text-gray-600">
                      By creating an academy, you agree to our{' '}
                      <a href="/legal-agreements" target="_blank" className="link">
                        Terms of Service and Privacy Policy
                      </a>
                      . Please review the sections specifically related to creating and managing academies. By
                      proceeding, you confirm that you have read, understood, and agree to be bound by these terms,
                      including any recent updates. Your use of this feature constitutes your ongoing acceptance of
                      these terms.
                    </div>

                    <CheckBox
                      id="tncsAccepted"
                      checked={formData.tncsAccepted}
                      onChange={(e) => setFormData((prev) => ({ ...prev, tncsAccepted: e.target.checked }))}
                      label="I confirm that I have read, understood, and agree to the Terms of Service and Privacy Policy sections related to creating and managing academies."
                    />
                  </div>
                </PageSection>

                <FormButtons
                  omniError={omniError}
                  onCancel={() => navigate('/')}
                  otherErrors={!formData.tncsAccepted}
                  isSubmitting={addAcademyIsPending}
                />
              </PageContent>
            </form>
          </PageLayout>
        }
      </ShowData>
    </WithProfile>
  )
}

import { UUID } from 'crypto'

import { sql } from 'drizzle-orm'

import { batchEventTable, batchRecommendationTable, batchTable } from '@/server/db/schema'
import { EventType } from '@/shared/batch/batch-utils.shared'
import { Time } from '@/shared/common/common-utils.shared'
import { WeekDay } from '@/shared/common/date-utils.shared'

type BatchEvent = {
  id: UUID
  at: Time
  durationMinutes: number
  days: WeekDay[]
  eventType: EventType
  eventXid: string
}

// Define the events query as a reusable SQL fragment
export const eventsQuery = sql<BatchEvent[]>`
  SELECT coalesce(
    json_agg(
      json_build_object(
        'id', ${batchEventTable.id},
        'at', ${batchEventTable.at},
        'durationMinutes', ${batchEventTable.durationMinutes},
        'days', ${batchEventTable.days},
        'eventType', ${batchEventTable.eventType},
        'eventXid', ${batchEventTable.eventXid}
      )
      order by ${batchEventTable.at}
    ),
    '[]'::json
  )
  FROM ${batchEventTable}
  WHERE ${batchEventTable.batchId} = ${batchTable.id}
`

export const batchColumns = {
  id: batchTable.id,
  courseId: batchTable.courseId,
  teacherId: batchTable.teacherId,
  startDate: batchTable.startDate,
  timezone: batchTable.timezone,
  seatCount: batchTable.seatCount,
  admissionOpen: batchTable.admissionOpen,
  fee: batchTable.fee,
  billingCycle: batchTable.billingCycle,
  cycleCount: batchTable.cycleCount,
  graceDays: batchTable.graceDays,
  events: sql<BatchEvent[]>`(${eventsQuery})`,
  recommended: sql<boolean>`(${batchRecommendationTable.batchId} is not null)`,
  over: batchTable.over,
} as const

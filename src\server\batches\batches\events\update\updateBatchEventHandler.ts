import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $EditBatchEventForm } from '@/shared/batch/batch-utils.shared'
import { $HasId } from '@/shared/common/common-utils.shared'

import { updateBatchEvent } from './updateBatchEvent'

export const updateBatchEventHandler = new Hono<HonoVars>().put(
  '/',
  zValidator('param', $HasId),
  zValidator('json', $EditBatchEventForm),
  async (c) => {
    const { id } = c.req.valid('param')
    const form = c.req.valid('json')
    const ctx = getCtx(c)

    await updateBatchEvent(ctx, id, form)
    return c.body(null, 204)
  },
)

import { UUID } from 'crypto'

import { z } from 'zod'

import { $Date, $PageSearch, $UUID, DeploymentEnv } from '../common/common-utils.shared'
import { s3Url } from '../common/s3-utils.shared'
import { HIGHEST_CHAR, LOWEST_CHAR } from '../common/str-math.shared'
import { slugOf } from '../common/string-utils.shared'

export const COURSE_NAME_MAX_LEN = 100
export const COURSE_DESCR_MAX_LEN = 5000
export const COURSE_ATTACHMENT_NAME_MAX_LEN = 1000
export const COURSE_ATTACHMENT_CONTENT_TYPE_MAX_LEN = 255

/**
 * Constants for managing attachment positions
 *
 * Using two-character length for MIN and MAX positions.
 * This balances readability while providing enough range for 100+ attachments.
 */
export const COURSE_ATTACHMENT_MIN_POSITION = LOWEST_CHAR.repeat(3)
export const COURSE_ATTACHMENT_MAX_POSITION = HIGHEST_CHAR.repeat(3)

export const $EditCourseForm = z.object({
  name: z.string().trim().min(3).max(COURSE_NAME_MAX_LEN),
  descr: z.string().trim().max(COURSE_DESCR_MAX_LEN).optional(),
})
export type EditCourseForm = z.infer<typeof $EditCourseForm>

export const $AddCourseForm = $EditCourseForm.extend({
  academyId: $UUID,
})

export type AddCourseForm = z.infer<typeof $AddCourseForm>
export const MAX_COURSES_PER_ACADEMY = 500

export const $CoursesSearch = $PageSearch.extend({
  academyId: $UUID.optional(), // custom filter field
  includeUnpublished: z.string().optional(), // custom filter field
  fromStartDate: $Date.optional(), // for cursor-based pagination
  beyondId: $UUID.optional(), // for cursor-based pagination
})

export type CoursesSearch = z.infer<typeof $CoursesSearch>

export const coursePath = (course: { id: UUID; name: string }) => `/courses/${slugOf(course.name)}-${course.id}`

export const $EditCourseAttachmentForm = z.object({
  name: z.string().trim().min(1).max(COURSE_ATTACHMENT_NAME_MAX_LEN),
  free: z.boolean(),
})
export type EditCourseAttachmentForm = z.infer<typeof $EditCourseAttachmentForm>

export const $AddCourseAttachmentForm = $EditCourseAttachmentForm.extend({
  contentType: z.string().trim().min(1).max(COURSE_ATTACHMENT_CONTENT_TYPE_MAX_LEN),
  sizeBytes: z.number().int().positive(),
})
export type AddCourseAttachmentForm = z.infer<typeof $AddCourseAttachmentForm>

export const courseAttachmentS3Path = (env: DeploymentEnv, attachmentId: UUID) =>
  `${env}/course-attachments/${attachmentId}`

export const courseAttachmentUrl = (env: DeploymentEnv, attachmentId: UUID) =>
  s3Url(courseAttachmentS3Path(env, attachmentId))

export const $MoveCourseAttachmentForm = z.object({
  beforePosition: z.string().trim().min(1).max(200),
})
export type MoveCourseAttachmentForm = z.infer<typeof $MoveCourseAttachmentForm>

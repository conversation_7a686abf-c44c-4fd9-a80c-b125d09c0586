import { Hono } from 'hono'
import { ContentfulStatusCode } from 'hono/utils/http-status'
import { Logger } from 'pino'
import { render } from 'vike/abort'
import { renderPage } from 'vike/server'

import { toErrorPayload } from '@/server/middleware/error/toErrorPayload'

import { HonoVars } from './server/common/hono-utils.server'

// Extend PageContext to include our logger
declare global {
  namespace Vike {
    interface PageContext {
      log: Logger
    }
  }
}

// TODO: Try to improve by referring to https://vike.dev/streaming#basics and https://vike.dev/streaming#api
export const vikeHandler = new Hono<HonoVars>().get(async (c, next) => {
  const log = c.get('log').child({ vikeHandler: true })
  const pageContextInit = {
    urlOriginal: c.req.url,
    headersOriginal: c.req.header(),
    log,
  }

  try {
    const pageContext = await renderPage(pageContextInit)
    const { httpResponse } = pageContext

    if (!httpResponse) {
      return await next()
    }

    // Set status code and headers
    const { statusCode, headers } = httpResponse
    headers.forEach(([name, value]) => c.header(name, value))
    c.status(statusCode)

    // Handle streaming response
    if (httpResponse.pipe) {
      const { readable, writable } = new TransformStream()
      httpResponse.pipe(writable)
      return c.newResponse(readable)
    }

    // Handle non-streaming response
    if (httpResponse.body) {
      return c.body(httpResponse.body)
    }

    return c.body(null)
  } catch (error) {
    const payload = toErrorPayload(log, error as Error)
    const status = getAbortStatusCode(payload.status)
    throw render(status, payload)
  }
})

type AbortStatusCode = 401 | 403 | 404 | 410 | 429 | 500 | 503

function getAbortStatusCode(statusCode: ContentfulStatusCode): AbortStatusCode {
  if ([401, 403, 404, 429, 500, 503].includes(statusCode)) return statusCode as AbortStatusCode
  return statusCode >= 500 ? 500 : 403
}

/* Three types of configration:

// Without streaming:

import { Hono } from 'hono'
import { renderPage } from 'vike/server'
import { Logger } from 'pino'
import { HonoVars } from './server/common/hono-utils.server'

// Extend PageContext to include our logger
declare global {
  namespace Vike {
    interface PageContext {
      log: Logger
    }
  }
}

 // https://github.com/phonzammi/vike-hono-example/blob/main/server/index.ts
export const vikeHandler = new Hono<HonoVars>().get(async (c, next) => {
  const pageContextInit = {
    headersOriginal: c.req.header(),
    urlOriginal: c.req.url,
    log: c.get('log'),
  }
  const pageContext = await renderPage(pageContextInit)
  const { httpResponse } = pageContext
  if (!httpResponse) {
    return next()
  } else {
    const { body, statusCode, headers } = httpResponse
    headers.forEach(([name, value]) => c.header(name, value))
    c.status(statusCode)

    return c.body(body)
  }
})

// With streaming enabled, e.g. setting `extends: [vikeReact, vikeReactQuery]` in +config.ts:

// With Node.js Stream:

import { Readable, Writable } from 'node:stream'
import { pipeline } from 'node:stream/promises'
import { Hono } from 'hono'
import { renderPage } from 'vike/server'
import { Logger } from 'pino'
import { HonoVars } from './server/common/hono-utils.server'

// Extend PageContext to include our logger
declare global {
  namespace Vike {
    interface PageContext {
      log: Logger
    }
  }
}

export const vikeHandler = new Hono<HonoVars>().get(async (c, next) => {
  const pageContextInit = {
    headersOriginal: c.req.header(),
    urlOriginal: c.req.url,
    log: c.get('log'),
  }
  const pageContext = await renderPage(pageContextInit)
  const { httpResponse } = pageContext

  if (!httpResponse) {
    return next()
  }

  // Set status code and headers
  const { statusCode, headers } = httpResponse
  headers.forEach(([name, value]) => c.header(name, value))
  c.status(statusCode)

  // Handle streaming response
  if (httpResponse.pipe) {
    const chunks: Buffer[] = []
    const writable = new Writable({
      write(chunk, encoding, callback) {
        chunks.push(Buffer.from(chunk))
        callback()
      },
    })

    httpResponse.pipe(writable)
    return c.body(Buffer.concat(chunks))
  }

  // Handle non-streaming response
  if (httpResponse.body) {
    return c.body(httpResponse.body)
  }

  return c.body('')
})

// With Web stream
// Note: it needs "stream: 'web'" in +config.ts, otherwise will produce the error: [vike][Wrong Usage] The onRenderHtml() hook defined by vike-react/__internal/integration/onRenderHtml provides a Node.js Stream Pipe while a Web Writable was passed to pageContext.httpResponse.pipe() which is contradictory. You cannot mix a Web Stream with a Node.js Stream.

import { Hono } from 'hono'
import { renderPage } from 'vike/server'
import { Logger } from 'pino'

import { HonoVars } from './server/common/hono-utils.server'

// Extend PageContext to include our logger
declare global {
  namespace Vike {
    interface PageContext {
      log: Logger
    }
  }
}

export const vikeHandler = new Hono<HonoVars>().get(async (c, next) => {
  const pageContextInit = {
    urlOriginal: c.req.url,
    log: c.get('log'),
  }
  const pageContext = await renderPage(pageContextInit)
  const { httpResponse } = pageContext

  if (!httpResponse) {
    return next()
  }

  // Set status code and headers
  const { statusCode, headers } = httpResponse
  headers.forEach(([name, value]) => c.header(name, value))
  c.status(statusCode)

  // Handle streaming response
  if (httpResponse.pipe) {
    const { readable, writable } = new TransformStream()
    httpResponse.pipe(writable)
    return c.newResponse(readable)
  }

  // Handle non-streaming response
  if (httpResponse.body) {
    return c.body(httpResponse.body)
  }

  return c.body('')
})
*/

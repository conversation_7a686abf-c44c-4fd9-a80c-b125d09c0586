import { DateTime } from 'luxon'
import { describe, expect, test } from 'vitest'

import { plusCycles } from './payment-utils.shared'

/**
 * Test suite for plusDuration function
 *
 * This suite specifically tests the month duration functionality with various edge cases:
 * - Regular month transitions (e.g., 15th to 15th)
 * - Month end dates (28th, 29th, 30th, 31st)
 * - Leap year transitions
 * - Adding multiple months
 * - Month with fewer days than the current day of month
 */
describe('plusCycles with months', () => {
  // Helper function to create a DateTime for testing
  const createDateTime = (dateStr: string): DateTime<true> => {
    return DateTime.fromISO(`${dateStr}T00:00:00Z`, { zone: 'utc' }) as DateTime<true>
  }

  describe('regular month transitions', () => {
    test.each([
      { date: '2024-01-15', months: 1, expected: '2024-02-15', description: 'middle of month +1 month' },
      { date: '2024-01-01', months: 1, expected: '2024-02-01', description: 'start of month +1 month' },
      { date: '2024-06-15', months: 2, expected: '2024-08-15', description: 'middle of month +2 months' },
      { date: '2024-12-15', months: 1, expected: '2025-01-15', description: 'year boundary transition' },
      { date: '2024-12-15', months: 12, expected: '2025-12-15', description: 'full year transition' },
      { date: '2024-01-15', months: -1, expected: '2023-12-15', description: 'negative month transition' },
      { date: '2025-04-23', months: 0, expected: '2025-04-23', description: '+0 months' },
    ])('should handle $description', ({ date, months, expected }) => {
      const result = plusCycles(createDateTime(date), 'months', months)
      expect(result.toISODate()).toBe(expected)
    })
  })

  describe('month end dates (31 days)', () => {
    test.each([
      { date: '2024-01-31', months: 1, expected: '2024-02-29', description: '31st to February (leap year)' },
      { date: '2023-01-31', months: 1, expected: '2023-02-28', description: '31st to February (non-leap year)' },
      { date: '2024-03-31', months: 1, expected: '2024-04-30', description: '31st to 30-day month' },
      { date: '2024-05-31', months: 1, expected: '2024-06-30', description: '31st to 30-day month again' },
      { date: '2024-03-31', months: 2, expected: '2024-05-31', description: '31st to another 31-day month' },
      { date: '2024-01-31', months: 2, expected: '2024-03-31', description: '31st through February to 31-day month' },
      { date: '2024-01-31', months: 6, expected: '2024-07-31', description: '31st + multiple months to 31-day month' },
    ])('should handle $description', ({ date, months, expected }) => {
      const result = plusCycles(createDateTime(date), 'months', months)
      expect(result.toISODate()).toBe(expected)
    })
  })

  describe('month end dates (30 days)', () => {
    test.each([
      { date: '2024-04-30', months: 1, expected: '2024-05-30', description: '30th to 31-day month' },
      { date: '2024-04-30', months: 2, expected: '2024-06-30', description: '30th to another 30-day month' },
      { date: '2024-04-30', months: 10, expected: '2025-02-28', description: '30th to February (non-leap year)' },
      { date: '2024-04-30', months: -2, expected: '2024-02-29', description: '30th to February (leap year) backwards' },
    ])('should handle $description', ({ date, months, expected }) => {
      const result = plusCycles(createDateTime(date), 'months', months)
      expect(result.toISODate()).toBe(expected)
    })
  })

  describe('February and leap year cases', () => {
    test.each([
      { date: '2024-02-29', months: 1, expected: '2024-03-29', description: 'leap day to regular month' },
      { date: '2024-02-29', months: 12, expected: '2025-02-28', description: 'leap day to next February (non-leap)' },
      { date: '2024-02-29', months: 24, expected: '2026-02-28', description: 'leap day + 2 years (non-leap)' },
      { date: '2024-02-29', months: 48, expected: '2028-02-29', description: 'leap day + 4 years (leap year)' },
      { date: '2023-02-28', months: 12, expected: '2024-02-28', description: 'last day Feb to leap year (same day)' },
      { date: '2024-02-28', months: 12, expected: '2025-02-28', description: 'leap year Feb 28 + 1 year' },
      { date: '2024-01-29', months: 1, expected: '2024-02-29', description: '29th to leap day' },
      { date: '2024-01-30', months: 1, expected: '2024-02-29', description: '30th to leap day (capped)' },
      { date: '2024-01-31', months: 1, expected: '2024-02-29', description: '31st to leap day (capped)' },
      { date: '2023-01-29', months: 1, expected: '2023-02-28', description: '29th to non-leap February (capped)' },
      { date: '2023-01-30', months: 1, expected: '2023-02-28', description: '30th to non-leap February (capped)' },
      { date: '2023-01-31', months: 1, expected: '2023-02-28', description: '31st to non-leap February (capped)' },
    ])('should handle $description', ({ date, months, expected }) => {
      const result = plusCycles(createDateTime(date), 'months', months)
      expect(result.toISODate()).toBe(expected)
    })
  })

  describe('multi-month transitions with varied days', () => {
    test.each([
      {
        date: '2024-01-31',
        months: 3,
        expected: '2024-04-30',
        description: '31st + 3 months through February (leap) to April',
      },
      {
        date: '2024-01-29',
        months: 4,
        expected: '2024-05-29',
        description: '29th + 4 months through February (leap)',
      },
      {
        date: '2023-01-31',
        months: 3,
        expected: '2023-04-30',
        description: '31st + 3 months through February (non-leap) to April',
      },
      {
        date: '2023-12-31',
        months: 2,
        expected: '2024-02-29',
        description: '31st + 2 months crossing year to February (leap)',
      },
      {
        date: '2024-08-31',
        months: 6,
        expected: '2025-02-28',
        description: '31st + 6 months crossing year to February (non-leap)',
      },
      {
        date: '2023-03-30',
        months: 11,
        expected: '2024-02-29',
        description: '30th + 11 months to February (leap)',
      },
      {
        date: '2024-03-30',
        months: 11,
        expected: '2025-02-28',
        description: '30th + 11 months to February (non-leap)',
      },
    ])('should handle $description', ({ date, months, expected }) => {
      const result = plusCycles(createDateTime(date), 'months', months)
      expect(result.toISODate()).toBe(expected)
    })
  })

  describe('adding zero or large number of months', () => {
    test.each([
      { date: '2024-02-29', months: 0, expected: '2024-02-29', description: 'adding zero months to leap day' },
      { date: '2024-01-31', months: 0, expected: '2024-01-31', description: 'adding zero months to 31st' },
      { date: '2024-02-29', months: 48, expected: '2028-02-29', description: 'leap day + 48 months (4 years)' },
      { date: '2024-02-28', months: 48, expected: '2028-02-28', description: 'Feb 28 + 48 months (4 years)' },
      { date: '2024-01-31', months: 49, expected: '2028-02-29', description: 'Jan 31 + 49 months ending in leap Feb' },
      { date: '2023-03-31', months: 120, expected: '2033-03-31', description: 'adding 10 years (120 months)' },
    ])('should handle $description', ({ date, months, expected }) => {
      const result = plusCycles(createDateTime(date), 'months', months)
      expect(result.toISODate()).toBe(expected)
    })
  })
})

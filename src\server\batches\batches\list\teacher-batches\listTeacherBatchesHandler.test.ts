import { UUID } from 'crypto'

import { DateTime } from 'luxon'
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'

import app from '@/hono-entry'
import { env } from '@/server/common/env.server'
import { db } from '@/server/db/db'
import {
  academyStaffTable,
  academyTable,
  batchTable,
  courseTable,
  profileTable,
  userTable,
  userTncAcceptedTable,
  userTncSignTable,
} from '@/server/db/schema'
import { initDb, SUNDARGARH_DISTRICT_ID, TNC_PRIVACY_V1_ID, TNC_TOS_V1_ID } from '@/server/test/initDb'
import { createAccessToken } from '@/server/users/common/auth-token-utils.server'
import { UUID_REGEX } from '@/shared/common/common-utils.shared'
import { PROFILE_HEADER } from '@/shared/profiles/profile-utils.shared'

const mocked = {
  now: DateTime.now(),
} as const

const teacherUser = {
  id: crypto.randomUUID() as UUID,
  googleId: 'teacher-google-id',
  googleRefreshToken: null,
  name: 'Teacher User',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/teacher-picture',
  language: 'en-US',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '9876543210',
  mobileVerified: true,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

const teacherProfile = {
  id: crypto.randomUUID() as UUID,
  userId: teacherUser.id,
  role: 'teacher' as const,
  displayName: 'Test Teacher',
  approvedAt: mocked.now.toJSDate(),
  tncsAcceptedAt: mocked.now.toJSDate(),
} as const

const staffUser = {
  id: crypto.randomUUID() as UUID,
  googleId: 'staff-google-id',
  googleRefreshToken: null,
  name: 'Staff User',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/staff-picture',
  language: 'en-US',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '9876543211',
  mobileVerified: true,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

const staffProfile = {
  id: crypto.randomUUID() as UUID,
  userId: staffUser.id,
  role: 'principal' as const,
  displayName: 'Test Staff',
  approvedAt: mocked.now.toJSDate(),
  tncsAcceptedAt: mocked.now.toJSDate(),
} as const

const regularUser = {
  id: crypto.randomUUID() as UUID,
  googleId: 'regular-google-id',
  googleRefreshToken: null,
  name: 'Regular User',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/regular-picture',
  language: 'en-US',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '9876543212',
  mobileVerified: true,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

const regularProfile = {
  id: crypto.randomUUID() as UUID,
  userId: regularUser.id,
  role: 'student' as const,
  displayName: 'Regular Student',
  approvedAt: mocked.now.toJSDate(),
  tncsAcceptedAt: mocked.now.toJSDate(),
} as const

const testAcademy = {
  id: crypto.randomUUID() as UUID,
  name: 'Test Academy',
  email: '<EMAIL>',
  emailVerified: true,
  mobileCountryCode: 'IN',
  mobile: '9876543200',
  mobileVerified: true,
  currency: 'INR' as const,
  upiId: 'test@upi',
  districtId: SUNDARGARH_DISTRICT_ID as UUID,
  pincode: '770001',
  area: 'Test Area',
  tncsAcceptedAt: mocked.now.toJSDate(),
  approvedAt: mocked.now.toJSDate(),
} as const

const publishedCourse = {
  id: crypto.randomUUID() as UUID,
  academyId: testAcademy.id,
  name: 'Published Course',
  descr: '<p>A published course for testing</p>\n',
  publishedAt: mocked.now.toJSDate(),
} as const

const unpublishedCourse = {
  id: crypto.randomUUID() as UUID,
  academyId: testAcademy.id,
  name: 'Unpublished Course',
  descr: '<p>An unpublished course for testing</p>\n',
  publishedAt: null,
} as const

const publishedBatch = {
  id: crypto.randomUUID() as UUID,
  courseId: publishedCourse.id,
  teacherId: teacherProfile.id,
  fee: 500000, // 5000.00 in cents
  billingCycle: 'months' as const,
  graceDays: 3,
  startDate: mocked.now.plus({ days: 30 }).toFormat('yyyy-MM-dd'),
  timezone: 'Asia/Kolkata',
  cycleCount: 6,
  over: false,
  seatCount: 30,
  studentCount: 0,
  admissionOpen: true,
} as const

const unpublishedBatch = {
  id: crypto.randomUUID() as UUID,
  courseId: unpublishedCourse.id,
  teacherId: teacherProfile.id,
  fee: 600000, // 6000.00 in cents
  billingCycle: 'months' as const,
  graceDays: 3,
  startDate: mocked.now.plus({ days: 45 }).toFormat('yyyy-MM-dd'),
  timezone: 'Asia/Kolkata',
  cycleCount: 4,
  over: false,
  seatCount: 25,
  studentCount: 0,
  admissionOpen: true,
} as const

// Mock date utilities to have consistent test results
vi.mock(import('@/shared/common/date-utils-basic.shared'), async (importOriginal) => {
  const mod = await importOriginal()
  return {
    ...mod,
    utc: () => mocked.now,
  }
})

const makeRequest = async (teacherId: UUID, accessToken?: string, profileId?: UUID) => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  }

  if (accessToken) {
    headers.Authorization = `Bearer ${accessToken}`
  }

  if (profileId) {
    headers[PROFILE_HEADER] = profileId
  }

  return app.request(
    `/api/profiles/${teacherId}/batches`,
    {
      method: 'GET',
      headers,
    },
    {},
  )
}

const makeUnauthenticatedRequest = async (teacherId: UUID) => {
  return makeRequest(teacherId)
}

const makeAuthenticatedRequest = async (teacherId: UUID, userId: UUID, profileId?: UUID) => {
  const accessToken = await createAccessToken(env.JWT_SECRET_KEY, userId)
  return makeRequest(teacherId, accessToken, profileId)
}

const setupTestData = async () => {
  // Create academy
  await db.insert(academyTable).values(testAcademy)

  // Create courses
  await db.insert(courseTable).values([publishedCourse, unpublishedCourse])

  // Create users with TnC acceptance
  const users = [teacherUser, staffUser, regularUser]
  for (const user of users) {
    await db.insert(userTable).values(user)

    const acceptedTnCs = [TNC_TOS_V1_ID as UUID, TNC_PRIVACY_V1_ID as UUID]
    for (const tncId of acceptedTnCs) {
      await db.insert(userTncSignTable).values({
        id: crypto.randomUUID() as UUID,
        userId: user.id,
        tncVersionId: tncId,
        accepted: true,
      })

      await db.insert(userTncAcceptedTable).values({
        id: crypto.randomUUID() as UUID,
        userId: user.id,
        tncVersionId: tncId,
      })
    }
  }

  // Create profiles
  await db.insert(profileTable).values([teacherProfile, staffProfile, regularProfile])

  // Create teacher staff relationship (teacher must be staff of the academy)
  await db.insert(academyStaffTable).values({
    profileId: teacherProfile.id,
    academyId: testAcademy.id,
  })

  // Create academy staff relationship (staff can see unpublished batches)
  await db.insert(academyStaffTable).values({
    profileId: staffProfile.id,
    academyId: testAcademy.id,
  })

  // Create batches
  await db.insert(batchTable).values([publishedBatch, unpublishedBatch])
}

describe('listTeacherBatchesHandler', () => {
  beforeEach(async () => {
    await initDb()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  test('should successfully list teacher batches for unauthenticated user (public data only)', async () => {
    // given
    await setupTestData()

    // when
    const res = await makeUnauthenticatedRequest(teacherProfile.id)

    // then
    expect(res.status, 'Status check').toBe(200)
    const responseData = await res.json()
    expect(responseData).toHaveProperty('rows')
    expect(Array.isArray(responseData.rows), 'Response should have rows array').toBe(true)

    // Should only see published batches for unauthenticated users
    expect(responseData.rows).toHaveLength(1)
    const batch = responseData.rows[0]
    expect(batch.id).toBe(publishedBatch.id)
    expect(batch.courseId).toBe(publishedCourse.id)
    expect(batch.fee).toBe(publishedBatch.fee)
    expect(batch.admissionOpen).toBe(publishedBatch.admissionOpen)
  })

  test.each([
    { profileId: regularProfile.id, description: 'with profile header' },
    { profileId: undefined, description: 'without profile header' },
  ])(
    'should successfully list teacher batches for authenticated regular user (public data only) $description',
    async ({ profileId }) => {
      // given
      await setupTestData()

      // when
      const res = await makeAuthenticatedRequest(teacherProfile.id, regularUser.id, profileId)

      // then
      expect(res.status, 'Status check').toBe(200)
      const responseData = await res.json()
      expect(responseData).toHaveProperty('rows')
      expect(Array.isArray(responseData.rows), 'Response should have rows array').toBe(true)

      // Should only see published batches for regular users (not staff)
      expect(responseData.rows).toHaveLength(1)
      const batch = responseData.rows[0]
      expect(batch.id).toBe(publishedBatch.id)
      expect(batch.courseId).toBe(publishedCourse.id)
    },
  )

  test('should list all batches for academy staff (including unpublished)', async () => {
    // given
    await setupTestData()

    // when
    const res = await makeAuthenticatedRequest(teacherProfile.id, staffUser.id, staffProfile.id)

    // then
    expect(res.status, 'Status check').toBe(200)
    const responseData = await res.json()
    expect(responseData).toHaveProperty('rows')
    expect(Array.isArray(responseData.rows), 'Response should have rows array').toBe(true)

    // Should see both published and unpublished batches for staff
    expect(responseData.rows).toHaveLength(2)

    const batchIds = responseData.rows.map((batch: { id: UUID }) => batch.id)
    expect(batchIds).toContain(publishedBatch.id)
    expect(batchIds).toContain(unpublishedBatch.id)
  })

  test('should return empty array when teacher has no batches', async () => {
    // given
    await setupTestData()
    const anotherTeacher = {
      id: crypto.randomUUID() as UUID,
      userId: regularUser.id, // Reuse existing user
      role: 'teacher' as const,
      displayName: 'Another Teacher',
      approvedAt: mocked.now.toJSDate(),
      tncsAcceptedAt: mocked.now.toJSDate(),
    }
    await db.insert(profileTable).values(anotherTeacher)

    // when
    const res = await makeUnauthenticatedRequest(anotherTeacher.id)

    // then
    expect(res.status, 'Status check').toBe(200)
    const responseData = await res.json()
    expect(responseData).toHaveProperty('rows')
    expect(responseData.rows).toHaveLength(0)
  })

  test('should return 400 for invalid UUID parameter', async () => {
    // when
    const res = await app.request(
      '/api/profiles/invalid-uuid/batches',
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
      {},
    )

    // then
    expect(res.status, 'Status check').toBe(400)
    await expect(res).toBeError(400, [
      {
        code: 'custom',
        path: ['id'],
        message: 'Must be a UUID v4 string',
      },
    ])
  })

  test('should work with valid UUID that does not exist (returns empty array)', async () => {
    // given
    await setupTestData()
    const nonExistentTeacherId = crypto.randomUUID() as UUID

    // when
    const res = await makeUnauthenticatedRequest(nonExistentTeacherId)

    // then
    expect(res.status, 'Status check').toBe(200)
    const responseData = await res.json()
    expect(responseData).toHaveProperty('rows')
    expect(responseData.rows).toHaveLength(0)
  })

  test('should include all required batch fields in response', async () => {
    // given
    await setupTestData()

    // when
    const res = await makeUnauthenticatedRequest(teacherProfile.id)

    // then
    expect(res.status, 'Status check').toBe(200)
    const responseData = await res.json()
    expect(responseData.rows).toHaveLength(1)

    const batch = responseData.rows[0]

    // Verify all expected fields are present
    expect(batch).toHaveProperty('id')
    expect(batch.id).toMatch(UUID_REGEX)
    expect(batch).toHaveProperty('courseId')
    expect(batch.courseId).toMatch(UUID_REGEX)
    expect(batch).toHaveProperty('startDate')
    expect(batch).toHaveProperty('timezone')
    expect(batch).toHaveProperty('fee')
    expect(batch).toHaveProperty('billingCycle')
    expect(batch).toHaveProperty('cycleCount')
    expect(batch).toHaveProperty('events')
    expect(batch).toHaveProperty('admissionOpen')

    // Verify data types
    expect(typeof batch.fee).toBe('number')
    expect(typeof batch.cycleCount).toBe('number')
    expect(typeof batch.admissionOpen).toBe('boolean')
    expect(Array.isArray(batch.events)).toBe(true)
  })

  test('should order batches by start date descending, then creation date descending', async () => {
    // given
    await setupTestData()

    // Add another batch with a later start date
    const laterBatch = {
      id: crypto.randomUUID() as UUID,
      courseId: publishedCourse.id,
      teacherId: teacherProfile.id,
      fee: 700000, // 7000.00 in cents
      billingCycle: 'months' as const,
      graceDays: 3,
      startDate: mocked.now.plus({ days: 60 }).toFormat('yyyy-MM-dd'), // Later than publishedBatch
      timezone: 'Asia/Kolkata',
      cycleCount: 3,
      over: false,
      seatCount: 20,
      studentCount: 0,
      admissionOpen: true,
    }
    await db.insert(batchTable).values(laterBatch)

    // when
    const res = await makeUnauthenticatedRequest(teacherProfile.id)

    // then
    expect(res.status, 'Status check').toBe(200)
    const responseData = await res.json()
    expect(responseData.rows).toHaveLength(2)

    // Should be ordered by start date descending (laterBatch first)
    expect(responseData.rows[0].id).toBe(laterBatch.id)
    expect(responseData.rows[1].id).toBe(publishedBatch.id)
  })
})

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { pino } from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { sendOtp } from '@/server/common/sms/otp/sendOtp'
import { db } from '@/server/db/db'
import { userTable } from '@/server/db/schema/user-schema'
import { phoneNumber } from '@/shared/common/common-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

const findUserDaf = (log: pino.Logger, userId: UUID) =>
  withLog(log, 'findUserDaf', () =>
    db.query.userTable.findFirst({
      columns: {
        mobile: true,
        mobileVerified: true,
        language: true,
      },
      with: {
        mobileCountry: {
          columns: {
            phonePrefix: true,
          },
        },
      },
      where: eq(userTable.id, userId),
    }),
  )

export const sendMobileOtp = async (c: Ctx) => {
  ensureUser(c.user, { ignoreEmailVerification: true, ignoreMobileVerification: true })

  const user = await findUserDaf(c.log, c.user.id)
  ensureExists(user, c.user.id, 'User', 422)

  ensure(!user.mobileVerified, {
    logMessage: `Mobile already verified for user: ${c.user.id}`,
    issueMessage: 'Your mobile number is already verified',
    statusCode: 422,
  })

  const mobile = phoneNumber(user.mobileCountry.phonePrefix, user.mobile)
  await sendOtp(c.log, mobile, user.language)
}

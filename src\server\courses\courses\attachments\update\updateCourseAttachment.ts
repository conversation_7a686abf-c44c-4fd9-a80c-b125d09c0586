import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { appendCommandDaf } from '@/server/common/command/processCommands'
import { env } from '@/server/common/env.server'
import { s3Flock } from '@/server/common/s3/s3-utils.server'
import { db, Transaction } from '@/server/db/db'
import { courseAttachmentTable } from '@/server/db/schema/course-schema'
import { withLog } from '@/shared/common/withLog.shared'
import { courseAttachmentS3Path, EditCourseAttachmentForm } from '@/shared/course/course-utils.shared'

import { ensureCanUpdateAttachment } from '../common/ensureCanUpdateAttachment'

import { UPDATE_S3OBJECT_COMMAND, UpdateS3ObjectCommandData } from './updateS3ObjectCommand'

const findCourseAttachmentDaf = (log: pino.Logger, attachmentId: UUID) =>
  withLog(log, 'findCourseAttachment', () =>
    db
      .select({ free: courseAttachmentTable.free })
      .from(courseAttachmentTable)
      .where(eq(courseAttachmentTable.id, attachmentId))
      .limit(1),
  )

const updateCourseAttachmentDaf = async (
  log: pino.Logger,
  db: Transaction,
  attachmentId: UUID,
  form: EditCourseAttachmentForm,
) =>
  withLog(log, 'updateCourseAttachmentDaf', () =>
    db
      .update(courseAttachmentTable)
      .set({
        name: form.name,
        free: form.free,
      })
      .where(eq(courseAttachmentTable.id, attachmentId)),
  )

export const updateCourseAttachment = async (c: Ctx, attachmentId: UUID, form: EditCourseAttachmentForm) => {
  await ensureCanUpdateAttachment(c, attachmentId)
  const [attachment] = await findCourseAttachmentDaf(c.log, attachmentId)

  await db.transaction(async (tx) => {
    await updateCourseAttachmentDaf(c.log, tx, attachmentId, form)
    if (attachment?.free !== form.free) {
      await appendCommandDaf<UpdateS3ObjectCommandData>(tx, c.log, s3Flock(), {
        command: UPDATE_S3OBJECT_COMMAND,
        data: {
          path: courseAttachmentS3Path(env.APP_ENV, attachmentId),
          free: form.free,
        },
      })
    }
  })
}

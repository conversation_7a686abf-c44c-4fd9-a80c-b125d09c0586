import { SuspendedBadge } from '@ui/profiles/common/SuspendedBadge'
import { UnapprovedBadge } from '@ui/profiles/common/UnapprovedBadge'

import type { AcademyDetail } from '@/server/academies/get/getAcademy.server'

export const AcademyHeader = ({ academy }: { academy: AcademyDetail }) => (
  <div className="py-2">
    <h1 className="page-title">{academy.name}</h1>
    <div className="flex items-center gap-x-2">
      <div>
        <UnapprovedBadge approvedAt={academy.approvedAt} />
        <SuspendedBadge suspendedAt={academy.suspendedAt} />
      </div>
    </div>
  </div>
)

import { UUID } from 'crypto'

import clsx from 'clsx'
import { clientOnly } from 'vike-react/clientOnly'

import { AcademyItem } from '@/server/academies/list/listAcademies'
import { TeacherBatch } from '@/server/batches/batches/list/teacher-batches/listTeacherBatches'
import { CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { Profile } from '@/server/profiles/get/getProfile.server'
import { academyPath } from '@/shared/academies/academy-utils.shared'
import { batchPath } from '@/shared/batch/batch-utils.shared'
import { formatDate, timezoneOffset } from '@/shared/common/date-utils.shared'
import { profilePath } from '@/shared/profiles/profile-utils.shared'
import { useListAcademiesSuspenseQuery } from '@ui/academies/common/academy-queries'
import { WarningAlert } from '@ui/common/alerts/WarningAlert'
import { ShowData } from '@ui/common/ShowData'
import { useTeacherBatchesSuspenseQuery } from '@ui/courses/@id/batches/common/batch-queries'
import { BatchEvents } from '@ui/courses/@id/batches/common/BatchEvents'
import { CourseTags } from '@ui/courses/common/CourseTags'
import { useCourseSuspenseQuery } from '@ui/courses/common/queries/course-queries'
import { useProfileSuspenseQuery } from '@ui/profiles/common/profile-queries'

const CopyStudentsEmailsButton = clientOnly(
  async () => (await import('@ui/courses/@id/batches/common/CopyStudentsEmailsButton')).CopyStudentsEmailsButton,
)

export const TeacherBatchesPage = ({ teacherId }: { teacherId: UUID }) => {
  const { data: profile, error } = useProfileSuspenseQuery(teacherId)

  return (
    <ShowData error={error}>
      {profile && profile.role === 'teacher' ?
        <TeacherBatches teacher={profile} />
      : <WarningAlert>The profile you are trying to access is not a teacher</WarningAlert>}
    </ShowData>
  )
}

export const TeacherBatches = ({ teacher }: { teacher: Profile }) => {
  const { data: academies, error: academiesError } = useListAcademiesSuspenseQuery({ profileId: teacher.id })
  const { data: batches, error: batchesError } = useTeacherBatchesSuspenseQuery(teacher.id)
  const academy = academies?.[0]

  return (
    <ShowData error={academiesError || batchesError} spinnerSize="1.25rem">
      {academy && batches && (
        <>
          <TeacherBatchesHeader teacher={teacher} academy={academy} />
          <div className="mt-6">
            {batches.length > 0 ?
              <BatchesTable academy={academy} batches={batches} />
            : <div className="text-gray-500 text-center border border-gray-200 rounded-lg p-4">
                No batches yet for this teacher. Check back soon!
              </div>
            }
          </div>
        </>
      )}
    </ShowData>
  )
}

const TeacherBatchesHeader = ({ teacher, academy }: { teacher: Profile; academy: AcademyItem }) => {
  return (
    <div className="flex items-center justify-between h-full">
      <div>
        <h1 className="page-title">
          <a href={profilePath(teacher)}>{teacher.displayName}</a>'s batches
        </h1>
        <p className="page-subtitle">
          <a href={academyPath(academy)}>{academy.name}</a>
        </p>
      </div>
      <div className="size-16 shrink-0">
        <img alt="Teacher picture" src={teacher.user.googlePictureUrl} className="size-16 rounded-full" />
      </div>
    </div>
  )
}

const BatchesTable = ({ academy, batches }: { academy: AcademyItem; batches: TeacherBatch[] }) => {
  return (
    <div className="space-y-6">
      {batches.map((batch) => (
        <BatchRow key={batch.id} batch={batch} academy={academy} />
      ))}
    </div>
  )
}

const BatchRow = ({ batch, academy }: { batch: TeacherBatch; academy: AcademyItem }) => {
  const { data: course } = useCourseSuspenseQuery(batch.courseId)
  if (!course) return null
  return (
    <div
      key={batch.id}
      className={clsx(
        'bg-white rounded-lg shadow overflow-hidden mb-1 border',
        batch.admissionOpen ? 'border-green-500' : 'border-gray-200',
      )}
    >
      <div className="p-2 grid gap-2 grid-cols-1 sm:grid-cols-5">
        <div className="pb-4 sm:pb-0 sm:pr-4">
          <CourseCell course={course} batchId={batch.id} />
        </div>
        <div>
          <div className="text-sm text-gray-500 mb-1">From</div>
          <div className="font-medium text-gray-900">{formatDate(batch.startDate)}</div>
          <div className="text-xs text-gray-700">
            for {batch.cycleCount} {batch.billingCycle}
          </div>
        </div>
        <div>
          <div className="text-sm text-gray-500 mb-1">Schedule ({timezoneOffset(batch.startDate, batch.timezone)})</div>
          <div className="mt-1 text-gray-900">
            {batch.events.length > 0 ?
              <BatchEvents batchId={batch.id} events={batch.events} setEditingEvent={() => {}} showActions={false} />
            : 'No schedules fed yet'}
          </div>
        </div>
        <div>
          <div className="text-sm text-gray-500 mb-1">Pricing</div>
          <div className="text-gray-900">
            <span className="font-medium">Fee:</span> {academy?.currency} {batch.fee}
          </div>
          <div className="text-xs italic text-gray-500">
            {batch.admissionOpen ? 'admission open' : 'admission closed'}
          </div>
        </div>
        <div className="flex flex-col justify-center">
          <CopyStudentsEmailsButton batchId={batch.id} />
        </div>
      </div>
    </div>
  )
}

const CourseCell = ({ course, batchId }: { course: CourseWithoutDescr; batchId: UUID }) => {
  return (
    <div className="flex flex-col justify-center h-full">
      <div className="mt-1 leading-5">
        <a className="link" href={batchPath(course, batchId)}>
          {course.name}
        </a>
      </div>
      <div className="mt-1">
        <CourseTags tagIds={course.tagIds} />
      </div>
    </div>
  )
}

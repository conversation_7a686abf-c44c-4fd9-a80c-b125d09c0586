import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfileWithRole } from '@/server/common/error/ensure-profile.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { academyTable } from '@/server/db/schema/academy-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'

const approveAcademyDaf = (log: pino.Logger, academyId: UUID) =>
  withLog(log, 'approveAcademyDaf', () =>
    db
      .update(academyTable)
      .set({
        approvedAt: utc().toJSDate(),
      })
      .where(and(eq(academyTable.id, academyId))),
  )

export const approveAcademy = async (c: Ctx, academyId: UUID) => {
  ensureUser(c.user)
  ensureProfileWithRole('admin', c.profile)

  const result = await approveAcademyDaf(c.log, academyId)
  ensure(result.count > 0, {
    statusCode: 404,
    issueMessage: 'Academy not found.',
    logMessage: `Academy ${academyId} not found`,
  })
}

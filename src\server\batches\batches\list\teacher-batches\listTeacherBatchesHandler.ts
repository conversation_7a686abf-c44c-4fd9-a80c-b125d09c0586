import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { listTeacherBatches } from './listTeacherBatches'

export const listTeacherBatchesHandler = new Hono<HonoVars>().get('/', zValidator('param', $HasId), async (c) => {
  const { id: teacherId } = c.req.valid('param')
  const profile = c.get('profile')
  const batches = await listTeacherBatches(c.get('log'), teacherId, profile?.id)
  return c.json({ rows: batches }, 200)
})

import { ChevronDownIcon } from '@heroicons/react/16/solid'

import { OmniError } from '@/shared/common/error-utils.shared'
import { useCountriesSuspenseQuery } from '@ui/masters/common/master-queries'

import { ShowData } from '../ShowData'
import { ShowErrors } from '../ShowErrors'

export const MobileEditor = ({
  mobileCountryCode,
  mobile,
  setMobileCountryCode,
  setMobile,
  omniError,
}: {
  mobileCountryCode: string
  mobile: string
  setMobileCountryCode: (mobileCountryCode: string) => void
  setMobile: (mobile: string) => void
  omniError: OmniError | undefined
}) => {
  const { data: countries, error: countriesError } = useCountriesSuspenseQuery()

  return (
    <ShowData error={countriesError} spinnerSize="1.25rem">
      <div>
        <div className="mt-2 sm:col-span-2 sm:mt-0">
          <div className="flex rounded-md bg-white outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline-2 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-600 sm:max-w-md">
            <div className="grid shrink-0 grid-cols-1 focus-within:relative">
              <select
                id="mobile-prefix-select"
                value={mobileCountryCode}
                onChange={(e) => setMobileCountryCode(e.target.value)}
                className="col-start-1 row-start-1 w-full appearance-none rounded-md py-1.5 pl-3 pr-7 text-base text-gray-500 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
              >
                {countries?.map((country) => (
                  <option key={country.code} value={country.code}>
                    {country.code}
                  </option>
                ))}
              </select>
              <ChevronDownIcon
                className="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 sm:size-4"
                aria-hidden="true"
              />
            </div>
            <input
              id="mobile-input"
              value={mobile}
              onChange={(e) => setMobile(e.target.value)}
              type="tel"
              pattern="[0-9]{10}"
              placeholder="10 digits"
              className="block min-w-0 grow py-1.5 pr-3 pl-1 text-base text-gray-900 placeholder:text-gray-400 focus:outline-none sm:text-sm/6"
            />
          </div>
          <ShowErrors error={omniError} path={['mobile']} />
        </div>
      </div>
    </ShowData>
  )
}

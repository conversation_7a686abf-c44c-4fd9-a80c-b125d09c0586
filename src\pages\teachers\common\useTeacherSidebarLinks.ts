import { ClockIcon } from '@heroicons/react/24/outline'

import { profilePath } from '@/shared/profiles/profile-utils.shared'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

export const useTeacherSidebarLinks = () => {
  const { currentProfile } = useCurrentProfile()

  return currentProfile?.role === 'teacher' ?
      [{ name: 'My Batches', href: `${profilePath(currentProfile)}/batches`, icon: ClockIcon }]
    : []
}

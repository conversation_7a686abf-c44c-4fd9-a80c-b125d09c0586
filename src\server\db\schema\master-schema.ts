import { relations, sql } from 'drizzle-orm'
import { check, pgTable, text } from 'drizzle-orm/pg-core'

import { suspensionCheck, suspensionColumns, uid } from './helpers'

export const countryTable = pgTable(
  'country',
  {
    code: text().primaryKey(),
    name: text().unique().notNull(),
    phonePrefix: text().unique().notNull(), // e.g. 91 for India
    ...suspensionColumns,
  },
  (t) => [
    check('code_len', sql`char_length(${t.code}) <= 5`),
    check('name_len', sql`char_length(${t.name}) <= 100`),
    check('phone_prefix_len', sql`char_length(${t.phonePrefix}) <= 10`),
    ...suspensionCheck(t),
  ],
)

export const stateTable = pgTable(
  'state',
  {
    id: uid().primaryKey(),
    name: text().unique().notNull(),
    countryCode: text()
      .references(() => countryTable.code)
      .notNull(),
    ...suspensionColumns,
  },
  (t) => [check('name_len', sql`char_length(${t.name}) <= 100`), ...suspensionCheck(t)],
)

export const districtTable = pgTable(
  'district',
  {
    id: uid().primaryKey(),
    name: text().unique().notNull(),
    stateId: uid()
      .references(() => stateTable.id)
      .notNull(),
    ...suspensionColumns,
  },
  (t) => [check('name_len', sql`char_length(${t.name}) <= 100`), ...suspensionCheck(t)],
)

export const districtRelations = relations(districtTable, ({ one }) => ({
  state: one(stateTable, {
    fields: [districtTable.stateId],
    references: [stateTable.id],
  }),
}))

export const stateRelations = relations(stateTable, ({ one }) => ({
  country: one(countryTable, {
    fields: [stateTable.countryCode],
    references: [countryTable.code],
  }),
}))

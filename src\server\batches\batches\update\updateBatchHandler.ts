import { UUID } from 'crypto'

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $EditBatchForm } from '@/shared/batch/batch-utils.shared'

import { updateBatch } from './updateBatch'

export const updateBatchHandler = new Hono<HonoVars>().put('/', zValidator('json', $EditBatchForm), async (c) => {
  const form = c.req.valid('json')
  const batchId = c.req.param('id') as UUID

  await updateBatch(getCtx(c), batchId, form)
  return c.body(null, 204)
})

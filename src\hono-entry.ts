import 'dotenv/config'

import { Hono } from 'hono'
import { requestId } from 'hono/request-id'
import { secureHeaders } from 'hono/secure-headers'

import { apiRoutes } from './server/api-routes.server'
import { processCommands } from './server/common/command/processCommands'
import { initServerLogger } from './server/init-logger.server'
import { authMiddleware } from './server/middleware/auth/authMiddleware.server'
import { onError } from './server/middleware/error/onError.server'
import { logMiddleware } from './server/middleware/logMiddleware.server'
import { vikeHandler } from './vike-handler'

initServerLogger({ hono: true })

const app = new Hono()

app.use('*', requestId())
app.use('*', logMiddleware)

app.use(
  '*',
  secureHeaders({
    contentSecurityPolicy: {
      // defaultSrc: ["'self'"],
      imgSrc: ["'self'", 'data:', 'https://images.unsplash.com', 'https://lh3.googleusercontent.com'],
      mediaSrc: ["'self'", 'data:', 'https://www.youtube.com'],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com', 'https://fonts.gstatic.com'],
      fontSrc: ["'self'", 'https://fonts.googleapis.com', 'https://fonts.gstatic.com'],
      // scriptSrc: ["'self'"],
      connectSrc: ["'self'", 'https://*.amazonaws.com', 'https://tuitionlance.s3.ap-south-1.amazonaws.com'],
      upgradeInsecureRequests: [],
    },
    crossOriginOpenerPolicy: 'same-origin-allow-popups',
  }),
)

app.use('/api/*', authMiddleware)

app.route('/api', apiRoutes)
app.all('/api/*', (c) => c.notFound()) // Not having this causes issues when testing in development

/**
 * Vike route
 *
 * @link {@see https://vike.dev}
 **/
app.route('*', vikeHandler)

app.onError(onError)

void processCommands()
export default app

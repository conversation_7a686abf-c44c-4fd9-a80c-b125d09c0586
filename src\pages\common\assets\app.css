@import "tailwindcss";

@theme {
  --font-sans: Inter, 'Inter Fallback', sans-serif;
}

@layer base {

  button {
    @apply hover:cursor-pointer;
  }

  button:disabled {
    @apply cursor-not-allowed;
  }
}

@layer components {

  .text-error {
    @apply text-red-700;
  }
  .text-warning {
    @apply text-yellow-700;
  }
  .text-help {
    @apply text-gray-500;
  }
  .link {
    @apply text-indigo-600 hover:text-indigo-800 hover:cursor-pointer;
  }
  .form-label {
    @apply block text-sm/6 font-medium text-gray-900;
  }
  .form-input {
    @apply block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6;
  }
  .form-descr {
    @apply mt-2 text-sm text-gray-600;
  }
  .page-title {
    @apply text-3xl font-semibold text-gray-900;
  }
  .page-subtitle {
    @apply text-lg text-gray-900 mt-1;
  }

  .tooltip {
    @apply invisible absolute;
  }
  .has-tooltip:hover .tooltip {
    @apply visible z-50
  }
}

/* Hide LastPass Elements */
div[data-lastpass-icon-root],
div[data-lastpass-icon-root=""],
div[data-lastpass-infield],
div[data-lastpass-infield=""] {
  position: absolute !important;
  width: 0 !important;
  height: 0 !important;
  opacity: 0 !important;
  pointer-events: none !important;
  visibility: hidden !important;
  display: none !important;
  float: none !important;
}

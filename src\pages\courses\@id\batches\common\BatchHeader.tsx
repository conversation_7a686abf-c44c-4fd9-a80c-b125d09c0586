import { Batch } from '@/server/batches/batches/get/getBatch.server'
import { CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { academyPath } from '@/shared/academies/academy-utils.shared'
import { batchPath } from '@/shared/batch/batch-utils.shared'
import { formatDate, today } from '@/shared/common/date-utils.shared'
import { profilePath } from '@/shared/profiles/profile-utils.shared'
import { useAcademySuspenseQuery } from '@ui/academies/common/academy-queries'
import { CourseTags } from '@ui/courses/common/CourseTags'
import { useProfileSuspenseQuery } from '@ui/profiles/common/profile-queries'

export const BatchHeader = ({ course, batch, h1 }: { course: CourseWithoutDescr; batch: Batch; h1: string }) => {
  const { data: academy } = useAcademySuspenseQuery(course.academyId, false)
  const { data: profile } = useProfileSuspenseQuery(batch.teacherId)

  return (
    <div className="sm:flex-auto">
      <h1 className="page-title">{h1}</h1>
      <p className="page-subtitle">
        <a href={batchPath(course, batch.id)}>{course.name}</a>
      </p>
      {academy && (
        <p className="text-sm text-gray-700">
          {profile && (
            <>
              {'By '}
              <a href={profilePath(profile)}>{profile?.displayName}</a>
            </>
          )}
          {academy && (
            <>
              {', '}
              <a href={academyPath({ id: course.academyId, name: academy.name })}>{academy.name}</a>
            </>
          )}
        </p>
      )}

      <p className="text-sm text-gray-500 mb-2">
        {batch.startDate < today() ? 'Started' : 'Starts'} on {formatDate(batch.startDate)} for {batch.cycleCount}{' '}
        {batch.billingCycle}
      </p>
      <CourseTags tagIds={course.tagIds} />
    </div>
  )
}

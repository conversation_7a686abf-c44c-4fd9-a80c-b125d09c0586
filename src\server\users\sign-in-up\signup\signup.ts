import { randomUUID, UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { encrypt } from '@/server/common/encryption-utils.server'
import { env } from '@/server/common/env.server'
import { ensure } from '@/server/common/error/ensure.server'
import { db } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { userTable, userTncAcceptedTable, userTncSignTable } from '@/server/db/schema/user-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { nowTruncated } from '@/shared/common/date-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { GoogleUserInfo } from '../getGoogleUserInfo'
import { createAuthTokenForUser, fallbackLocale } from '../signin-up-utils.server'
import { validateSignupOrUpdate } from '../validateSignupOrUpdate'

import type { UserForm } from '@/shared/users/user-utils.shared'

const findExistingUserDaf = (log: Logger, googleId: string) =>
  withLog(log, 'findExistingUserDaf', () =>
    db.query.userTable.findFirst({
      ...dummyColumn,
      where: eq(userTable.googleId, googleId),
    }),
  )

const insertUserWithTnCsDaf = (
  log: Logger,
  userId: UUID,
  userInfo: GoogleUserInfo,
  encryptedRefreshToken: string | null,
  form: UserForm,
) =>
  withLog(log, 'insertUserWithTnCsDaf', () =>
    db.transaction(async (tx) => {
      await tx.insert(userTable).values({
        id: userId,
        googleId: userInfo.sub,
        googleRefreshToken: encryptedRefreshToken,
        name: userInfo.name,
        email: userInfo.email,
        emailVerified: userInfo.email_verified,
        googlePictureUrl: userInfo.picture,
        language: userInfo.language || fallbackLocale,
        tokensValidFrom: nowTruncated(utc()),
        mobileCountryCode: form.mobileCountryCode,
        mobile: form.mobile,
        mobileVerified: false,
      })

      // Insert and TnC sign records in parallel
      await Promise.all([
        // Insert user TnC sign records
        tx.insert(userTncSignTable).values(
          form.acceptedTnCs.map((tncVersionId) => ({
            id: randomUUID(),
            userId,
            tncVersionId: tncVersionId,
            signedAt: utc().toJSDate(),
            accepted: true,
          })),
        ),

        // Insert user TnC acceptance records
        tx.insert(userTncAcceptedTable).values(
          form.acceptedTnCs.map((tncVersionId) => ({
            id: randomUUID(),
            userId,
            tncVersionId: tncVersionId,
          })),
        ),
      ])
    }),
  )

export const signup = async (log: Logger, form: UserForm, acceptLanguage?: string, existingUserId?: UUID) => {
  ensure(!existingUserId, {
    statusCode: 409,
    issueMessage: 'Please sign out before trying to sign up.',
    logMessage: `User ${existingUserId} attempted to sign up while signed in.`,
  })

  const { userInfo, refreshToken } = await validateSignupOrUpdate(log, form, acceptLanguage)

  // Verify user with same googleId doesn't exist
  const existingUser = await findExistingUserDaf(log, userInfo.sub)

  ensure(!existingUser, {
    statusCode: 409,
    issueMessage: 'A user with this Google account already exists. Please sign in instead.',
    logMessage: `User with Google ID ${userInfo.sub} attempted to create duplicate account.`,
  })

  // Create user
  const userId = randomUUID()
  await insertUserWithTnCsDaf(log, userId, userInfo, refreshToken ? encrypt(refreshToken) : null, form)
  return createAuthTokenForUser(env.JWT_SECRET_KEY, userId)
}

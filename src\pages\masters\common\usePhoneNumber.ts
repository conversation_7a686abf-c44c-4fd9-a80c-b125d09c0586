import { phoneNumber } from '@/shared/common/common-utils.shared'
import { ErrorPayload } from '@/shared/common/error-utils.shared'

import { useCountriesSuspenseQuery } from './master-queries'

export const usePhoneNumber = (
  countryCode: string,
  mobile: string,
): { fullNumber: string; countriesError: ErrorPayload | null } => {
  const { data: countries, error: countriesError } = useCountriesSuspenseQuery()
  if (countriesError) return { fullNumber: mobile, countriesError }
  const country = countries?.find((c) => c.code === countryCode)
  return { fullNumber: phoneNumber(country?.phonePrefix ?? '', mobile), countriesError: null }
}

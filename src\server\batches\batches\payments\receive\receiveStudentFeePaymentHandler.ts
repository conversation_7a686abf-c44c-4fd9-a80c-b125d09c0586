import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { receiveStudentFeePayment } from './receiveBatchStudentPayment'

export const receiveStudentFeePaymentHandler = new Hono<HonoVars>().post(
  '/',
  zValidator('param', $HasId),
  async (c) => {
    const { id: studentFeePaymentId } = c.req.valid('param')
    await receiveStudentFeePayment(getCtx(c), studentFeePaymentId)
    return c.body(null, 204)
  },
)

import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { ensureCanUpdateBatch } from '@/server/batches/batches/common/ensureCanUpdateBatch'
import { Ctx } from '@/server/common/auth-context-types.server'
import { db } from '@/server/db/db'
import { batchAttachmentTable } from '@/server/db/schema/batch-schema'
import { withLog } from '@/shared/common/withLog.shared'

const removeBatchAttachmentDaf = (log: pino.Logger, batchId: UUID, attachmentId: UUID) =>
  withLog(log, 'removeBatchAttachmentDaf', () =>
    db
      .delete(batchAttachmentTable)
      .where(and(eq(batchAttachmentTable.batchId, batchId), eq(batchAttachmentTable.attachmentId, attachmentId))),
  )

export const removeBatchAttachment = async (c: Ctx, batchId: UUID, attachmentId: UUID) => {
  await ensureCanUpdateBatch(c, batchId)
  await removeBatchAttachmentDaf(c.log, batchId, attachmentId)
}

import { UUID } from 'crypto'

import { and, desc, eq, isNull, sql } from 'drizzle-orm'
import { Logger } from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfileWithRole } from '@/server/common/error/ensure-profile.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { batchRecommendationTable, batchStudentTable, batchTable, courseTable } from '@/server/db/schema'
import { withLog } from '@/shared/common/withLog.shared'

import { eventsQuery } from '../../common/batch-select-helper'
import { BatchEvent } from '../../get/getBatch.server'

const findBatchesForStudentDaf = (log: Logger, studentId: UUID) => {
  return withLog(log, 'findBatchesForStudentDaf', async () => {
    return db
      .select({
        id: batchTable.id,
        courseId: batchTable.courseId,
        teacherId: batchTable.teacherId,
        startDate: batchTable.startDate,
        timezone: batchTable.timezone,
        fee: batchTable.fee,
        billingCycle: batchTable.billingCycle,
        cycleCount: batchTable.cycleCount,
        events: sql<BatchEvent[]>`(${eventsQuery})`,
        over: batchTable.over,
        academyId: courseTable.academyId,
        firstJoinedAt: batchStudentTable.firstJoinedAt,
        paidTillCycle: batchStudentTable.paidTillCycle,
        lastReminderType: batchStudentTable.lastReminderType,
      })
      .from(batchStudentTable)
      .innerJoin(batchTable, eq(batchTable.id, batchStudentTable.batchId))
      .innerJoin(courseTable, eq(courseTable.id, batchTable.courseId))
      .leftJoin(batchRecommendationTable, eq(batchRecommendationTable.batchId, batchTable.id))
      .where(and(eq(batchStudentTable.studentId, studentId), isNull(batchStudentTable.leftAt)))
      .orderBy(courseTable.academyId, desc(batchTable.startDate), desc(batchTable.createdAt))
  })
}

export const listStudentBatches = (c: Ctx) => {
  ensureUser(c.user)
  ensureProfileWithRole('student', c.profile)
  return findBatchesForStudentDaf(c.log, c.profile.id)
}

export type StudentBatch = Awaited<ReturnType<typeof listStudentBatches>>[number]

import { UUID } from 'crypto'

import z from 'zod'

import { $Mobile, $UUID } from '../common/common-utils.shared'
import { today } from '../common/date-utils.shared'
import { $CountryCode } from '../masters/master-utils.shared'

export const DISPLAY_NAME_MAX_LEN = 30
export const EMAIL_MAX_LEN = 100
export const LEGAL_NAME_MAX_LEN = 100
export const DEFAULT_VALIDITY_DAYS = 30
export const MIN_VALIDITY_DAYS = 1
export const MAX_VALIDITY_DAYS = 10000

export type AuthTokenData = {
  userId: UUID
  accessToken: string
  accessTokenValidUntil: string
}

export const $UserForm = z.object({
  mobileCountryCode: $CountryCode,
  mobile: $Mobile,
  acceptedTnCs: z.array($UUID),
  authCode: z.string(),
  legalAgeDeclaration: z.boolean().refine((val) => val === true, {
    message: 'Please accept',
  }),
  informationAccuracyDeclaration: z.boolean().refine((val) => val === true, {
    message: 'Please accept',
  }),
})

export type UserForm = z.infer<typeof $UserForm>

export const $SignInForm = z.object({
  code: z.string().min(1).max(2000),
  validityDays: z.number().min(MIN_VALIDITY_DAYS).max(MAX_VALIDITY_DAYS).optional(),
  forUserId: $UUID.optional(),
})

export type SignInForm = z.infer<typeof $SignInForm>

/**
 * urgent - user aleady blocked
 * last - user going to be blocked within next day
 * prudent - user going to be blocked within next 3 days
 * gentle - user going to be blocked within next 7 days
 * advance - user going to be blocked within next 30 days
 */
export type TnCReminderType = 'urgent' | 'last' | 'prudent' | 'gentle' | 'advance'

export const whetherRequiredTnCsAccepted = (
  tncs: { id: UUID; effectiveDate: string; expiryDate: string | null }[],
  acceptedTnCs: UUID[],
) =>
  whetherAllTnCsAccepted(
    // Already effective and latest
    tncs.filter((tnc) => tnc.effectiveDate <= today() && !tnc.expiryDate),
    acceptedTnCs,
  )

export const whetherAllTnCsAccepted = (tncs: { id: UUID }[], acceptedTnCs: UUID[]) =>
  tncs.every((tnc) => acceptedTnCs.includes(tnc.id))

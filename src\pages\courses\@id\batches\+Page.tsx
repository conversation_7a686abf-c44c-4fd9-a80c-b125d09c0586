import { usePageContext } from 'vike-react/usePageContext'

import { $CourseBatchesSearch } from '@/shared/batch/batch-utils.shared'
import { extractUuid } from '@/shared/common/common-utils.shared'

import { BatchesPage } from './BatchesPage'

export default () => {
  const pageContext = usePageContext()
  const courseId = extractUuid(pageContext.routeParams.id)
  const search = $CourseBatchesSearch.parse(pageContext.urlParsed.search)

  return <BatchesPage courseId={courseId} search={search} />
}

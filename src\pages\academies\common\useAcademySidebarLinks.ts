import {
  HomeModernIcon,
  PlusCircleIcon,
  InformationCircleIcon,
  ShoppingCartIcon,
  BookOpenIcon,
} from '@heroicons/react/24/outline'

import { academyPath } from '@/shared/academies/academy-utils.shared'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'

import { useMyAcademyQuery } from './academy-queries'

export const useAcademySidebarLinks = () => {
  const { data: myAcademy } = useMyAcademyQuery()
  const { currentProfile } = useCurrentProfile()

  const help = { name: 'Academy Help', href: '#', icon: InformationCircleIcon }

  if (myAcademy) {
    return [
      { name: 'Academy', href: academyPath(myAcademy), icon: HomeModernIcon },
      { name: 'Courses', href: `/courses?academyId=${myAcademy.id}&includeUnpublished=true`, icon: BookOpenIcon },
      { name: 'Payments', href: `${academyPath(myAcademy)}/payments?statuses=pending`, icon: ShoppingCartIcon },
      help,
    ]
  }

  if (currentProfile?.role === 'principal') {
    return [{ name: 'Create an Academy', href: '/academies/add', icon: PlusCircleIcon }, help]
  }

  return []
}

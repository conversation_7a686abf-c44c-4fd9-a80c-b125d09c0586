# Attachment Position Management

## Overview
This document explains our approach to managing ordered attachments in courses using string-based positions.

## Position Constants
- **MIN_POSITION**: `!!` - The smallest possible position value
- **MAX_POSITION**: `~~` - The largest possible position value

We chose two-character length position values because:
1. They provide adequate spacing for inserting items in between
2. They're readable and easy to debug
3. With the ASCII range from `!` (33) to `~` (126), we have 93 characters
4. Two characters gives us thousands of possible combinations (93²), far exceeding our expected needs

## Adding Attachments
When adding a new attachment:
1. We retrieve the position of the last attachment in the course
2. We calculate a new position between the last attachment's position and MAX_POSITION using `nextBetween`
3. This ensures new attachments are always added at the end of the list

## The `nextBetween` Function
The `nextBetween` function in `str-math.ts` calculates a string that lexicographically falls between two given strings.

### Limitation of `increase` to 4
The function limits the `increase` value to 4 when determining the character code for the new position string. This is a good balance because:

1. **Pros of limit=4**:
   - Provides predictable spacing between positions
   - Prevents huge jumps in character values
   - Keeps positions readable and within similar ranges

2. **Cons of increasing the limit**:
   - Positions might become less evenly distributed
   - Could make debugging harder if positions vary widely

3. **Cons of decreasing the limit**:
   - Less room for inserting new items between existing ones
   - Might need position recalculation sooner

For our expected usage (around 100 attachments per course with occasional reordering), the current limit of 4 should be sufficient.

import { eq } from 'drizzle-orm'

import { ensureExists } from '@/server/common/error/ensureExists'
import { db } from '@/server/db/db'
import { districtTable } from '@/server/db/schema/master-schema'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'

import type { UUID } from 'crypto'
import type { Logger } from 'pino'

const findDistrictDaf = (log: Logger, districtId: UUID) =>
  withLog(log, 'findDistrictDaf', () =>
    db.query.districtTable.findFirst({
      columns: {
        id: true,
        name: true,
      },
      with: {
        state: {
          columns: {
            id: true,
            name: true,
          },
          with: {
            country: {
              columns: {
                code: true,
                name: true,
              },
            },
          },
        },
      },
      where: eq(districtTable.id, districtId),
    }),
  )

export const getDistrict = async (log: Logger, districtId: UUID) => {
  const district = await findDistrictDaf(log, districtId)
  ensureExists(district, districtId, 'District')
  return district
}

export type GetDistrict = typeof getDistrict
export type District = Awaited<ReturnType<GetDistrict>>
setQuery('getDistrict', getDistrict)

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $AddBatchEventForm } from '@/shared/batch/batch-utils.shared'

import { addBatchEvent } from './addBatchEvent'

export const addBatchEventHandler = new Hono<HonoVars>().post(
  '/',
  zValidator('json', $AddBatchEventForm),
  async (c) => {
    const form = c.req.valid('json')
    const ctx = getCtx(c)

    await addBatchEvent(ctx, form)
    return c.body(null, 201)
  },
)

import { eq } from 'drizzle-orm'
import { pino } from 'pino'

import { ContextProfile, Ctx } from '@/server/common/auth-context-types.server'
import { env } from '@/server/common/env.server'
import { ensureProfile } from '@/server/common/error/ensure-profile.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { mail } from '@/server/common/mail/mail'
import { db } from '@/server/db/db'
import { userTable } from '@/server/db/schema/user-schema'
import { createInvitationToken } from '@/server/profiles/common/invitation-token-utils.server'
import { withLog } from '@/shared/common/withLog.shared'
import { InviteProfileForm } from '@/shared/profiles/profile-utils.shared'
import { ERoles, ROLES_PROVIDING_SERVICE } from '@/shared/profiles/role-utils.shared'

export const inviteProfile = async (c: Ctx, form: InviteProfileForm) => {
  ensureUser(c.user)
  ensureProfile(c.profile, { roleAnyOf: ROLES_PROVIDING_SERVICE })

  ensure(ERoles[c.profile.role].serviceRecipients.includes(form.role), {
    logMessage: `Profile ${c.profile.id} is not authorized to invite profiles of role ${form.role}`,
    issueMessage: `Your role, ${c.profile.role}, does not have permission to invite profiles of role ${form.role}`,
    statusCode: 409,
  })

  const user = await findUserByEmail(c.log, form.email)
  ensure(user, {
    logMessage: `User with email ${form.email} not found`,
    issueMessage: `User with email ${form.email} not found. Please ask them to sign up first.`,
    statusCode: 404,
  })

  ensure(!user.suspendedAt, {
    logMessage: `User with email ${form.email} is suspended`,
    issueMessage: `User with email ${form.email} is suspended. Please ask them to contact support.`,
    statusCode: 409,
  })

  ensure(user.emailVerified, {
    logMessage: `User with email ${form.email} is not verified`,
    issueMessage: `User with email ${form.email} is not verified. Please ask them to first verify their email at Google.`,
    statusCode: 409,
  })

  const inviteToken = await createInvitationToken(env.JWT_SECRET_KEY, c.profile.id, form)
  await sendInvitationEmail(c.log, c.profile, form, inviteToken)
}

const sendInvitationEmail = (
  log: pino.Logger<never, boolean>,
  invitor: ContextProfile,
  form: InviteProfileForm,
  invitationToken: string,
) =>
  withLog(log, 'sendInvitationEmail', () =>
    mail(log, true, 'invitation', 'en', {
      to: form.email,
      data: {
        invitorName: invitor.displayName,
        invitorRole: ERoles[invitor.role].name,
        inviteeRole: ERoles[form.role].name,
        invitationUrl: `${env.HOME_URL}/profiles/add?role=${form.role}&invitationToken=${invitationToken}`,
      },
    }),
  )

const findUserByEmail = async (log: pino.Logger, email: string) =>
  db.query.userTable.findFirst({
    columns: { emailVerified: true, suspendedAt: true },
    where: eq(userTable.email, email),
  })

import { navigate } from 'vike/client/router'

import { confirmAndRun } from '@/pages/common/confirmation/confirmation-store'
import { showNotification } from '@/pages/common/notification/notification-store'
import { ShowData } from '@/pages/common/ShowData'
import { getErrorMessage } from '@/pages/common/utils/error-utils.ui'
import { type Profile } from '@/server/profiles/get/getProfile.server'
import { ERoles } from '@/shared/profiles/role-utils.shared'

import { useCurrentProfile } from '../common/current-profile-store'
import {
  useRemoveProfileMutation,
  useSubmitProfileForApprovalMutation,
  useApproveProfileMutation,
} from '../common/profile-queries'

export const ProfileActions = ({ profile }: { profile: NonNullable<Profile> }) => {
  const { currentProfile, profileIsPending, profileError } = useCurrentProfile()
  const removeProfileMutation = useRemoveProfileMutation(profile.id)
  const submitProfileForApprovalMutation = useSubmitProfileForApprovalMutation(profile.id)
  const approveProfileMutation = useApproveProfileMutation(profile.id)

  const handleRemove = () => {
    confirmAndRun(
      async () => {
        try {
          await removeProfileMutation.mutateAsync()
          showNotification({
            heading: 'Profile removed',
            type: 'success',
          })
          void navigate('/')
        } catch (error) {
          showNotification({
            heading: 'Error removing profile',
            message: getErrorMessage(error),
            type: 'error',
          })
        }
      },
      {
        heading: 'Remove Profile',
        message: `Are you sure you want to remove ${profile.displayName} (${ERoles[profile.role].name})? This action cannot be undone.`,
        okLabel: 'Remove',
      },
    )
  }

  const handleSubmitForApproval = async () => {
    try {
      await submitProfileForApprovalMutation.mutateAsync()
      showNotification({
        heading: 'Profile submitted for approval',
        type: 'success',
      })
    } catch (error) {
      showNotification({
        heading: getErrorMessage(error),
        type: 'error',
      })
    }
  }

  const handleApprove = async () => {
    try {
      await approveProfileMutation.mutateAsync()
      showNotification({
        heading: 'Profile approved',
        type: 'success',
      })
    } catch (error) {
      showNotification({
        heading: getErrorMessage(error),
        type: 'error',
      })
    }
  }

  const isMyProfile = currentProfile?.userId === profile.userId
  const toApprove =
    currentProfile?.id === profile.serviceProviderId && profile.submittedForApprovalAt && !profile.approvedAt

  return (
    <ShowData isPending={profileIsPending} error={profileError}>
      {(isMyProfile || toApprove) && (
        <div className="flex flex-row sm:flex-col gap-x-3 text-sm link underline items-start">
          {toApprove && (
            <button type="button" disabled={approveProfileMutation.isPending} onClick={handleApprove}>
              Approve
            </button>
          )}
          {isMyProfile && (
            <>
              <button type="button" onClick={() => navigate(`/profiles/${profile.id}/edit`)}>
                Edit
              </button>
              {!profile.submittedForApprovalAt && !profile.approvedAt && (
                <button
                  type="button"
                  disabled={submitProfileForApprovalMutation.isPending}
                  onClick={handleSubmitForApproval}
                >
                  Submit for Approval
                </button>
              )}
              <button type="button" disabled={removeProfileMutation.isPending} onClick={handleRemove}>
                Remove
              </button>
              <button type="button" onClick={() => navigate('/users/me')}>
                User
              </button>
            </>
          )}
        </div>
      )}
    </ShowData>
  )
}

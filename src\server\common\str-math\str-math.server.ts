/**
 * Utility module for string-based positional operations.
 * Used primarily for maintaining ordered sequences in the database
 * without having to reindex everything when inserting elements.
 */

import { HIGHEST_CHAR, LOWEST_CHAR } from '@/shared/common/str-math.shared'

import { ensure, getMyException } from '../error/ensure.server'

/**
 * Generates the next string in lexicographical order.
 *
 * @param str The base string
 * @returns The next string in lexicographical order, or null if str consists of all highest characters
 */
export function getNext(str: string): string | null {
  let i = str.length - 1
  let result = str

  while (i >= 0) {
    const currentCharCode = str.charCodeAt(i)
    const highestCharCode = HIGHEST_CHAR.charCodeAt(0)

    if (currentCharCode < highestCharCode) {
      const nextChar = String.fromCharCode(currentCharCode + 1)
      return result.substring(0, i) + nextChar + result.substring(i + 1)
    }

    result = result.substring(0, i) + LOWEST_CHAR + result.substring(i + 1)
    i--
  }

  return null
}

/**
 * Finds a string value that lexicographically falls between two given strings.
 * Used for positioning elements within collections without having to reindex when inserting elements.
 *
 * @param low The lower bound string
 * @param high The upper bound string
 * @returns A string that falls between low and high in lexicographical order, or null if not possible
 * @throws Error if the calculated string is not between low and high
 */
export function nextBetween(low: string, high: string): string {
  ensure(low < high, {
    statusCode: 400,
    issueMessage: 'Low must be less than high. Please contact support.',
    logMessage: `Low must be less than high: LOW=${low} HIGH=${high}`,
  })

  let result = ''
  const lowestCharCode = LOWEST_CHAR.charCodeAt(0)
  const highestCharCode = HIGHEST_CHAR.charCodeAt(0)

  for (let i = 0; i < 10000; i++) {
    const lowCharCode = i < low.length ? low.charCodeAt(i) : lowestCharCode
    const highCharCode = i < high.length ? high.charCodeAt(i) : highestCharCode
    const gap = highCharCode - lowCharCode

    if (gap > 1) {
      let increase = Math.floor(gap / 2)
      if (increase > 4) {
        increase = 4
      }

      result += String.fromCharCode(lowCharCode + increase)

      ensure(result > low && result < high, {
        statusCode: 500,
        issueMessage: `String average not in order. Please contact support.`,
        logMessage: `String average not in order: LOW=${low} AVG=${result} HIGH=${high}`,
      })

      return result
    }

    result += String.fromCharCode(lowCharCode)
  }

  throw getMyException({
    statusCode: 500,
    issueMessage: `Could not find a string between ${low} and ${high}, after 10000 attempts. Please contact support.`,
    logMessage: `Could not find a string between ${low} and ${high}, after 10000 attempts.`,
  })
}

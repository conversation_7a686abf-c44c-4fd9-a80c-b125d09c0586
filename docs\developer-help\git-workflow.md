# Git Workflow Documentation

This document outlines our team's Git workflow, branch strategy, and development process. Follow these guidelines to ensure a smooth development process.

## Branch Structure

- `main` - Protected development branch with linear history (pull requests required) that automatically deploys to staging environment
- `production` - Deployment branch for the production environment

## Development Workflow

### 1. Setting Up Your Local Repository

```bash
# Clone the repository
git clone https://github.com/naturalprogrammer/np-tuition-vr.git
cd np-tuition-vr

# Configure Git to rebase by default when pulling
git config pull.rebase true

# Set up Git to prune remote branches automatically
git config fetch.prune true

# Set VS Code as your Git editor (recommended)
git config --global core.editor "code --wait"
```

### 2. Feature Development

#### Creating a Feature Branch

```bash
# Ensure you have the latest main branch
git checkout main
git pull

# Create a new feature branch
git checkout -b feature/your-feature-name
```

#### Working on Your Feature

```bash
# Make changes to files
# ...

# Stage your changes
git add .

# Commit your changes with a descriptive message
git commit -m "#37: Add meaningful commit message"

# Regularly rebase with main to keep up-to-date
git fetch origin
git rebase origin/main
```

> **Note**: All commit messages must follow the format `#[issue-number]: description` (e.g. `#37: Fix login validation`), where the issue number corresponds to the GitHub issue being addressed.

#### Cleaning Your Commit History

Before submitting your work for review, clean up your commit history:

```bash
# Interactive rebase to clean up commits
git rebase -i origin/main

# In the editor, mark commits to squash/fixup/reword
# - keep atomic, meaningful commits
# - squash or fixup implementation details
# - ensure each commit has a clear purpose
```

### 3. Code Review Process

#### Submitting a Pull Request

```bash
# Push your branch to GitHub
git push -u origin feature/your-feature-name

# Create a pull request through GitHub UI targeting main branch
# Provide clear description of changes and reference any related issues
```

#### Addressing Review Feedback

```bash
# Make requested changes
# ...

# Stage and commit changes
git add .
git commit -m "#37: Address review feedback: description of changes"

# Rebase and clean up commit history again if needed
git rebase -i origin/main

# Force push your updated branch
git push --force-with-lease origin # if the remote branch is already set
git push --force-with-lease origin feature/your-feature-name # if the remote branch is not yet set
```

### 4. Merging to Main

After approval, your PR will be merged to `main` using GitHub's UI, which enforces our branch protection rules.

Once merged to `main`, changes will automatically deploy to our staging environment for testing.

### 5. Deployment Process

#### Deploying to Production

After testing on staging (which automatically reflects the latest `main` branch):

```bash
# Switch to production branch
git checkout production

# Update production with changes from main
git pull
git rebase origin/main

# Push updated production branch
git push --force-with-lease origin production
```

This will trigger a deployment to our DigitalOcean production environment.

## Using VS Code for Git Operations

VS Code provides a much more user-friendly interface for Git operations, especially for rebasing and conflict resolution. We strongly recommend using VS Code for Git operations.

### Setting Up VS Code for Git

1. **Install the GitLens extension**
   - Open VS Code and go to Extensions (Ctrl+Shift+X)
   - Search for "GitLens" and install it
   - This enhances Git capabilities in VS Code with powerful features

2. **Configure VS Code as your Git editor**
   - Run this command if you haven't already:
   ```bash
   git config --global core.editor "code --wait"
   ```

### Interactive Rebasing with VS Code

When you run `git rebase -i origin/main`, VS Code will open instead of the terminal editor:

1. **Editing the rebase file**
   - VS Code will show a list of commits with actions (pick, squash, etc.)
   - Change "pick" to other commands as needed:
     - `s` or `squash`: combine with previous commit
     - `f` or `fixup`: combine with previous commit (discard message)
     - `r` or `reword`: keep commit but edit message
     - `d` or `drop`: remove the commit
   - Save (Ctrl+S) and close the file to continue the rebase

2. **For commit messages**
   - If you used `squash` or `reword`, VS Code will open another file for editing commit messages
   - Edit as needed, then save and close to continue
   - Remember to maintain the `#[issue-number]: description` format for all commit messages

### Resolving Conflicts in VS Code

When Git encounters conflicts during a rebase:

1. **Open the conflicted files**
   - All conflicted files will be visible in the Source Control panel
   - Click on a file to open it

2. **Use VS Code's conflict resolution interface**
   - VS Code shows conflicts with "Current Change" (your branch) and "Incoming Change" (main branch)
   - Choose from options in the conflict editor:
     - "Accept Current Change" - keep your version
     - "Accept Incoming Change" - take the version from main
     - "Accept Both Changes" - keep both versions
     - "Compare Changes" - see side-by-side diff

3. **After resolving all conflicts**
   - Stage the resolved files with the "+" button in VS Code's Source Control panel
   - Continue the rebase:
   ```bash
   git rebase --continue
   ```

### Using VS Code's Source Control Panel

For common Git operations without the command line:

1. **Stage changes**: Click the "+" next to modified files
2. **Commit**: Enter message in the text field and click the checkmark (remember to use the format `#[issue-number]: description`)
3. **Pull/Push/Sync**: Use the buttons in the Source Control panel
4. **View history**: Right-click a file and select "Show File History" (with GitLens)
5. **Branch operations**: Click on the branch name in the status bar for a menu of branch operations

## Best Practices

1. **Keep Branches Short-lived**: Complete and merge feature branches within a few days when possible.

2. **Write Clear Commit Messages**: 
   - All commit messages must start with the GitHub issue number: `#[issue-number]: description` (e.g. `#37: Fix login validation`)
   - First line: issue number and concise summary (50 chars or less)
   - Blank line
   - Detailed explanation if needed

3. **Rebase vs. Merge**: We prefer rebasing to maintain a linear, clean history:
   - Rebase your feature branch on main before requesting reviews
   - Clean up your commits during rebase
   - Use `--force-with-lease` when pushing rebased branches

4. **Local Testing**: Always run tests locally before submitting a PR:
   ```bash
   # Run tests (replace with actual test command)
   npm run test
   ```

5. **Branch Naming Convention**:
   - Feature branches: `feature/short-description`
   - Bug fixes: `fix/issue-description`
   - Documentation: `docs/what-changed`

## Branch Protection Rules

Our repository has the following protection rules:

1. **Main Branch**:
   - Requires pull requests with at least 1 review
   - Requires linear history
   - No force pushes or deletions
   - Stale PR approvals are dismissed when new commits are pushed
   - Changes trigger automatic deployment to staging environment

2. **Production Branch**:
   - No direct pushes (use rebase workflow described above)
   - Requires linear history
   - No deletions

## Git Cheat Sheet

### Everyday Commands
```bash
# Update your local copy
git pull

# Create new branch
git checkout -b branch-name

# Check status of your changes
git status

# Stage files
git add filename    # specific file
git add .           # all changes

# Commit changes
git commit -m "#37: Your message"

# Push to remote
git push -u origin branch-name   # first push
git push                         # subsequent pushes
```

### Rebasing
```bash
# Rebase on main
git rebase origin/main

# Interactive rebase
git rebase -i HEAD~3    # Rebase last 3 commits
git rebase -i origin/main

# Continue after resolving conflicts
git rebase --continue

# Abort rebase
git rebase --abort
```

### Advanced
```bash
# Amend most recent commit
git commit --amend

# Temporarily store changes
git stash
git stash pop

# View commit history
git log --oneline --graph

# Force push (with safety)
git push --force-with-lease
```

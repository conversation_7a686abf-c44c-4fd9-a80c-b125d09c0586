import { Menu, Menu<PERSON>utton, MenuItem, MenuItems } from '@headlessui/react'
import {
  ArrowRightStartOnRectangleIcon,
  ArrowsRightLeftIcon,
  EnvelopeIcon,
  UserCircleIcon,
  UserIcon,
  UserPlusIcon,
} from '@heroicons/react/20/solid'

import { profilePath } from '@/shared/profiles/profile-utils.shared'
import { ERoles } from '@/shared/profiles/role-utils.shared'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'
import { clearAuth } from '@ui/users/auth/auth-token-store'

import { setProfileModalVisibility } from '../modal/profile-modal-store'

import { ProfileMenuTrigger } from './ProfileMenuTrigger'

export const ProfileMenu = () => {
  const { currentProfile } = useCurrentProfile()
  const isServiceProvider = currentProfile ? ERoles[currentProfile.role].serviceRecipients.length > 0 : false

  return (
    <Menu as="div" className="relative inline-block text-left">
      <MenuButton className="-m-1.5 flex items-center p-1.5">
        <ProfileMenuTrigger />
      </MenuButton>

      <MenuItems
        transition
        className="absolute right-0 z-10 mt-2 w-56 origin-top-right divide-y divide-gray-200 rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none data-closed:scale-95 data-closed:transform data-closed:opacity-0 data-enter:duration-100 data-enter:ease-out data-leave:duration-75 data-leave:ease-in"
      >
        {currentProfile && (
          <div className="py-1">
            <MenuItem>
              <button
                type="button"
                className="group flex items-center px-4 py-2 text-sm text-gray-700 data-focus:bg-gray-100 data-focus:text-gray-900 data-focus:outline-none w-full"
                onClick={() => setProfileModalVisibility(true)}
              >
                <ArrowsRightLeftIcon
                  className="mr-3 size-5 text-gray-400 group-data-focus:text-gray-500"
                  aria-hidden="true"
                />
                Switch Profile
              </button>
            </MenuItem>
          </div>
        )}

        <div className="py-1">
          <MenuItem>
            <a
              href="/profiles/add"
              className="group flex items-center px-4 py-2 text-sm text-gray-700 data-focus:bg-gray-100 data-focus:text-gray-900 data-focus:outline-none"
            >
              <UserPlusIcon className="mr-3 size-5 text-gray-400 group-data-focus:text-gray-500" aria-hidden="true" />
              Add a Profile
            </a>
          </MenuItem>

          {isServiceProvider && (
            <MenuItem>
              <a
                href="/profiles/invite"
                className="group flex items-center px-4 py-2 text-sm text-gray-700 data-focus:bg-gray-100 data-focus:text-gray-900 data-focus:outline-none"
              >
                <EnvelopeIcon className="mr-3 size-5 text-gray-400 group-data-focus:text-gray-500" aria-hidden="true" />
                Invite a Profile
              </a>
            </MenuItem>
          )}
        </div>

        <div className="py-1">
          {currentProfile && (
            <MenuItem>
              <a
                href={profilePath(currentProfile)}
                className="group flex items-center px-4 py-2 text-sm text-gray-700 data-focus:bg-gray-100 data-focus:text-gray-900 data-focus:outline-none"
              >
                <UserIcon className="mr-3 size-5 text-gray-400 group-data-focus:text-gray-500" aria-hidden="true" />
                View Profile Data
              </a>
            </MenuItem>
          )}

          <MenuItem>
            <a
              href="/users/me"
              className="group flex items-center px-4 py-2 text-sm text-gray-700 data-focus:bg-gray-100 data-focus:text-gray-900 data-focus:outline-none"
            >
              <UserCircleIcon className="mr-3 size-5 text-gray-400 group-data-focus:text-gray-500" aria-hidden="true" />
              View User Data
            </a>
          </MenuItem>
        </div>
        <div className="py-1">
          <MenuItem>
            <button
              type="button"
              className="group flex w-full items-center px-4 py-2 text-sm text-gray-700 data-focus:bg-gray-100 data-focus:text-gray-900 data-focus:outline-none"
              onClick={() => clearAuth()}
            >
              <ArrowRightStartOnRectangleIcon
                className="mr-3 size-5 text-gray-400 group-data-focus:text-gray-500"
                aria-hidden="true"
              />
              Sign out
            </button>
          </MenuItem>
        </div>
      </MenuItems>
    </Menu>
  )
}

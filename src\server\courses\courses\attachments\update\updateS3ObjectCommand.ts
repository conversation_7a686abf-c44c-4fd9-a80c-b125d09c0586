import { pino } from 'pino'

import { addCommand, Command } from '@/server/common/command/processCommands'
import { makeObjectPrivate, makeObjectPublic } from '@/server/common/s3/s3-utils.server'
import { getLogger } from '@/shared/common/logger.shared'
import { withLog } from '@/shared/common/withLog.shared'

export const UPDATE_S3OBJECT_COMMAND = 'updateS3Object'

export type UpdateS3ObjectCommandData = {
  path: string
  free: boolean
}

async function executeCommand(log: pino.Logger, command: Command<UpdateS3ObjectCommandData>) {
  const data = command.data
  await withLog(
    log,
    'updateS3ObjectCommand',
    () => (data.free ? makeObjectPublic(data.path) : makeObjectPrivate(data.path)),
    {
      data: {
        path: data.path,
        free: data.free,
      },
    },
  )
}

addCommand(UPDATE_S3OBJECT_COMMAND, executeCommand)
getLogger().info(`Loaded ${import.meta.url}`)

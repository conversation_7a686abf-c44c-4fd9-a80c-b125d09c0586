import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $AddBatchStudentPaymentForm } from '@/shared/batch/batch-utils.shared'
import { $HasId } from '@/shared/common/common-utils.shared'

import { addStudentFeePayment } from './addStudentFeePayment'

export const addStudentFeePaymentHandler = new Hono<HonoVars>().post(
  '/',
  zValidator('param', $HasId),
  zValidator('json', $AddBatchStudentPaymentForm),
  async (c) => {
    const { id: batchId } = c.req.valid('param')
    const form = c.req.valid('json')
    await addStudentFeePayment(getCtx(c), batchId, form)
    return c.body(null, 201)
  },
)

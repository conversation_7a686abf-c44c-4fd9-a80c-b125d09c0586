import { PageContext } from 'vike/types'

import { setLocale } from '@/shared/common/date-utils.shared'
import { updateStore } from '@ui/common/utils/updateStore'

import { getTokenFromSecureStorage, tokenStore } from './users/auth/auth-token-store'

import type { OnHydrationEndAsync } from 'vike/types'

const onHydrationEnd: OnHydrationEndAsync = async (_pageContext: PageContext): ReturnType<OnHydrationEndAsync> => {
  setLocale(navigator.language)
  const token = getTokenFromSecureStorage()
  if (token) {
    console.log('onHydrationEnd: Setting token', token)
    updateStore(tokenStore, { token })
  }
  console.log('onHydrationEnd: The page is now interactive')
}

export { onHydrationEnd }

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { Logger } from 'pino'

import { db } from '@/server/db/db'
import { userTable } from '@/server/db/schema/user-schema'
import { withLog } from '@/shared/common/withLog.shared'

const findUserParams = {
  columns: {
    email: true,
    emailVerified: true,
    mobileVerified: true,
    suspendedAt: true,
    tokensValidFrom: true,
    language: true,
  },
} as const

const findUserWithTnCsParams = {
  ...findUserParams,
  with: {
    tncAcceptances: {
      columns: {
        tncVersionId: true,
      },
    },
  },
} as const

const findUserByIdWithoutTnCsDaf = (log: Logger, userId: UUID) => {
  return withLog(log, 'findUserForAuthDaf.findUserByIdWithoutTnCsDaf', () =>
    db.query.userTable.findFirst({
      ...findUserParams,
      where: eq(userTable.id, userId),
    }),
  )
}

const findUserByIdWithTnCsDaf = (log: Logger, userId: UUID) => {
  return withLog(log, 'findUserForAuthDaf.findUserByIdWithTnCsDaf', () =>
    db.query.userTable.findFirst({
      ...findUserWithTnCsParams,
      where: eq(userTable.id, userId),
    }),
  )
}

export type UserWithTnCsRow = NonNullable<Awaited<ReturnType<typeof findUserByIdWithTnCsDaf>>>

export const findUserForAuthDaf = (log: Logger, userId: UUID, withTnCs: boolean) =>
  withTnCs ? findUserByIdWithTnCsDaf(log, userId) : findUserByIdWithoutTnCsDaf(log, userId)

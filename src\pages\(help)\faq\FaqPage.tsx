import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/react'
import { MinusIcon, PlusIcon } from '@heroicons/react/24/outline'

const faqs = [
  {
    question: 'What is TuitionLance?',
    answer:
      'TuitionLance is a platform for academies to offer live online tuition to students. We bridge the gap between traditional and digital education with scheduled live classes and interactive learning sessions.',
  },
  {
    question: 'How do I join a batch as a student?',
    answer:
      'You can browse available courses, their batches, and select "Join" on the ones you are interested in. You will then be enrolled in the batch and can attend scheduled live classes.',
  },
  {
    question: 'Can I leave a batch after joining?',
    answer:
      "Yes, you can leave a batch at any time. When you leave, you will be removed from the batch and won't be able to access batch resources anymore.",
  },
  {
    question: 'What roles are available on TuitionLance?',
    answer:
      'TuitionLance offers different roles including Principal, Mentor, Teacher, Student, Admin, Manager, and Executive. Each role has specific permissions and responsibilities within the platform.',
  },
  {
    question: 'How does fee payment work?',
    answer:
      'Each batch has a periodic fee. You will need to pay this fee by the due date, though there is a grace period. If payment is not made by the end of the grace period, access to batch resources and events will be temporarily blocked until payment is completed.',
  },
  {
    question: 'Can I work with multiple academies?',
    answer:
      "Yes. However, each principal, mentor, and teacher profile is associated with one academy only. If you work with multiple academies, you'll need to create separate profiles for each academy.",
  },
  {
    question: 'How do I get access to course materials?',
    answer:
      'Course materials (resources) are released gradually as your batch progresses. Some resources are publicly visible, while restricted ones are made available by academy staff at appropriate times in your learning journey.',
  },
  {
    question: 'How do I create a profile?',
    answer:
      'You can sign up using Google login and create multiple profiles. Student profiles can be freely created, while other roles (e.g. Principal, Mentor, Teacher) require an invitation from an authorized user.',
  },
]

export const FaqPage = () => {
  return (
    <div className="mx-auto p-4">
      <h2 className="text-3xl font-semibold tracking-tight text-gray-900 sm:text-4xl">Frequently asked questions</h2>
      <dl className="mt-16 divide-y divide-gray-900/10">
        {faqs.map((faq) => (
          <Disclosure key={faq.question} as="div" className="py-6 first:pt-0 last:pb-0">
            <dt>
              <DisclosureButton className="group flex w-full items-start justify-between text-left text-gray-900">
                <span className="text-base/7 font-semibold">{faq.question}</span>
                <span className="ml-6 flex h-7 items-center">
                  <PlusIcon aria-hidden="true" className="size-6 group-data-open:hidden" />
                  <MinusIcon aria-hidden="true" className="size-6 group-not-data-open:hidden" />
                </span>
              </DisclosureButton>
            </dt>
            <DisclosurePanel as="dd" className="mt-2 pr-12">
              <p className="text-base/7 text-gray-600">{faq.answer}</p>
            </DisclosurePanel>
          </Disclosure>
        ))}
      </dl>
    </div>
  )
}

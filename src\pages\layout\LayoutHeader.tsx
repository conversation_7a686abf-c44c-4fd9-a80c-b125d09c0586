import { BellIcon } from '@heroicons/react/24/outline'
import { clientOnly } from 'vike-react/clientOnly'

import { ViewDraftStudentPaymentsTrigger } from '@ui/students/me/payments/ViewDraftStudentPaymentsTrigger'
const UserSection = clientOnly(async () => (await import('./UserSection')).UserSection)

export const LayoutHeader = () => {
  return (
    <div className="flex self-stretch flex-1 gap-x-4 lg:gap-x-6 min-h-[4.5rem]">
      {/* <form className="relative flex-1 max-w-3xl sm:w-[40rem] sm:min-w-[20rem]" action="#" method="GET">
        <div className="flex items-center h-full">
          <MagnifyingGlassIcon
            className="absolute left-3 text-gray-400 pointer-events-none size-5"
            aria-hidden="true"
          />
          <input
            type="search"
            name="search"
            aria-label="Search"
            className="block w-full h-10 pl-10 pr-3 text-base text-gray-900 bg-white border border-gray-200 rounded-lg outline-none placeholder:text-gray-400 sm:text-sm/6 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500"
            placeholder="Search"
          />
        </div>
      </form> */}
      <div className="flex items-center gap-x-4 lg:gap-x-6 ml-auto">
        <button type="button" className="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500">
          <span className="sr-only">View notifications</span>
          <BellIcon className="size-6" aria-hidden="true" />
        </button>
        <ViewDraftStudentPaymentsTrigger />
        <div className="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10" aria-hidden="true" />
        <div className="min-w-[2.5rem]">
          <UserSection />
        </div>
      </div>
    </div>
  )
}

import { isNull } from 'drizzle-orm'

import { db } from '@/server/db/db'
import { countryTable } from '@/server/db/schema/master-schema'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'

import type { Logger } from 'pino'

const findCountriesDaf = (log: Logger) =>
  withLog(log, 'findCountriesDaf', () =>
    db.query.countryTable.findMany({
      columns: {
        code: true,
        name: true,
        phonePrefix: true,
      },
      where: isNull(countryTable.suspendedAt),
      orderBy: countryTable.name,
    }),
  )

export const listCountries = (log: Logger) => findCountriesDaf(log)
export type ListCountries = typeof listCountries
export type Country = Awaited<ReturnType<ListCountries>>[number]
setQuery('listCountries', listCountries)

import nodemailer from 'nodemailer'
import { Logger } from 'pino'

import { env } from '@/server/common/env.server'
import { Email } from '@/shared/common/common-utils.shared'

import { SENDER_EMAIL, SENDER_EMAIL_WITH_NAME } from '../mail-utils'

const transporter = nodemailer.createTransport({
  host: 'smtp.improvmx.com',
  port: 587,
  secure: false, // true for 465, false for other ports
  auth: {
    user: SENDER_EMAIL,
    pass: env.SMTP_PASSWORD,
  },
})

// Only verify transporter if SMTP_PASSWORD is configured and not in test environment
if (env.SMTP_PASSWORD && env.APP_ENV !== 'test') {
  await transporter.verify()
}

export const sendSmtpMail = async (log: Logger, to: Email, subject: string, body: string) => {
  try {
    const info = await transporter.sendMail({
      from: SENDER_EMAIL_WITH_NAME,
      to,
      subject,
      html: body, // HTML body
    })
    log.info({ smtp: { to, subject, info } }, `Email sent to ${to}: ${subject}`)
  } catch (error) {
    log.error(error)
    throw error
  }
}

import { UUID, randomUUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { ContextUser, Ctx } from '@/server/common/auth-context-types.server'
import { env } from '@/server/common/env.server'
import { ensure } from '@/server/common/error/ensure.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db, Transaction } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { academyStaffTable } from '@/server/db/schema/academy-schema'
import { profileTable } from '@/server/db/schema/profile-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { markdown2Html } from '@/shared/common/markdown-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { AddProfileForm, MAX_PROFILES_PER_USER } from '@/shared/profiles/profile-utils.shared'
import { <PERSON><PERSON><PERSON>, Role } from '@/shared/profiles/role-utils.shared'

import { parseInvitationToken } from '../common/invitation-token-utils.server'
import { ensureGoodProfile, findProfileForVerificationDaf } from '../common/profile-check.server'

const findExistingProfileDaf = (log: pino.Logger, userId: UUID, role: Role, displayName: string) =>
  withLog(log, 'findExistingProfileDaf', () =>
    db.query.profileTable.findFirst({
      ...dummyColumn,
      where: and(
        eq(profileTable.userId, userId),
        eq(profileTable.role, role),
        eq(profileTable.displayName, displayName),
      ),
    }),
  )

const countUserProfilesDaf = (log: pino.Logger, userId: UUID) =>
  withLog(log, 'countUserProfilesDaf', () => db.$count(profileTable, eq(profileTable.userId, userId)))

const insertProfileDaf = (
  log: pino.Logger,
  db: Transaction,
  userId: UUID,
  newProfileId: UUID,
  form: AddProfileForm,
  serviceProviderId: UUID | null,
) =>
  withLog(log, 'insertProfileDaf', () => {
    const now = utc().toJSDate()
    const approved = ERoles[form.role].autoApproved
    return db.insert(profileTable).values({
      id: newProfileId,
      userId,
      role: form.role,
      displayName: form.displayName,
      descr: form.descr ? markdown2Html(form.descr) : null,
      tncsAcceptedAt: now,
      serviceProviderId,
      submittedForApprovalAt: approved ? now : null,
      approvedAt: approved ? now : null,
    })
  })

const getInvitorProfileId = async (form: AddProfileForm, user: ContextUser) => {
  // Validate invitationToken
  if (form.invitationToken) {
    const payload = await parseInvitationToken(env.JWT_SECRET_KEY, form.invitationToken)

    ensure(payload.sub === user.email, {
      statusCode: 409,
      issueMessage: `The invitation token is not meant for your email address.`,
      logMessage: `User ${user.id} attempted to create a profile with an invitation token with wrong email address ${payload.sub}`,
    })
    ensure(form.role === payload.inviteeRole, {
      statusCode: 409,
      issueMessage: `The invitation token is not meant for your role.`,
      logMessage: `User ${user.id} attempted to create a profile with an invitation token with wrong role ${form.role}`,
    })
    return payload.invitorProfileId as UUID
  }
  ensure(ERoles[form.role].autoApproved, {
    statusCode: 409,
    issueMessage: `You need to be invited to create a profile.`,
    logMessage: `User ${user.id} attempted to create a profile without an invitation token.`,
  })
  return null
}

const ensureGoodInvitor = async (log: pino.Logger, invitorProfileId: UUID | null, inviteeRole: Role) => {
  if (invitorProfileId) {
    const invitorProfile = await findProfileForVerificationDaf(log, invitorProfileId)
    ensureGoodProfile(invitorProfileId, invitorProfile, 'Invitor profile')
    ensure(ERoles[invitorProfile.role].serviceRecipients.includes(inviteeRole), {
      statusCode: 409,
      issueMessage: `Invitor is not eligible to invite ${inviteeRole} profiles.`,
      logMessage: `Invitor profile with id ${invitorProfileId} is not eligible to invite ${inviteeRole} profiles.`,
    })
  }
}

const findAcademyStaffOfInvitorDaf = (log: pino.Logger, invitorProfileId: UUID) =>
  withLog(log, 'findAcademyStaffOfInvitorDaf', () =>
    db.query.academyStaffTable.findFirst({
      columns: {
        academyId: true,
      },
      where: eq(academyStaffTable.profileId, invitorProfileId),
    }),
  )

const insertAcademyStaffDaf = (log: pino.Logger, db: Transaction, newProfileId: UUID, academyId: UUID) =>
  withLog(log, 'insertAcademyStaffDaf', async () =>
    db.insert(academyStaffTable).values({
      profileId: newProfileId,
      academyId,
    }),
  )

export const addProfile = async (c: Ctx, form: AddProfileForm) => {
  ensureUser(c.user)

  const invitorProfileId = await getInvitorProfileId(form, c.user)
  await ensureGoodInvitor(c.log, invitorProfileId, form.role)

  // Check max profiles limit
  const profileCount = await countUserProfilesDaf(c.log, c.user.id)
  ensure(profileCount < MAX_PROFILES_PER_USER, {
    statusCode: 422,
    issueMessage: `You can only create up to ${MAX_PROFILES_PER_USER} profiles.`,
    logMessage: `User ${c.user.id} attempted to create more than ${MAX_PROFILES_PER_USER} profiles.`,
  })

  // Check if profile already exists
  const existingProfile = await findExistingProfileDaf(c.log, c.user.id, form.role, form.displayName)
  ensure(!existingProfile, {
    statusCode: 422,
    issueMessage: `Profile with role ${form.role} and display name ${form.displayName} already exists.`,
    logMessage: `User ${c.user.id} attempted to create a profile with role ${form.role} and display name ${form.displayName}, but this profile already exists.`,
  })

  // Create new profile
  const newProfileId = randomUUID()

  const invitorStaff = invitorProfileId ? await findAcademyStaffOfInvitorDaf(c.log, invitorProfileId) : null
  await db.transaction(async (tx) => {
    await insertProfileDaf(c.log, tx, c.user!.id, newProfileId, form, invitorProfileId)

    // If there's an invitor, add the new profile as staff of their academy
    if (invitorStaff) {
      await insertAcademyStaffDaf(c.log, tx, newProfileId, invitorStaff.academyId)
    }
  })

  return {
    id: newProfileId,
  }
}

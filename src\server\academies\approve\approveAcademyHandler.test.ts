import crypto, { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { DateTime } from 'luxon'
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'

import app from '@/hono-entry'
import { env } from '@/server/common/env.server'
import { db } from '@/server/db/db'
import { academyTable } from '@/server/db/schema/academy-schema'
import { profileTable } from '@/server/db/schema/profile-schema'
import { userTable, userTncAcceptedTable, userTncSignTable } from '@/server/db/schema/user-schema'
import { initDb, SUNDARGARH_DISTRICT_ID, TNC_PRIVACY_V1_ID, TNC_TOS_V1_ID } from '@/server/test/initDb'
import { createAccessToken } from '@/server/users/common/auth-token-utils.server'
import { PROFILE_HEADER } from '@/shared/profiles/profile-utils.shared'

const mocked = {
  now: DateTime.now(),
} as const

// Mock date utilities to have consistent test results
vi.mock(import('@/shared/common/date-utils-basic.shared'), async (importOriginal) => {
  const mod = await importOriginal()
  return {
    ...mod,
    utc: () => mocked.now,
  }
})

const testUser = {
  id: crypto.randomUUID(),
  googleId: 'test-google-id',
  googleRefreshToken: null,
  name: 'Test Admin',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/picture',
  language: 'en-US',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '**********',
  mobileVerified: true,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

const testProfile = {
  id: crypto.randomUUID(),
  userId: testUser.id,
  role: 'admin' as const,
  displayName: 'Test Admin',
  approvedAt: mocked.now.toJSDate(),
  tncsAcceptedAt: mocked.now.toJSDate(),
} as const

const testAcademy = {
  id: crypto.randomUUID(),
  name: 'Test Academy',
  email: '<EMAIL>',
  emailVerified: true,
  mobileCountryCode: 'IN',
  mobile: '**********',
  mobileVerified: true,
  currency: 'INR' as const,
  upiId: 'test@upi',
  districtId: SUNDARGARH_DISTRICT_ID,
  pincode: '770001',
  area: 'Test Area',
  tncsAcceptedAt: mocked.now.toJSDate(),
  approvedAt: null, // Academy starts unapproved
} as const

const makeAuthenticatedRequest = async (academyId: UUID, accessToken?: string) => {
  const token = accessToken || (await createAccessToken(env.JWT_SECRET_KEY, testUser.id))

  return app.request(
    `/api/academies/${academyId}/approve`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        [PROFILE_HEADER]: testProfile.id,
      },
    },
    {},
  )
}

const makeUnauthenticatedRequest = async (academyId: UUID) => {
  return app.request(
    `/api/academies/${academyId}/approve`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
    },
    {},
  )
}

const setupBasicTestData = async () => {
  // Create user
  await db.insert(userTable).values(testUser)

  // Add TnC acceptance records (required by authMiddleware for API endpoints)
  const acceptedTnCs = [TNC_TOS_V1_ID, TNC_PRIVACY_V1_ID] as const
  for (const tncId of acceptedTnCs) {
    await db.insert(userTncSignTable).values({
      id: crypto.randomUUID(),
      userId: testUser.id,
      tncVersionId: tncId,
      accepted: true,
    })

    await db.insert(userTncAcceptedTable).values({
      id: crypto.randomUUID(),
      userId: testUser.id,
      tncVersionId: tncId,
    })
  }

  // Create profile
  await db.insert(profileTable).values(testProfile)

  // Create academy
  await db.insert(academyTable).values(testAcademy)
}

const assertAcademyApproved = async (academyId: UUID) => {
  // Verify academy was approved in database
  const academies = await db.select().from(academyTable).where(eq(academyTable.id, academyId))
  expect(academies.length, 'Academy should exist in database').toBe(1)

  const academy = academies[0]
  expect(academy.approvedAt, 'Academy should be approved').toEqual(mocked.now.toJSDate())
}

describe('approveAcademyHandler', () => {
  beforeEach(async () => {
    await initDb()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Success scenarios', () => {
    test('should approve academy with valid admin user', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(testAcademy.id)

      // then
      expect(res.status, 'Status check').toBe(204)

      // Should return empty body for 204 No Content
      const responseText = await res.text()
      expect(responseText).toBe('')

      await assertAcademyApproved(testAcademy.id)
    })

    test('should approve academy that was already approved (idempotent)', async () => {
      // given
      await setupBasicTestData()

      await db
        .update(academyTable)
        .set({ approvedAt: new Date(2023, 0, 1) })
        .where(eq(academyTable.id, testAcademy.id))

      // when
      const res = await makeAuthenticatedRequest(testAcademy.id)

      // then
      expect(res.status, 'Status check').toBe(204)

      // Verify the approvedAt timestamp was updated to current time
      const academies = await db.select().from(academyTable).where(eq(academyTable.id, testAcademy.id))
      expect(academies[0].approvedAt).toEqual(mocked.now.toJSDate())
    })
  })

  describe('Authentication and Authorization errors', () => {
    test('should return 401 when no authorization header', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeUnauthenticatedRequest(testAcademy.id)

      // then
      expect(res.status, 'Status check').toBe(401)
    })

    test('should return 401 with invalid token', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeAuthenticatedRequest(testAcademy.id, 'invalid-token')

      // then
      expect(res.status, 'Status check').toBe(401)
    })

    test('should return 403 when user has not accepted TnCs', async () => {
      // given
      await db.insert(userTable).values(testUser)
      // Not adding TnC acceptance records
      await db.insert(profileTable).values(testProfile)
      await db.insert(academyTable).values(testAcademy)

      // when
      const res = await makeAuthenticatedRequest(testAcademy.id)

      // then
      expect(res.status, 'Status check').toBe(403)
      await expect(res).toBeError(403, [
        {
          code: 'custom',
          path: [],
          message: 'Please first accept all TnCs',
        },
      ])
    })

    test('should return 403 when no profile header', async () => {
      // given
      await db.insert(userTable).values(testUser)

      // Add TnC acceptance records
      const acceptedTnCs = [TNC_TOS_V1_ID, TNC_PRIVACY_V1_ID] as const
      for (const tncId of acceptedTnCs) {
        await db.insert(userTncSignTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
          accepted: true,
        })

        await db.insert(userTncAcceptedTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
        })
      }

      await db.insert(academyTable).values(testAcademy)

      const token = await createAccessToken(env.JWT_SECRET_KEY, testUser.id)
      const res = await app.request(
        `/api/academies/${testAcademy.id}/approve`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
            // Not including PROFILE_HEADER
          },
        },
        {},
      )

      // then
      expect(res.status, 'Status check').toBe(403)
      await expect(res).toBeError(403, [
        {
          code: 'custom',
          path: [],
          message: 'TL-Profile request header missing',
        },
      ])
    })

    test('should return 403 when profile is suspended', async () => {
      // given
      await db.insert(userTable).values(testUser)

      // Add TnC acceptance records
      const acceptedTnCs = [TNC_TOS_V1_ID, TNC_PRIVACY_V1_ID] as const
      for (const tncId of acceptedTnCs) {
        await db.insert(userTncSignTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
          accepted: true,
        })

        await db.insert(userTncAcceptedTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
        })
      }

      await db.insert(profileTable).values({
        ...testProfile,
        suspendedAt: mocked.now.toJSDate(),
      })

      await db.insert(academyTable).values(testAcademy)

      // when
      const res = await makeAuthenticatedRequest(testAcademy.id)

      // then
      expect(res.status, 'Status check').toBe(403)
      await expect(res).toBeError(403, [
        {
          code: 'custom',
          path: [],
          message: 'Profile is suspended',
        },
      ])
    })

    test('should return 403 when profile is not approved', async () => {
      // given
      await db.insert(userTable).values(testUser)

      // Add TnC acceptance records
      const acceptedTnCs = [TNC_TOS_V1_ID, TNC_PRIVACY_V1_ID] as const
      for (const tncId of acceptedTnCs) {
        await db.insert(userTncSignTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
          accepted: true,
        })

        await db.insert(userTncAcceptedTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
        })
      }

      await db.insert(profileTable).values({
        ...testProfile,
        approvedAt: null,
      })

      await db.insert(academyTable).values(testAcademy)

      // when
      const res = await makeAuthenticatedRequest(testAcademy.id)

      // then
      expect(res.status, 'Status check').toBe(403)
      await expect(res).toBeError(403, [
        {
          code: 'custom',
          path: [],
          message: 'Profile is not approved',
        },
      ])
    })

    test('should return 403 when user email is not verified', async () => {
      // given
      await db.insert(userTable).values({ ...testUser, emailVerified: false })

      // Add TnC acceptance records
      const acceptedTnCs = [TNC_TOS_V1_ID, TNC_PRIVACY_V1_ID] as const
      for (const tncId of acceptedTnCs) {
        await db.insert(userTncSignTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
          accepted: true,
        })

        await db.insert(userTncAcceptedTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
        })
      }

      await db.insert(profileTable).values(testProfile)
      await db.insert(academyTable).values(testAcademy)

      // when
      const res = await makeAuthenticatedRequest(testAcademy.id)

      // then
      expect(res.status, 'Status check').toBe(403)
      await expect(res).toBeError(403, [
        {
          code: 'custom',
          path: [],
          message: 'Please first verify your email at Google',
        },
      ])
    })

    test('should return 403 when profile has wrong role', async () => {
      // given
      await db.insert(userTable).values(testUser)

      // Add TnC acceptance records
      const acceptedTnCs = [TNC_TOS_V1_ID, TNC_PRIVACY_V1_ID] as const
      for (const tncId of acceptedTnCs) {
        await db.insert(userTncSignTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
          accepted: true,
        })

        await db.insert(userTncAcceptedTable).values({
          id: crypto.randomUUID(),
          userId: testUser.id,
          tncVersionId: tncId,
        })
      }

      await db.insert(profileTable).values({
        ...testProfile,
        role: 'principal', // Only admin role is allowed
      })

      await db.insert(academyTable).values(testAcademy)

      // when
      const res = await makeAuthenticatedRequest(testAcademy.id)

      // then
      expect(res.status, 'Status check').toBe(403)
      await expect(res).toBeError(403, [
        {
          code: 'custom',
          path: [],
          message: 'Only one of admin is allowed to do this operation',
        },
      ])
    })
  })

  describe('Business logic errors', () => {
    test('should return 404 when academy does not exist', async () => {
      // given
      await setupBasicTestData()
      const nonExistentAcademyId = crypto.randomUUID()

      // when
      const res = await makeAuthenticatedRequest(nonExistentAcademyId)

      // then
      expect(res.status, 'Status check').toBe(404)
      await expect(res).toBeError(404, [
        {
          code: 'custom',
          path: [],
          message: 'Academy not found.',
        },
      ])
    })
  })

  describe('Edge cases', () => {
    test('should handle UUID validation in path parameter', async () => {
      // given
      await setupBasicTestData()

      const token = await createAccessToken(env.JWT_SECRET_KEY, testUser.id)
      const res = await app.request(
        '/api/academies/invalid-uuid/approve',
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
            [PROFILE_HEADER]: testProfile.id,
          },
        },
        {},
      )

      // then
      // Note: This test checks how the system handles invalid UUIDs in path params
      // The exact error response depends on the routing/validation implementation
      expect(res.status).toBeGreaterThanOrEqual(400)
    })

    test('should approve academy with minimum required fields', async () => {
      // given
      await setupBasicTestData()

      // Create academy with minimal data
      const minimalAcademy = {
        id: crypto.randomUUID(),
        name: 'Minimal Academy',
        email: '<EMAIL>',
        emailVerified: false,
        mobileCountryCode: 'IN',
        mobile: '**********',
        mobileVerified: false,
        currency: 'INR' as const,
        upiId: 'minimal@upi',
        districtId: SUNDARGARH_DISTRICT_ID,
        pincode: '770001',
        area: 'Minimal Area',
        tncsAcceptedAt: mocked.now.toJSDate(),
        approvedAt: null,
      } as const
      await db.insert(academyTable).values(minimalAcademy)

      // when
      const res = await makeAuthenticatedRequest(minimalAcademy.id)

      // then
      expect(res.status, 'Status check').toBe(204)
      await assertAcademyApproved(minimalAcademy.id)
    })
  })
})

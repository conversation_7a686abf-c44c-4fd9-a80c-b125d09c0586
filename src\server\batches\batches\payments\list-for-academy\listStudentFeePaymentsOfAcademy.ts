import { UUID } from 'crypto'

import { and, desc, eq, inArray } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfile } from '@/server/common/error/ensure-profile.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { academyStaffTable, profileTable, studentFeePaymentTable, userTable } from '@/server/db/schema'
import { StudentFeePaymentOfAcademySearch } from '@/shared/batch/batch-utils.shared'
import { PAYMENT_STATUSES } from '@/shared/common/payment-utils/payment-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'

const findStudentFeePaymentsOfAcademyDaf = (
  log: pino.Logger,
  academyId: UUID,
  profileId: UUID,
  search: StudentFeePaymentOfAcademySearch,
) => {
  return withLog(log, 'findStudentFeePaymentsOfAcademyDaf', () => {
    const toFilterByStatus = search.statuses.length < PAYMENT_STATUSES.length
    return db
      .select({
        id: studentFeePaymentTable.id,
        studentId: studentFeePaymentTable.studentId,
        studentName: profileTable.displayName,
        studentEmail: userTable.email,
        method: studentFeePaymentTable.method,
        currency: studentFeePaymentTable.currency,
        cents: studentFeePaymentTable.cents,
        status: studentFeePaymentTable.status,
        createdAt: studentFeePaymentTable.createdAt,
        paidAt: studentFeePaymentTable.paidAt,
        paidRemarks: studentFeePaymentTable.paidRemarks,
      })
      .from(studentFeePaymentTable)
      .innerJoin(profileTable, eq(studentFeePaymentTable.studentId, profileTable.id))
      .innerJoin(userTable, eq(profileTable.userId, userTable.id))
      .innerJoin(academyStaffTable, eq(studentFeePaymentTable.academyId, academyStaffTable.academyId))
      .where(
        and(
          eq(academyStaffTable.profileId, profileId), // current profile is staff of the academy
          eq(studentFeePaymentTable.academyId, academyId), // payment belongs to the given academy
          toFilterByStatus ? inArray(studentFeePaymentTable.status, search.statuses) : undefined,
          search.studentEmail ? eq(userTable.email, search.studentEmail) : undefined,
        ),
      )
      .orderBy(studentFeePaymentTable.academyId, desc(studentFeePaymentTable.createdAt))
  })
}

export const listStudentFeePaymentsOfAcademy = async (
  c: Ctx,
  academyId: UUID,
  search: StudentFeePaymentOfAcademySearch,
) => {
  ensureUser(c.user)
  ensureProfile(c.profile, { roleAnyOf: ACADEMY_STAFF })

  c.log.info({ search }, 'listStudentFeePaymentsOfAcademy')
  return findStudentFeePaymentsOfAcademyDaf(c.log, academyId, c.profile.id, search)
}

export type StudentFeePaymentOfAcademy = Awaited<ReturnType<typeof findStudentFeePaymentsOfAcademyDaf>>[number]

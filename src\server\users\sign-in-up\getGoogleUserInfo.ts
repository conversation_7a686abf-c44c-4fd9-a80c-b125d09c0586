import { OAuth2Client } from 'google-auth-library'
import { ofetch } from 'ofetch'

import { ensure } from '@/server/common/error/ensure.server'
import { MyException } from '@/server/common/error/MyException.server'
import { newGoogleOAuth2Client } from '@/server/common/google/google-utils'
import { UNSAFE_CHARS } from '@/shared/common/common-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { googleOauthScopes } from '@/shared/users/googleOauthScopes.shared'
import { DISPLAY_NAME_MAX_LEN } from '@/shared/users/user-utils.shared'

import type { GaxiosError } from 'gaxios'
import type { Logger } from 'pino'

export type GoogleUserInfo = {
  sub: string
  name: string
  picture: string
  email: string
  email_verified: boolean
  language?: string
}

export const GOOGLE_OPENID_ENDPOINT = 'https://accounts.google.com/.well-known/openid-configuration'

export const getGoogleUserInfo = async (log: Logger, authCode: string, frontEndLanguage?: string) => {
  // See https://cloud.google.com/nodejs/docs/reference/google-auth-library/latest#oauth2
  const oAuth2Client = newGoogleOAuth2Client()

  const { tokens } = await withLog(log, 'getTokens', () => getTokens(log, oAuth2Client, authCode))
  ensure(tokens.access_token, {
    statusCode: 429,
    issueMessage: 'Failed to obtain access token from google. Please try again',
    logMessage: 'Failed to obtain access token from google',
  })
  ensure(tokens.refresh_token, {
    statusCode: 429,
    issueMessage: 'Failed to obtain refresh token from google. Please try again',
    logMessage: 'Failed to obtain refresh token from google',
  })

  const accessToken = tokens.access_token // So that the type narrowed by ensure is preserved in next line (i.e. across async boundaries)
  const tokenInfo = await withLog(log, 'getTokenInfo', () => oAuth2Client.getTokenInfo(accessToken))

  ensure(
    googleOauthScopes.every((scope) => tokenInfo.scopes.includes(scope)),
    {
      statusCode: 422,
      issueMessage: 'Please allow us to access your email, profile, calendar and events',
      logMessage: 'The access token does not have the required scopes',
    },
  )

  oAuth2Client.setCredentials(tokens)
  // See https://blog.logrocket.com/guide-adding-google-login-react-app/ for details
  const userInfoEndpoint = await withLog(log, 'fetchUserInfoEndpoint', fetchUserInfoEndpoint)
  const userInfo = await withLog(log, 'fetchUserInfo', () => fetchUserInfo(log, oAuth2Client, userInfoEndpoint))
  userInfo.language = userInfo.language ?? frontEndLanguage

  if (!tokens.refresh_token) {
    log.error(`Google user ${userInfo.sub} logging in: Refresh token not found`)
  }
  const refreshToken = /* tokens.refresh_token ?? */ null
  return { refreshToken, userInfo }
}

const getTokens = async (log: Logger, oAuth2Client: OAuth2Client, code: string) => {
  try {
    return await oAuth2Client.getToken(code)
  } catch (ex) {
    const error = ex as GaxiosError
    log.error(
      {
        error: error.message,
        details: error.response?.data, // Google often includes more details in the response
      },
      'Failed to exchange authorization code',
    )
    throw new MyException('Failed to exchange authorization code', 500, [
      {
        path: [],
        code: 'custom',
        message: 'Failed to authenticate with Google. Please try again.',
      },
    ])
  }
}

const fetchUserInfoEndpoint = async () => {
  const { userinfo_endpoint } = await ofetch<{ userinfo_endpoint: string }>(GOOGLE_OPENID_ENDPOINT, {
    retry: 3,
    retryDelay: 500, // ms
    timeout: 3000, // Timeout after 3 seconds
  })
  return userinfo_endpoint
}

const fetchUserInfo = async (log: Logger, client: OAuth2Client, userInfoEndpoint: string) => {
  const response = await client.request<GoogleUserInfo>({ url: userInfoEndpoint })
  ensure(response.status === 200, {
    statusCode: 429,
    issueMessage: 'Failed to fetch user info from google. Please try again',
    logMessage: 'Failed to fetch user info from google',
  })

  const userInfo = response.data
  log.info(`Received user info from google: ${JSON.stringify(userInfo)}`)

  userInfo.name = userInfo.name.replaceAll(UNSAFE_CHARS, '').substring(0, DISPLAY_NAME_MAX_LEN)

  log.info(`User info to be saved: ${JSON.stringify(userInfo)}`)
  return userInfo
}

# AI Agent Development Guide: NP Tuition VR

This guide provides AI agents with essential information to contribute to the NP Tuition VR codebase while following established patterns and conventions.

## 🏗️ Project Architecture

### Technology Stack
- **Framework**: Vike (SSR React framework) with React 19
- **Backend**: Hono.js (Express.js alternative)
- **Database**: PostgreSQL with Drizzle ORM
- **Styling**: TailwindCSS v4
- **State Management**: TanStack Query + React Store
- **Validation**: Zod schemas
- **Testing**: Vitest
- **Package Manager**: pnpm
- **Deployment**: Docker + DigitalOcean Registry

### Directory Structure
```
src/
├── pages/           # Vike pages (file-system routing)
├── server/          # Backend API routes and logic
├── shared/          # Shared utilities, types, validation (Zod) schemas
└── [entry files]    # Hono entry points
```

## 🔧 Critical Development Rules

### 1. Code Quality Standards
- **Quality over speed**: Always prioritize code quality over development time
- **Follow existing patterns**: Strictly adhere to established patterns unless absolutely necessary to deviate
- **Read before writing**: Always examine similar existing code to understand patterns
- **Windows compatibility**: Use Windows-specific commands (PowerShell, paths, etc.)

### 2. Import Organization
```typescript
// 1. Built-in modules (node)
import { UUID } from 'crypto'

// 2. External libraries
import { sql } from 'drizzle-orm'
import { clsx } from 'clsx'

// 3. Internal modules (absolute imports)
import { ErrorBoundary } from '@/shared/common/error-boundary'
import { getUserHandler } from '@/server/users/get/getUserHandler'
import { UserCard } from '@ui/common/UserCard' // Note that we use `@ui` instead of `@/pages`

// 4. Relative imports
import { LocalComponent } from './LocalComponent'

// 5. Type imports (separate)
import type { User } from '@/shared/users/user-utils.shared'
```

**Path Aliases**:
- `@ui/*` → `src/pages/*` (UI components and pages)
- `@/*` → `src/*` (all other source files)

### 3. Default Export Rules
- **NEVER** use default exports except in:
  - `+Page.tsx` files (Vike requirement)
  - `+Head.tsx` files (Vike requirement)
  - `+Wrapper.tsx` files (Vike requirement)
  - Config files (`*config.ts`)
  - Entry files (`src/hono-entry.ts`)

## 📁 File Naming Conventions

### File Extensions & Suffixes
- `.shared.ts` - Shared utilities across client/server
- `.server.ts` - Server-only code
- `+Page.tsx` - Vike page components
- `+Head.tsx` - Vike head components
- `+config.ts` - Vike configuration files

### Naming Patterns
- **Components**: PascalCase (`UserCard.tsx`)
- **Utilities**: kebab-case (`user-utils.shared.ts`)
- **Schemas**: kebab-case (`user-schema.ts`)

## 🗃️ Database Patterns

### Schema Organization
- **Location**: `src/server/db/schema/`
- **Pattern**: One file per domain (`user-schema.ts`, `academy-schema.ts`)
- **Export**: All schemas exported from `src/server/db/schema/index.ts`

### Table Definition Pattern
```typescript
import { boolean, integer, pgTable, text, uuid } from 'drizzle-orm/pg-core'
import { auditColumns, suspensionColumns, uid, timestampz } from './helpers'

export const userTable = pgTable('usr', {
  id: uid().primaryKey(),
  name: text().notNull(),
  email: text().notNull(),
  emailVerified: boolean().notNull(),
  // ... other columns
  ...auditColumns, // createdAt, creationRemarks, updatedAt, updateRemarks
  ...suspensionColumns, // suspendedAt, suspensionReason
})
```

### Key Helpers (Always Use)
- `uid()` - For UUID primary keys typed as `UUID`
- `timestampz()` - For timestamps with timezone
- `auditColumns` - Standard audit fields
- `suspensionColumns` - Standard suspension fields
- `deferrable()` - For deferrable foreign keys

These are defined in `src\server\db\schema\helpers.ts`

## 🔄 Shared Types & Validation

### Validation (Zod) Schema Pattern (`src/shared/*/`)
```typescript
import { z } from 'zod'

// Zod schemas with $ prefix
export const $AddUserForm = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email(),
  role: z.enum(['student', 'teacher', 'admin'])
})

// TypeScript types inferred from schemas
export type AddUserForm = z.infer<typeof $AddUserForm>

// Common types
export type UUID = import('crypto').UUID
export type Email = string
export type DeploymentEnv = 'development' | 'local' | 'staging' | 'production' | 'test'
```

### File Organization
- `user-utils.shared.ts` - User-related types, schemas, utilities
- `common-utils.shared.ts` - Cross-domain utilities
- `error-utils.shared.ts` - Error handling patterns

## 🌐 API Routes Pattern

### Single Mega Router (`src/server/api-routes.server.ts`)
The project uses a single central router that imports all individual handlers and defines all routes in one place:

```typescript
// api-routes.server.ts - Central route definition
import { Hono } from 'hono'

// Import all handlers
import { addUserHandler } from './users/add/addUserHandler'
import { getUserHandler } from './users/get/getUserHandler'
import { addAcademyHandler } from './academies/add/addAcademyHandler'
// ... many more imports

export const apiRoutes = new Hono()
  // Users routes
  .route('/users/signup', signupHandler)
  .route('/users/signin', signInHandler)
  .route('/users/me', getUserHandler)
  
  // Academy routes
  .route('/academies', addAcademyHandler)
  .route('/academies/:id', getAcademyHandler)
  
  // ... many more routes

export type ApiType = typeof apiRoutes
```

### Individual Handler Organization
Each API endpoint has its own handler in a nested directory structure.

```
src/server/
├── users/
│   ├── get-user/
│   │   └── getUserHandler.ts
│   ├── sign-in-up/
│   │   ├── signin/
│   │   │   └── signinHandler.ts
│   │   └── signup/
│   │       └── signupHandler.ts
├── academies/
│   ├── add/
│   │   └── addAcademyHandler.ts
│   └── get/
│       └── getAcademyHandler.ts
```

### Endpoint logic and test organization
Beside each handler file, we also have
- a file having business logic, which is called from the handler. See `src\server\users\revoke-all-google-tokens\revokeAllGoogleTokens.ts` for example.
- one (or more) `*Helper.ts` files having external calls. See `src\server\users\revoke-all-google-tokens\revokeAllGoogleTokensHelper.ts` for example.
- Integration test files. See `src\server\users\sign-in-up\signin\signinHandler.test.ts` for example.
- Unit test files. See `src\server\batches\batches\payments\process\update-student-reminder-type\updateStudentPaymentReminderType.test.ts` for example.

### Sample handler pattern
```typescript
// users/get-user/getUserHandler.ts
import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { $GetUserRequest } from '@/shared/users/user-utils.shared'

export const getUserHandler = new Hono()
  .get('/', zValidator('query', $GetUserRequest), async (c) => {
    const data = c.req.valid('query')
    // Implementation
    return c.json({ user: userData })
  })
```

## 🎨 Frontend Patterns

### Page Components (`src/pages/`)
Vike `+Page.tsx` files should be thin wrappers that extract route parameters and delegate to business logic components:

```typescript
// src/pages/courses/@id/batches/@batchId/+Page.tsx
import { UUID } from 'crypto'

import { usePageContext } from 'vike-react/usePageContext'

import { extractUuid } from '@/shared/common/common-utils.shared'

import { BatchPage } from './BatchPage'

export default () => {
  const pageContext = usePageContext()
  const courseId = extractUuid(pageContext.routeParams.id)
  const batchId = pageContext.routeParams.batchId as UUID

  return <BatchPage courseId={courseId} batchId={batchId} />
}
```

The actual business logic goes in a separate component (e.g., `BatchPage.tsx`):

```typescript
// BatchPage.tsx - Contains actual business logic
import { UUID } from 'crypto'
import { useQuery } from '@tanstack/react-query'

export const BatchPage = ({ courseId, batchId }: { courseId: UUID; batchId: UUID }) => {
  const { data: course, error: courseError } = useCourseSuspenseQuery(courseId)
  const { data: batch, error: batchError } = useBatchSuspenseQuery(batchId)

  return (
    <ShowData error={courseError || batchError} spinnerSize="1.25rem">
      <ViewBatch course={course!} batch={batch!} />
    </ShowData>
  )
}
```

### Component Patterns
```typescript
// Prefer named exports
export function UserCard({ user }: { user: User }) {
  return (
    <div className={clsx(
      'rounded-lg border p-4',
      user.active ? 'border-green-200' : 'border-gray-200'
    )}>
      <h3 className="font-semibold">{user.name}</h3>
      <p className="text-gray-600">{user.email}</p>
    </div>
  )
}
```

## 🎯 TanStack Query Patterns

### Query Configuration
```typescript
// +queryClientConfig.ts
export const queryClientConfig = {
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 3,
    },
  },
}
```

### Query Keys Convention
```typescript
// Hierarchical query keys
const userQueries = {
  all: () => ['users'],
  lists: () => [...userQueries.all(), 'list'],
  list: (filters: UserFilters) => [...userQueries.lists(), filters],
  details: () => [...userQueries.all(), 'detail'],
  detail: (id: UUID) => [...userQueries.details(), id],
}
```

## 🎨 Styling Conventions

### TailwindCSS Usage
- **Utility-first**: Use Tailwind utilities directly
- **Component variants**: Use `clsx` for conditional classes
- **Responsive**: Mobile-first approach (`sm:`, `md:`, `lg:`)
- **Custom components**: Create reusable components for complex patterns

### Color Palette
- Primary: `indigo-*` series (buttons, links, focus states)
- Success: `green-*` series  
- Error: `red-*` series
- Warning: `yellow-*` series
- Neutral: `gray-*` series
- Information: `blue-*` series (limited usage for info states)

## 📋 Code Quality Tools

### ESLint Configuration
- **Import order**: Enforced with specific grouping and alphabetical sorting
- **No default exports**: Except for Vike files and configs
- **TypeScript strict**: Full type checking enabled
- **Module boundaries**: Enforced separation between domains
- **Promise handling**: All promises must be properly handled

### Prettier Configuration
```json
{
  "printWidth": 120,
  "semi": false,
  "singleQuote": true,
  "experimentalTernaries": true,
  "endOfLine": "auto"
}
```

## 🧪 Testing Patterns

### Test Organization
- **Integration tests** for API handlers (co-located with handlers)
- **Unit tests** for business logic  
- Use `.test.ts` extension
- **No UI component tests**

### Essential Setup Pattern
```typescript
import { initDb, SUNDARGARH_DISTRICT_ID, TNC_PRIVACY_V1_ID, TNC_TOS_V1_ID } from '@/server/test/initDb'

describe('handlerName', () => {
  beforeEach(async () => {
    await initDb() // Cleans and initializes test database
  })

  afterEach(() => {
    vi.clearAllMocks() // Clear any mocked functions
  })
})
```

### Authentication Requirements for API Tests

**To determine if an endpoint requires authentication, check the handler and business logic chain:**
- **Public endpoints**: No `ensureUser` call anywhere in the chain - these work for unauthenticated users
- **Protected endpoints**: Call `ensureUser` somewhere in the chain - require JWT token. Also require TnC acceptance unless the endpoint is skipped in `whetherToCheckTnCs` of `src\server\middleware\auth\authMiddleware.server.ts`

**When endpoints require authentication:**
- **User with accepted TnCs** (`TNC_TOS_V1_ID`, `TNC_PRIVACY_V1_ID`): All endpoints except those skipped in `whetherToCheckTnCs` of `src\server\middleware\auth\authMiddleware.server.ts`
- **Valid JWT token** via `createAccessToken()`: All endpoints calling `ensureUser` somewhere in the chain
- **Profile header** for profile operations: `[PROFILE_HEADER]: profileId`: All endpoints calling `ensureProfile` somewhere in the chain
- **Academy staff relationship** for academy operations: All endpoints calling `ensureCanUpdateAcademy` somewhere in the chain

### Critical: PROFILE_HEADER Understanding

**The `PROFILE_HEADER` is essential for role-based operations:**

- **JWT token contains**: Only the `userId` (which user is authenticated)
- **PROFILE_HEADER contains**: Which `profileId` the user is acting as (their role/permissions)
- **Without PROFILE_HEADER**: System treats request as unauthenticated, even with valid JWT token

**Common mistake in tests:**
```typescript
// ❌ Wrong - Creates token but no profile context
const token = await createAccessToken(env.JWT_SECRET_KEY, staffUser.id)
const res = await makeRequest(endpoint, token) // Missing profile header!

// ✅ Correct - Include both token and profile
const token = await createAccessToken(env.JWT_SECRET_KEY, staffUser.id)
const res = await app.request(endpoint, {
  headers: {
    Authorization: `Bearer ${token}`,
    [PROFILE_HEADER]: staffProfile.id  // This tells system the user's role
  }
})
```

**When you need PROFILE_HEADER:**
- Any endpoint that checks user roles/permissions
- Any endpoint calling `ensureProfile()` or related functions
- Staff-only operations (academy staff, course staff, etc.)
- Role-specific data access (staff seeing unpublished content, etc.)

**Testing Strategy:**
- For **public endpoints**: Test both authenticated and unauthenticated scenarios to verify different access levels
- For **protected endpoints**: Test authentication/authorization failures (401/403) and success scenarios

### Key Test Scenarios to Cover
1. **Success scenarios** - Happy path, optional fields
2. **Auth/authz errors** - 401/403 responses (see error reference below)
3. **Validation errors** - 400 Zod validation responses  
4. **Business logic errors** - 422 constraint violations
5. **Edge cases** - Special characters, boundary conditions

### Error Response Reference
- `ensureUser.server.ts` - User auth errors (401/403)
- `ensure-profile.server.ts` - Profile validation (403) 
- `ensureCanUpdateAcademy.ts` - Academy staff auth (403)
- `authMiddleware.server.ts` - TnC validation (403)

### Custom Test Matchers
Use `toBeError` for structured error validation:
```typescript
await expect(res).toBeError(403, [
  { code: 'custom', path: [], message: 'Profile is suspended' }
])
```

### Parameterized Tests with `test.each`

Use [`test.each`](https://vitest.dev/api/#test-each) to reduce duplication when testing the **same logic** with different inputs, but avoid it when it makes tests harder to understand.

**✅ Good use cases (simplifies):**
```typescript
// Testing same endpoint behavior with different auth scenarios
test.each([
  { profileId: regularProfile.id, description: 'with profile header' },
  { profileId: undefined, description: 'without profile header' },
])('should return public data only $description', async ({ profileId }) => {
  const res = await makeAuthenticatedRequest(endpoint, userId, profileId)
  expect(res.status).toBe(200)
  expect(res.data.rows).toHaveLength(1) // Same expectation for both
})

// Testing validation with multiple invalid inputs
test.each([
  { input: '', expectedError: 'Required' },
  { input: 'x'.repeat(256), expectedError: 'Too long' },
  { input: 'invalid-email', expectedError: 'Invalid email format' },
])('should reject invalid email: $input', async ({ input, expectedError }) => {
  const res = await makeRequest({ email: input })
  expect(res.status).toBe(400)
  expect(res.errors[0].message).toContain(expectedError)
})
```

**❌ Avoid when it complicates:**
```typescript
// ❌ Don't use when expectations are completely different
test.each([
  { role: 'admin', expectStatus: 200, expectCount: 10, canDelete: true },
  { role: 'user', expectStatus: 403, expectCount: 0, canDelete: false },
])('role permissions $role', ({ role, expectStatus, expectCount, canDelete }) => {
  // Logic becomes confusing with too many conditional branches
  if (expectStatus === 200) {
    expect(res.data.items).toHaveLength(expectCount)
    if (canDelete) {
      expect(res.data.items[0]).toHaveProperty('deleteUrl')
    }
  } else {
    expect(res.status).toBe(expectStatus)
  }
})

// ✅ Better as separate, focused tests
test('admin can see all items with delete permissions', async () => { /* clear logic */ })
test('user gets 403 forbidden', async () => { /* clear logic */ })
```

**Guidelines:**
- Use `test.each` when the **test logic is identical** and only inputs/expected outputs change
- Avoid when you need **many conditional branches** or **different assertion patterns**
- Keep parameter objects **simple** - complex nested objects make tests hard to read
- Use **descriptive `$variable` interpolation** in test names for clarity

### TypeScript Guidelines for Tests

**Critical rules to prevent TypeScript errors in tests:**

1. **Never use `any` type** - Always provide explicit types to avoid TypeScript errors:
2. **Be cautious with `as const`** - Don't use `as const` on test data objects that need to match database schemas:
   ```typescript
   // ❌ Wrong - `as const` can make fields readonly and cause insertion errors
   const testBatch = {
     id: crypto.randomUUID() as UUID,
     fee: 5000,
     billingCycle: 'monthly',
   } as const
   
   // ✅ Correct - omit `as const` for database insertion objects
   const testBatch = {
     id: crypto.randomUUID() as UUID,
     fee: 500000, // In cents
     billingCycle: 'months' as const, // Only use `as const` for specific enum values
   }
   ```

3. **Always check schema files** - Before creating test data, examine `src/server/db/schema/*.ts` files:
   ```typescript
   // Check src/server/db/schema/course-schema.ts to see:
   // - Field is named `descr`, not `description`
   // - `publishedAt` uses `timestampz()` type
   
   const course = {
     descr: '<p>Course description</p>\n', // ✅ Correct field name
     publishedAt: mocked.now.toJSDate(),   // ✅ Correct type
   }
   ```

4. **Look up enum/type definitions** - Find exact constant values from shared types:
   ```typescript
   // Check src/shared/batch/batch-utils.shared.ts for BillingCycle type
   import { BillingCycle } from '@/shared/batch/batch-utils.shared'
   
   const batch = {
     billingCycle: 'months' as const, // ✅ Correct - 'months', not 'monthly'
     startDate: '2024-01-01',         // ✅ Correct - string format, not Date object
   }
   ```

5. **Match database field types exactly**:
   - `startDate` → string in `'yyyy-MM-dd'` format (not `Date` object)
   - `fee` → number in cents (e.g., `500000` for ₹5000.00)
   - Foreign keys → UUID strings

### Complete Examples
- **Integration test**: `src/server/users/sign-in-up/signup/signupHandler.test.ts`
- **Unit test**: `src/server/batches/batches/payments/process/processStudentBillingHandler.test.ts`
- **Auth setup**: `src/server/courses/courses/add/addCourseHandler.test.ts`

## 🚀 Deployment & Scripts

### Key Scripts
- `pnpm dev` - Development server
- `pnpm build` - Production build
- `pnpm lint` - ESLint check and fix
- `pnpm tsc` - TypeScript check
- `pnpm check` - Run both lint and tsc
- `pnpm test` - Run tests with database migration
- `pnpm drizzle:generate` - Generate DB migrations
- `pnpm drizzle:migrate` - Apply DB migrations

## 🔍 Module Boundaries

### Domain Separation
The project enforces strict module boundaries:

- **UI domains** (`src/pages/users/`, `src/pages/profiles/`) can only import from:
  - Their corresponding server domain
  - Common UI utilities
  - Shared utilities

- **Server domains** (`src/server/users/`, `src/server/profiles/`) can only import from:
  - Server common utilities
  - Database schemas
  - Shared utilities

### Boundary Rules
```typescript
// ✅ Allowed
import { UserCard } from '@ui/common/UserCard'              // common UI
import { userRoutes } from '@/server/users/user-routes'     // corresponding server
import { $AddUserForm } from '@/shared/users/user-utils'    // shared domain

// ❌ Not allowed
import { ProfileCard } from '@ui/profiles/ProfileCard'      // cross-domain UI
import { academyRoutes } from '@/server/academies/'         // cross-domain server
```

---

## 🚨 AI Agent Checklist

When contributing code, ensure you:

1. ✅ Read existing similar code to understand patterns
2. ✅ Follow the established directory structure
3. ✅ Use proper file naming conventions
4. ✅ Import modules in the correct order
5. ✅ Avoid default exports (except where allowed)
6. ✅ Code and use shared Zod schemas and types in `src/shared/*`
7. ✅ Follow database schema patterns with helpers
9. ✅ Write tests for new functionality
10. ✅ Run `pnpm check` before committing
11. ✅ Use Windows-compatible commands and paths
12. ✅ Respect module boundaries between domains

For any clarification or when patterns are unclear, refer to existing code in the same domain or ask for specific examples.

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { getCourseAttachmentUrl } from './getCourseAttachmentUrl'

export const getCourseAttachmentUrlHandler = new Hono<HonoVars>().get('/', zValidator('param', $HasId), async (c) => {
  const attachment = c.req.valid('param')

  const url = await getCourseAttachmentUrl(getCtx(c), attachment.id)
  return c.json(url, 200)
})

import { randomUUID, UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { ensureGoodStaff } from '@/server/academies/common/ensureGoodStaff'
import { Ctx } from '@/server/common/auth-context-types.server'
import { ensure } from '@/server/common/error/ensure.server'
import { db } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { courseTable } from '@/server/db/schema/course-schema'
import { markdown2Html } from '@/shared/common/markdown-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'
import { AddCourseForm, MAX_COURSES_PER_ACADEMY } from '@/shared/course/course-utils.shared'

const findExistingCourseDaf = (log: pino.Logger, academyId: UUID, name: string) =>
  withLog(log, 'findExistingCourseDaf', () =>
    db.query.courseTable.findFirst({
      ...dummyColumn,
      where: and(eq(courseTable.academyId, academyId), eq(courseTable.name, name)),
    }),
  )

const insertCourseDaf = (log: pino.Logger, newCourseId: UUID, form: AddCourseForm, profileId: UUID) =>
  withLog(log, 'insertProfileDaf', () => {
    return db.insert(courseTable).values({
      id: newCourseId,
      name: form.name,
      descr: form.descr ? markdown2Html(form.descr) : null,
      academyId: form.academyId,
      creationRemarks: `${profileId} profilecreated this course`,
    })
  })

const countAcademyCoursesDaf = (log: pino.Logger, academyId: UUID) =>
  withLog(log, 'countAcademyCoursesDaf', () => db.$count(courseTable, eq(courseTable.academyId, academyId)))

export const addCourse = async (c: Ctx, form: AddCourseForm) => {
  await ensureGoodStaff(c, form.academyId)

  // Check max courses limit
  const courseCount = await countAcademyCoursesDaf(c.log, form.academyId)
  ensure(courseCount < MAX_COURSES_PER_ACADEMY, {
    statusCode: 422,
    issueMessage: `Academy already has ${courseCount} courses.`,
    logMessage: `Academy ${form.academyId} already has ${courseCount} courses.`,
  })

  // Check if course already exists
  const existingCourse = await findExistingCourseDaf(c.log, form.academyId, form.name)
  ensure(!existingCourse, {
    statusCode: 422,
    issueMessage: `Course with name ${form.name} already exists.`,
    logMessage: `Academy ${form.academyId} attempted to create a course with name ${form.name}, but this course already exists.`,
  })

  // Create new course
  const newCourseId = randomUUID()
  await insertCourseDaf(c.log, newCourseId, form, c.profile!.id)

  return {
    id: newCourseId,
  }
}

import { UUID } from 'crypto'

import { and, eq, exists, isNull } from 'drizzle-orm'
import { Logger } from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureProfileWithRole } from '@/server/common/error/ensure-profile.server'
import { ensureExists } from '@/server/common/error/ensureExists'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { db } from '@/server/db/db'
import { batchStudentTable, batchTable } from '@/server/db/schema/batch-schema'
import { profileTable } from '@/server/db/schema/profile-schema'
import { userTable } from '@/server/db/schema/user-schema'
import { withLog } from '@/shared/common/withLog.shared'

const findTeacherContactDaf = (log: Logger, studentId: UUID, teacherId: UUID) =>
  withLog(log, 'findTeacherContactDaf', () =>
    db
      .select({
        mobileCountryCode: userTable.mobileCountryCode,
        mobile: userTable.mobile,
        email: userTable.email,
      })
      .from(profileTable)
      .innerJoin(userTable, eq(profileTable.userId, userTable.id))
      .where(
        and(
          eq(profileTable.id, teacherId),
          // Check that the student is in a batch taught by the teacher
          exists(
            db
              .select({ id: batchStudentTable.id })
              .from(batchStudentTable)
              .innerJoin(batchTable, eq(batchStudentTable.batchId, batchTable.id))
              .where(
                and(
                  eq(batchStudentTable.studentId, studentId),
                  eq(batchTable.teacherId, teacherId),
                  // Only consider active enrollments (student has not left)
                  isNull(batchStudentTable.leftAt),
                ),
              ),
          ),
        ),
      ),
  )

/**
 * Get a profile by its id
 * Returns basic profile information including approval and suspension status
 */
export const getProfileContact = async (c: Ctx, profileId: UUID) => {
  ensureUser(c.user)
  ensureProfileWithRole('student', c.profile)

  const [profile] = await findTeacherContactDaf(c.log, c.profile.id, profileId)

  ensureExists(profile, profileId, 'Profile')
  return profile
}

export type ProfileContact = Awaited<ReturnType<typeof getProfileContact>>

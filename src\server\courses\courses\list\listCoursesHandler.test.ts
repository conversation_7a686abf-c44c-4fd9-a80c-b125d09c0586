import { UUID } from 'crypto'

import { DateTime } from 'luxon'
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'

import app from '@/hono-entry'
import { env } from '@/server/common/env.server'
import { db } from '@/server/db/db'
import { academyStaffTable, academyTable } from '@/server/db/schema/academy-schema'
import { batchRecommendationTable, batchTable } from '@/server/db/schema/batch-schema'
import { courseTable } from '@/server/db/schema/course-schema'
import { profileTable } from '@/server/db/schema/profile-schema'
import { userTable, userTncAcceptedTable, userTncSignTable } from '@/server/db/schema/user-schema'
import { initDb, SUNDARGARH_DISTRICT_ID, TNC_PRIVACY_V1_ID, TNC_TOS_V1_ID } from '@/server/test/initDb'
import { createAccessToken } from '@/server/users/common/auth-token-utils.server'
import { UUID_REGEX } from '@/shared/common/common-utils.shared'
import { PROFILE_HEADER } from '@/shared/profiles/profile-utils.shared'

const mocked = {
  now: DateTime.now(),
} as const

// Mock date utilities to have consistent test results
vi.mock(import('@/shared/common/date-utils-basic.shared'), async (importOriginal) => {
  const mod = await importOriginal()
  return {
    ...mod,
    utc: () => mocked.now,
  }
})

const testUser = {
  id: crypto.randomUUID() as UUID,
  googleId: 'test-google-id',
  googleRefreshToken: null,
  name: 'Test User',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/picture',
  language: 'en-US',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '9876543210',
  mobileVerified: true,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

const testProfile = {
  id: crypto.randomUUID() as UUID,
  userId: testUser.id,
  role: 'principal' as const,
  displayName: 'Test Principal',
  approvedAt: mocked.now.toJSDate(),
  tncsAcceptedAt: mocked.now.toJSDate(),
} as const

const testAcademy = {
  id: crypto.randomUUID() as UUID,
  name: 'Test Academy',
  email: '<EMAIL>',
  emailVerified: true,
  mobileCountryCode: 'IN',
  mobile: '9876543210',
  mobileVerified: true,
  currency: 'INR' as const,
  upiId: 'test@upi',
  districtId: SUNDARGARH_DISTRICT_ID as UUID,
  pincode: '770001',
  area: 'Test Area',
  tncsAcceptedAt: mocked.now.toJSDate(),
  approvedAt: mocked.now.toJSDate(),
} as const

const otherAcademy = {
  id: crypto.randomUUID() as UUID,
  name: 'Other Academy',
  email: '<EMAIL>',
  emailVerified: true,
  mobileCountryCode: 'IN',
  mobile: '9876543211',
  mobileVerified: true,
  currency: 'INR' as const,
  upiId: 'other@upi',
  districtId: SUNDARGARH_DISTRICT_ID as UUID,
  pincode: '770002',
  area: 'Other Area',
  tncsAcceptedAt: mocked.now.toJSDate(),
  approvedAt: mocked.now.toJSDate(),
} as const

const publishedCourse = {
  id: crypto.randomUUID() as UUID,
  name: 'Published Course',
  descr: '<p>Published course description</p>\n',
  academyId: testAcademy.id,
  publishedAt: mocked.now.toJSDate(),
  creationRemarks: `${testProfile.id} profile created this course`,
} as const

const unpublishedCourse = {
  id: crypto.randomUUID() as UUID,
  name: 'Unpublished Course',
  descr: '<p>Unpublished course description</p>\n',
  academyId: testAcademy.id,
  publishedAt: null,
  creationRemarks: `${testProfile.id} profile created this course`,
} as const

const otherAcademyCourse = {
  id: crypto.randomUUID() as UUID,
  name: 'Other Academy Course',
  descr: '<p>Other academy course</p>\n',
  academyId: otherAcademy.id,
  publishedAt: mocked.now.toJSDate(),
  creationRemarks: `${testProfile.id} profile created this course`,
} as const

interface CourseListItem {
  id: UUID
  name: string
  academyId: UUID
  publishedAt: string | null
  recommendedBatchId: UUID | null
  tagIds: UUID[]
}

interface CourseListResponse {
  rows: CourseListItem[]
}

const makeRequest = async (queryParams?: string, profileId?: UUID, accessToken?: string) => {
  const url = `/api/courses${queryParams ? `?${queryParams}` : ''}`
  const headers: Record<string, string> = { 'Content-Type': 'application/json' }

  if (accessToken) {
    headers.Authorization = `Bearer ${accessToken}`
  }

  if (profileId) {
    headers[PROFILE_HEADER] = profileId
  }

  return app.request(url, { method: 'GET', headers }, {})
}

const makeAuthenticatedRequest = async (queryParams?: string, profileId?: UUID) => {
  const accessToken = await createAccessToken(env.JWT_SECRET_KEY, testUser.id)
  return makeRequest(queryParams, profileId, accessToken)
}

const makeUnauthenticatedRequest = async (queryParams?: string) => {
  return makeRequest(queryParams)
}

const setupBasicTestData = async () => {
  // Create academies
  await db.insert(academyTable).values([testAcademy, otherAcademy])

  // Create courses
  await db.insert(courseTable).values([publishedCourse, unpublishedCourse, otherAcademyCourse])

  return { batchId: null as UUID | null }
}

const setupAuthenticatedUserData = async () => {
  // Create user with TnC acceptance
  await db.insert(userTable).values(testUser)

  const acceptedTnCs = [TNC_TOS_V1_ID as UUID, TNC_PRIVACY_V1_ID as UUID]
  for (const tncId of acceptedTnCs) {
    await db.insert(userTncSignTable).values({
      id: crypto.randomUUID() as UUID,
      userId: testUser.id,
      tncVersionId: tncId,
      accepted: true,
    })

    await db.insert(userTncAcceptedTable).values({
      id: crypto.randomUUID() as UUID,
      userId: testUser.id,
      tncVersionId: tncId,
    })
  }

  // Create profile
  await db.insert(profileTable).values(testProfile)

  // Create academy staff relationship
  await db.insert(academyStaffTable).values({
    profileId: testProfile.id,
    academyId: testAcademy.id,
  })
}

const setupBatchRecommendation = async () => {
  const testBatch = {
    id: crypto.randomUUID() as UUID,
    courseId: publishedCourse.id,
    teacherId: testProfile.id,
    fee: 500000, // 5000.00 in cents
    billingCycle: 'months' as const,
    graceDays: 3,
    startDate: mocked.now.plus({ days: 30 }).toFormat('yyyy-MM-dd'),
    timezone: 'Asia/Kolkata',
    cycleCount: 12,
    over: false,
    seatCount: 50,
    studentCount: 0,
    admissionOpen: true,
    creationRemarks: `${testProfile.id} profile created this batch`,
  }

  await db.insert(batchTable).values(testBatch)
  await db.insert(batchRecommendationTable).values({
    batchId: testBatch.id,
    courseId: publishedCourse.id,
  })

  return testBatch.id
}

const assertValidCourseListResponse = (responseBody: CourseListResponse, expectedCourseIds: UUID[]) => {
  expect(responseBody.rows).toBeDefined()
  expect(Array.isArray(responseBody.rows)).toBe(true)
  expect(responseBody.rows.length).toBe(expectedCourseIds.length)

  for (const course of responseBody.rows) {
    expect(course.id).toMatch(UUID_REGEX)
    expect(expectedCourseIds).toContain(course.id)
    expect(course.name).toBeDefined()
    expect(course.academyId).toMatch(UUID_REGEX)
    expect(course.tagIds).toBeDefined()
    expect(Array.isArray(course.tagIds)).toBe(true)

    if (course.publishedAt) {
      expect(new Date(course.publishedAt)).toBeInstanceOf(Date)
    }

    if (course.recommendedBatchId) {
      expect(course.recommendedBatchId).toMatch(UUID_REGEX)
    }
  }
}

describe('listCoursesHandler', () => {
  beforeEach(async () => {
    await initDb()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Public access scenarios (unauthenticated)', () => {
    test('should list all published courses for unauthenticated users', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeUnauthenticatedRequest()

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      assertValidCourseListResponse(responseBody, [publishedCourse.id, otherAcademyCourse.id])

      // Verify only published courses are returned
      const unpublishedCourseInResponse = responseBody.rows.find((c) => c.id === unpublishedCourse.id)
      expect(unpublishedCourseInResponse).toBeUndefined()
    })

    test('should filter by academyId for unauthenticated users', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeUnauthenticatedRequest(`academyId=${testAcademy.id}`)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      assertValidCourseListResponse(responseBody, [publishedCourse.id])

      // Verify only courses from the specified academy are returned
      for (const course of responseBody.rows) {
        expect(course.academyId).toBe(testAcademy.id)
      }
    })

    test('should not include unpublished courses for unauthenticated users even with includeUnpublished=true', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeUnauthenticatedRequest(`academyId=${testAcademy.id}&includeUnpublished=true`)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      assertValidCourseListResponse(responseBody, [publishedCourse.id])

      // Verify unpublished course is not included
      const unpublishedCourseInResponse = responseBody.rows.find((c) => c.id === unpublishedCourse.id)
      expect(unpublishedCourseInResponse).toBeUndefined()
    })

    test('should return empty array when no courses match the filter for unauthenticated users', async () => {
      // given
      await setupBasicTestData()
      const nonExistentAcademyId = crypto.randomUUID() as UUID

      // when
      const res = await makeUnauthenticatedRequest(`academyId=${nonExistentAcademyId}`)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      expect(responseBody.rows).toEqual([])
    })
  })

  describe('Authenticated access scenarios', () => {
    test('should list all published courses for authenticated users', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()

      // when
      const res = await makeAuthenticatedRequest()

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      assertValidCourseListResponse(responseBody, [publishedCourse.id, otherAcademyCourse.id])
    })

    test('should include unpublished courses when user is academy staff and includeUnpublished is true', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()

      // when
      const res = await makeAuthenticatedRequest(`academyId=${testAcademy.id}&includeUnpublished=true`, testProfile.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      assertValidCourseListResponse(responseBody, [publishedCourse.id, unpublishedCourse.id])
    })

    test('should not include unpublished courses when user is not academy staff even with includeUnpublished=true', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()

      // when - Request without profile or with profile that is not staff of the academy
      const res = await makeAuthenticatedRequest(`academyId=${testAcademy.id}&includeUnpublished=true`)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      assertValidCourseListResponse(responseBody, [publishedCourse.id])

      // Verify unpublished course is not included
      const unpublishedCourseInResponse = responseBody.rows.find((c) => c.id === unpublishedCourse.id)
      expect(unpublishedCourseInResponse).toBeUndefined()
    })

    test('should return courses with batch recommendation when available', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      const batchId = await setupBatchRecommendation()

      // when
      const res = await makeAuthenticatedRequest(`academyId=${testAcademy.id}`)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      const publishedCourseInResponse = responseBody.rows.find((c) => c.id === publishedCourse.id)
      expect(publishedCourseInResponse).toBeDefined()
      expect(publishedCourseInResponse!.recommendedBatchId).toBe(batchId)
    })
  })

  describe('Pagination scenarios', () => {
    const setupPaginationTestData = async () => {
      // Create 5 courses to test pagination with pageSize=2
      const courses = []
      for (let i = 1; i <= 5; i++) {
        const course = {
          id: crypto.randomUUID() as UUID,
          name: `Course ${i}`,
          descr: `<p>Course ${i} description</p>\n`,
          academyId: testAcademy.id,
          publishedAt: mocked.now.toJSDate(),
          creationRemarks: `${testProfile.id} profile created this course`,
        }
        courses.push(course)
      }

      await db.insert(courseTable).values(courses)
      return courses.sort((a, b) => a.id.localeCompare(b.id)) // Sort by ID for predictable ordering
    }

    test('should return first page with specified pageSize', async () => {
      // given
      await setupBasicTestData()
      await setupPaginationTestData()

      // when
      const res = await makeUnauthenticatedRequest(`academyId=${testAcademy.id}&pageSize=2`)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      expect(responseBody.rows).toBeDefined()
      expect(responseBody.rows.length).toBe(2)

      // Verify all courses are from the correct academy
      for (const course of responseBody.rows) {
        expect(course.academyId).toBe(testAcademy.id)
      }
    })

    test('should return second page using forward pagination', async () => {
      // given
      await setupBasicTestData()
      await setupPaginationTestData()

      // Get first page
      const firstPageRes = await makeUnauthenticatedRequest(`academyId=${testAcademy.id}&pageSize=2`)
      const firstPageBody = (await firstPageRes.json()) as CourseListResponse
      expect(firstPageBody.rows.length).toBe(2)

      const lastItemFromFirstPage = firstPageBody.rows[firstPageBody.rows.length - 1]

      // when - Get second page using cursor from first page
      const secondPageRes = await makeUnauthenticatedRequest(
        `academyId=${testAcademy.id}&pageSize=2&beyondId=${lastItemFromFirstPage.id}`,
      )

      // then
      expect(secondPageRes.status).toBe(200)
      const secondPageBody = (await secondPageRes.json()) as CourseListResponse
      expect(secondPageBody.rows).toBeDefined()
      expect(secondPageBody.rows.length).toBeGreaterThan(0)

      // Verify no overlap between pages
      const firstPageIds = firstPageBody.rows.map((c) => c.id)
      const secondPageIds = secondPageBody.rows.map((c) => c.id)
      const overlap = firstPageIds.filter((id) => secondPageIds.includes(id))
      expect(overlap).toEqual([])
    })

    test('should return previous page using backward pagination', async () => {
      // given
      await setupBasicTestData()
      await setupPaginationTestData()

      // Get first page to establish baseline
      const firstPageRes = await makeUnauthenticatedRequest(`academyId=${testAcademy.id}&pageSize=2`)
      const firstPageBody = (await firstPageRes.json()) as CourseListResponse
      const lastItemFromFirstPage = firstPageBody.rows[firstPageBody.rows.length - 1]

      // Get second page
      const secondPageRes = await makeUnauthenticatedRequest(
        `academyId=${testAcademy.id}&pageSize=2&beyondId=${lastItemFromFirstPage.id}`,
      )
      const secondPageBody = (await secondPageRes.json()) as CourseListResponse
      expect(secondPageBody.rows.length).toBeGreaterThan(0)

      const firstItemFromSecondPage = secondPageBody.rows[0]

      // when - Get previous page using backward pagination
      const backwardRes = await makeUnauthenticatedRequest(
        `academyId=${testAcademy.id}&pageSize=2&beyondId=${firstItemFromSecondPage.id}&previous=true`,
      )

      // then
      expect(backwardRes.status).toBe(200)
      const backwardBody = (await backwardRes.json()) as CourseListResponse
      expect(backwardBody.rows).toBeDefined()
      expect(backwardBody.rows.length).toBeGreaterThan(0)

      // The backward pagination should return courses that come before the cursor
      const backwardIds = backwardBody.rows.map((c) => c.id)
      expect(backwardIds).not.toContain(firstItemFromSecondPage.id)
    })

    test('should handle pageSize larger than total results', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeUnauthenticatedRequest(`academyId=${testAcademy.id}&pageSize=100`)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      expect(responseBody.rows).toBeDefined()
      // Should return only the published course from testAcademy (unpublished is filtered out for unauthenticated)
      expect(responseBody.rows.length).toBe(1)
      expect(responseBody.rows[0].id).toBe(publishedCourse.id)
    })

    test('should handle empty results with pagination parameters', async () => {
      // given
      await setupBasicTestData()
      const nonExistentAcademyId = crypto.randomUUID() as UUID

      // when
      const res = await makeUnauthenticatedRequest(`academyId=${nonExistentAcademyId}&pageSize=2`)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      expect(responseBody.rows).toEqual([])
    })

    test('should handle pagination with batch recommendations', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      const batchId = await setupBatchRecommendation()
      await setupPaginationTestData()

      // when
      const res = await makeAuthenticatedRequest(`academyId=${testAcademy.id}&pageSize=2`)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      expect(responseBody.rows).toBeDefined()
      expect(responseBody.rows.length).toBe(2)

      // Check if the published course with batch recommendation is included
      const courseWithBatch = responseBody.rows.find((c) => c.id === publishedCourse.id)
      if (courseWithBatch) {
        expect(courseWithBatch.recommendedBatchId).toBe(batchId)
      }
    })

    test('should validate pageSize parameter', async () => {
      // given
      await setupBasicTestData()

      // when - Invalid pageSize (non-numeric)
      const res1 = await makeUnauthenticatedRequest(`academyId=${testAcademy.id}&pageSize=invalid`)

      // then
      expect(res1.status).toBe(400)
      const responseBody1 = await res1.json()
      expect(responseBody1.error.issues).toBeDefined()
      expect(responseBody1.error.issues.some((issue: { path: string[] }) => issue.path.includes('pageSize'))).toBe(true)

      // when - PageSize too large
      const res2 = await makeUnauthenticatedRequest(`academyId=${testAcademy.id}&pageSize=101`)

      // then
      expect(res2.status).toBe(400)
      const responseBody2 = await res2.json()
      expect(responseBody2.error.issues).toBeDefined()
      expect(responseBody2.error.issues.some((issue: { path: string[] }) => issue.path.includes('pageSize'))).toBe(true)
    })

    test('should validate beyondId parameter', async () => {
      // given
      await setupBasicTestData()

      // when - Invalid beyondId (non-UUID)
      const res = await makeUnauthenticatedRequest(`academyId=${testAcademy.id}&beyondId=invalid-uuid`)

      // then
      expect(res.status).toBe(400)
      const responseBody = await res.json()
      expect(responseBody.error.issues).toBeDefined()
      expect(responseBody.error.issues.some((issue: { path: string[] }) => issue.path.includes('beyondId'))).toBe(true)
    })

    test('should handle pagination with includeUnpublished for staff', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()
      await setupPaginationTestData()

      // when - Get all courses as staff with includeUnpublished=true
      const staffRes = await makeAuthenticatedRequest(
        `academyId=${testAcademy.id}&includeUnpublished=true`,
        testProfile.id,
      )

      // when - Get all courses as non-staff (should not include unpublished)
      const nonStaffRes = await makeAuthenticatedRequest(`academyId=${testAcademy.id}&includeUnpublished=true`)

      // then
      expect(staffRes.status).toBe(200)
      expect(nonStaffRes.status).toBe(200)

      const staffBody = (await staffRes.json()) as CourseListResponse
      const nonStaffBody = (await nonStaffRes.json()) as CourseListResponse

      // Staff should see more courses (including unpublished) than non-staff
      expect(staffBody.rows.length).toBeGreaterThan(nonStaffBody.rows.length)

      // Verify staff can see unpublished course
      const staffCourseIds = staffBody.rows.map((c) => c.id)
      expect(staffCourseIds).toContain(unpublishedCourse.id)
      expect(staffCourseIds).toContain(publishedCourse.id)

      // Verify non-staff cannot see unpublished course
      const nonStaffCourseIds = nonStaffBody.rows.map((c) => c.id)
      expect(nonStaffCourseIds).not.toContain(unpublishedCourse.id)
      expect(nonStaffCourseIds).toContain(publishedCourse.id)

      // Test pagination with small pageSize for staff
      const paginatedRes = await makeAuthenticatedRequest(
        `academyId=${testAcademy.id}&pageSize=2&includeUnpublished=true`,
        testProfile.id,
      )

      expect(paginatedRes.status).toBe(200)
      const paginatedBody = (await paginatedRes.json()) as CourseListResponse
      expect(paginatedBody.rows.length).toBe(2)

      // All returned courses should be from the correct academy
      for (const course of paginatedBody.rows) {
        expect(course.academyId).toBe(testAcademy.id)
      }
    })
  })

  describe('Validation error scenarios', () => {
    test('should return 400 when academyId is invalid UUID', async () => {
      // given
      await setupBasicTestData()

      // when
      const res = await makeUnauthenticatedRequest('academyId=invalid-uuid')

      // then
      expect(res.status).toBe(400)
      const responseBody = await res.json()
      expect(responseBody.error.issues).toBeDefined()
      expect(responseBody.error.issues.some((issue: { path: string[] }) => issue.path.includes('academyId'))).toBe(true)
    })
  })

  describe('Edge cases', () => {
    test('should handle empty database', async () => {
      // given - No test data setup

      // when
      const res = await makeUnauthenticatedRequest()

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      expect(responseBody.rows).toEqual([])
    })

    test('should handle query parameters with special characters', async () => {
      // given
      await setupBasicTestData()

      // when - Test URL encoding
      const res = await makeUnauthenticatedRequest(`academyId=${encodeURIComponent(testAcademy.id)}`)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      assertValidCourseListResponse(responseBody, [publishedCourse.id])
    })

    test('should handle multiple query parameters correctly', async () => {
      // given
      await setupBasicTestData()
      await setupAuthenticatedUserData()

      // when
      const res = await makeAuthenticatedRequest(`academyId=${testAcademy.id}&includeUnpublished=false`, testProfile.id)

      // then
      expect(res.status).toBe(200)
      const responseBody = (await res.json()) as CourseListResponse
      assertValidCourseListResponse(responseBody, [publishedCourse.id])

      // Verify unpublished course is not included even though user is staff
      const unpublishedCourseInResponse = responseBody.rows.find((c) => c.id === unpublishedCourse.id)
      expect(unpublishedCourseInResponse).toBeUndefined()
    })
  })
})

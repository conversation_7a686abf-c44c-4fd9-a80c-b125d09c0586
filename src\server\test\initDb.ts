import fs from 'fs/promises'
import path from 'path'

import { sql } from 'drizzle-orm'
import pino from 'pino'

import { db } from '@/server/db/db'
import { getLogger } from '@/shared/common/logger.shared'

export const SUNDARGARH_DISTRICT_ID = 'b3b76d88-1336-4e97-a38a-c49cec97fe61'
export const TNC_TOS_V1_ID = 'a44b55fa-3339-45c5-b71d-92f60390d995'
export const TNC_PRIVACY_V1_ID = '************************************'

export const initDb = async () => {
  const log = getLogger()
  await cleanDb(log)
  await initMasters(log, '0001_seed-masters.sql')
}

const cleanDb = async (log: pino.Logger) => {
  log.info('🗑️ Emptying database')
  const query = sql<string>`SELECT table_name
  FROM information_schema.tables
  WHERE table_schema = 'public'
    AND table_type = 'BASE TABLE'`

  const tables = await db.execute(query)
  let retry: boolean

  do {
    retry = false
    for (const table of tables) {
      log.info(`Deleting data from table ${table.table_name}`)
      try {
        await db.execute(sql.raw(`DELETE FROM ${table.table_name};`))
      } catch (error) {
        if (isConstraintViolationError(error)) {
          retry = true
          log.info({ error }, 'Retrying ...')
        } else {
          log.error({ error }, `❌ Failed to delete data from table ${table.table_name}`)
          throw error
        }
      }
    }
  } while (retry)
  log.info('✅ Database emptied')
}

const initMasters = async (log: pino.Logger, script: string) => {
  log.info('🔑 Initializing masters')
  const scriptPath = path.join(process.cwd(), 'drizzle', script)
  try {
    const sqlScript = await fs.readFile(scriptPath, 'utf-8')
    // Execute the script content using db.execute and sql.raw
    // Note: Splitting by semicolon might be too naive if semicolons exist within string literals or comments in the SQL.
    // A more robust parser might be needed for complex scripts.
    // For this specific script, splitting by semicolon should work.
    const statements = sqlScript.split(';').filter((s) => s.trim())
    await db.transaction(async (tx) => {
      for (const statement of statements) {
        if (statement.trim()) {
          log.info(`Executing: ${statement.substring(0, 50)}...`) // Log snippet for brevity
          await tx.execute(sql.raw(statement))
        }
      }
    })
    log.info('✅ Masters initialized successfully')
  } catch (error) {
    log.error(error, `❌ Failed to initialize masters from ${scriptPath}`)
    // Optionally re-throw the error or handle it as needed
    throw error
  }
}

/**
{
  "query": "DELETE FROM country;",
  "params": [],
  "cause": {
    "name": "PostgresError",
    "severity_local": "ERROR",
    "severity": "ERROR",
    "code": "23503",
    "detail": "Key (code)=(IN) is still referenced from table \"state\".",
    "schema_name": "public",
    "table_name": "state",
    "constraint_name": "state_country_code_country_code_fk",
    "file": "ri_triggers.c",
    "line": "2633",
    "routine": "ri_ReportViolation"
  }
}
*/
const isConstraintViolationError = (error: unknown) =>
  typeof error === 'object' &&
  error !== null &&
  'cause' in error &&
  typeof error.cause === 'object' &&
  error.cause !== null &&
  'code' in error.cause &&
  error.cause.code === '23503'

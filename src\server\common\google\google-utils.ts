import { OAuth2Client } from 'google-auth-library'

import { decrypt } from '../encryption-utils.server.js'
import { env } from '../env.server.js'

export const newGoogleOAuth2Client = () =>
  new OAuth2Client({
    clientId: env.GOOGLE_CLIENT_ID,
    clientSecret: env.GOOGLE_CLIENT_SECRET,
    redirectUri: env.HOME_URL,
  })

export const getGoogleAuth = (refreshToken: string) => {
  const oAuth2Client = newGoogleOAuth2Client()

  oAuth2Client.setCredentials({
    refresh_token: refreshToken,
  })

  return oAuth2Client
}

type GoogleApi = (data: { version: 'v3'; auth: OAuth2Client }) => unknown

export const getGoogleApiForRefreshToken = (encryptedRefreshToken: string, googleApi: GoogleApi) => {
  const refreshToken = decrypt(encryptedRefreshToken)
  const auth = getGoogleAuth(refreshToken)
  return googleApi({ version: 'v3', auth })
}

export type GoogleErrorReason = 'duplicate' | 'fileIdInUse'
export const isGoogleError = (e: unknown, reason: GoogleErrorReason) =>
  !!e &&
  typeof e === 'object' &&
  'errors' in e &&
  Array.isArray(e.errors) &&
  e.errors.length === 1 &&
  !!e.errors[0] &&
  typeof e.errors[0] === 'object' &&
  (e.errors[0] as { reason?: string }).reason === reason

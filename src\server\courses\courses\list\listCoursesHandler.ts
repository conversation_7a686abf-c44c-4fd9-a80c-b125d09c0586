import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { HonoVars } from '@/server/common/hono-utils.server'
import { $CoursesSearch } from '@/shared/course/course-utils.shared'

import { listCourses } from './listCourses.server'

export const listCoursesHandler = new Hono<HonoVars>().get('/', zValidator('query', $CoursesSearch), async (c) => {
  const profile = c.get('profile')
  const search = c.req.valid('query')
  const courses = await listCourses(c.get('log'), search, profile?.id)
  return c.json({ rows: courses }, 200)
})

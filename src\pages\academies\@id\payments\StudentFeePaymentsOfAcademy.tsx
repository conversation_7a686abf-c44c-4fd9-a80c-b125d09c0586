import { UUID } from 'crypto'

import qs from 'qs'
import { useEffect, useState } from 'react'
import { navigate } from 'vike/client/router'

import { $StudentFeePaymentOfAcademySearch, StudentFeePaymentOfAcademySearch } from '@/shared/batch/batch-utils.shared'
import { formatJsDate2Timestamp } from '@/shared/common/date-utils.shared'
import { OmniError } from '@/shared/common/error-utils.shared'
import { PAYMENT_STATUSES, PaymentStatus, paymentStatuses } from '@/shared/common/payment-utils/payment-utils.shared'
import { profilePath } from '@/shared/profiles/profile-utils.shared'
import { useMyAcademyQuery } from '@ui/academies/common/academy-queries'
import { ErrorAlert } from '@ui/common/alerts/ErrorAlert'
import { CheckBox } from '@ui/common/form/CheckBox'
import { PaymentStatusBadge } from '@ui/common/payment/PaymentStatusBadge'
import { ShowData } from '@ui/common/ShowData'
import { ShowErrors } from '@ui/common/ShowErrors'
import {
  useReceiveStudentFeePaymentMutation,
  useStudentFeePaymentsOfAcademyQuery,
} from '@ui/courses/@id/batches/common/student-fee-payment-queries'

export const StudentFeePaymentsOfAcademyPage = ({
  academyId,
  search,
}: {
  academyId: UUID
  search: StudentFeePaymentOfAcademySearch
}) => {
  const { data: academy, isPending: academyIsPending, error: academyError } = useMyAcademyQuery()
  return (
    <ShowData isPending={academyIsPending} error={academyError}>
      {academy?.id === academyId ?
        <StudentFeePaymentsOfAcademySkeleton academyId={academyId} search={search} />
      : <ErrorAlert>You must be a staff of this academy to view this page</ErrorAlert>}
    </ShowData>
  )
}

const StudentFeePaymentsOfAcademySkeleton = ({
  academyId,
  search,
}: {
  academyId: UUID
  search: StudentFeePaymentOfAcademySearch
}) => {
  return (
    <div>
      <FilterForm academyId={academyId} search={search} />
      <StudentFeePaymentsOfAcademy academyId={academyId} search={search} />
    </div>
  )
}

const FilterForm = ({ academyId, search }: { academyId: UUID; search: StudentFeePaymentOfAcademySearch }) => {
  console.log('search', search)
  const [formData, setFormData] = useState<StudentFeePaymentOfAcademySearch>({
    statuses: search.statuses ?? [],
    studentEmail: search.studentEmail ?? '',
  })
  const [omniError, setOmniError] = useState<OmniError | undefined>()

  // Validate form data when it changes
  useEffect(() => {
    const parsed = $StudentFeePaymentOfAcademySearch.safeParse({ ...formData })
    setOmniError(parsed.error)
  }, [formData])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    void navigate(`/academies/${academyId}/payments?${qs.stringify(formData, { arrayFormat: 'repeat' })}`, {
      keepScrollPosition: true,
    })
  }

  return (
    <form onSubmit={handleSubmit}>
      <div className="space-y-12">
        <div className="border-b border-gray-900/10 pb-12">
          <h2 className="text-base/7 font-semibold text-gray-900">Filters</h2>
          <p className="mt-1 text-sm/6 text-gray-600">Filter the list of student fee payments</p>
          <ShowErrors error={omniError} />
          <div className="mt-10 space-y-10">
            <fieldset>
              <legend className="text-sm/6 font-semibold text-gray-900">Statuses</legend>
              <div className="mt-6 space-y-6">
                {PAYMENT_STATUSES.map((status) => (
                  <CheckBox
                    key={status}
                    checked={formData.statuses.includes(status)}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        statuses:
                          e.target.checked ?
                            [...formData.statuses, status]
                          : formData.statuses.filter((s) => s !== status),
                      })
                    }
                    label={paymentStatuses[status].label}
                    description={paymentStatuses[status].descr}
                  />
                ))}
              </div>
              <ShowErrors error={omniError} path={['statuses']} />
            </fieldset>
            <div>
              <label htmlFor="email" className="block text-sm/6 font-medium text-gray-900">
                Student's email address
              </label>
              <div className="mt-2">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  className="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                  value={formData.studentEmail}
                  onChange={(e) => setFormData({ ...formData, studentEmail: e.target.value })}
                />
              </div>
              <ShowErrors error={omniError} path={['studentEmail']} />
            </div>
          </div>
        </div>
      </div>
      <div className="mt-6 flex items-center justify-end gap-x-6">
        <button
          type="submit"
          className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          Apply
        </button>
      </div>
    </form>
  )
}

const StudentFeePaymentsOfAcademy = ({
  academyId,
  search,
}: {
  academyId: UUID
  search: StudentFeePaymentOfAcademySearch
}) => {
  const {
    data: studentFeePayments,
    isPending: studentFeePaymentsIsPending,
    error: studentFeePaymentsError,
  } = useStudentFeePaymentsOfAcademyQuery(academyId, search)
  return (
    <ShowData isPending={studentFeePaymentsIsPending} error={studentFeePaymentsError}>
      <div className="mt-8 flow-root">
        <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                      Student
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Amount
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Status
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Dates
                    </th>
                    <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {studentFeePayments?.map((payment) => (
                    <tr key={payment.id}>
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 sm:pl-6">
                        <div className="flex items-center">
                          <div>
                            <div className="font-medium text-gray-900">
                              <a
                                href={profilePath({
                                  id: payment.studentId,
                                  displayName: payment.studentName,
                                  role: 'student',
                                })}
                                className="hover:text-indigo-600"
                              >
                                {payment.studentName}
                              </a>
                            </div>
                            <div className="text-gray-500">
                              <a href={`mailto:${payment.studentEmail}`} className="hover:text-indigo-600">
                                {payment.studentEmail}
                              </a>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                        {payment.currency} {payment.cents / 100}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm">
                        <PaymentStatusBadge status={payment.status} />
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                        <p>
                          <time dateTime={payment.createdAt.toISOString()}>
                            Created at {formatJsDate2Timestamp(payment.createdAt)}
                          </time>
                        </p>
                        {payment.paidAt && (
                          <p>
                            <time dateTime={payment.paidAt.toISOString()}>
                              Paid at {formatJsDate2Timestamp(payment.paidAt)}
                            </time>
                          </p>
                        )}
                        {payment.paidRemarks && (
                          <p>
                            <i>{payment.paidRemarks}</i>
                          </p>
                        )}
                      </td>
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                        <div className="flex items-center justify-end gap-2">
                          <ReceiveStudentFeePaymentButton payment={payment} />
                          <a href={`/student-fee-payments/${payment.id}`} className="link">
                            View<span className="sr-only">, {payment.studentName}</span>
                          </a>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </ShowData>
  )
}

export const ReceiveStudentFeePaymentButton = ({ payment }: { payment: { id: UUID; status: PaymentStatus } }) => {
  const { mutate, isPending } = useReceiveStudentFeePaymentMutation(payment.id)

  if (payment.status !== 'pending') {
    return null
  }

  return (
    <button
      type="button"
      className="rounded-sm bg-indigo-50 px-2 py-1 text-sm font-semibold text-indigo-600 shadow-xs hover:bg-indigo-100"
      onClick={() => mutate()}
      disabled={isPending}
    >
      Receive
    </button>
  )
}

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $HasId } from '@/shared/common/common-utils.shared'

import { getStudentFeePayment } from './getStudentFeePayment'

export const getStudentFeePaymentHandler = new Hono<HonoVars>().get('/', zValidator('param', $HasId), async (c) => {
  const { id } = c.req.valid('param')
  const payment = await getStudentFeePayment(getCtx(c), id)
  return c.json(payment, 200)
})

import pino from 'pino'

import { DeploymentEnv, isClient } from './common-utils.shared'
export const redact = ['password', 'otp', 'authCode']

const standByLogger = pino({
  name: 'np-tuition-standby',
  level: 'info',
  redact,
}).child({ env: 'unknown', standby: true })

let log!: pino.Logger

export const initLogger = (logger: pino.Logger, env: DeploymentEnv) => {
  if (log && env !== 'development') {
    throw new Error('Logger already initialized')
  }
  log = logger
}

export const getLogger = (fallback?: pino.Logger) => {
  if (log) return log
  if (isClient()) {
    log = pino({
      name: 'np-tuition-web',
      level: 'debug',
      redact,
      transport: {
        target: 'pino-pretty',
        options: {
          singleLine: true,
        },
      },
    }).child({ env: 'unknown', client: true })
    return log
  }
  if (fallback) {
    log = fallback
    return fallback
  }
  return standByLogger
}

import { UUID } from 'crypto'

import { InformationCircleIcon, PaperClipIcon } from '@heroicons/react/20/solid'
import { Bars2Icon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { motion, Reorder, useDragControls } from 'framer-motion'
import { useEffect, useRef, useState, useTransition } from 'react'
import { clientOnly } from 'vike-react/clientOnly'
import { Config } from 'vike-react/Config'
import { Head } from 'vike-react/Head'

import { CourseAttachmentUrl } from '@/server/courses/courses/attachments/add/addCourseAttachment'
import { CourseAttachment } from '@/server/courses/courses/common/course-select-helper'
import { CourseWithDescr, CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { batchPath } from '@/shared/batch/batch-utils.shared'
import { formatDate, today } from '@/shared/common/date-utils.shared'
import { COURSE_ATTACHMENT_MAX_POSITION, coursePath } from '@/shared/course/course-utils.shared'
import { useMyAcademyQuery } from '@ui/academies/common/academy-queries'
import '@ui/common/assets/rich-content.css'
import { confirmAndRun } from '@ui/common/confirmation/confirmation-store'
import { CheckBox } from '@ui/common/form/CheckBox'
import { showNotification } from '@ui/common/notification/notification-store'
import { ShowData } from '@ui/common/ShowData'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'
import { CourseHeader } from '@ui/courses/common/CourseHeader'

import { CourseAttachmentBlock } from '../common/CourseAttachmentBlock'
import {
  useAddCourseAttachmentMutation,
  useCourseSuspenseQuery,
  useMarkUploadedCourseAttachmentMutation,
  useMoveCourseAttachmentMutation,
  useRemoveCourseAttachmentMutation,
} from '../common/queries/course-queries'

import { useBatchSuspenseQuery } from './batches/common/batch-queries'

const CourseActions = clientOnly(async () => (await import('./CourseActions')).CourseActions)
const CourseTagEditorPanel = clientOnly(async () => (await import('./CourseTagEditor')).CourseTagEditorPanel)

export const CoursePage = ({ courseId }: { courseId: UUID }) => {
  const { data: course, error: courseError } = useCourseSuspenseQuery(courseId, true)
  return (
    <ShowData error={courseError} spinnerSize="1.25rem">
      <ViewCourse course={course as CourseWithDescr} />
    </ShowData>
  )
}

const ViewCourse = ({ course }: { course: CourseWithDescr }) => {
  const { data: myAcademy } = useMyAcademyQuery()
  const [showTagEditor, setShowTagEditor] = useState(course.tagIds.length === 0)
  return (
    <>
      <Config title={`${course.name}`} />
      <Head>
        <meta name="description" content={`${course.name}`} />
      </Head>
      <div className="px-4 sm:px-0">
        {/* Header section with name and role */}
        <div className="flex flex-col sm:flex-row items-start justify-between">
          <CourseHeader course={course} />
          <CourseActions course={course} setShowTagEditor={setShowTagEditor} />
        </div>

        <ViewBatches course={course} />
        {/* Tag editor */}
        <CourseTagEditorPanel course={course} showTagEditor={showTagEditor} setShowTagEditor={setShowTagEditor} />

        {/* About section */}
        <div className="mt-6">
          {course.descr ?
            <div className="rich-content">
              <div dangerouslySetInnerHTML={{ __html: course.descr }} />
            </div>
          : <div className="mt-6 rounded-lg bg-gray-50 px-6 py-12">
              <div className="text-center">
                <p className="text-sm text-gray-500 italic">No course description available</p>
              </div>
            </div>
          }
        </div>

        {myAcademy?.id === course.academyId && <UploadAttachmentForm courseId={course.id} />}
        <ViewAttachments course={course} />
      </div>
    </>
  )
}

const UploadAttachmentForm = ({ courseId }: { courseId: UUID }) => {
  const [file, setFile] = useState<File | null>(null)
  const [free, setFree] = useState(false)
  const { mutate: addAttachment, isPending: addAttachmentPending } = useAddCourseAttachmentMutation(courseId)
  const [uploadPending, startUpload] = useTransition()
  const { mutate: markAsUploaded, isPending: markUploadedPending } = useMarkUploadedCourseAttachmentMutation(courseId)
  const { mutate: removeAttachment, isPending: removeAttachmentPending } = useRemoveCourseAttachmentMutation(courseId)

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFile(event.target.files?.[0] ?? null)
  }

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (file) {
      addAttachment(
        { name: file.name, free, contentType: file.type, sizeBytes: file.size },
        {
          onSuccess: (attachment) => startUpload(() => uploadToS3(attachment)),
          onError: (error) => {
            showNotification({
              heading: 'Error uploading attachment',
              message: getErrorMessage(error),
              type: 'error',
            })
          },
        },
      )
    }
  }

  const uploadToS3 = async (attachment: CourseAttachmentUrl) => {
    try {
      const res = await fetch(attachment.url, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file!.type,
        },
      })
      if (res.ok) {
        markAsUploaded(attachment.id, {
          onSuccess: () => {
            setFile(null)
          },
        })
      } else {
        const error = await res.json()
        showNotification({
          heading: `Error ${res.status} uploading attachment`,
          message: getErrorMessage(error),
          type: 'error',
        })
        removeAttachment(attachment.id)
      }
    } catch (error) {
      showNotification({
        heading: 'Error uploading attachment',
        message: getErrorMessage(error),
        type: 'error',
      })
      removeAttachment(attachment.id)
    }
  }

  const isPending = addAttachmentPending || uploadPending || markUploadedPending || removeAttachmentPending
  return (
    <div className="mt-8 border-t border-gray-100 pt-6">
      <h2 className="text-base font-semibold leading-7 text-gray-900">Add Course Resource</h2>
      <p className="mt-1 text-sm leading-6 text-gray-500">Upload files that students will need for this course.</p>
      <form onSubmit={handleSubmit} className="mt-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="space-y-4">
            <div>
              <label htmlFor="file" className="block text-sm font-medium text-gray-700">
                Select File
              </label>
              <div className="mt-1">
                <input
                  id="file"
                  name="file"
                  type="file"
                  autoComplete="file"
                  onChange={handleFileChange}
                  className="block w-full rounded-md border-gray-300 px-2 py-1.5 text-sm text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <CheckBox label="Publicly available" checked={free} onChange={() => setFree(!free)} />
              </div>

              <button
                type="submit"
                disabled={!file || isPending}
                className={clsx(
                  'inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-offset-2 focus-visible:outline-indigo-600',
                  (!file || isPending) && 'opacity-50 cursor-not-allowed',
                )}
              >
                <PaperClipIcon className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
                {isPending ? 'Uploading...' : 'Upload'}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  )
}

const ViewBatches = ({ course }: { course: CourseWithoutDescr }) => {
  return (
    <div className="rounded-md border border-blue-200 p-4 mt-4">
      <div className="flex">
        <div className="shrink-0">
          <InformationCircleIcon aria-hidden="true" className="size-5 text-blue-400" />
        </div>
        <div className="ml-3 flex-1 md:flex md:justify-between">
          <p className="text-sm text-blue-700">
            {course.recommendedBatchId ?
              <RecommendedBatch course={course} batchId={course.recommendedBatchId} />
            : 'No recommended batches found.'}
          </p>
          <p className="mt-3 text-sm md:mt-0 md:ml-6">
            <a
              href={`${coursePath(course)}/batches`}
              className="font-medium whitespace-nowrap text-blue-700 hover:text-blue-600"
            >
              View All Batches
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}

const RecommendedBatch = ({ course, batchId }: { course: CourseWithoutDescr; batchId: UUID }) => {
  const { data: batch } = useBatchSuspenseQuery(batchId)
  return batch ?
      <a href={batchPath(course, batchId)}>
        Batch {batch.startDate < today() ? 'started' : 'starts'} on {formatDate(batch.startDate)}
      </a>
    : 'No recommended batches found.'
}

const ViewAttachments = ({ course }: { course: CourseWithDescr }) => {
  const { data: myAcademy } = useMyAcademyQuery()
  const [attachments, setAttachments] = useState<CourseAttachment[]>(course.attachments)
  const isAcademyStaff = myAcademy?.id === course.academyId

  // Update local state when course attachments change
  useEffect(() => {
    setAttachments(course.attachments)
  }, [course.attachments])

  const handleReorder = (reorderedAttachments: CourseAttachment[]) => {
    setAttachments(reorderedAttachments)
  }

  // Store the original server state for recovery on error
  const originalAttachmentsRef = useRef<CourseAttachment[]>(course.attachments)

  useEffect(() => {
    originalAttachmentsRef.current = course.attachments
  }, [course.attachments])

  // Reset to server state
  const resetToOriginalOrder = () => {
    setAttachments([...originalAttachmentsRef.current])
  }

  return (
    <div className="mt-8 border-t border-gray-100 pt-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-base font-semibold leading-7 text-gray-900">Resources</h2>
      </div>
      {attachments.length > 0 ?
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <Reorder.Group
            as="ul"
            axis="y"
            values={attachments}
            onReorder={handleReorder}
            className="divide-y divide-gray-200"
          >
            {attachments.map((attachment) => (
              <DraggableAttachment
                key={attachment.id}
                course={course}
                attachment={attachment}
                isAcademyOwner={isAcademyStaff}
                attachments={attachments}
                onMoveError={resetToOriginalOrder}
              />
            ))}
          </Reorder.Group>
        </div>
      : <div className="py-6 text-center bg-gray-50 rounded-lg border border-gray-200">
          <PaperClipIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-semibold text-gray-900">No resources</h3>
          <p className="mt-1 text-sm text-gray-500">No course resources have been uploaded yet.</p>
        </div>
      }
    </div>
  )
}

const DraggableAttachment = ({
  course,
  attachment,
  isAcademyOwner: isStaff,
  attachments,
  onMoveError,
}: {
  course: CourseWithDescr
  attachment: CourseAttachment
  isAcademyOwner: boolean
  attachments: CourseAttachment[]
  onMoveError: () => void
}) => {
  const controls = useDragControls()
  const { mutate: moveAttachment } = useMoveCourseAttachmentMutation(course.id, attachment.id)
  const nodeRef = useRef<HTMLLIElement>(null)
  const [isDragging, setIsDragging] = useState(false)

  const handleDragEnd = () => {
    setIsDragging(false)

    // Find the current index of the attachment
    const currentIndex = attachments.findIndex((item) => item.id === attachment.id)

    if (currentIndex === -1) return

    // Get the next attachment's position, or use MAX_POSITION if it's the last one
    const nextPosition =
      currentIndex < attachments.length - 1 ? attachments[currentIndex + 1].position : COURSE_ATTACHMENT_MAX_POSITION

    // Call the mutation to move the attachment
    moveAttachment(
      { beforePosition: nextPosition },
      {
        onError: (error) => {
          showNotification({
            heading: 'Error moving attachment',
            message: getErrorMessage(error),
            type: 'error',
          })
          // Reset to original order from server
          onMoveError()
        },
      },
    )
  }

  // Start drag handler
  const handleDragStart = () => {
    setIsDragging(true)
  }

  return (
    <Reorder.Item
      ref={nodeRef}
      value={attachment}
      dragControls={controls}
      dragListener={false}
      onDragEnd={handleDragEnd}
      onDragStart={handleDragStart}
      as="li"
      className={`relative ${isDragging ? 'select-none' : ''}`}
      whileDrag={{
        boxShadow: '0px 10px 15px rgba(0, 0, 0, 0.1)',
        backgroundColor: '#f8fafc',
        zIndex: 10,
      }}
      initial={{ backgroundColor: 'rgba(255, 255, 255, 0)' }}
      animate={{ backgroundColor: 'rgba(255, 255, 255, 0)' }}
      exit={{ backgroundColor: 'rgba(255, 255, 255, 0)' }}
      transition={{ duration: 0.3 }}
    >
      <motion.div
        key={attachment.id}
        className="flex items-center p-4 transition-colors duration-150"
        initial={{ backgroundColor: 'rgba(255, 255, 255, 0)' }}
        whileHover={{ backgroundColor: 'rgba(249, 250, 251, 1)' }}
        animate={{ backgroundColor: 'rgba(255, 255, 255, 0)' }}
      >
        {isStaff && (
          <div
            className="mr-2 cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600 transition-colors select-none"
            onPointerDown={(e) => {
              e.preventDefault() // This prevents unwanted selection when starting a drag
              controls.start(e)
            }}
          >
            <Bars2Icon className="h-5 w-5" aria-hidden="true" />
          </div>
        )}
        <CourseAttachmentBlock
          attachment={attachment}
          actions={
            isStaff && (
              <>
                <a href={`/courses/${course.id}/attachments/${attachment.id}/edit`} className="link underline">
                  Edit
                </a>
                <RemoveButton course={course} attachment={attachment} />
              </>
            )
          }
        />
      </motion.div>
    </Reorder.Item>
  )
}

const RemoveButton = ({ course, attachment }: { course: CourseWithDescr; attachment: CourseAttachment }) => {
  const { mutate: removeAttachment, isPending: removeAttachmentPending } = useRemoveCourseAttachmentMutation(course.id)

  const handleRemoveAttachment = () =>
    confirmAndRun(() => removeAttachment(attachment.id), {
      heading: 'Remove Attachment',
      message: `Are you sure you want to remove ${attachment.name}?`,
      okLabel: 'Remove',
    })

  return (
    <button
      className={clsx('link underline', removeAttachmentPending && 'cursor-wait')}
      disabled={removeAttachmentPending}
      onClick={handleRemoveAttachment}
    >
      Remove
    </button>
  )
}

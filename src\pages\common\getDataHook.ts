import { ContentfulStatusCode } from 'hono/utils/http-status'
import { render } from 'vike/abort'
import { PageContext } from 'vike/types'

import { toErrorPayload } from '@/server/middleware/error/toErrorPayload'
import { ErrorPayload } from '@/shared/common/error-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

type DataHookFunction<T> = (pageContext: PageContext) => Promise<T>

/**
 * A wrapper for Vike's +data hooks that provides consistent error handling and logging
 * @param hookName - Name of the data hook for logging purposes
 * @param fn - The actual data hook function
 * @returns A wrapped data hook function with error handling
 *
 * @example
 * ```ts
 * // In your +data.ts file:
 * const _data = async (pageContext: PageContext) => {
 *   // Your data fetching logic here
 *   return { someData }
 * }
 *
 * export const data = getDataHook('/courses/@id', _data)
 * ```
 */
export const getDataHook = <T>(
  hookName: string,
  fn: DataHookFunction<T>,
  renderErrorPage = true,
): DataHookFunction<typeof renderErrorPage extends true ? T : { data?: T; error: ErrorPayload | null }> => {
  return async (pageContext: PageContext) => {
    const { log } = pageContext

    try {
      const data = await withLog(log, `${hookName}/+data`, () => fn(pageContext))
      return (renderErrorPage ? data : { data, error: null }) as typeof renderErrorPage extends true ? T
      : { data: T; error: null }
    } catch (error: unknown) {
      const payload = toErrorPayload(log, error as Error)
      if (renderErrorPage) {
        const status = getAbortStatusCode(payload.status)
        throw render(status, payload)
      }
      return { error: payload } as typeof renderErrorPage extends true ? T : { error: ErrorPayload }
    }
  }
}

export type Data<F extends DataHookFunction<unknown>> = Awaited<ReturnType<F>>
export type DataOrError<F extends DataHookFunction<unknown>> = { data?: Data<F>; error: ErrorPayload | null }

type AbortStatusCode = 401 | 403 | 404 | 410 | 429 | 500 | 503

function getAbortStatusCode(statusCode: ContentfulStatusCode): AbortStatusCode {
  if ([401, 403, 404, 429, 500, 503].includes(statusCode)) return statusCode as AbortStatusCode
  return statusCode >= 500 ? 500 : 403
}

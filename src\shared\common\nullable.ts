const deepSafeAccess = <T>(obj: Record<string, unknown> | null | undefined, path: string[]): T | null | undefined => {
  if (obj === null) return null
  if (obj === undefined) return undefined
  if (path.length === 0) return obj as T

  const [first, ...rest] = path
  const value = obj[first]

  if (rest.length === 0) return value as T
  if (value === null) return null
  if (value === undefined) return undefined
  if (typeof value !== 'object') return undefined

  return deepSafeAccess(value as Record<string, unknown>, rest)
}

export const nullable = <T>(obj: Record<string, unknown> | null | undefined, path: string): T | null | undefined =>
  deepSafeAccess(obj, path.split('.'))

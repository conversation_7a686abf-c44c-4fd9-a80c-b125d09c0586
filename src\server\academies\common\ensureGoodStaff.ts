import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'

import { Ctx } from '@/server/common/auth-context-types.server'
import { db } from '@/server/db/db'
import { dummyColumn } from '@/server/db/db-utils'
import { academyStaffTable } from '@/server/db/schema/academy-schema'
import { UNKNOWN_UUID } from '@/shared/common/common-utils.shared'
import { Role } from '@/shared/profiles/role-utils.shared'

import { ensureCanUpdateAcademy } from './ensureCanUpdateAcademy'

const findAcademyStaff = (academyId: UUID, profileId: UUID) =>
  db
    .select(dummyColumn.extras)
    .from(academyStaffTable)
    .where(and(eq(academyStaffTable.profileId, profileId), eq(academyStaffTable.academyId, academyId)))
    .limit(1)

export const ensureGoodStaff = async (c: Ctx, academyId: UUID, roleAnyOf: readonly Role[] = ['principal']) =>
  ensureCanUpdateAcademy(c, findAcademyStaff(academyId, c.profile?.id ?? UNKNOWN_UUID), roleAnyOf)

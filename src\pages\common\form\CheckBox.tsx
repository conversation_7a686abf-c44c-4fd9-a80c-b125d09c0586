/**
 * A reusable checkbox component that supports rich content for both label and description.
 *
 * @example
 * // Basic usage with strings
 * <CheckBox
 *   label="Simple Label"
 *   description="Simple description"
 *   checked={isChecked}
 *   onChange={handleChange}
 * />
 *
 * @example
 * // Using JSX elements
 * <CheckBox
 *   label={<>Label with <strong>bold</strong> text</>}
 *   description={<>Description with <a href="#">link</a></>}
 *   checked={isChecked}
 *   onChange={handleChange}
 * />
 *
 * @example
 * // With components and mixed content
 * <CheckBox
 *   label={<span className="flex items-center">Label with <Icon className="ml-2" /></span>}
 *   description={<CustomDescriptionComponent />}
 *   checked={isChecked}
 *   onChange={handleChange}
 * />
 */

import clsx from 'clsx'
import { type ChangeEvent, type ReactNode } from 'react'

type CheckBoxProps = {
  checked: boolean
  onChange: (e: ChangeEvent<HTMLInputElement>) => void
  label: ReactNode
  disabled?: boolean
  description?: ReactNode
  id?: string
}

export const CheckBox = ({ disabled, checked, onChange, label, description, id }: CheckBoxProps) => (
  <div className="flex gap-3">
    <div className="flex h-6 shrink-0 items-center">
      <div className="group grid size-4 grid-cols-1">
        <input
          id={id}
          type="checkbox"
          checked={checked}
          onChange={onChange}
          disabled={disabled}
          aria-describedby={description ? `${id}-description` : undefined}
          className="col-start-1 row-start-1 appearance-none rounded-sm border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"
        />
        <svg
          fill="none"
          viewBox="0 0 14 14"
          className="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-disabled:stroke-gray-950/25"
        >
          <path
            d="M3 8L6 11L11 3.5"
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
            className="opacity-0 group-has-checked:opacity-100"
          />
          <path
            d="M3 7H11"
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
            className="opacity-0 group-has-indeterminate:opacity-100"
          />
        </svg>
      </div>
    </div>
    <div className="text-sm/6">
      <label htmlFor={id} className={clsx('text-gray-900', description && 'font-medium')}>
        {label}
      </label>
      {description && (
        <div id={`${id}-description`} className="text-gray-500">
          {description}
        </div>
      )}
    </div>
  </div>
)

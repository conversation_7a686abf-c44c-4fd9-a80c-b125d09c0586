{"id": "4ac96ca5-40fa-4e8a-9ced-9698b4c41084", "prevId": "047f961e-a5ff-4d89-8131-64a585be561a", "version": "7", "dialect": "postgresql", "tables": {"public.academy_staff": {"name": "academy_staff", "schema": "", "columns": {"profile_id": {"name": "profile_id", "type": "uuid", "primaryKey": true, "notNull": true}, "academy_id": {"name": "academy_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}, "suspended_at": {"name": "suspended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "suspension_reason": {"name": "suspension_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_academy_employee_academy_id": {"name": "idx_academy_employee_academy_id", "columns": [{"expression": "academy_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"academy_staff_profile_id_profile_id_fk": {"name": "academy_staff_profile_id_profile_id_fk", "tableFrom": "academy_staff", "tableTo": "profile", "columnsFrom": ["profile_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "academy_staff_academy_id_academy_id_fk": {"name": "academy_staff_academy_id_academy_id_fk", "tableFrom": "academy_staff", "tableTo": "academy", "columnsFrom": ["academy_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.academy": {"name": "academy", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "tncs_accepted_at": {"name": "tncs_accepted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "approved_at": {"name": "approved_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "descr": {"name": "descr", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "mobile_country_code": {"name": "mobile_country_code", "type": "text", "primaryKey": false, "notNull": true}, "mobile": {"name": "mobile", "type": "text", "primaryKey": false, "notNull": true}, "mobile_verified": {"name": "mobile_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true}, "upi_id": {"name": "upi_id", "type": "text", "primaryKey": false, "notNull": true}, "trade_name": {"name": "trade_name", "type": "text", "primaryKey": false, "notNull": false}, "gstin": {"name": "gstin", "type": "text", "primaryKey": false, "notNull": false}, "district_id": {"name": "district_id", "type": "uuid", "primaryKey": false, "notNull": true}, "pincode": {"name": "pincode", "type": "text", "primaryKey": false, "notNull": true}, "area": {"name": "area", "type": "text", "primaryKey": false, "notNull": true}, "address_line1": {"name": "address_line1", "type": "text", "primaryKey": false, "notNull": false}, "address_line2": {"name": "address_line2", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}, "suspended_at": {"name": "suspended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "suspension_reason": {"name": "suspension_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_academy_name": {"name": "idx_academy_name", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"academy_mobile_country_code_country_code_fk": {"name": "academy_mobile_country_code_country_code_fk", "tableFrom": "academy", "tableTo": "country", "columnsFrom": ["mobile_country_code"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "no action"}, "academy_district_id_district_id_fk": {"name": "academy_district_id_district_id_fk", "tableFrom": "academy", "tableTo": "district", "columnsFrom": ["district_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"academy_name_unique": {"name": "academy_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {"chk_name_len": {"name": "chk_name_len", "value": "char_length(\"academy\".\"name\") <= 255"}, "descr_len": {"name": "descr_len", "value": "char_length(\"academy\".\"descr\") <= 10000"}, "chk_currency_len": {"name": "chk_currency_len", "value": "char_length(\"academy\".\"currency\") <= 10"}, "chk_upi_id_len": {"name": "chk_upi_id_len", "value": "char_length(\"academy\".\"upi_id\") <= 255"}, "suspension_reason_len": {"name": "suspension_reason_len", "value": "char_length(\"academy\".\"suspension_reason\") <= 5000"}}, "isRLSEnabled": false}, "public.batch_attachment": {"name": "batch_attachment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "batch_id": {"name": "batch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "attachment_id": {"name": "attachment_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_batch_attachment_batch_id_attachment_id": {"name": "idx_batch_attachment_batch_id_attachment_id", "columns": [{"expression": "batch_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "attachment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "idx_batch_attachment_attachment_id": {"name": "idx_batch_attachment_attachment_id", "columns": [{"expression": "attachment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"batch_attachment_batch_id_batch_id_fk": {"name": "batch_attachment_batch_id_batch_id_fk", "tableFrom": "batch_attachment", "tableTo": "batch", "columnsFrom": ["batch_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "batch_attachment_attachment_id_course_attachment_id_fk": {"name": "batch_attachment_attachment_id_course_attachment_id_fk", "tableFrom": "batch_attachment", "tableTo": "course_attachment", "columnsFrom": ["attachment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.batch_event": {"name": "batch_event", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "batch_id": {"name": "batch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "at": {"name": "at", "type": "text", "primaryKey": false, "notNull": true}, "duration_minutes": {"name": "duration_minutes", "type": "smallint", "primaryKey": false, "notNull": true}, "days": {"name": "days", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'::text[]"}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'meet'"}, "event_xid": {"name": "event_xid", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_batch_event_batch_id": {"name": "idx_batch_event_batch_id", "columns": [{"expression": "batch_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"batch_event_batch_id_batch_id_fk": {"name": "batch_event_batch_id_batch_id_fk", "tableFrom": "batch_event", "tableTo": "batch", "columnsFrom": ["batch_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"chk_at_len": {"name": "chk_at_len", "value": "char_length(\"batch_event\".\"at\") = 5"}, "chk_event_type_len": {"name": "chk_event_type_len", "value": "char_length(\"batch_event\".\"event_type\") <= 50"}, "chk_event_xid_len": {"name": "chk_event_xid_len", "value": "char_length(\"batch_event\".\"event_xid\") <= 255"}}, "isRLSEnabled": false}, "public.batch_recommendation": {"name": "batch_recommendation", "schema": "", "columns": {"course_id": {"name": "course_id", "type": "uuid", "primaryKey": true, "notNull": true}, "batch_id": {"name": "batch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"batch_recommendation_course_id_course_id_fk": {"name": "batch_recommendation_course_id_course_id_fk", "tableFrom": "batch_recommendation", "tableTo": "course", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "batch_recommendation_batch_id_batch_id_fk": {"name": "batch_recommendation_batch_id_batch_id_fk", "tableFrom": "batch_recommendation", "tableTo": "batch", "columnsFrom": ["batch_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.batch_student": {"name": "batch_student", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "batch_id": {"name": "batch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "first_joined_at": {"name": "first_joined_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "left_at": {"name": "left_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "paid_till_cycle": {"name": "paid_till_cycle", "type": "smallint", "primaryKey": false, "notNull": true, "default": 0}, "last_reminder_type": {"name": "last_reminder_type", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_batch_student_batch_id": {"name": "idx_batch_student_batch_id", "columns": [{"expression": "batch_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_batch_student_student_id": {"name": "idx_batch_student_student_id", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_batch_student_batch_id_student_id": {"name": "idx_batch_student_batch_id_student_id", "columns": [{"expression": "batch_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"batch_student_batch_id_batch_id_fk": {"name": "batch_student_batch_id_batch_id_fk", "tableFrom": "batch_student", "tableTo": "batch", "columnsFrom": ["batch_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "batch_student_student_id_profile_id_fk": {"name": "batch_student_student_id_profile_id_fk", "tableFrom": "batch_student", "tableTo": "profile", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.batch": {"name": "batch", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "course_id": {"name": "course_id", "type": "uuid", "primaryKey": false, "notNull": true}, "teacher_id": {"name": "teacher_id", "type": "uuid", "primaryKey": false, "notNull": true}, "fee": {"name": "fee", "type": "integer", "primaryKey": false, "notNull": true}, "billing_cycle": {"name": "billing_cycle", "type": "text", "primaryKey": false, "notNull": true}, "grace_days": {"name": "grace_days", "type": "smallint", "primaryKey": false, "notNull": true, "default": 3}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "timezone": {"name": "timezone", "type": "text", "primaryKey": false, "notNull": true}, "cycle_count": {"name": "cycle_count", "type": "smallint", "primaryKey": false, "notNull": true}, "over": {"name": "over", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "seat_count": {"name": "seat_count", "type": "smallint", "primaryKey": false, "notNull": true}, "student_count": {"name": "student_count", "type": "smallint", "primaryKey": false, "notNull": true, "default": 0}, "admission_open": {"name": "admission_open", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}, "suspended_at": {"name": "suspended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "suspension_reason": {"name": "suspension_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_batch_course_id_start_date_created_at": {"name": "idx_batch_course_id_start_date_created_at", "columns": [{"expression": "course_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "start_date", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_batch_teacher_id_start_date_created_at": {"name": "idx_batch_teacher_id_start_date_created_at", "columns": [{"expression": "teacher_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "start_date", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_batch_start_date_course_id": {"name": "idx_batch_start_date_course_id", "columns": [{"expression": "start_date", "isExpression": false, "asc": false, "nulls": "last"}, {"expression": "course_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"batch_course_id_course_id_fk": {"name": "batch_course_id_course_id_fk", "tableFrom": "batch", "tableTo": "course", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "batch_teacher_id_profile_id_fk": {"name": "batch_teacher_id_profile_id_fk", "tableFrom": "batch", "tableTo": "profile", "columnsFrom": ["teacher_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"chk_timezone_len": {"name": "chk_timezone_len", "value": "char_length(\"batch\".\"timezone\") <= 50"}, "suspension_reason_len": {"name": "suspension_reason_len", "value": "char_length(\"batch\".\"suspension_reason\") <= 5000"}}, "isRLSEnabled": false}, "public.student_fee_payment_item": {"name": "student_fee_payment_item", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "student_fee_payment_id": {"name": "student_fee_payment_id", "type": "uuid", "primaryKey": false, "notNull": true}, "batch_id": {"name": "batch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "cycle": {"name": "cycle", "type": "smallint", "primaryKey": false, "notNull": true}, "fee": {"name": "fee", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_student_fee_payment_item_batch_id": {"name": "idx_student_fee_payment_item_batch_id", "columns": [{"expression": "batch_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_student_fee_payment_item_student_fee_payment_id": {"name": "idx_student_fee_payment_item_student_fee_payment_id", "columns": [{"expression": "student_fee_payment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"student_fee_payment_item_student_fee_payment_id_student_fee_payment_id_fk": {"name": "student_fee_payment_item_student_fee_payment_id_student_fee_payment_id_fk", "tableFrom": "student_fee_payment_item", "tableTo": "student_fee_payment", "columnsFrom": ["student_fee_payment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_fee_payment_item_batch_id_batch_id_fk": {"name": "student_fee_payment_item_batch_id_batch_id_fk", "tableFrom": "student_fee_payment_item", "tableTo": "batch", "columnsFrom": ["batch_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_fee_payment": {"name": "student_fee_payment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "academy_id": {"name": "academy_id", "type": "uuid", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true}, "cents": {"name": "cents", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "paid_at": {"name": "paid_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "paid_remarks": {"name": "paid_remarks", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_student_fee_payment_student_id_academy_id_status": {"name": "idx_student_fee_payment_student_id_academy_id_status", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "academy_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_student_fee_payment_academy_id": {"name": "idx_student_fee_payment_academy_id", "columns": [{"expression": "academy_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"student_fee_payment_student_id_profile_id_fk": {"name": "student_fee_payment_student_id_profile_id_fk", "tableFrom": "student_fee_payment", "tableTo": "profile", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_fee_payment_academy_id_academy_id_fk": {"name": "student_fee_payment_academy_id_academy_id_fk", "tableFrom": "student_fee_payment", "tableTo": "academy", "columnsFrom": ["academy_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"chk_paid_remarks_len": {"name": "chk_paid_remarks_len", "value": "char_length(\"student_fee_payment\".\"paid_remarks\") <= 255"}}, "isRLSEnabled": false}, "public.course_attachment": {"name": "course_attachment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "course_id": {"name": "course_id", "type": "uuid", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "content_type": {"name": "content_type", "type": "text", "primaryKey": false, "notNull": true}, "size_bytes": {"name": "size_bytes", "type": "integer", "primaryKey": false, "notNull": true}, "free": {"name": "free", "type": "boolean", "primaryKey": false, "notNull": true}, "uploaded": {"name": "uploaded", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_course_attachment_course_id_name": {"name": "idx_course_attachment_course_id_name", "columns": [{"expression": "course_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "idx_course_attachment_course_id_position": {"name": "idx_course_attachment_course_id_position", "columns": [{"expression": "course_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "position", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"course_attachment_course_id_course_id_fk": {"name": "course_attachment_course_id_course_id_fk", "tableFrom": "course_attachment", "tableTo": "course", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"chk_position_len": {"name": "chk_position_len", "value": "char_length(\"course_attachment\".\"position\") <= 255"}, "chk_name_len": {"name": "chk_name_len", "value": "char_length(\"course_attachment\".\"name\") <= 255"}, "chk_content_type_len": {"name": "chk_content_type_len", "value": "char_length(\"course_attachment\".\"content_type\") <= 255"}, "chk_size_bytes": {"name": "chk_size_bytes", "value": "\"course_attachment\".\"size_bytes\" > 0"}}, "isRLSEnabled": false}, "public.course": {"name": "course", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "descr": {"name": "descr", "type": "text", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "academy_id": {"name": "academy_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}, "suspended_at": {"name": "suspended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "suspension_reason": {"name": "suspension_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_course_name": {"name": "idx_course_name", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_course_academy_id": {"name": "idx_course_academy_id", "columns": [{"expression": "academy_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"course_academy_id_academy_id_fk": {"name": "course_academy_id_academy_id_fk", "tableFrom": "course", "tableTo": "academy", "columnsFrom": ["academy_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"chk_name_len": {"name": "chk_name_len", "value": "char_length(\"course\".\"name\") <= 255"}, "descr_len": {"name": "descr_len", "value": "char_length(\"course\".\"descr\") <= 10000"}, "suspension_reason_len": {"name": "suspension_reason_len", "value": "char_length(\"course\".\"suspension_reason\") <= 5000"}}, "isRLSEnabled": false}, "public.course_tag_assignment": {"name": "course_tag_assignment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "course_id": {"name": "course_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tag_id": {"name": "tag_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {"idx_course_tag_assignment_course_id": {"name": "idx_course_tag_assignment_course_id", "columns": [{"expression": "course_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_course_tag_assignment_tag_id": {"name": "idx_course_tag_assignment_tag_id", "columns": [{"expression": "tag_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"course_tag_assignment_course_id_course_id_fk": {"name": "course_tag_assignment_course_id_course_id_fk", "tableFrom": "course_tag_assignment", "tableTo": "course", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "course_tag_assignment_tag_id_course_tag_id_fk": {"name": "course_tag_assignment_tag_id_course_tag_id_fk", "tableFrom": "course_tag_assignment", "tableTo": "course_tag", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.course_tag_group": {"name": "course_tag_group", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "smallint", "primaryKey": false, "notNull": true}, "suspended_at": {"name": "suspended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "suspension_reason": {"name": "suspension_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"course_tag_group_name_unique": {"name": "course_tag_group_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {"chk_name_len": {"name": "chk_name_len", "value": "char_length(\"course_tag_group\".\"name\") <= 30"}, "suspension_reason_len": {"name": "suspension_reason_len", "value": "char_length(\"course_tag_group\".\"suspension_reason\") <= 5000"}}, "isRLSEnabled": false}, "public.course_tag": {"name": "course_tag", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "group_id": {"name": "group_id", "type": "uuid", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "smallint", "primaryKey": false, "notNull": true}, "descr": {"name": "descr", "type": "text", "primaryKey": false, "notNull": true}, "suspended_at": {"name": "suspended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "suspension_reason": {"name": "suspension_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_course_tag_group_id": {"name": "idx_course_tag_group_id", "columns": [{"expression": "group_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"course_tag_group_id_course_tag_group_id_fk": {"name": "course_tag_group_id_course_tag_group_id_fk", "tableFrom": "course_tag", "tableTo": "course_tag_group", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"course_tag_name_unique": {"name": "course_tag_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {"chk_name_len": {"name": "chk_name_len", "value": "char_length(\"course_tag\".\"name\") <= 30"}, "descr_len": {"name": "descr_len", "value": "char_length(\"course_tag\".\"descr\") <= 255"}, "suspension_reason_len": {"name": "suspension_reason_len", "value": "char_length(\"course_tag\".\"suspension_reason\") <= 5000"}}, "isRLSEnabled": false}, "public.country": {"name": "country", "schema": "", "columns": {"code": {"name": "code", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "phone_prefix": {"name": "phone_prefix", "type": "text", "primaryKey": false, "notNull": true}, "suspended_at": {"name": "suspended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "suspension_reason": {"name": "suspension_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"country_name_unique": {"name": "country_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "country_phonePrefix_unique": {"name": "country_phonePrefix_unique", "nullsNotDistinct": false, "columns": ["phone_prefix"]}}, "policies": {}, "checkConstraints": {"code_len": {"name": "code_len", "value": "char_length(\"country\".\"code\") <= 5"}, "name_len": {"name": "name_len", "value": "char_length(\"country\".\"name\") <= 100"}, "phone_prefix_len": {"name": "phone_prefix_len", "value": "char_length(\"country\".\"phone_prefix\") <= 10"}, "suspension_reason_len": {"name": "suspension_reason_len", "value": "char_length(\"country\".\"suspension_reason\") <= 5000"}}, "isRLSEnabled": false}, "public.district": {"name": "district", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "state_id": {"name": "state_id", "type": "uuid", "primaryKey": false, "notNull": true}, "suspended_at": {"name": "suspended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "suspension_reason": {"name": "suspension_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"district_state_id_state_id_fk": {"name": "district_state_id_state_id_fk", "tableFrom": "district", "tableTo": "state", "columnsFrom": ["state_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"district_name_unique": {"name": "district_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {"name_len": {"name": "name_len", "value": "char_length(\"district\".\"name\") <= 100"}, "suspension_reason_len": {"name": "suspension_reason_len", "value": "char_length(\"district\".\"suspension_reason\") <= 5000"}}, "isRLSEnabled": false}, "public.state": {"name": "state", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "country_code": {"name": "country_code", "type": "text", "primaryKey": false, "notNull": true}, "suspended_at": {"name": "suspended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "suspension_reason": {"name": "suspension_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"state_country_code_country_code_fk": {"name": "state_country_code_country_code_fk", "tableFrom": "state", "tableTo": "country", "columnsFrom": ["country_code"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"state_name_unique": {"name": "state_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {"name_len": {"name": "name_len", "value": "char_length(\"state\".\"name\") <= 100"}, "suspension_reason_len": {"name": "suspension_reason_len", "value": "char_length(\"state\".\"suspension_reason\") <= 5000"}}, "isRLSEnabled": false}, "public.user_snapshot": {"name": "user_snapshot", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "snapshot_at": {"name": "snapshot_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {"idx_user_snapshot_user_id": {"name": "idx_user_snapshot_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_snapshot_user_id_usr_id_fk": {"name": "user_snapshot_user_id_usr_id_fk", "tableFrom": "user_snapshot", "tableTo": "usr", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.usr": {"name": "usr", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "google_id": {"name": "google_id", "type": "text", "primaryKey": false, "notNull": true}, "google_refresh_token": {"name": "google_refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "google_picture_url": {"name": "google_picture_url", "type": "text", "primaryKey": false, "notNull": true}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": true}, "tokens_valid_from": {"name": "tokens_valid_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "mobile_country_code": {"name": "mobile_country_code", "type": "text", "primaryKey": false, "notNull": true}, "mobile": {"name": "mobile", "type": "text", "primaryKey": false, "notNull": true}, "mobile_verified": {"name": "mobile_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "legal_age_declared_at": {"name": "legal_age_declared_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "information_accuracy_declared_at": {"name": "information_accuracy_declared_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "tnc_reminder_mailed_at": {"name": "tnc_reminder_mailed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "tnc_reminder_type": {"name": "tnc_reminder_type", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}, "suspended_at": {"name": "suspended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "suspension_reason": {"name": "suspension_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_user_mobile_country_code": {"name": "idx_user_mobile_country_code", "columns": [{"expression": "mobile_country_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_tnc_reminder_scan": {"name": "idx_user_tnc_reminder_scan", "columns": [{"expression": "tnc_reminder_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_google_refresh_token": {"name": "idx_user_google_refresh_token", "columns": [{"expression": "google_refresh_token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"usr_mobile_country_code_country_code_fk": {"name": "usr_mobile_country_code_country_code_fk", "tableFrom": "usr", "tableTo": "country", "columnsFrom": ["mobile_country_code"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"usr_googleId_unique": {"name": "usr_googleId_unique", "nullsNotDistinct": false, "columns": ["google_id"]}}, "policies": {}, "checkConstraints": {"name_len": {"name": "name_len", "value": "char_length(\"usr\".\"name\") <= 255"}, "email_len": {"name": "email_len", "value": "char_length(\"usr\".\"email\") <= 255"}, "google_picture_url_len": {"name": "google_picture_url_len", "value": "char_length(\"usr\".\"google_picture_url\") <= 2000"}, "language_len": {"name": "language_len", "value": "char_length(\"usr\".\"language\") <= 100"}, "mobile_len": {"name": "mobile_len", "value": "char_length(\"usr\".\"mobile\") <= 20"}, "suspension_reason_len": {"name": "suspension_reason_len", "value": "char_length(\"usr\".\"suspension_reason\") <= 5000"}}, "isRLSEnabled": false}, "public.user_tnc_accepted": {"name": "user_tnc_accepted", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tnc_version_id": {"name": "tnc_version_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_user_tnc_accepted_user_id": {"name": "idx_user_tnc_accepted_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_tnc_accepted_tnc_version_id": {"name": "idx_user_tnc_accepted_tnc_version_id", "columns": [{"expression": "tnc_version_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_tnc_accepted_user_tnc_version": {"name": "idx_user_tnc_accepted_user_tnc_version", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "tnc_version_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_tnc_accepted_user_id_usr_id_fk": {"name": "user_tnc_accepted_user_id_usr_id_fk", "tableFrom": "user_tnc_accepted", "tableTo": "usr", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_tnc_accepted_tnc_version_id_user_tnc_version_id_fk": {"name": "user_tnc_accepted_tnc_version_id_user_tnc_version_id_fk", "tableFrom": "user_tnc_accepted", "tableTo": "user_tnc_version", "columnsFrom": ["tnc_version_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_tnc_sign": {"name": "user_tnc_sign", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tnc_version_id": {"name": "tnc_version_id", "type": "uuid", "primaryKey": false, "notNull": true}, "signed_at": {"name": "signed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "accepted": {"name": "accepted", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_user_tnc_sign_user_id": {"name": "idx_user_tnc_sign_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_tnc_sign_tnc_version_id": {"name": "idx_user_tnc_sign_tnc_version_id", "columns": [{"expression": "tnc_version_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_tnc_sign_user_id_usr_id_fk": {"name": "user_tnc_sign_user_id_usr_id_fk", "tableFrom": "user_tnc_sign", "tableTo": "usr", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_tnc_sign_tnc_version_id_user_tnc_version_id_fk": {"name": "user_tnc_sign_tnc_version_id_user_tnc_version_id_fk", "tableFrom": "user_tnc_sign", "tableTo": "user_tnc_version", "columnsFrom": ["tnc_version_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_tnc": {"name": "user_tnc", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "base_url": {"name": "base_url", "type": "text", "primaryKey": false, "notNull": true, "default": "'/legal-agreements/terms-of-service'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_tnc_version": {"name": "user_tnc_version", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "tnc_id": {"name": "tnc_id", "type": "uuid", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "smallint", "primaryKey": false, "notNull": true, "default": 1}, "effective_date": {"name": "effective_date", "type": "date", "primaryKey": false, "notNull": true, "default": "now()"}, "expiry_date": {"name": "expiry_date", "type": "date", "primaryKey": false, "notNull": false}}, "indexes": {"user_tnc_version_tnc_id_idx": {"name": "user_tnc_version_tnc_id_idx", "columns": [{"expression": "tnc_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_tnc_version_active": {"name": "idx_user_tnc_version_active", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "effective_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"user_tnc_version\".\"expiry_date\" IS NULL", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_tnc_version_tnc_id_user_tnc_id_fk": {"name": "user_tnc_version_tnc_id_user_tnc_id_fk", "tableFrom": "user_tnc_version", "tableTo": "user_tnc", "columnsFrom": ["tnc_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.app_command": {"name": "app_command", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "flock": {"name": "flock", "type": "text", "primaryKey": false, "notNull": true}, "command": {"name": "command", "type": "text", "primaryKey": false, "notNull": true}, "log_bindings": {"name": "log_bindings", "type": "jsonb", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}, "first_attempted_at": {"name": "first_attempted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "aborted_at": {"name": "aborted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_app_command_aborted_at": {"name": "idx_app_command_aborted_at", "columns": [{"expression": "aborted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_app_command_flock_id": {"name": "idx_app_command_flock_id", "columns": [{"expression": "flock", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.app_lock": {"name": "app_lock", "schema": "", "columns": {"subject": {"name": "subject", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.mobile_otp": {"name": "mobile_otp", "schema": "", "columns": {"mobile": {"name": "mobile", "type": "text", "primaryKey": true, "notNull": true}, "otp": {"name": "otp", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "try_failed_at": {"name": "try_failed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "otp_try_count": {"name": "otp_try_count", "type": "smallint", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.profile": {"name": "profile", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "descr": {"name": "descr", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "service_provider_id": {"name": "service_provider_id", "type": "uuid", "primaryKey": false, "notNull": false}, "submitted_for_approval_at": {"name": "submitted_for_approval_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "approved_at": {"name": "approved_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "tncs_accepted_at": {"name": "tncs_accepted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "creation_remarks": {"name": "creation_remarks", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "update_remarks": {"name": "update_remarks", "type": "text", "primaryKey": false, "notNull": false}, "suspended_at": {"name": "suspended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "suspension_reason": {"name": "suspension_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_profile_user_id_role": {"name": "idx_profile_user_id_role", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_profile_supporter_id": {"name": "idx_profile_supporter_id", "columns": [{"expression": "service_provider_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"profile_user_id_usr_id_fk": {"name": "profile_user_id_usr_id_fk", "tableFrom": "profile", "tableTo": "usr", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "profile_service_provider_id_profile_id_fk": {"name": "profile_service_provider_id_profile_id_fk", "tableFrom": "profile", "tableTo": "profile", "columnsFrom": ["service_provider_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"chk_display_name_len": {"name": "chk_display_name_len", "value": "char_length(\"profile\".\"display_name\") <= 255"}, "chk_role": {"name": "chk_role", "value": "\"profile\".\"role\" IN ('principal', 'mentor', 'teacher', 'student', 'admin', 'manager', 'executive')"}, "descr_len": {"name": "descr_len", "value": "char_length(\"profile\".\"descr\") <= 10000"}, "suspension_reason_len": {"name": "suspension_reason_len", "value": "char_length(\"profile\".\"suspension_reason\") <= 5000"}}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}
import { Store, useStore } from '@tanstack/react-store'

const diagnosticStore = new Store({
  logs: [] as string[],
})

export const useDiagnosticLogs = () => useStore(diagnosticStore, (state) => state.logs)

export const addDiagnosticLog = (log: string) =>
  diagnosticStore.setState((currentState) => ({ logs: [...currentState.logs, log] }))

export const clearDiagnosticLogs = () => diagnosticStore.setState((_) => ({ logs: [] }))

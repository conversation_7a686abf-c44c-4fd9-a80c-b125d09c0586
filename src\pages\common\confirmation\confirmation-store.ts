import { Store, useStore } from '@tanstack/react-store'

import { Runnable } from '@/shared/common/common-utils.shared'

import { updateStore } from '../utils/updateStore'

type ConfirmationLabels = {
  heading?: string
  message?: string
  cancelLabel?: string
  okLabel?: string
}

export const confirmationLabels: ConfirmationLabels = {
  heading: 'Please confirm',
  message: 'Are you sure you want to proceed?',
  cancelLabel: 'Cancel',
  okLabel: 'Proceed',
}

const confirmationModalStore = new Store({
  visible: false,
})

export const useConfirmationModalVisible = () => useStore(confirmationModalStore, (state) => state.visible)

let handleConfirm: Runnable

export const confirmAndRun = (runnable: Runnable, labels: ConfirmationLabels = {}) => {
  handleConfirm = runnable
  if (labels.heading) confirmationLabels.heading = labels.heading
  if (labels.message) confirmationLabels.message = labels.message
  if (labels.cancelLabel) confirmationLabels.cancelLabel = labels.cancelLabel
  if (labels.okLabel) confirmationLabels.okLabel = labels.okLabel
  updateStore(confirmationModalStore, { visible: true })
}

export const confirmed = () => {
  updateStore(confirmationModalStore, { visible: false })
  void handleConfirm()
}

export const canceled = () => {
  updateStore(confirmationModalStore, { visible: false })
  handleConfirm = () => undefined
}

import { UUID } from 'crypto'

import { LockClosedIcon, LockOpenIcon, PaperClipIcon } from '@heroicons/react/20/solid'
import clsx from 'clsx'
import { DateTime } from 'luxon'
import { useMemo, useState } from 'react'
import { clientOnly } from 'vike-react/clientOnly'
import { Config } from 'vike-react/Config'
import { Head } from 'vike-react/Head'

import { MyBatchStudent } from '@/server/batches/batch-students/my/getMyBatchStudent'
import { BatchAttachment } from '@/server/batches/batches/attachments/list/listBatchAttachments'
import { Batch, BatchEvent } from '@/server/batches/batches/get/getBatch.server'
import { CourseWithoutDescr } from '@/server/courses/courses/get/getCourse.server'
import { ProfileContact } from '@/server/profiles/get-contact/getProfileContact'
import { getNextPaymentDueOn } from '@/shared/batch/batch-due-utils'
import { billingCycles } from '@/shared/batch/batch-utils.shared'
import { formatDate, today } from '@/shared/common/date-utils.shared'
import { coursePath } from '@/shared/course/course-utils.shared'
import { profilePath } from '@/shared/profiles/profile-utils.shared'
import { useAcademySuspenseQuery, useMyAcademyQuery } from '@ui/academies/common/academy-queries'
import { ContactPara } from '@ui/common/contact/ContactPara'
import { ShowData } from '@ui/common/ShowData'
import { CourseAttachmentBlock } from '@ui/courses/common/CourseAttachmentBlock'
import { useCourseSuspenseQuery } from '@ui/courses/common/queries/course-queries'
import { usePhoneNumber } from '@ui/masters/common/usePhoneNumber'
import { useCurrentProfile } from '@ui/profiles/common/current-profile-store'
import { useProfileContactQuery, useProfilesSuspenseQuery } from '@ui/profiles/common/profile-queries'

import {
  useAddBatchAttachmentMutation,
  useBatchAttachmentsSuspenseQuery,
  useRemoveBatchAttachmentMutation,
} from '../common/batch-attachment-queries'
import { useBatchSuspenseQuery } from '../common/batch-queries'
import { useMyBatchStudentQuery } from '../common/batch-student-queries'
import { BatchEvents } from '../common/BatchEvents'
import { BatchHeader } from '../common/BatchHeader'
import { RecommendBatchButton } from '../common/RecommendBatchButton'
import { useAddStudentFeePaymentMutation } from '../common/student-fee-payment-queries'

const BatchActions = clientOnly(async () => (await import('./BatchActions')).BatchActions)
const AddBatchEvent = clientOnly(async () => (await import('./events/AddBatchEvent')).AddBatchEvent)
const EditBatchEvent = clientOnly(async () => (await import('./events/EditBatchEvent')).EditBatchEvent)
const CopyStudentsEmailsButton = clientOnly(
  async () => (await import('@ui/courses/@id/batches/common/CopyStudentsEmailsButton')).CopyStudentsEmailsButton,
)

export const BatchPage = ({ courseId, batchId }: { courseId: UUID; batchId: UUID }) => {
  const { data: course, error: courseError } = useCourseSuspenseQuery(courseId)
  const { data: batch, error: batchError } = useBatchSuspenseQuery(batchId)

  return (
    <ShowData error={courseError || batchError} spinnerSize="1.25rem">
      <ViewBatch course={course!} batch={batch!} />
    </ShowData>
  )
}

const ViewBatch = ({ course, batch }: { course: CourseWithoutDescr; batch: Batch }) => {
  const { data: academy } = useAcademySuspenseQuery(course.academyId, false)

  const { data: teacherData } = useProfilesSuspenseQuery({ profileId: batch.teacherId })
  const teacher = teacherData?.[0]
  const [addingEvent, setAddingEvent] = useState(batch.events.length === 0)
  const [editingEvent, setEditingEvent] = useState<BatchEvent | null>(null)

  const { currentProfile } = useCurrentProfile()
  const { data: myAcademy } = useMyAcademyQuery()
  const isStaff = myAcademy?.id === course.academyId
  const isStudent = currentProfile?.role === 'student'

  const { data: batchStudent } = useMyBatchStudentQuery(batch.id)
  const isStudentOfBatch = isStudent && batchStudent && !batchStudent.leftAt
  const nextDueOn = useMemo(
    () => (isStudentOfBatch ? getNextPaymentDueOn(batch, batchStudent) : null),
    [batchStudent, batch, isStudentOfBatch],
  )

  return (
    <div>
      <Config title={`${course.name} - Batch ${batch.id}`} />
      <Head>
        <title>{`${course.name} - Batch ${batch.id}`}</title>
      </Head>
      <div className="flex flex-col sm:flex-row items-start justify-between">
        <BatchHeader course={course} batch={batch} h1="Batch Details" />
        <BatchActions
          course={course}
          batch={batch}
          isStaff={isStaff}
          isStudent={isStudent}
          batchStudent={batchStudent}
        />
      </div>
      {isStaff && (
        <div className="mt-2">
          <CopyStudentsEmailsButton batchId={batch.id} />
        </div>
      )}
      <div className="mt-6">
        <dl className="grid grid-cols-1 sm:grid-cols-2">
          <div className="border-t border-gray-100 px-4 py-6 sm:col-span-1 sm:px-0">
            <dt className="text-sm/6 font-medium text-gray-900">Teacher</dt>
            <dd className="mt-1 text-sm/6 text-gray-700 sm:mt-2">
              {teacher ?
                <div className="flex items-center gap-4">
                  <div className="size-9 shrink-0">
                    <img alt="Teacher picture" src={teacher.googlePictureUrl} className="size-9 rounded-full" />
                  </div>
                  <a href={profilePath(teacher)}>{teacher?.displayName}</a>
                </div>
              : 'Not assigned yet'}
            </dd>
          </div>
          <div className="border-t border-gray-100 px-4 py-6 sm:col-span-1 sm:px-0">
            <dt className="text-sm/6 font-medium text-gray-900">Admission</dt>
            <dd className="mt-1 text-sm/6 text-gray-700 sm:mt-2">
              {batch.admissionOpen ?
                <div className="text-green-700 flex items-center gap-2">
                  <LockOpenIcon className="size-5 shrink-0 text-green-400" />
                  Open
                  {batch.recommended && (
                    <span className="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-green-600/20 ring-inset">
                      Recommended
                    </span>
                  )}
                  {isStaff && !batch.recommended && <RecommendBatchButton batchId={batch.id} courseId={course.id} />}
                </div>
              : <div className="text-red-700 flex items-center gap-2">
                  <LockClosedIcon className="size-5 shrink-0 text-red-400" /> Closed
                </div>
              }
            </dd>
          </div>
          <div className="border-t border-gray-100 px-4 py-6 sm:col-span-1 sm:px-0">
            <dt className="text-sm/6 font-medium text-gray-900">
              {batch.startDate < today() ? 'Started' : 'Starts'} on
            </dt>
            <dd className="mt-1 text-sm/6 text-gray-700 sm:mt-2">{formatDate(batch.startDate)}</dd>
          </div>
          <div className="border-t border-gray-100 px-4 py-6 sm:col-span-1 sm:px-0">
            <dt className="text-sm/6 font-medium text-gray-900">Runs for</dt>
            <dd className="mt-1 text-sm/6 text-gray-700 sm:mt-2">
              {batch.cycleCount} {batch.billingCycle}
            </dd>
          </div>
          <div className="border-t border-gray-100 px-4 py-6 sm:col-span-1 sm:px-0">
            <dt className="text-sm/6 font-medium text-gray-900">
              Fee per {billingCycles[batch.billingCycle].singular}
            </dt>
            <dd className="mt-1 text-sm/6 text-gray-700 sm:mt-2">
              <p>
                {academy?.currency} {batch.fee}
              </p>
              {nextDueOn && <NextDueInfo batchId={batch.id} batchStudent={batchStudent!} nextDueOn={nextDueOn} />}
            </dd>
          </div>
          <div className="border-t border-gray-100 px-4 py-6 sm:col-span-1 sm:px-0">
            <dt className="text-sm/6 font-medium text-gray-900">Grace days</dt>
            <dd className="mt-1 text-sm/6 text-gray-700 sm:mt-2">
              <p>{batch.graceDays}</p>
              <p className="form-descr">Days a student can delay paying fee before getting restricted.</p>
            </dd>
          </div>
          <div className="border-t border-gray-100 px-4 py-6 sm:col-span-1 sm:px-0">
            <dt className="text-sm/6 font-medium text-gray-900">Seats</dt>
            <dd className="mt-1 text-sm/6 text-gray-700 sm:mt-2">{batch.seatCount}</dd>
          </div>
          <div className="border-t border-gray-100 px-4 py-6 sm:col-span-1 sm:px-0">
            <dt className="text-sm/6 font-medium text-gray-900">Schedule</dt>
            <dd className="mt-1 text-sm/6 text-gray-700 sm:mt-2">
              <div>
                <div>
                  {batch.events.length > 0 ?
                    <BatchEvents
                      batchId={batch.id}
                      events={batch.events}
                      setEditingEvent={setEditingEvent}
                      showActions={isStaff}
                    />
                  : 'No schedule set yet'}
                </div>
                {isStaff && (
                  <div>
                    <button
                      className="text-xs link underline mt-3"
                      onClick={() => {
                        setAddingEvent(true)
                      }}
                    >
                      Add a Schedule
                    </button>
                  </div>
                )}
              </div>
            </dd>
          </div>
          <div className="border-t border-gray-100 px-4 py-6 sm:col-span-1 sm:px-0">
            <dt className="text-sm/6 font-medium text-gray-900">Timezone</dt>
            <dd className="mt-1 text-sm/6 text-gray-700 sm:mt-2">{batch.timezone}</dd>
          </div>
        </dl>
        <ViewAttachments batchId={batch.id} isStaff={isStaff} currentProfileId={currentProfile?.id} />
        {isStudentOfBatch && <TeacherContact teacherId={batch.teacherId} />}
      </div>
      <div className="text-center text-sm text-gray-200 mt-5">
        <a href={`${coursePath(course)}/batches`} className="link">
          View All Batches
        </a>{' '}
        |{' '}
        <a href={`${coursePath(course)}`} className="link">
          View Course
        </a>
      </div>
      {isStaff && (
        <>
          <AddBatchEventIfDesired batchId={batch.id} addingEvent={addingEvent} setAddingEvent={setAddingEvent} />
          <EditBatchEventIfDesired batchId={batch.id} event={editingEvent} setEvent={setEditingEvent} />
        </>
      )}
    </div>
  )
}

const NextDueInfo = ({
  batchId,
  batchStudent,
  nextDueOn,
}: {
  batchId: UUID
  batchStudent: MyBatchStudent
  nextDueOn: DateTime
}) => {
  const { mutate: addBatchStudentPaymentItem } = useAddStudentFeePaymentMutation(batchId)
  return (
    <>
      <p className="form-descr">Next due on {nextDueOn.toLocaleString(DateTime.DATE_FULL)}</p>
      <button
        className="text-xs link underline"
        onClick={() => {
          addBatchStudentPaymentItem({
            cycle: batchStudent.paidTillCycle + 1,
          })
        }}
      >
        Add to cart
      </button>
    </>
  )
}

const TeacherContact = ({ teacherId }: { teacherId: UUID }) => {
  const { data: teacherContact, isPending, error } = useProfileContactQuery(teacherId)
  return (
    <div className="mt-6">
      <h2 className="text-base font-semibold leading-7 text-gray-900">Contact Teacher</h2>
      <ShowData isPending={isPending} error={error} spinnerSize="1.25rem">
        {teacherContact && <TeacherContactPara teacherContact={teacherContact} />}
      </ShowData>
    </div>
  )
}

const TeacherContactPara = ({ teacherContact }: { teacherContact: ProfileContact }) => {
  const { fullNumber } = usePhoneNumber(teacherContact.mobileCountryCode, teacherContact.mobile)
  return <ContactPara email={teacherContact.email} mobile={fullNumber} />
}

const AddBatchEventIfDesired = ({
  batchId,
  addingEvent,
  setAddingEvent,
}: {
  batchId: UUID
  addingEvent: boolean
  setAddingEvent: (addingEvent: boolean) => void
}) => {
  if (!addingEvent) return null
  return <AddBatchEvent batchId={batchId} open={true} setOpen={setAddingEvent} />
}

const EditBatchEventIfDesired = ({
  batchId,
  event,
  setEvent,
}: {
  batchId: UUID
  event: BatchEvent | null
  setEvent: (event: BatchEvent | null) => void
}) => {
  const setOpen = (open: boolean) => {
    if (open) {
      throw new Error('Unsupported operation')
    }
    setEvent(null)
  }
  if (!event) return null
  return <EditBatchEvent batchId={batchId} event={event} open={true} setOpen={setOpen} />
}

const ViewAttachments = ({
  batchId,
  isStaff,
  currentProfileId,
}: {
  batchId: UUID
  isStaff: boolean
  currentProfileId: UUID | undefined
}) => {
  const { data, error: attachmentsError } = useBatchAttachmentsSuspenseQuery(batchId, currentProfileId)
  const attachments = data as BatchAttachment[]

  return (
    <ShowData error={attachmentsError} spinnerSize="1.25rem">
      {attachments && (
        <div className="mt-8 border-t border-gray-100 pt-6">
          <div className="mb-4">
            <h2 className="text-base font-semibold leading-7 text-gray-900">Resources</h2>
          </div>
          {attachments?.length > 0 ?
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <ul className="divide-y divide-gray-200">
                {attachments.map((attachment) => (
                  <li
                    key={attachment.id}
                    className="flex items-center p-4 transition-colors duration-150 hover:bg-gray-50"
                  >
                    <CourseAttachmentBlock
                      attachment={attachment}
                      grayedOut={attachment.hidden}
                      actions={
                        isStaff && (
                          <ShowHideButton batchId={batchId} attachmentId={attachment.id} hidden={attachment.hidden} />
                        )
                      }
                    />
                  </li>
                ))}
              </ul>
            </div>
          : <div className="py-6 text-center bg-gray-50 rounded-lg border border-gray-200">
              <PaperClipIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">No resources</h3>
              <p className="mt-1 text-sm text-gray-500">No course resources have been shared yet.</p>
            </div>
          }
        </div>
      )}
    </ShowData>
  )
}

const ShowHideButton = ({ batchId, attachmentId, hidden }: { batchId: UUID; attachmentId: UUID; hidden: boolean }) => {
  const { mutate: add, isPending: addIsPending } = useAddBatchAttachmentMutation(batchId)
  const { mutate: remove, isPending: removeIsPending } = useRemoveBatchAttachmentMutation(batchId)
  const isPending = addIsPending || removeIsPending
  const handleShowHide = () => (hidden ? add({ attachmentId }) : remove(attachmentId))
  return (
    <button
      className={clsx('link underline', isPending && 'cursor-wait')}
      disabled={isPending}
      onClick={handleShowHide}
    >
      {hidden ? 'Show' : 'Hide'}
    </button>
  )
}

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import pino from 'pino'

import { ensureCanUpdateBatch } from '@/server/batches/batches/common/ensureCanUpdateBatch'
import { Ctx } from '@/server/common/auth-context-types.server'
// import { appendCommandDaf } from '@/server/common/command/processCommands'
import { ensureExists } from '@/server/common/error/ensureExists'
// import { calendarFlock } from '@/server/common/google/calendar/calendar-utils'
import { db, Transaction } from '@/server/db/db'
import { batchEventTable } from '@/server/db/schema/batch-schema'
import { EditBatchEventForm } from '@/shared/batch/batch-utils.shared'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'

// import {
//   UPDATE_GOOGLE_EVENT_FOR_BATCH_COMMAND,
//   UpdateGoogleEventForBatchEventCommandData,
// } from './updateGoogleEventForBatchEventCommand'

const findBatchEventDaf = (log: pino.Logger, eventId: UUID) =>
  withLog(log, 'findBatchEventDaf', () =>
    db.query.batchEventTable.findFirst({
      columns: {
        batchId: true,
      },
      with: {
        batch: {
          with: {
            teacher: {
              with: {
                user: {
                  columns: {
                    id: true,
                  },
                },
              },
            },
          },
        },
      },
      where: eq(batchEventTable.id, eventId),
    }),
  )

const updateBatchEventDaf = (log: pino.Logger, db: Transaction, eventId: UUID, form: EditBatchEventForm) =>
  withLog(log, 'updateBatchEventDaf', () =>
    db
      .update(batchEventTable)
      .set({
        at: form.at,
        durationMinutes: form.durationMinutes,
        days: form.days,
        updatedAt: utc().toJSDate(),
      })
      .where(eq(batchEventTable.id, eventId)),
  )

export const updateBatchEvent = async (c: Ctx, eventId: UUID, form: EditBatchEventForm) => {
  // Find the event to get its batch ID
  const event = await findBatchEventDaf(c.log, eventId)
  ensureExists(event, eventId, 'Batch event')

  // Ensure the user has permission to update the batch
  await ensureCanUpdateBatch(c, event.batchId)

  // Update the batch event in the database
  await db.transaction(async (tx) => {
    const result = await updateBatchEventDaf(c.log, tx, eventId, form)
    ensureExists(result.count === 1, eventId, 'Batch event')
    // await appendCommandDaf<UpdateGoogleEventForBatchEventCommandData>(
    //   tx,
    //   c.log,
    //   calendarFlock(event.batch.teacher.user.id),
    //   {
    //     command: UPDATE_GOOGLE_EVENT_FOR_BATCH_COMMAND,
    //     data: {
    //       eventId,
    //       teacherUserId: event.batch.teacher.user.id,
    //     },
    //   },
    // )
  })
}

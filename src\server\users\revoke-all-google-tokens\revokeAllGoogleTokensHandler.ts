import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { ensureProfileWithRole } from '@/server/common/error/ensure-profile.server'
import { ensureUser } from '@/server/common/error/ensureUser.server'
import { HonoVars } from '@/server/common/hono-utils.server'

import { revokeAllGoogleTokens } from './revokeAllGoogleTokens'

export const revokeAllGoogleTokensHandler = new Hono<HonoVars>().post('/', async (c) => {
  const ctx = getCtx(c)
  ensureUser(ctx.user)
  ensureProfileWithRole('admin', ctx.profile)
  void revokeAllGoogleTokens(ctx.log)
  return c.body(null, 202)
})

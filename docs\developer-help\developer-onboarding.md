# Developer Onboarding Guide

Welcome to the NP Tuition VR project! This guide will help you get started as a new developer. It provides a step-by-step approach to understanding our tech stack and making your first contribution.

## Prerequisites

Before you begin, ensure you have the following installed on your Windows machine:

- [Node.js](https://nodejs.org/) (LTS version recommended)
- [Git](https://git-scm.com/download/win)
- [pnpm](https://pnpm.io/installation) (our package manager)
- [Visual Studio Code](https://code.visualstudio.com/) (recommended editor)
- [PostgreSQL](https://www.postgresql.org/download/windows/) (our database)
- [Docker Desktop](https://www.docker.com/products/docker-desktop/) (optional, for local development and running MCP servers)

## Getting Started

1. **Clone the repository**
   ```powershell
   git clone https://github.com/naturalprogrammer/np-tuition-vr.git
   cd np-tuition-vr
   ```

2. **Install dependencies**
   ```powershell
   pnpm install
   ```

3. **Set up your environment**
   - Create a `.env` file in the root directory (ask a team member for the required values)
   - Configure your local PostgreSQL database

4. **Start the development server**
   ```powershell
   pnpm dev
   ```

## Learning Path

As a new developer, focus on learning these technologies in the following order:

### 1. Frontend Basics
- [HTML](https://developer.mozilla.org/en-US/docs/Learn/HTML)
- [CSS](https://developer.mozilla.org/en-US/docs/Learn/CSS)
- [JavaScript](https://developer.mozilla.org/en-US/docs/Learn/JavaScript)

### 2. Frontend Framework
- [React](https://react.dev/learn) - Our UI library
- [TypeScript](https://www.typescriptlang.org/docs/handbook/intro.html) - Strongly typed JavaScript
- [TailwindCSS](https://tailwindcss.com/docs) - Our CSS framework

### 3. Backend Technologies
- [Hono](https://hono.dev/) - Backend API framework
- [Drizzle ORM](https://orm.drizzle.team/docs/overview) - Database ORM
- [PostgreSQL](https://www.postgresql.org/docs/current/tutorial.html) - SQL database

### 4. Project-Specific Tools
- [Vike](https://vike.dev) - Our page router
- [React Query](https://tanstack.com/query/latest/docs/framework/react/overview) - Data fetching library
- [Zod](https://zod.dev/) - TypeScript schema validation

## Project Structure

Our codebase is organized as follows:

- `src/pages/` - Frontend pages and components
- `src/server/` - Backend API routes and server logic
- `src/shared/` - Shared code between frontend and backend

## Making Your First Contribution

1. **Read our Git workflow documentation**
   - See [Git Workflow](./git-workflow.md) for details on our branching strategy

2. **Start with small tasks**
   - Look for issues labeled "good first issue" in our issue tracker
   - Ask a mentor to guide you through your first task

3. **Follow the development workflow**
   - Create a feature branch for your changes
   - Make small, focused commits
   - Write tests for your code
   - Submit a pull request for review

## Learning Resources

### Frontend Development
- [MDN Web Docs](https://developer.mozilla.org/)
- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/handbook/intro.html)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

### Backend Development
- [Hono Documentation](https://hono.dev/)
- [Drizzle ORM Documentation](https://orm.drizzle.team/docs/overview)
- [PostgreSQL Tutorial](https://www.postgresqltutorial.com/)

### Git and GitHub
- [Git Basics](https://git-scm.com/book/en/v2/Getting-Started-Git-Basics)
- [GitHub Pull Request Tutorial](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests)

## Getting Help

- Ask questions in our team chat
- Reach out to your assigned mentor
- Attend our weekly developer meetings
- Document what you learn to help future team members

Remember, everyone on the team was once a beginner. Don't hesitate to ask questions and seek guidance when needed.

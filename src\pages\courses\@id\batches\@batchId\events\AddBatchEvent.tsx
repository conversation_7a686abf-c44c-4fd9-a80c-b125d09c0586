import { UUID } from 'crypto'

import { useEffect, useRef, useState } from 'react'

import { $AddBatchEventForm, AddBatchEventForm, EditBatchEventForm, EventType } from '@/shared/batch/batch-utils.shared'
import { OmniError } from '@/shared/common/error-utils.shared'

import { useAddBatchEventMutation } from '../../common/batch-queries'

import { BatchEventEditor } from './BatchEventEditor'

export const AddBatchEvent = ({
  batchId,
  open,
  setOpen,
}: {
  batchId: UUID
  open: boolean
  setOpen: (open: boolean) => void
}) => {
  const id = useRef(crypto.randomUUID())

  const [formData, setFormData] = useState<Omit<AddBatchEventForm, 'id' | 'batchId'>>({
    days: ['MO', 'TU', 'WE', 'TH', 'FR'],
    at: '19:00',
    durationMinutes: 60,
    eventType: 'meet',
  })

  // Error state
  const [omniError, setOmniError] = useState<OmniError | undefined>()

  // Add batch event mutation
  const { mutate: addBatchEvent, isPending } = useAddBatchEventMutation()

  // Validate form data when it changes
  useEffect(() => {
    const parsed = $AddBatchEventForm.safeParse({ ...formData, batchId, id: id.current })
    setOmniError(parsed.error)
  }, [formData])

  // Form submission handler
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    addBatchEvent(
      { ...formData, batchId, id: id.current },
      {
        onSuccess: () => {
          setOpen(false)
        },
        onError: (error) => {
          setOmniError(error.error)
        },
      },
    )
  }

  return (
    <BatchEventEditor
      formData={formData}
      setFormData={setFormData as (form: EditBatchEventForm) => void}
      error={omniError}
      onSubmit={handleSubmit}
      isPending={isPending}
      open={open}
      setOpen={setOpen}
    >
      <div>
        <fieldset>
          <legend className="text-sm/6 font-semibold text-gray-900">Meeting type</legend>
          <div className="mt-6 space-y-6">
            <div className="flex items-center">
              <input
                value="meet"
                checked={formData.eventType === 'meet'}
                onChange={(e) => setFormData({ ...formData, eventType: e.target.value as EventType })}
                id="meet"
                name="eventType"
                type="radio"
                className="relative size-4 appearance-none rounded-full border border-gray-300 bg-white before:absolute before:inset-1 before:rounded-full before:bg-white not-checked:before:hidden checked:border-indigo-600 checked:bg-indigo-600 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:before:bg-gray-400 forced-colors:appearance-auto forced-colors:before:hidden"
              />
              <label htmlFor="meet" className="ml-3 block text-sm/6 font-medium text-gray-900">
                Google Meet
              </label>
            </div>
          </div>
        </fieldset>
      </div>
    </BatchEventEditor>
  )
}

import markdownit from 'markdown-it'
import TurndownService from 'turndown'

import { sanitizedHtml } from './html-utils.shared'

// Configure markdown-it with safe defaults
const md = markdownit({
  html: false, // Disable raw HTML in markdown for security
  breaks: true, // Convert '\n' to <br>
  linkify: false, // Auto-convert URL-like text to links
  typographer: true, // Enable smart quotes and other typographic replacements
})

// Configure Turndown with options matching our allowed markdown features
const turndownService = new TurndownService({
  headingStyle: 'atx', // Use # style headings
  hr: '---', // Use --- for horizontal rules
  bulletListMarker: '-', // Use - for bullet lists
  codeBlockStyle: 'fenced', // Use ``` for code blocks
  emDelimiter: '_', // Use _ for emphasis
  strongDelimiter: '**', // Use ** for strong
})

// Sanitize the HTML after markdown rendering
export const markdown2Html = (markdown: string) => {
  // First convert markdown to HTML
  const html = md.render(markdown)
  return sanitizedHtml(html)
}

// Validate and clean markdown by converting to sanitized HTML and back to markdown
export const sanitizedMarkdown = (markdown: string) => {
  // First convert to sanitized HTML using our secure pipeline
  const sanitizedHtml = markdown2Html(markdown.trim())

  // Then convert back to clean markdown
  return html2Markdown(sanitizedHtml)
}

export const html2Markdown = (html: string) => {
  return turndownService.turndown(html)
}

import { expect } from 'vitest'

import { ErrorPayload, MyIssue } from '@/shared/common/error-utils.shared'

interface CustomMatchers<R = unknown> {
  toBeFoo: (a: string, b: string) => R
  toBeError: (status: number, issues?: MyIssue[]) => R
  toEventuallyResolve: (options?: { timeout?: number; interval?: number; timeoutMessage?: string }) => Promise<R>
}

declare module 'vitest' {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any,@typescript-eslint/no-empty-object-type
  interface Assertion<T = any> extends CustomMatchers<T> {}

  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  interface AsymmetricMatchersContaining extends CustomMatchers {}
}

expect.extend({
  toBeFoo(received, a: string, b: string) {
    const { isNot } = this
    return {
      // do not alter your "pass" based on isNot. Vitest does it for you
      pass: received === a + b,
      message: () => `${received} is${isNot ? ' not' : ''} foo`,
    }
  },
  async toBeError(response: Response, status: number, issues?: MyIssue[]) {
    const { isNot, equals } = this
    const body = (await response.json()) as ErrorPayload

    // Check response status
    const statusMatches = response.status === status

    // Check body status if it exists
    const bodyStatusMatches = !('status' in body) || body.status === status

    // Check issues if provided
    let issuesMatch = true
    let issuesMessage = ''

    if (issues) {
      const actualIssues =
        body.error?.issues.map((issue) => ({
          code: issue.code,
          message: issue.message,
          path: issue.path,
        })) || []

      const issuesDefined = Array.isArray(actualIssues) && actualIssues.length > 0
      const lengthMatches = actualIssues.length === issues.length

      if (!issuesDefined) {
        issuesMatch = false
        issuesMessage = 'Issues are not defined in the response'
      } else if (!lengthMatches) {
        issuesMatch = false
        issuesMessage = `Expected ${issues.length} issues but got ${actualIssues.length}`
      } else {
        // Check if all expected issues exist in actual issues
        const missingIssues = issues.filter(
          (expectedIssue) =>
            !actualIssues.some((actualIssue) =>
              equals(
                { code: actualIssue.code, message: actualIssue.message, path: actualIssue.path },
                { code: expectedIssue.code, message: expectedIssue.message, path: expectedIssue.path },
              ),
            ),
        )

        if (missingIssues.length > 0) {
          issuesMatch = false
          issuesMessage = `Missing expected issues: ${JSON.stringify(missingIssues)}`
        }
      }
    }

    // All conditions must pass
    const pass = statusMatches && bodyStatusMatches && issuesMatch

    return {
      pass,
      message: () => {
        const parts = []

        if (!statusMatches) {
          parts.push(`Expected response status to be ${status} but got ${response.status}`)
        }

        if (!bodyStatusMatches) {
          parts.push(`Expected body status to be ${status} but got ${body.status}`)
        }

        if (!issuesMatch && issuesMessage) {
          parts.push(issuesMessage)
        }

        if (parts.length === 0) {
          return `Expected response ${isNot ? 'not ' : ''}to be an error with status ${status}`
        }

        return parts.join('\n')
      },
      actual: {
        status: response.status,
        bodyStatus: 'status' in body ? body.status : undefined,
        issues: body.error?.issues,
      },
      expected: {
        status,
        bodyStatus: status,
        issues,
      },
    }
  },
  async toEventuallyResolve(
    received: () => Promise<boolean> | boolean,
    options: { timeout?: number; interval?: number; timeoutMessage?: string } = {},
  ) {
    const { isNot } = this
    const { timeout = 5000, interval = 100, timeoutMessage = 'Timed out waiting for condition' } = options

    const startTime = Date.now()
    let pass = false
    let lastError: Error | null = null

    while (Date.now() - startTime < timeout) {
      try {
        if (await received()) {
          pass = true
          break
        }
      } catch (e) {
        // Catch errors from the condition function, store them but continue polling
        lastError = e instanceof Error ? e : new Error(String(e))
      }
      await new Promise((resolve) => setTimeout(resolve, interval))
    }

    return {
      pass,
      message: () => {
        if (pass) {
          return `Expected condition ${isNot ? 'not ' : ''}to eventually resolve, but it did.`
        }
        let msg = timeoutMessage
        if (lastError) {
          msg += `
Last error: ${lastError.message}`
        }
        return msg
      },
    }
  },
})

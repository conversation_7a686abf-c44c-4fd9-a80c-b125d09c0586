import { PageContext } from 'vike/types'

import { ErrorPayload } from '@/shared/common/error-utils.shared'

// eslint-disable-next-line import/no-default-export
export default (_pageContext: PageContext) => ({
  defaultOptions: {
    queries: {
      staleTime: Infinity,
      gcTime: Infinity,
      retry: (failureCount: number, error: ErrorPayload) =>
        error.status < 500 ?
          false // 4xx errors shouldn't be retried
        : failureCount <= 3, // 5xx should be retried 3 times
    },
  },
})

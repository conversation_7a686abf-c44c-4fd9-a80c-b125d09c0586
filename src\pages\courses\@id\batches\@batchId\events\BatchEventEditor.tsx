import { Dialog, DialogPanel, DialogTitle } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/outline'

import { EditBatchEventForm } from '@/shared/batch/batch-utils.shared'
import { WeekDay, weekDays } from '@/shared/common/date-utils.shared'
import { OmniError } from '@/shared/common/error-utils.shared'
import { CheckBox } from '@ui/common/form/CheckBox'
import { ShowErrors } from '@ui/common/ShowErrors'
export const BatchEventEditor = ({
  formData,
  setFormData,
  error,
  onSubmit,
  isPending,
  children,
  open,
  setOpen,
}: {
  formData: EditBatchEventForm
  setFormData: (form: EditBatchEventForm) => void
  error: OmniError | undefined
  onSubmit: (e: React.FormEvent) => void
  isPending: boolean
  children?: React.ReactNode
  open: boolean
  setOpen: (open: boolean) => void
}) => {
  const close = () => setOpen(false)
  return (
    <Dialog open={open} onClose={close} className="relative z-10">
      <div className="fixed inset-0" />

      <div className="fixed inset-0 overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10 sm:pl-16">
            <DialogPanel
              transition
              className="pointer-events-auto w-screen max-w-md transform transition duration-500 ease-in-out data-closed:translate-x-full sm:duration-700"
            >
              <form onSubmit={onSubmit} className="flex h-full flex-col divide-y divide-gray-200 bg-white shadow-xl">
                <div className="h-0 flex-1 overflow-y-auto">
                  <div className="bg-indigo-700 px-4 py-6 sm:px-6">
                    <div className="flex items-center justify-between">
                      <DialogTitle className="text-base font-semibold text-white">Schedule</DialogTitle>
                      <div className="ml-3 flex h-7 items-center">
                        <button
                          type="button"
                          onClick={close}
                          className="relative rounded-md bg-indigo-700 text-indigo-200 hover:text-white focus:ring-2 focus:ring-white focus:outline-hidden"
                        >
                          <span className="absolute -inset-2.5" />
                          <span className="sr-only">Close panel</span>
                          <XMarkIcon aria-hidden="true" className="size-6" />
                        </button>
                      </div>
                    </div>
                    <div className="mt-1">
                      <p className="text-sm text-indigo-300">Enter session schedule for the batch.</p>
                    </div>
                  </div>
                  <div className="flex flex-1 flex-col justify-between">
                    <div className="divide-y divide-gray-200 px-4 sm:px-6">
                      <div className="space-y-6 pt-6 pb-5">
                        <div>
                          <label htmlFor="at" className="block text-sm/6 font-medium text-gray-900">
                            At (00:00 to 23:59)
                          </label>
                          <div className="mt-2">
                            <input
                              id="at"
                              name="at"
                              type="time"
                              value={formData.at}
                              onChange={(e) => setFormData({ ...formData, at: e.target.value })}
                              className="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                            />
                          </div>
                          <ShowErrors error={error} path={['at']} />
                        </div>
                        <div>
                          <label htmlFor="durationMinutes" className="block text-sm/6 font-medium text-gray-900">
                            Duration (in minutes)
                          </label>
                          <div className="mt-2">
                            <input
                              id="durationMinutes"
                              name="durationMinutes"
                              type="number"
                              value={formData.durationMinutes}
                              onChange={(e) => setFormData({ ...formData, durationMinutes: parseInt(e.target.value) })}
                              className="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                            />
                          </div>
                          <ShowErrors error={error} path={['durationMinutes']} />
                        </div>
                        <fieldset>
                          <legend className="text-sm/6 font-medium text-gray-900">Runs on</legend>
                          <div className="mt-2 space-y-4">
                            {Object.entries(weekDays).map(([day, { name }]) => (
                              <div key={day}>
                                <CheckBox
                                  id={day}
                                  label={name}
                                  checked={formData.days.includes(day as WeekDay)}
                                  onChange={(e) =>
                                    setFormData({
                                      ...formData,
                                      days:
                                        e.target.checked ?
                                          [...formData.days, day as WeekDay]
                                        : formData.days.filter((d) => d !== (day as WeekDay)),
                                    })
                                  }
                                />
                              </div>
                            ))}
                          </div>
                          <ShowErrors error={error} path={['days']} />
                        </fieldset>
                        {children}
                      </div>
                    </div>
                  </div>
                </div>
                <ShowErrors error={error} />
                <div className="flex shrink-0 justify-end px-4 py-4">
                  <button
                    type="button"
                    onClick={close}
                    className="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={!!error || isPending}
                    className="ml-4 inline-flex justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  >
                    Save
                  </button>
                </div>
              </form>
            </DialogPanel>
          </div>
        </div>
      </div>
    </Dialog>
  )
}

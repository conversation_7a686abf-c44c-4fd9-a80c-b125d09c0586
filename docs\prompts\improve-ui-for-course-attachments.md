In `src\pages\courses\@id\CoursePage.tsx`, for managing attachments, we have `UploadAttachmentForm` and `ViewAttachments`. These display the list of atttachments to all users, and let academy staff upload attachments.

I've coded the functionality, but the UI is rough and basic. Please design a professional UI, keeping in mind the existing color scheme and templates we are using in the project. Additionally, an "Edit" link for each attachment. It'd eventually point to an edit page, but leave the url blank for now.

The table should be fully visible even on small screens, and so, I think to prefer flex over tables.

Don't touch other parts of the UI except the form and table mentioned above. Also, don't code any tests.

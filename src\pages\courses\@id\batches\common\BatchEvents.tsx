import { UUID } from 'crypto'

import { BatchEvent } from '@/server/batches/batches/get/getBatch.server'
import { formatTime, weekDays } from '@/shared/common/date-utils.shared'
import { showNotification } from '@ui/common/notification/notification-store'
import { getErrorMessage } from '@ui/common/utils/error-utils.ui'

import { useRemoveBatchEventMutation } from './batch-queries'

export const BatchEvents = ({
  batchId,
  events,
  setEditingEvent,
  showActions,
}: {
  batchId: UUID
  events: BatchEvent[]
  setEditingEvent: (event: BatchEvent) => void
  showActions: boolean
}) => {
  const { mutate: removeEvent, isPending } = useRemoveBatchEventMutation(batchId)

  const handleRemoveEvent = (event: BatchEvent) => {
    removeEvent(event.id, {
      onError: (error) => {
        showNotification({ type: 'error', message: getErrorMessage(error) })
      },
    })
  }

  return (
    <div className="flex flex-col gap-3">
      {events.map((event) => (
        <div key={event.at}>
          <div>
            {formatTime(event.at)} for {event.durationMinutes} minutes
          </div>
          <div className="text-xs text-gray-500">{event.days.map((day) => weekDays[day].name).join(', ')}</div>
          {showActions && (
            <div className="text-xs mt-1">
              <button className="link underline" onClick={() => setEditingEvent(event)}>
                Edit
              </button>{' '}
              <button className="link underline" onClick={() => handleRemoveEvent(event)} disabled={isPending}>
                Remove
              </button>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

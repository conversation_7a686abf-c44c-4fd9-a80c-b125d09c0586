import {
  AcademicCapIcon as AcademicCapIconSolid20,
  BuildingLibraryIcon as BuildingLibraryIconSolid20,
  ChartBarIcon as ChartBarIconSolid20,
  ClipboardDocumentListIcon as ClipboardDocumentListIconSolid20,
  Cog6ToothIcon as Cog6<PERSON>oothIconSolid20,
  IdentificationIcon as IdentificationIconSolid20,
  UserIcon as UserIconSolid20,
} from '@heroicons/react/20/solid'
import {
  AcademicCapIcon,
  BuildingLibraryIcon,
  ChartBarIcon,
  ClipboardDocumentListIcon,
  Cog6ToothIcon,
  IdentificationIcon,
  UserIcon,
} from '@heroicons/react/24/outline'

import { Role } from '@/shared/profiles/role-utils.shared'

const roleIcons = {
  outline24: {
    principal: BuildingLibraryIcon,
    mentor: <PERSON>Cap<PERSON><PERSON>,
    teacher: IdentificationI<PERSON>,
    student: UserIcon,
    admin: Cog6ToothIcon,
    manager: ClipboardDocumentListIcon,
    executive: ChartBarIcon,
  },
  solid20: {
    principal: BuildingLibraryIconSolid20,
    mentor: AcademicCap<PERSON>conSolid<PERSON>,
    teacher: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>olid<PERSON>,
    student: User<PERSON>conSolid20,
    admin: Cog<PERSON><PERSON><PERSON><PERSON><PERSON>conSolid20,
    manager: <PERSON><PERSON>boardDocumentListIconSolid20,
    executive: ChartBarIconSolid20,
  },
} as const

type RoleIconType = keyof typeof roleIcons

type RoleIconProps = {
  type: RoleIconType
  role: Role
  className?: string
}

export const RoleIcon = ({ type, role, className }: RoleIconProps) => {
  const IconComponent = roleIcons[type][role]

  return (
    <IconComponent
      className={`rounded-full h-8 w-8 p-1 bg-blue-50 text-blue-700 ring-1 ring-inset ring-blue-700/10 ${className ?? ''}`}
      aria-hidden="true"
    />
  )
}

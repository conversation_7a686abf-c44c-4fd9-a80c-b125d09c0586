import { DateTime } from 'luxon'
import pino from 'pino'
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'

import * as processCommands from '@/server/common/command/processCommands'
import { Transaction } from '@/server/db/db'
import * as batchDueUtils from '@/shared/batch/batch-due-utils'
import * as paymentUtils from '@/shared/common/payment-utils/payment-utils.shared'
import { PaymentReminderType } from '@/shared/common/payment-utils/payment-utils.shared'

import * as updateGoogleEventForBatch from '../../../common/updateGoogleEventForBatch'

import { updateStudentPaymentReminderType } from './updateStudentPaymentReminderType'
import * as helper from './updateStudentPaymentReminderTypeHelper'

describe('updateStudentPaymentReminderType', () => {
  const log = { info: vi.fn(), error: vi.fn() } as unknown as pino.Logger
  const db = {} as unknown as Transaction

  const nextDueOn = DateTime.fromISO('2023-12-15', { zone: 'Asia/Kolkata' })
  const nextDueOnISO = '2023-12-15'

  beforeEach(() => {
    vi.spyOn(helper, 'updateLastReminderTypeDaf').mockResolvedValue(undefined)
    vi.spyOn(batchDueUtils, 'getNextPaymentDueOn').mockReturnValue(nextDueOn)
    vi.spyOn(paymentUtils, 'getPaymentReminderTypeToSend').mockReturnValue('urgent')
    vi.spyOn(processCommands, 'appendCommandDaf').mockResolvedValue(undefined)
    vi.spyOn(updateGoogleEventForBatch, 'updateGoogleEventForBatch').mockResolvedValue(undefined)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  // Sample batch and batchStudent to be used in tests
  const getBatch = () => ({
    id: crypto.randomUUID(),
    startDate: '2023-01-01',
    timezone: 'Asia/Kolkata',
    billingCycle: 'months' as const,
    graceDays: 5,
    cycleCount: 3,
    teacher: {
      user: {
        id: crypto.randomUUID(),
      },
    },
    events: [
      {
        id: crypto.randomUUID(),
      },
    ],
  })

  const getBatchStudent = (lastReminderType: PaymentReminderType | null) => ({
    id: crypto.randomUUID(),
    firstJoinedAt: new Date('2023-01-01'),
    paidTillCycle: 2,
    lastReminderType, // Can be customized for different test scenarios
  })

  test('should update the student reminder type when it changes from null to urgent', async () => {
    // Given
    const batch = getBatch()
    const batchStudent = getBatchStudent(null) // Currently no reminder

    // When
    await updateStudentPaymentReminderType(log, db, batch, batchStudent)

    // Then
    expect(batchDueUtils.getNextPaymentDueOn).toHaveBeenCalledWith(batch, batchStudent)
    expect(batchDueUtils.getNextPaymentDueOn).toHaveBeenCalledOnce()

    expect(paymentUtils.getPaymentReminderTypeToSend).toHaveBeenCalledWith(nextDueOn, batch.graceDays)
    expect(paymentUtils.getPaymentReminderTypeToSend).toHaveBeenCalledOnce()

    expect(helper.updateLastReminderTypeDaf).toHaveBeenCalledWith(
      log,
      db,
      batchStudent,
      'urgent', // The new reminder type
    )
    expect(helper.updateLastReminderTypeDaf).toHaveBeenCalledOnce()

    expect(processCommands.appendCommandDaf).toHaveBeenCalledWith(db, log, expect.any(String), {
      command: 'mailStudentPaymentReminder',
      data: {
        batchStudentId: batchStudent.id,
        reminderType: 'urgent',
        dueDate: nextDueOnISO,
      },
    })
    expect(processCommands.appendCommandDaf).toHaveBeenCalledWith(db, log, expect.any(String), {
      command: 'smsStudentPaymentReminder',
      data: {
        batchStudentId: batchStudent.id,
        reminderType: 'urgent',
        dueDate: nextDueOnISO,
      },
    })
    expect(processCommands.appendCommandDaf).toHaveBeenCalledTimes(2)

    expect(updateGoogleEventForBatch.updateGoogleEventForBatch).not.toHaveBeenCalled()
  })

  test('should not update when reminder type does not change', async () => {
    // Given - already has urgent reminder type
    const batch = getBatch()
    const batchStudent = getBatchStudent('urgent')

    // When
    await updateStudentPaymentReminderType(log, db, batch, batchStudent)

    // Then - early return, no updates
    expect(batchDueUtils.getNextPaymentDueOn).toHaveBeenCalledWith(batch, batchStudent)
    expect(paymentUtils.getPaymentReminderTypeToSend).toHaveBeenCalledWith(nextDueOn, batch.graceDays)

    // No updates should happen
    expect(helper.updateLastReminderTypeDaf).not.toHaveBeenCalled()
    expect(processCommands.appendCommandDaf).not.toHaveBeenCalled()
    expect(updateGoogleEventForBatch.updateGoogleEventForBatch).not.toHaveBeenCalled()
  })

  test('should update lastReminderType only when changing to null (no reminders needed)', async () => {
    // Given
    const batch = getBatch()
    const batchStudent = getBatchStudent('urgent') // Currently has urgent reminder
    vi.spyOn(paymentUtils, 'getPaymentReminderTypeToSend').mockReturnValueOnce(null) // Override to return null

    // When
    await updateStudentPaymentReminderType(log, db, batch, batchStudent)

    // Then
    expect(batchDueUtils.getNextPaymentDueOn).toHaveBeenCalledWith(batch, batchStudent)
    expect(paymentUtils.getPaymentReminderTypeToSend).toHaveBeenCalledWith(nextDueOn, batch.graceDays)

    // Should update the lastReminderType but not send any commands
    expect(helper.updateLastReminderTypeDaf).toHaveBeenCalledWith(log, db, batchStudent, null)
    expect(helper.updateLastReminderTypeDaf).toHaveBeenCalledOnce()

    // No commands should be sent when changing to null
    expect(processCommands.appendCommandDaf).not.toHaveBeenCalled()
    expect(updateGoogleEventForBatch.updateGoogleEventForBatch).not.toHaveBeenCalled()
  })

  describe('handling different reminder types', () => {
    // Test for each of the possible reminder types
    test.each([
      { fromType: 'gentle', toType: 'prudent', description: 'gentle to prudent' },
      { fromType: 'prudent', toType: 'last', description: 'prudent to last' },
      { fromType: 'last', toType: 'urgent', description: 'last to urgent' },
      { fromType: 'urgent', toType: 'gentle', description: 'urgent to gentle' },
      { fromType: null, toType: 'gentle', description: 'null to gentle' },
    ] as const)('should handle reminder change from $fromType to $toType', async ({ fromType, toType }) => {
      // Given
      const batch = getBatch()
      const batchStudent = getBatchStudent(fromType)
      vi.spyOn(paymentUtils, 'getPaymentReminderTypeToSend').mockReturnValueOnce(toType)

      // When
      await updateStudentPaymentReminderType(log, db, batch, batchStudent)

      // Then
      expect(helper.updateLastReminderTypeDaf).toHaveBeenCalledWith(log, db, batchStudent, toType)

      expect(processCommands.appendCommandDaf).toHaveBeenCalledWith(db, log, expect.any(String), {
        command: 'mailStudentPaymentReminder',
        data: {
          batchStudentId: batchStudent.id,
          reminderType: toType,
          dueDate: nextDueOnISO,
        },
      })
      expect(processCommands.appendCommandDaf).toHaveBeenCalledWith(db, log, expect.any(String), {
        command: 'smsStudentPaymentReminder',
        data: {
          batchStudentId: batchStudent.id,
          reminderType: toType,
          dueDate: nextDueOnISO,
        },
      })
      expect(processCommands.appendCommandDaf).toHaveBeenCalledTimes(2)

      // For non-blocked reminders, Google calendar should not be updated
      expect(updateGoogleEventForBatch.updateGoogleEventForBatch).not.toHaveBeenCalled()
    })

    test('should update Google calendar events when changing to blocked reminder type', async () => {
      // Given
      const batch = getBatch()
      const batchStudent = getBatchStudent('urgent')
      vi.spyOn(paymentUtils, 'getPaymentReminderTypeToSend').mockReturnValueOnce('blocked')

      // When
      await updateStudentPaymentReminderType(log, db, batch, batchStudent)

      // Then
      expect(helper.updateLastReminderTypeDaf).toHaveBeenCalledWith(log, db, batchStudent, 'blocked')

      expect(processCommands.appendCommandDaf).toHaveBeenCalledWith(db, log, expect.any(String), {
        command: 'mailStudentPaymentReminder',
        data: {
          batchStudentId: batchStudent.id,
          reminderType: 'blocked',
          dueDate: nextDueOnISO,
        },
      })
      expect(processCommands.appendCommandDaf).toHaveBeenCalledWith(db, log, expect.any(String), {
        command: 'smsStudentPaymentReminder',
        data: {
          batchStudentId: batchStudent.id,
          reminderType: 'blocked',
          dueDate: nextDueOnISO,
        },
      })
      expect(processCommands.appendCommandDaf).toHaveBeenCalledTimes(2)

      // For blocked reminders, Google calendar should be updated
      expect(updateGoogleEventForBatch.updateGoogleEventForBatch).toHaveBeenCalledWith(
        db,
        log,
        batch.teacher.user.id,
        batch.events,
      )
      expect(updateGoogleEventForBatch.updateGoogleEventForBatch).toHaveBeenCalledOnce()
    })
  })
})

import { UseMutationResult } from '@tanstack/react-query'
import { useEffect, useState } from 'react'

import { ShowErrors } from '@/pages/common/ShowErrors'
import { useTimer } from '@/pages/common/useTimer'
import { $VerifyMobileForm, OTP_SENDING_INTERVAL_SECONDS, VerifyMobileForm } from '@/shared/common/common-utils.shared'
import { type ErrorPayload, type OmniError } from '@/shared/common/error-utils.shared'
import { PageContent, PageLayout, PageSection, SubmitButton } from '@ui/common/form/page-layout'

// Pure function moved outside component
const getSendOtpButtonLabel = (
  otpSentTick: number,
  currentTick: number,
  isPending: boolean,
  intervalSeconds: number,
) => {
  if (otpSentTick === -1) return 'Send OTP'
  if (isPending) return 'Sending...'
  if (otpSentTick <= currentTick - intervalSeconds) return 'Resend OTP'
  return `Resend OTP in ${intervalSeconds - (currentTick - otpSentTick)} seconds`
}

export const VerifyMobile = ({
  title,
  description,
  sendOtpMutation,
  verifyMutation,
}: {
  title: string
  description: string
  sendOtpMutation: UseMutationResult
  verifyMutation: UseMutationResult
}) => {
  const currentTick = useTimer()
  const [otpSentTick, setOtpSentTick] = useState(-1) // in timer ticks, -1 means never sent yet

  const [formData, setFormData] = useState<VerifyMobileForm>({ otp: '' })
  const [omniError, setOmniError] = useState<OmniError | undefined>()

  const sendingOtpDisabled =
    sendOtpMutation.isPending ||
    verifyMutation.isPending ||
    (otpSentTick !== -1 && currentTick - otpSentTick < OTP_SENDING_INTERVAL_SECONDS)

  // Validate form data on change
  useEffect(() => {
    const parsed = $VerifyMobileForm.safeParse(formData)
    setOmniError(parsed.error)
  }, [formData])

  const handleSendOtp = async () => {
    try {
      await sendOtpMutation.mutateAsync({})
      setOtpSentTick(currentTick)
    } catch (ex) {
      const error = ex as ErrorPayload
      setOmniError(error?.error)
    }
  }

  const handleVerifyOtp = (e: React.FormEvent) => {
    e.preventDefault()
    verifyMutation.mutate(formData, {
      onError: (error) => {
        setOmniError(error.error)
      },
    })
  }

  return (
    <form onSubmit={handleVerifyOtp}>
      <PageLayout title={title} description={description} maxWidth="max-w-2xl">
        <PageContent>
          <PageSection title="Get an OTP" description="Let's send an OTP to your WhatsApp">
            <div className="mt-8">
              <button
                type="button"
                disabled={sendingOtpDisabled}
                className="justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={handleSendOtp}
              >
                {getSendOtpButtonLabel(
                  otpSentTick,
                  currentTick,
                  sendOtpMutation.isPending,
                  OTP_SENDING_INTERVAL_SECONDS,
                )}
              </button>
            </div>
          </PageSection>
          <PageSection title="Enter OTP" description="Enter the OTP you received">
            <div>
              <div className="mt-8">
                <input
                  id="otp"
                  value={formData.otp}
                  onChange={(e) => setFormData({ otp: e.target.value })}
                  type="text"
                  inputMode="numeric"
                  className="form-input max-w-25"
                />
                <ShowErrors error={omniError} path={['otp']} />
              </div>
            </div>
          </PageSection>
          <PageSection>
            <ShowErrors error={omniError} />
            <SubmitButton hasError={omniError} isSubmitting={verifyMutation.isPending} />
          </PageSection>
        </PageContent>
      </PageLayout>
    </form>
  )
}

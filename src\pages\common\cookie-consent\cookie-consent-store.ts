import { Store, useStore } from '@tanstack/react-store'
import { useEffect } from 'react'

import { updateStore } from '@ui/common/utils/updateStore'

const COOKIE_CONSENT_KEY = 'cookieConsent'
const cookieConsentStore = new Store({
  accepted: false,
  visible: false,
})

let isCookieConsentInitialized = false

export const useCookieConsent = () => {
  useEffect(() => {
    if (!isCookieConsentInitialized) {
      const storedConsent = localStorage.getItem(COOKIE_CONSENT_KEY)
      updateStore(cookieConsentStore, {
        accepted: storedConsent === 'true',
        visible: storedConsent === null,
      })
      isCookieConsentInitialized = true
    }
  }, [])

  return useStore(cookieConsentStore)
}

export const acceptCookies = () => {
  updateStore(cookieConsentStore, { accepted: true, visible: false })
  localStorage.setItem(COOKIE_CONSENT_KEY, 'true')
}

export const rejectCookies = () => {
  updateStore(cookieConsentStore, { accepted: false, visible: false })
  localStorage.setItem(COOKIE_CONSENT_KEY, 'false')
}

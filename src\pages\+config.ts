import vikeReact from 'vike-react/config'
import vikeReactQuery from 'vike-react-query/config'

import { AppLayout } from './layout/AppLayout'

import type { Config } from 'vike/types'

// Default config (can be overridden by pages)
// https://vike.dev/config

export default {
  // https://vike.dev/Layout
  Layout: AppLayout,

  // https://vike.dev/head-tags
  title: 'TuitionLance',
  description: 'TuitionLance',

  // Tailwind UI Layout needs this
  htmlAttributes: { class: 'h-full bg-white' },
  bodyAttributes: { class: 'h-full' },

  stream: 'web',

  extends: [vikeReact, vikeReactQuery], // Using vikeReactQuery (i.e. [vikeReact, vikeReactQuery]) enables streaming, resulting in issues in vike-handler.
} satisfies Config

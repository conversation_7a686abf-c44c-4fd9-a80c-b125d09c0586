import pino from 'pino'

import { env } from '../env.server'

import { sendWhatsAppMessage, SmsDetail } from './whatsapp/sendWhatsAppMessage'

type SmsTemplate =
  | 'tl_verify_mobile'
  | 'tl_student_payment_reminder_gentle'
  | 'tl_student_payment_reminder_prudent'
  | 'tl_student_payment_reminder_last'
  | 'tl_student_payment_reminder_urgent'
  | 'tl_student_payment_reminder_blocked'
  | 'tl_please_login_asap'

export const smsFlock = () => 'sms'

export const sms = async (
  log: pino.Logger,
  redact: boolean,
  template: SmsTemplate,
  language: string,
  detail: SmsDetail,
) => {
  const hideBody = redact && env.META_ACCESS_TOKEN
  const childLog = log.child({ sms: { template, language, detail: hideBody ? 'REDACTED' : detail } })
  childLog.info(`Sending SMS to ${detail.to}`)
  if (env.META_ACCESS_TOKEN) {
    await sendWhatsAppMessage(childLog, template, detail)
  }
}

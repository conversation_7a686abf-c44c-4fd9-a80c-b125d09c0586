import { UUID } from 'crypto'

import { DateTime } from 'luxon'
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'

import app from '@/hono-entry'
import { env } from '@/server/common/env.server'
import { db } from '@/server/db/db'
import {
  academyTable,
  profileTable,
  studentFeePaymentTable,
  userTable,
  userTncAcceptedTable,
  userTncSignTable,
} from '@/server/db/schema'
import { initDb, SUNDARGARH_DISTRICT_ID, TNC_PRIVACY_V1_ID, TNC_TOS_V1_ID } from '@/server/test/initDb'
import { createAccessToken } from '@/server/users/common/auth-token-utils.server'
import { StudentFeePaymentsSearch } from '@/shared/batch/batch-utils.shared'
import { PROFILE_HEADER } from '@/shared/profiles/profile-utils.shared'

// Mock date utilities to have consistent test results
const mocked = {
  now: DateTime.now(),
} as const

vi.mock(import('@/shared/common/date-utils-basic.shared'), async (importOriginal) => {
  const mod = await importOriginal()
  return {
    ...mod,
    utc: () => mocked.now,
  }
})

const testUser = {
  id: crypto.randomUUID() as UUID,
  googleId: 'test-google-id',
  googleRefreshToken: null,
  name: 'Test Student',
  email: '<EMAIL>',
  emailVerified: true,
  googlePictureUrl: 'https://google.com/picture',
  language: 'en-US',
  tokensValidFrom: new Date(2020, 4, 6),
  mobileCountryCode: 'IN',
  mobile: '9876543210',
  mobileVerified: true,
  legalAgeDeclaredAt: mocked.now.toJSDate(),
  informationAccuracyDeclaredAt: mocked.now.toJSDate(),
} as const

const testStudentProfile = {
  id: crypto.randomUUID() as UUID,
  userId: testUser.id,
  role: 'student' as const,
  displayName: 'Test Student',
  approvedAt: mocked.now.toJSDate(),
  tncsAcceptedAt: mocked.now.toJSDate(),
} as const

const testTeacherProfile = {
  id: crypto.randomUUID() as UUID,
  userId: testUser.id,
  role: 'teacher' as const,
  displayName: 'Test Teacher',
  approvedAt: mocked.now.toJSDate(),
  tncsAcceptedAt: mocked.now.toJSDate(),
} as const

const testAcademy1 = {
  id: '096a4ac8-da30-4429-9c99-5bf391cc2454',
  name: 'Test Academy 1',
  email: '<EMAIL>',
  emailVerified: true,
  mobileCountryCode: 'IN',
  mobile: '9876543210',
  mobileVerified: true,
  currency: 'INR' as const,
  upiId: 'test1@upi',
  districtId: SUNDARGARH_DISTRICT_ID as UUID,
  pincode: '770001',
  area: 'Test Area 1',
  tncsAcceptedAt: mocked.now.toJSDate(),
  approvedAt: mocked.now.toJSDate(),
} as const

const testAcademy2 = {
  id: 'bb6d6b43-294d-46b7-bcee-d0343c36fe85',
  name: 'Test Academy 2',
  email: '<EMAIL>',
  emailVerified: true,
  mobileCountryCode: 'IN',
  mobile: '9876543211',
  mobileVerified: true,
  currency: 'INR' as const,
  upiId: 'test2@upi',
  districtId: SUNDARGARH_DISTRICT_ID as UUID,
  pincode: '770002',
  area: 'Test Area 2',
  tncsAcceptedAt: mocked.now.toJSDate(),
  approvedAt: mocked.now.toJSDate(),
} as const

const setupBasicTestData = async () => {
  await db.insert(userTable).values(testUser)

  // Add TnC acceptance records
  const acceptedTnCs = [TNC_TOS_V1_ID, TNC_PRIVACY_V1_ID] as const
  for (const tncId of acceptedTnCs) {
    await db.insert(userTncSignTable).values({
      id: crypto.randomUUID(),
      userId: testUser.id,
      tncVersionId: tncId,
      accepted: true,
    })

    await db.insert(userTncAcceptedTable).values({
      id: crypto.randomUUID(),
      userId: testUser.id,
      tncVersionId: tncId,
    })
  }

  await db.insert(profileTable).values([testStudentProfile, testTeacherProfile])
  await db.insert(academyTable).values([testAcademy1, testAcademy2])
}

const createTestPayments = async () => {
  const payments = [
    {
      id: crypto.randomUUID() as UUID,
      studentId: testStudentProfile.id,
      academyId: testAcademy1.id,
      method: 'manual' as const,
      currency: 'INR' as const,
      cents: 500000, // ₹5000.00
      status: 'pending' as const,
      paidAt: null,
      paidRemarks: null,
      createdAt: mocked.now.minus({ days: 10 }).toJSDate(),
      creationRemarks: 'Test payment 1',
      updatedAt: mocked.now.minus({ days: 10 }).toJSDate(),
      updateRemarks: null,
    },
    {
      id: crypto.randomUUID() as UUID,
      studentId: testStudentProfile.id,
      academyId: testAcademy1.id,
      method: 'manual' as const,
      currency: 'INR' as const,
      cents: 600000, // ₹6000.00
      status: 'received' as const,
      paidAt: mocked.now.minus({ days: 5 }).toJSDate(),
      paidRemarks: 'Paid via UPI',
      createdAt: mocked.now.minus({ days: 8 }).toJSDate(),
      creationRemarks: 'Test payment 2',
      updatedAt: mocked.now.minus({ days: 5 }).toJSDate(),
      updateRemarks: 'Payment confirmed',
    },
    {
      id: crypto.randomUUID() as UUID,
      studentId: testStudentProfile.id,
      academyId: testAcademy2.id,
      method: 'manual' as const,
      currency: 'INR' as const,
      cents: 400000, // ₹4000.00
      status: 'pending' as const,
      paidAt: null,
      paidRemarks: null,
      createdAt: mocked.now.minus({ days: 3 }).toJSDate(),
      creationRemarks: 'Test payment 3',
      updatedAt: mocked.now.minus({ days: 3 }).toJSDate(),
      updateRemarks: null,
    },
  ]

  await db.insert(studentFeePaymentTable).values(payments)
  return payments
}

const makeUnauthenticatedRequest = async (search?: StudentFeePaymentsSearch) => {
  const searchParams = search ? new URLSearchParams(search as Record<string, string>) : ''
  const url = `/api/students/me/payments${searchParams ? `?${searchParams}` : ''}`
  return app.request(url, { method: 'GET' }, {})
}

const makeAuthenticatedRequest = async (search?: StudentFeePaymentsSearch, accessToken?: string, profileId?: UUID) => {
  const token = accessToken || (await createAccessToken(env.JWT_SECRET_KEY, testUser.id))
  const headers: Record<string, string> = {
    Authorization: `Bearer ${token}`,
  }

  if (profileId) {
    headers[PROFILE_HEADER] = profileId
  }

  const searchParams = search ? new URLSearchParams(search as Record<string, string>) : ''
  const url = `/api/students/me/payments${searchParams ? `?${searchParams}` : ''}`
  return app.request(url, { method: 'GET', headers }, {})
}

describe('listStudentFeePaymentsHandler', () => {
  beforeEach(async () => {
    await initDb()
    await setupBasicTestData()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Authentication & Authorization', () => {
    test('should return 401 when not authenticated', async () => {
      // when
      const res = await makeUnauthenticatedRequest()

      // then
      expect(res.status).toBe(401)
      await expect(res).toBeError(401, [
        {
          code: 'custom',
          path: [],
          message: 'Unauthorized',
        },
      ])
    })

    test('should return 401 when JWT token is invalid', async () => {
      // when
      const res = await makeAuthenticatedRequest({}, 'invalid-token')

      // then
      expect(res.status).toBe(401)
      await expect(res).toBeError(401, [
        {
          code: 'jwtError',
          path: [],
          message: 'JwtTokenInvalid',
        },
      ])
    })

    test('should return 403 when no profile header provided', async () => {
      // when
      const res = await makeAuthenticatedRequest({}, undefined, undefined)

      // then
      expect(res.status).toBe(403)
      await expect(res).toBeError(403, [
        {
          code: 'custom',
          path: [],
          message: 'TL-Profile request header missing',
        },
      ])
    })

    test('should return 403 when profile is not student role', async () => {
      // when
      const res = await makeAuthenticatedRequest({}, undefined, testTeacherProfile.id)

      // then
      expect(res.status).toBe(403)
      await expect(res).toBeError(403, [
        {
          code: 'custom',
          path: [],
          message: 'Only one of student is allowed to do this operation',
        },
      ])
    })
  })

  describe('Query Validation', () => {
    test('should return 400 when pageSize is invalid', async () => {
      // when
      const res = await makeAuthenticatedRequest({ pageSize: 'invalid' }, undefined, testStudentProfile.id)

      // then
      expect(res.status).toBe(400)
      await expect(res).toBeError(400, [
        {
          code: 'invalid_string',
          path: ['pageSize'],
          message: 'Page size must be a positive integer',
        },
        {
          code: 'custom',
          path: ['pageSize'],
          message: 'Page size must be at least 1',
        },
        {
          code: 'custom',
          path: ['pageSize'],
          message: 'Page size must not exceed 100',
        },
      ])
    })

    test('should return 400 when pageSize exceeds maximum', async () => {
      // when
      const res = await makeAuthenticatedRequest({ pageSize: '101' }, undefined, testStudentProfile.id)

      // then
      expect(res.status).toBe(400)
      await expect(res).toBeError(400, [
        {
          code: 'custom',
          path: ['pageSize'],
          message: 'Page size must not exceed 100',
        },
      ])
    })

    test('should return 400 when fromCreatedAt is invalid ISO string', async () => {
      // when
      const res = await makeAuthenticatedRequest({ fromCreatedAt: 'invalid-date' }, undefined, testStudentProfile.id)

      // then
      expect(res.status).toBe(400)
      await expect(res).toBeError(400, [
        {
          code: 'invalid_string',
          path: ['fromCreatedAt'],
          message: 'Invalid datetime',
        },
      ])
    })

    test('should return 400 when fromAcademyId is invalid UUID', async () => {
      // when
      const res = await makeAuthenticatedRequest(
        { fromAcademyId: 'invalid-uuid' as UUID },
        undefined,
        testStudentProfile.id,
      )

      // then
      expect(res.status).toBe(400)
      await expect(res).toBeError(400, [
        {
          code: 'custom',
          path: ['fromAcademyId'],
          message: 'Must be a UUID v4 string',
        },
      ])
    })

    test('should return 400 when beyondId is invalid UUID', async () => {
      // when
      const res = await makeAuthenticatedRequest({ beyondId: 'invalid-uuid' as UUID }, undefined, testStudentProfile.id)

      // then
      expect(res.status).toBe(400)
      await expect(res).toBeError(400, [
        {
          code: 'custom',
          path: ['beyondId'],
          message: 'Must be a UUID v4 string',
        },
      ])
    })
  })

  describe('Success Scenarios', () => {
    test('should return empty array when student has no payments', async () => {
      // when
      const res = await makeAuthenticatedRequest({}, undefined, testStudentProfile.id)

      // then
      expect(res.status).toBe(200)
      const data = await res.json()
      expect(data.rows).toEqual([])
    })

    test('should return student payments ordered by academyId ASC, createdAt DESC, id ASC', async () => {
      // given
      await createTestPayments()

      // when
      const res = await makeAuthenticatedRequest({}, undefined, testStudentProfile.id)

      // then
      expect(res.status).toBe(200)
      const data = await res.json()
      expect(data.rows).toHaveLength(3)

      // Verify ordering: Academy1 payments first (older academy), then Academy2
      // Within same academy, newer payments first (createdAt DESC)
      expect(data.rows[0].academyId).toBe(testAcademy1.id)
      expect(data.rows[1].academyId).toBe(testAcademy1.id)
      expect(data.rows[2].academyId).toBe(testAcademy2.id)

      // Verify payment fields are returned correctly
      expect(data.rows[0]).toMatchObject({
        id: expect.any(String),
        academyId: testAcademy1.id,
        method: 'manual',
        currency: 'INR',
        cents: expect.any(Number),
        status: expect.any(String),
        createdAt: expect.any(String),
      })
    })

    test('should respect pageSize parameter', async () => {
      // given
      await createTestPayments()

      // when
      const res = await makeAuthenticatedRequest({ pageSize: '2' }, undefined, testStudentProfile.id)

      // then
      expect(res.status).toBe(200)
      const data = await res.json()
      expect(data.rows).toHaveLength(2)
    })

    test('should handle pagination with previous=true', async () => {
      // given
      await createTestPayments()

      // when - Get first page with pageSize 2
      const firstRes = await makeAuthenticatedRequest({ pageSize: '2' }, undefined, testStudentProfile.id)
      expect(firstRes.status).toBe(200)
      const firstData = await firstRes.json()
      expect(firstData.rows).toHaveLength(2)

      // Set up cursor for previous page
      const firstPayment = firstData.rows[0]
      const previousSearch = {
        previous: 'true',
        fromAcademyId: firstPayment.academyId,
        fromCreatedAt: firstPayment.createdAt,
        beyondId: firstPayment.id,
      }

      // when - Get previous page (should be empty as we're at the beginning)
      const prevRes = await makeAuthenticatedRequest(previousSearch, undefined, testStudentProfile.id)

      // then
      expect(prevRes.status).toBe(200)
      const prevData = await prevRes.json()
      expect(prevData.rows).toEqual([])
    })

    test('should handle forward pagination correctly', async () => {
      // given
      await createTestPayments()

      // when - Get first page with pageSize 2
      const firstRes = await makeAuthenticatedRequest({ pageSize: '2' }, undefined, testStudentProfile.id)
      expect(firstRes.status).toBe(200)
      const firstData = await firstRes.json()
      expect(firstData.rows).toHaveLength(2)

      // Set up cursor for next page
      const lastPayment = firstData.rows[firstData.rows.length - 1]
      const nextSearch = {
        fromAcademyId: lastPayment.academyId,
        fromCreatedAt: lastPayment.createdAt,
        beyondId: lastPayment.id,
        pageSize: '2',
      }

      // when - Get next page
      const nextRes = await makeAuthenticatedRequest(nextSearch, undefined, testStudentProfile.id)

      // then
      expect(nextRes.status).toBe(200)
      const nextData = await nextRes.json()
      expect(nextData.rows).toHaveLength(1) // Remaining payment
      expect(nextData.rows[0].id).not.toBe(lastPayment.id) // Different payment
    })

    test('should only return payments for the authenticated student', async () => {
      // given
      await createTestPayments()

      // Create another student with payments
      const otherStudent = {
        id: crypto.randomUUID() as UUID,
        userId: testUser.id,
        role: 'student' as const,
        displayName: 'Other Student',
        approvedAt: mocked.now.toJSDate(),
        tncsAcceptedAt: mocked.now.toJSDate(),
      }
      await db.insert(profileTable).values(otherStudent)

      const otherPayment = {
        id: crypto.randomUUID() as UUID,
        studentId: otherStudent.id,
        academyId: testAcademy1.id,
        method: 'manual' as const,
        currency: 'INR' as const,
        cents: 300000,
        status: 'pending' as const,
        paidAt: null,
        paidRemarks: null,
        createdAt: mocked.now.minus({ days: 1 }).toJSDate(),
        creationRemarks: 'Other student payment',
        updatedAt: mocked.now.minus({ days: 1 }).toJSDate(),
        updateRemarks: null,
      }
      await db.insert(studentFeePaymentTable).values(otherPayment)

      // when
      const res = await makeAuthenticatedRequest({}, undefined, testStudentProfile.id)

      // then
      expect(res.status).toBe(200)
      const data = await res.json()
      expect(data.rows).toHaveLength(3) // Only test student's payments
      data.rows.forEach((payment: { id: string }) => {
        expect(payment.id).not.toBe(otherPayment.id)
      })
    })
  })
})

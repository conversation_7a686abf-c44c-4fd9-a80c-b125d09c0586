import { type OmniError } from '@/shared/common/error-utils.shared'
import { COURSE_DESCR_MAX_LEN, COURSE_NAME_MAX_LEN } from '@/shared/course/course-utils.shared'
import { NameAndDescrEditor } from '@ui/common/form/NameAndDescrEditor'

type CourseCommonEditorProps = {
  name: string
  descr: string
  omniError?: OmniError
  onNameChange: (value: string) => void
  onDescrChange: (value: string) => void
}

export const CourseCommonEditor = ({
  name,
  descr,
  omniError,
  onNameChange,
  onDescrChange,
}: CourseCommonEditorProps) => (
  <NameAndDescrEditor
    name={name}
    descr={descr}
    omniError={omniError}
    onNameChange={onNameChange}
    onDescrChange={onDescrChange}
    nameMaxLen={COURSE_NAME_MAX_LEN}
    descrMaxLen={COURSE_DESCR_MAX_LEN}
  />
)

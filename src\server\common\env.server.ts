import { Context } from 'hono'
import { env as honoEnv } from 'hono/adapter'

import { DeploymentEnv } from '@/shared/common/common-utils.shared'

type SvrEnv = {
  APP_ENV: DeploymentEnv
  HOME_URL: string
  PORT: string
  BETTER_STACK_SOURCE_TOKEN?: string
  DATABASE_URL: string
  GOOGLE_CLIENT_ID: string
  GOOGLE_CLIENT_SECRET: string
  ENCRYPTION_SECRET_KEY: string
  JWT_SECRET_KEY: string
  AWS_ACCESS_KEY: string
  AWS_SECRET_KEY: string
  META_ACCESS_TOKEN: string
  CRON_AUTH_KEY: string
  SMTP_PASSWORD: string
}

export const env = honoEnv<SvrEnv>({} as unknown as Context<{ Bindings: SvrEnv }>)

if (!env.DATABASE_URL) {
  throw new Error('DATABASE_URL is not set')
}

if (!env.ENCRYPTION_SECRET_KEY) {
  throw new Error('ENCRYPTION_SECRET_KEY is not set')
}

if (!env.JWT_SECRET_KEY) {
  throw new Error('JWT_SECRET_KEY is not set')
}

if (!env.CRON_AUTH_KEY) {
  throw new Error('CRON_AUTH_KEY is not set')
}

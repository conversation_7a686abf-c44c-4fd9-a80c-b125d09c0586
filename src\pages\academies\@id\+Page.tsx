import { usePageContext } from 'vike-react/usePageContext'

import { AcademyDetail } from '@/server/academies/get/getAcademy.server'
import { extractUuid } from '@/shared/common/common-utils.shared'
import { ShowData } from '@ui/common/ShowData'

import { useAcademyWithContact } from '../common/useAcademyWithContact'

import { AcademyPage } from './AcademyPage'

export default () => {
  const pageContext = usePageContext()
  const id = extractUuid(pageContext.routeParams.id)

  const { academy, academyMobile, error } = useAcademyWithContact(id, true)
  return (
    <ShowData error={error}>
      <AcademyPage academy={academy as AcademyDetail} academyMobile={academyMobile!} />
    </ShowData>
  )
}

import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { pino } from 'pino'

import { addCommand, Command } from '@/server/common/command/processCommands'
import { env } from '@/server/common/env.server'
import { mail } from '@/server/common/mail/mail'
import { db } from '@/server/db/db'
import { academyTable } from '@/server/db/schema/academy-schema'
import { batchStudentTable, batchTable } from '@/server/db/schema/batch-schema'
import { courseTable } from '@/server/db/schema/course-schema'
import { countryTable } from '@/server/db/schema/master-schema'
import { profileTable } from '@/server/db/schema/profile-schema'
import { userTable } from '@/server/db/schema/user-schema'
import { academyPath } from '@/shared/academies/academy-utils.shared'
import { batchPath } from '@/shared/batch/batch-utils.shared'
import { phoneNumber } from '@/shared/common/common-utils.shared'
import { formatDate } from '@/shared/common/date-utils.shared'
import { PaymentReminderType } from '@/shared/common/payment-utils/payment-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

export const MAIL_STUDENT_PAYMENT_REMINDER_COMMAND = 'mailStudentPaymentReminder'

export type MailStudentPaymentReminderCommandData = {
  batchStudentId: UUID
  reminderType: PaymentReminderType
  dueDate: string
}

const getStudentPaymentReminderMailDataDaf = async (log: pino.Logger, batchStudentId: UUID) =>
  withLog(log, 'getStudentPaymentReminderSmsDataDaf', () =>
    db
      .select({
        to: userTable.email,
        studentName: profileTable.displayName,
        language: userTable.language,
        courseId: batchTable.courseId,
        courseName: courseTable.name,
        batchId: batchTable.id,
        currency: academyTable.currency,
        fee: batchTable.fee,
        graceDays: batchTable.graceDays,
        academyId: academyTable.id,
        academyName: academyTable.name,
        supportMobile: academyTable.mobile,
        mobilePrefix: countryTable.phonePrefix,
        supportEmail: academyTable.email,
      })
      .from(batchStudentTable)
      .innerJoin(profileTable, eq(batchStudentTable.studentId, profileTable.id))
      .innerJoin(userTable, eq(profileTable.userId, userTable.id))
      .innerJoin(batchTable, eq(batchStudentTable.batchId, batchTable.id))
      .innerJoin(courseTable, eq(batchTable.courseId, courseTable.id))
      .innerJoin(academyTable, eq(courseTable.academyId, academyTable.id))
      .innerJoin(countryTable, eq(academyTable.mobileCountryCode, countryTable.code))
      .where(eq(batchStudentTable.id, batchStudentId)),
  )

const executeCommand = async (log: pino.Logger, command: Command<MailStudentPaymentReminderCommandData>) => {
  const data = command.data
  const [mailData] = await getStudentPaymentReminderMailDataDaf(log, data.batchStudentId)
  await mail(log, false, templateName(data.reminderType), mailData.language, {
    to: mailData.to,
    data: {
      studentName: mailData.studentName,
      currency: mailData.currency,
      fee: mailData.fee,
      courseName: mailData.courseName,
      batchUrl: `${env.HOME_URL}${batchPath({ id: mailData.courseId, name: mailData.courseName }, mailData.batchId)}`,
      academyName: mailData.academyName,
      academyUrl: `${env.HOME_URL}${academyPath({ id: mailData.academyId, name: mailData.academyName })}`,
      supportMobile: phoneNumber(mailData.mobilePrefix, mailData.supportMobile),
      supportEmail: mailData.supportEmail,
      graceDays: mailData.graceDays,
      dueDate: formatDate(data.dueDate, { language: mailData.language }),
    },
  })
}

const templateName = (reminderType: PaymentReminderType) => {
  switch (reminderType) {
    case 'blocked':
      return 'studentPaymentReminderBlocked'
    case 'gentle':
      return 'studentPaymentReminderGentle'
    case 'last':
      return 'studentPaymentReminderLast'
    case 'prudent':
      return 'studentPaymentReminderPrudent'
    case 'urgent':
      return 'studentPaymentReminderUrgent'
    default:
      throw new Error(`Invalid reminder type: ${reminderType}`)
  }
}

addCommand(MAIL_STUDENT_PAYMENT_REMINDER_COMMAND, executeCommand)

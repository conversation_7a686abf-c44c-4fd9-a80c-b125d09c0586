import clsx from 'clsx'

import { PaymentReminderType, paymentReminderTypes } from '@/shared/common/payment-utils/payment-utils.shared'

export const PaymentReminderTypeBadge = ({ reminderType }: { reminderType: PaymentReminderType | null }) => {
  if (!reminderType) return null
  const badgeColor = paymentReminderTypes[reminderType].badgeColor
  const badgeLabel = paymentReminderTypes[reminderType].label
  return (
    <span
      className={clsx(
        badgeColor,
        'has-tooltip inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset',
      )}
    >
      {badgeLabel}
      <span className="tooltip -mt-12 inline-flex items-center rounded-md bg-gray-50 px-3 py-1 text-xs font-medium text-gray-700 ring-1 ring-gray-600/20 ring-inset">
        {paymentReminderTypes[reminderType].descr}
      </span>
    </span>
  )
}

import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { HonoVars } from '@/server/common/hono-utils.server'
import { $UserForm } from '@/shared/users/user-utils.shared'

import { signup } from './signup'

export const signupHandler = new Hono<HonoVars>().post('/', zValidator('json', $UserForm), async (c) => {
  const formData = c.req.valid('json')
  const acceptLanguage = c.req.header('accept-language')
  const user = c.get('user')

  const token = await signup(c.get('log'), formData, acceptLanguage, user?.id)

  c.status(201)
  return c.json(token)
})

import { UUID } from 'crypto'

import { and, asc, desc, eq, gt, isNotNull, lt, or } from 'drizzle-orm'
import { DateTime } from 'luxon'
import pino, { Logger } from 'pino'

import { findCourseStaffDaf } from '@/server/courses/courses/common/course-staff-check'
import { db } from '@/server/db/db'
import { batchRecommendationTable, batchTable, courseTable } from '@/server/db/schema'
import { isGoodProfile } from '@/server/profiles/common/profile-check.server'
import { CourseBatchesSearch } from '@/shared/batch/batch-utils.shared'
import { setQuery } from '@/shared/common/serverQueries.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { batchColumns } from '../../common/batch-select-helper'

const DEFAULT_COURSE_BATCHES_PAGE_SIZE = 20

const pageFilter = (search: CourseBatchesSearch) => {
  const firstOrLastPage = !search.beyondId // if no beyondId and `previous` is true, its the last page
  if (firstOrLastPage) return undefined

  // Since beyondId exists, fromStartDate and fromCreatedAt must also exist (set by frontend)
  const cursorStartDate = search.fromStartDate!
  const cursorCreatedAt = DateTime.fromISO(search.fromCreatedAt!)

  if (search.previous === 'true') {
    // Backward pagination - get rows that come before the current cursor
    // With compound ordering startDate ASC, createdAt ASC, id DESC, get rows with:
    //   1. startDate > cursor.startDate, OR
    //   2. startDate = cursor.startDate AND createdAt > cursor.createdAt, OR
    //   3. startDate = cursor.startDate AND createdAt = cursor.createdAt AND id < cursor.id
    return or(
      gt(batchTable.startDate, cursorStartDate),
      and(
        eq(batchTable.startDate, cursorStartDate),
        // Note: postgres.js driver truncates JavaScript dates to milliseconds.
        // So, unless we add 1 millisecond, gt(batchTable.createdAt, cursorCreatedAt) will be true even for the same value.
        // See https://github.com/drizzle-team/drizzle-orm/issues/1148 for more details.
        gt(batchTable.createdAt, cursorCreatedAt.plus({ millisecond: 1 }).toJSDate()),
      ),
    )
  }

  // Forward pagination - get rows that come after the current cursor
  // With compound ordering startDate DESC, createdAt DESC, id ASC, get rows with:
  //   1. startDate < cursor.startDate, OR
  //   2. startDate = cursor.startDate AND createdAt < cursor.createdAt, OR
  //   3. startDate = cursor.startDate AND createdAt = cursor.createdAt AND id > cursor.id

  // Note: postgres.js driver truncates JavaScript dates to milliseconds.
  // So, unless we subtract 1 millisecond, lt(batchTable.createdAt, cursorCreatedAt) will be true even for the same value.
  // See https://github.com/drizzle-team/drizzle-orm/issues/1148 for more details.
  const cursorCreatedAtMinus1 = cursorCreatedAt.plus({ millisecond: -1 }).toJSDate()

  return or(
    lt(batchTable.startDate, cursorStartDate),
    and(eq(batchTable.startDate, cursorStartDate), lt(batchTable.createdAt, cursorCreatedAtMinus1)),
    and(
      eq(batchTable.startDate, cursorStartDate),
      eq(batchTable.createdAt, cursorCreatedAtMinus1),
      gt(batchTable.id, search.beyondId!),
    ),
  )
}

const findBatchesDaf = (log: Logger, courseId: UUID, search: CourseBatchesSearch, goodStaff: boolean) => {
  const orderBy =
    search.previous === 'true' ?
      [asc(batchTable.startDate), asc(batchTable.createdAt), desc(batchTable.id)]
    : [desc(batchTable.startDate), desc(batchTable.createdAt), asc(batchTable.id)]

  const publishedFilter = goodStaff ? undefined : isNotNull(courseTable.publishedAt)

  return withLog(log, 'findBatchesDaf', async () => {
    return db
      .select({ ...batchColumns, createdAt: batchTable.createdAt })
      .from(batchTable)
      .innerJoin(courseTable, eq(courseTable.id, batchTable.courseId))
      .leftJoin(batchRecommendationTable, eq(batchRecommendationTable.batchId, batchTable.id))
      .where(and(eq(batchTable.courseId, courseId), pageFilter(search), publishedFilter))
      .orderBy(...orderBy)
      .limit(search.pageSize ? parseInt(search.pageSize) : DEFAULT_COURSE_BATCHES_PAGE_SIZE)
  })
}

const isGoodCourseStaff = async (log: pino.Logger, courseId: UUID, profileId: UUID | undefined) => {
  if (!profileId) return false
  const staff = await findCourseStaffDaf(log, courseId, profileId)
  return isGoodProfile(staff)
}

export const listCourseBatches = async (log: Logger, courseId: UUID, search: CourseBatchesSearch, profileId?: UUID) => {
  const goodStaff = await isGoodCourseStaff(log, courseId, profileId)
  const batches = await findBatchesDaf(log, courseId, search, !!goodStaff)
  if (search.previous === 'true') batches.reverse()
  return batches
}

export type ListCourseBatches = typeof listCourseBatches
export type CourseBatch = Awaited<ReturnType<ListCourseBatches>>[number]

setQuery('listCourseBatches', listCourseBatches)

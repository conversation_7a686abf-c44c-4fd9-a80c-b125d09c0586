import { setupServer } from 'msw/node'

import { getLogger } from '@/shared/common/logger.shared'

export const mockServer = setupServer()

mockServer.events.on('request:start', ({ request }) => {
  void logRequest(request)
})

export const logRequest = async (request: Request) => {
  // Extract headers
  const headers: Record<string, string | undefined> = {}
  request.headers.forEach((value, name) => {
    headers[name] = value
  })

  const bodyString = await getBodyString(request)
  const body = (headers['content-type']?.endsWith('json') ? JSON.parse(bodyString) : { body: bodyString }) as unknown

  getLogger().debug({ Headers: headers, Body: body }, 'Outgoing %s %s', request.method, request.url)
}

const getBodyString = async (request: Request) => {
  try {
    return await request.clone().text()
  } catch (e: unknown) {
    getLogger().error(e)
    return ''
  }
}

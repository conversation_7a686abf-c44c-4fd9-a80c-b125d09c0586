import { UUID } from 'crypto'

import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'
import { z } from 'zod'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $UUID } from '@/shared/common/common-utils.shared'

import { addTagToCourse } from './addTagToCourse'

const $TagForm = z.object({
  tagId: $UUID,
})

export const addTagToCourseHandler = new Hono<HonoVars>().post('/', zValidator('json', $TagForm), async (c) => {
  const courseId = c.req.param('id') as UUID
  const form = c.req.valid('json')

  await addTagToCourse(getCtx(c), courseId, form.tagId)
  return c.body(null, 204)
})

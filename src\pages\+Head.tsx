// https://vike.dev/Head
import logoUrl from '@ui/common/assets/favicon.svg'

export default () => (
  <>
    <meta charSet="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href={logoUrl} />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
    <link
      rel="preload"
      as="style"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap&font-display=swap"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap&font-display=swap"
    />
    <style>
      {`
        /* Fallback font metrics to minimize layout shift */
        @font-face {
          font-family: 'Inter Fallback';
          size-adjust: 100%;
          ascent-override: 90%;
          descent-override: 22.43%;
          line-gap-override: 0%;
          src: local('Arial');
        }
        
        :root {
          font-family: 'Inter', 'Inter Fallback', sans-serif;
        }
      `}
    </style>
  </>
)

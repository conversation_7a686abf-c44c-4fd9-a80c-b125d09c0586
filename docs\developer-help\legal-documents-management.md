# Legal Documents - Simple Approach

Simple configuration for legal document redirects that ensures users always get current documents.

## Update 2025-05-31

I had to convert the agreements to HTML, because Google needs those as HTML when verifying the app. For that, I used the following approach:
1. Get HTMLs from word document: https://word2cleanhtml.com
1. Paste the HTMLs into `src/pages/legal-agreements/privacy-policy/+Page.tsx` and `src/pages/legal-agreements/terms-of-service/+Page.tsx`
1. Use AI to beautify those: "This is copied from some document exported from word to html. So, there are errors and, and there are no gaps among the paragraphs. Please remove the errors and introduce gaps. IMPORTANT: Don't alter the content at all."
1. Add links to the PDFs in the pages.
1. Some manual work. See commits "#34: Convert Privacy Policy to HTML (Google requirement)" and "#34: Convert Terms of Service to HTML (Google requirement)" for details.

So, the content below is no longer relevant.
-----------------------------------------------
## 🎯 **Overview**

- **Static URLs for latest version legal documents**: `/legal-agreements/privacy-policy` and `/legal-agreements/terms-of-service`
- **302 Redirects**: Temporary redirects that aren't cached by browsers
- **Cache-Busting**: Timestamp parameters ensure fresh documents
- **Google OAuth Compatible**: Static URLs work with Google OAuth consent screen

## 📋 **How It Works**

1. User visits `/legal-agreements/privacy-policy`
2. Server does 302 redirect to S3 URL with timestamp: `https://tuitionlance.s3.amazonaws.com/...-v1.pdf?t=1642345678901`
3. User gets current document, no caching issues

## 🔧 **Usage in Components**

## 🚀 **Updating Document Versions**

When you need to update from v1 to v2:

### Step 1: Upload new document
```bash
aws s3 cp privacy-policy-v2.pdf s3://tuitionlance/legal-agreements/privacy-policy-v2.pdf
```

### Step 2: Update the redirect
```typescript
// src\pages\legal-agreements\privacy-policy\+guard.ts
import { redirect } from 'vike/abort'

export function guard() {
  // 302 redirect with cache-busting to ensure users always get current document
  throw redirect(`https://tuitionlance.s3.amazonaws.com/legal-agreements/privacy-policy-v2.pdf?t=${Date.now()}`, 302)
}
```

### Step 3: Deploy
All users immediately get v2 (no cache issues!)

## ✅ **Benefits**

- ✅ **Simple**: One URL strategy for all use cases
- ✅ **Reliable**: Users always get current documents
- ✅ **Google OAuth Compatible**: Static URLs work with consent screens
- ✅ **No Cache Issues**: 302 redirects + timestamps prevent stale content
- ✅ **Easy Updates**: Change one config file, deploy, done

## 📊 **Trade-offs**

- ❌ **SEO**: 302 redirects don't pass SEO value (but legal docs don't need SEO)
- ❌ **Performance**: Extra redirect hop (but minimal impact)
- ✅ **Legal Compliance**: Always current documents (this is more important)

## 🧪 **Testing**

```bash
# Test redirect
curl -I https://www.tuitionlance.com/legal-agreements/privacy-policy

# Should return:
# HTTP/1.1 302 Found
# Location: https://tuitionlance.s3.amazonaws.com/...-v1.pdf?t=1642345678901
```

This approach prioritizes **reliability and legal compliance** over SEO optimization, which is the right choice for legal documents. 
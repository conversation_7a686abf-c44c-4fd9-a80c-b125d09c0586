## Configuring WhatsApp API

### Useful links

- [WhatsApp API Documentation](https://developers.facebook.com/docs/whatsapp/cloud-api)
  - [Concepts](https://developers.facebook.com/docs/whatsapp/cloud-api/overview)
- [NATURAL PROGRAMMER Facebook Business Portfolio](https://business.facebook.com/latest/settings/business_info?business_id=****************)
- [TuitionLance Facebook App Dashboard](https://developers.facebook.com/apps/***************/dashboard/?business_id=****************)
- [NATURAL PROGRAMMER WABA List](https://business.facebook.com/latest/settings/whatsapp_account?business_id=****************&selected_asset_type=whatsapp-business-account)
- [TuitionLance App WhatsApp API Setup](https://developers.facebook.com/apps/***************/whatsapp-business/wa-dev-console/?business_id=****************)
  - Here and only here you can add a phone number. Adding directly to the WABA Manager will not make the phone number status "connected". Also, note that you may need to "Generate Access token" to get your phone number status from pending to connected.
- [TuitionLance WABA Manager](https://business.facebook.com/latest/whatsapp_manager/phone_numbers/?business_id=****************&tab=phone-numbers&nav_ref=whatsapp_manager&asset_id=***************)
- [TuitionLance WABA Message templates](https://business.facebook.com/latest/whatsapp_manager/message_templates?business_id=****************&tab=message-templates&filters=%7B%22date_range%22%3A7%2C%22language%22%3A[]%2C%22quality%22%3A[]%2C%22search_text%22%3A%22%22%2C%22status%22%3A[%22APPROVED%22%2C%22IN_APPEAL%22%2C%22PAUSED%22%2C%22PENDING%22%2C%22REJECTED%22]%2C%22tag%22%3A[]%7D&nav_ref=whatsapp_manager&asset_id=***************)
- [Business Support - Update template category](https://business.facebook.com/business-support-home/****************/***************/?templates_tab=category_updates)

### IDs

- Natural Programmer Business Portfolio ID: `****************`
- TuitionLance Facebook App ID: `***************`
- TuitionLance WABA (WhatsApp Business Account) ID: `****************`
  - Phone Number ID (**********): `***************`

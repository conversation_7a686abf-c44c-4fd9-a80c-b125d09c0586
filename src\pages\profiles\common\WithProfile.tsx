import { type ReactNode } from 'react'

import { WarningAlert } from '@/pages/common/alerts/WarningAlert'
import { ShowData } from '@/pages/common/ShowData'
import { Protected } from '@/pages/users/auth/Protected'
import { ROLES, type Role } from '@/shared/profiles/role-utils.shared'

import { useCurrentProfile } from './current-profile-store'

type WithProfileProps = {
  allowSuspended?: boolean
  mustBeApproved?: boolean
  roleAnyOf?: readonly Role[]
  children: ReactNode
}

export const WithProfile = ({
  allowSuspended = false,
  mustBeApproved = true,
  roleAnyOf = ROLES,
  children,
}: WithProfileProps) => {
  const { currentProfile, profileIsPending, profileError } = useCurrentProfile()

  const showSuspendedWarning = !allowSuspended && currentProfile?.suspendedAt
  const showApprovalWarning = mustBeApproved && !currentProfile?.approvedAt
  const roleOk = currentProfile && roleAnyOf.includes(currentProfile.role)

  return (
    <Protected>
      <ShowData isPending={profileIsPending} error={profileError} spinnerSize="1.25rem">
        {showSuspendedWarning ?
          <WarningAlert>Suspended profiles are not allowed to access this page.</WarningAlert>
        : showApprovalWarning ?
          <WarningAlert>Unapproved profiles are not allowed to access this page.</WarningAlert>
        : roleOk ?
          children
        : <WarningAlert>Only profiles with {roleAnyOf.join(' or ')} roles can access this page.</WarningAlert>}
      </ShowData>
    </Protected>
  )
}

import { UUID } from 'crypto'

import { sql } from 'drizzle-orm'
import { check, text, time, timestamp, uuid } from 'drizzle-orm/pg-core'

import type { PgTimestampConfig } from 'drizzle-orm/pg-core'

export const uid = () => uuid().$type<UUID>()
export const timestampz = (opts?: PgTimestampConfig<'string' | 'date'>) => timestamp({ ...opts, withTimezone: true })
export const timez = (opts?: PgTimestampConfig<'string' | 'date'>) => time({ ...opts, withTimezone: true })

/**
 * Create a deferrable foreign key reference using raw SQL
 * This is a workaround until Drizzle ORM natively supports DEFERRABLE constraints
 * @param toTable The referenced table name
 * @param column The column name in the current table
 * @param toColumn The column name in the referenced table
 * @param onDelete Optional ON DELETE behavior ('cascade', 'set null', etc.)
 */
export const deferrable = (toTable: string, column: string, toColumn = 'id', onDelete?: string) => {
  const constraintName = `fk_${column}_${toTable}_${toColumn}`.toLowerCase()
  const onDeleteClause = onDelete ? ` ON DELETE ${onDelete}` : ''

  // Return a SQL expression that will be included in the table definition
  return sql`CONSTRAINT ${sql.identifier(constraintName)} 
             FOREIGN KEY (${sql.identifier(column)}) 
             REFERENCES ${sql.identifier(toTable)}(${sql.identifier(toColumn)})${sql.raw(onDeleteClause)} 
             DEFERRABLE INITIALLY IMMEDIATE`
}

export const auditColumns = {
  createdAt: timestampz().notNull().defaultNow(),
  creationRemarks: text(),
  updatedAt: timestampz(),
  updateRemarks: text(),
}

export const suspensionColumns = {
  suspendedAt: timestampz(),
  suspensionReason: text(),
}

export const suspensionCheck = (t: { suspensionReason: unknown }) => [
  check('suspension_reason_len', sql`char_length(${t.suspensionReason}) <= 5000`),
]

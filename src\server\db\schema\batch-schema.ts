import { relations, sql } from 'drizzle-orm'
import { boolean, check, date, index, integer, pgTable, smallint, text, uniqueIndex } from 'drizzle-orm/pg-core'

import { BillingCycle } from '@/shared/batch/batch-utils.shared'
import { Currency } from '@/shared/common/common-utils.shared'
import { WeekDay } from '@/shared/common/date-utils.shared'
import { PaymentReminderType, PaymentMethod, PaymentStatus } from '@/shared/common/payment-utils/payment-utils.shared'

import { academyTable } from './academy-schema'
import { courseAttachmentTable, courseTable } from './course-schema'
import { auditColumns, suspensionCheck, suspensionColumns, timestampz, uid } from './helpers'
import { profileTable } from './profile-schema'

export const batchTable = pgTable(
  'batch',
  {
    id: uid().primaryKey(),

    courseId: uid()
      .notNull()
      .references(() => courseTable.id),
    teacherId: uid()
      .notNull()
      .references(() => profileTable.id),

    fee: integer().notNull(),
    billingCycle: text().$type<BillingCycle>().notNull(),
    graceDays: smallint().notNull().default(3),

    startDate: date().notNull(),
    timezone: text().notNull(),
    cycleCount: smallint().notNull(),
    over: boolean().notNull().default(false),

    // A student can join a course only if admission is open and there are seats available
    seatCount: smallint().notNull(),
    studentCount: smallint().notNull().default(0),
    admissionOpen: boolean().notNull(),

    ...auditColumns,
    ...suspensionColumns,
  },
  (t) => [
    // deferrable('course', 'courseId'),
    //deferrable('profile', 'teacherId'),

    check('chk_timezone_len', sql`char_length(${t.timezone}) <= 50`),
    ...suspensionCheck(t),

    index('idx_batch_course_id_start_date_created_at').on(t.courseId, t.startDate, t.createdAt),
    index('idx_batch_teacher_id_start_date_created_at').on(t.teacherId, t.startDate, t.createdAt),
    index('idx_batch_start_date_course_id').on(t.startDate.desc().nullsLast(), t.courseId),
  ],
)

export const batchEventTable = pgTable(
  'batch_event',
  {
    id: uid().primaryKey(),
    batchId: uid()
      .notNull()
      .references(() => batchTable.id),
    at: text().notNull(), // HH:MM
    durationMinutes: smallint().notNull(),
    days: text()
      .$type<WeekDay>()
      .notNull()
      .array()
      .notNull()
      .default(sql`'{}'::text[]`), // See https://developers.google.com/calendar/api/guides/recurringevents
    eventType: text().notNull().default('meet'), // 'meet' or 'zoom'
    eventXid: text().notNull(), // Google Calendar event ID (Xid stands for external ID)
    ...auditColumns,
  },
  (t) => [
    check('chk_at_len', sql`char_length(${t.at}) = 5`),
    check('chk_event_type_len', sql`char_length(${t.eventType}) <= 50`),
    check('chk_event_xid_len', sql`char_length(${t.eventXid}) <= 255`),
    index('idx_batch_event_batch_id').on(t.batchId),
  ],
)

export const batchRecommendationTable = pgTable('batch_recommendation', {
  courseId: uid()
    .primaryKey()
    .references(() => courseTable.id),
  batchId: uid()
    .notNull()
    .references(() => batchTable.id),
  ...auditColumns,
})

export const batchRelations = relations(batchTable, ({ one, many }) => ({
  course: one(courseTable, {
    fields: [batchTable.courseId],
    references: [courseTable.id],
  }),
  teacher: one(profileTable, {
    fields: [batchTable.teacherId],
    references: [profileTable.id],
  }),
  events: many(batchEventTable),
  students: many(batchStudentTable),
  attachments: many(batchAttachmentTable),
}))

export const batchEventRelations = relations(batchEventTable, ({ one }) => ({
  batch: one(batchTable, {
    fields: [batchEventTable.batchId],
    references: [batchTable.id],
  }),
}))

export const batchRecommendationRelations = relations(batchRecommendationTable, ({ one }) => ({
  course: one(courseTable, {
    fields: [batchRecommendationTable.courseId],
    references: [courseTable.id],
  }),
  recommendedBatch: one(batchTable, {
    fields: [batchRecommendationTable.batchId],
    references: [batchTable.id],
  }),
}))

export const batchStudentTable = pgTable(
  'batch_student',
  {
    id: uid().primaryKey(),
    batchId: uid()
      .notNull()
      .references(() => batchTable.id),
    studentId: uid()
      .notNull()
      .references(() => profileTable.id),
    firstJoinedAt: timestampz().notNull(),
    leftAt: timestampz(),
    paidTillCycle: smallint().notNull().default(0), // 1 based cycle number
    lastReminderType: text().$type<PaymentReminderType>(), // urgent means student blocked from viewing course
    ...auditColumns,
  },
  (t) => [
    index('idx_batch_student_batch_id').on(t.batchId),
    index('idx_batch_student_student_id').on(t.studentId),
    uniqueIndex('idx_batch_student_batch_id_student_id').on(t.batchId, t.studentId),
  ],
)

export const batchStudentRelations = relations(batchStudentTable, ({ one, many }) => ({
  batch: one(batchTable, {
    fields: [batchStudentTable.batchId],
    references: [batchTable.id],
  }),
  student: one(profileTable, {
    fields: [batchStudentTable.studentId],
    references: [profileTable.id],
  }),
  payments: many(studentFeePaymentTable),
}))

export const studentFeePaymentTable = pgTable(
  'student_fee_payment',
  {
    id: uid().primaryKey(),
    studentId: uid()
      .notNull()
      .references(() => profileTable.id),
    academyId: uid()
      .notNull()
      .references(() => academyTable.id),
    method: text().$type<PaymentMethod>(),
    currency: text().$type<Currency>().notNull(),
    cents: integer().notNull(),
    status: text().$type<PaymentStatus>().notNull(),
    paidAt: timestampz(),
    paidRemarks: text(),
    ...auditColumns,
  },
  (t) => [
    check('chk_paid_remarks_len', sql`char_length(${t.paidRemarks}) <= 255`),
    index('idx_student_fee_payment_student_id_academy_id_status').on(t.studentId, t.academyId, t.status),
    index('idx_student_fee_payment_academy_id').on(t.academyId),
  ],
)

export const studentFeePaymentItemTable = pgTable(
  'student_fee_payment_item',
  {
    id: uid().primaryKey(),
    studentFeePaymentId: uid()
      .notNull()
      .references(() => studentFeePaymentTable.id),
    batchId: uid()
      .notNull()
      .references(() => batchTable.id),
    cycle: smallint().notNull(),
    fee: integer().notNull(),
    ...auditColumns,
  },
  (t) => [
    index('idx_student_fee_payment_item_batch_id').on(t.batchId),
    index('idx_student_fee_payment_item_student_fee_payment_id').on(t.studentFeePaymentId),
  ],
)

export const studentFeePaymentRelations = relations(studentFeePaymentTable, ({ one, many }) => ({
  student: one(profileTable, {
    fields: [studentFeePaymentTable.studentId],
    references: [profileTable.id],
  }),
  academy: one(academyTable, {
    fields: [studentFeePaymentTable.academyId],
    references: [academyTable.id],
  }),
  items: many(studentFeePaymentItemTable),
}))

export const studentFeePaymentItemRelations = relations(studentFeePaymentItemTable, ({ one }) => ({
  payment: one(studentFeePaymentTable, {
    fields: [studentFeePaymentItemTable.studentFeePaymentId],
    references: [studentFeePaymentTable.id],
  }),
  batch: one(batchTable, {
    fields: [studentFeePaymentItemTable.batchId],
    references: [batchTable.id],
  }),
}))

export const batchAttachmentTable = pgTable(
  'batch_attachment',
  {
    id: uid().primaryKey(),
    batchId: uid()
      .notNull()
      .references(() => batchTable.id),
    attachmentId: uid()
      .notNull()
      .references(() => courseAttachmentTable.id),
    ...auditColumns,
  },
  (t) => [
    uniqueIndex('idx_batch_attachment_batch_id_attachment_id').on(t.batchId, t.attachmentId),
    index('idx_batch_attachment_attachment_id').on(t.attachmentId),
  ],
)

export const batchAttachmentRelations = relations(batchAttachmentTable, ({ one }) => ({
  batch: one(batchTable, {
    fields: [batchAttachmentTable.batchId],
    references: [batchTable.id],
  }),
  attachment: one(courseAttachmentTable, {
    fields: [batchAttachmentTable.attachmentId],
    references: [courseAttachmentTable.id],
  }),
}))

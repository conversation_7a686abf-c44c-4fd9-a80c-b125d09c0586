import fs from 'fs'

import Handlebars from 'handlebars'
import { type Logger } from 'pino'

import { language2locales } from '@/shared/common/date-utils.shared'

type TemplateType = 'mail' | 'sms'
const templateCache: Record<string, Handlebars.TemplateDelegate<unknown>> = {}

export const getTemplate = async (log: Logger, template: string, type: TemplateType, language: string) => {
  const locales = language2locales(language)

  for (const locale of locales) {
    const localizedTemplateName = `${type}/${template}-${locale}`
    if (localizedTemplateName in templateCache) return templateCache[localizedTemplateName]

    const localizedTemplateFile = await readTemplateFile(log, localizedTemplateName)
    if (localizedTemplateFile) {
      templateCache[localizedTemplateName] = Handlebars.compile(localizedTemplateFile)
      return templateCache[localizedTemplateName]
    }
  }
  const templateName = `${type}/${template}`
  if (templateName in templateCache) return templateCache[templateName]
  const file = await readTemplateFile(log, templateName)
  if (!file) throw new Error(`${type} template ${template} not found`)

  templateCache[templateName] = Handlebars.compile(file)
  return templateCache[templateName]
}

const readTemplateFile = (log: Logger, name: string): Promise<string | null> => {
  const path = `./templates/${name}.html`
  log.info(`Reading file ${path} ...`)
  return new Promise((resolve, reject) => {
    fs.readFile(path, 'utf-8', (err, data) => {
      if (err) {
        // If file is not found, return null
        if (err.code === 'ENOENT') {
          resolve(null)
        } else {
          // For other errors, still resolve with null but log the error
          log.error(err, `Error reading HTML template ${path}`)
          reject(err)
        }
      } else {
        resolve(data)
      }
    })
  })
}

import { UUID } from 'crypto'

import { Hono } from 'hono'

import { HonoVars, queryFull } from '@/server/common/hono-utils.server'

import { getCourse } from './getCourse.server'

export const getCourseHandler = new Hono<HonoVars>().get('/', queryFull, async (c) => {
  const courseId = c.req.param('id') as UUID
  const query = c.req.valid('query')
  const full = query.full === 'true'

  const course = await getCourse(c.get('log'), courseId, full, c.get('profile'))
  return c.json(course, 200)
})

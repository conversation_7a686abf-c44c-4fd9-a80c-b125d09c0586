import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator'
import { Hono } from 'hono'

import { getCtx } from '@/server/common/auth-context-types.server'
import { HonoVars } from '@/server/common/hono-utils.server'
import { $AddBatchAttachmentForm } from '@/shared/batch/batch-utils.shared'
import { $HasId } from '@/shared/common/common-utils.shared'

import { addBatchAttachment } from './addBatchAttachment'

export const addBatchAttachmentHandler = new Hono<HonoVars>().post(
  '/',
  zValidator('param', $HasId),
  zValidator('json', $AddBatchAttachmentForm),
  async (c) => {
    const { id: batchId } = c.req.valid('param')
    const form = c.req.valid('json')
    await addBatchAttachment(getCtx(c), batchId, form)
    return c.body(null, 201)
  },
)

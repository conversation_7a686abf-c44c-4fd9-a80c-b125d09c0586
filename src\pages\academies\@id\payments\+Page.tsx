import { usePageContext } from 'vike-react/usePageContext'

import { WithProfile } from '@/pages/profiles/common/WithProfile'
import { extractUuid } from '@/shared/common/common-utils.shared'
import { PaymentStatus } from '@/shared/common/payment-utils/payment-utils.shared'
import { ACADEMY_STAFF } from '@/shared/profiles/role-utils.shared'

import { StudentFeePaymentsOfAcademyPage } from './StudentFeePaymentsOfAcademy'
export default function Page() {
  const { routeParams, urlParsed } = usePageContext()
  const academyId = extractUuid(routeParams.id)

  return (
    <WithProfile roleAnyOf={ACADEMY_STAFF}>
      <StudentFeePaymentsOfAcademyPage
        academyId={academyId}
        search={{
          statuses: urlParsed.searchAll.statuses as PaymentStatus[],
          studentEmail: urlParsed.search.studentEmail ?? '',
        }}
      />
    </WithProfile>
  )
}

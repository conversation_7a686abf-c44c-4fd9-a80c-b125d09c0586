import { UUID } from 'crypto'

import { and, eq, gt, isNull, lt, lte } from 'drizzle-orm'
import { DateTime } from 'luxon'
import pino from 'pino'

import { db } from '@/server/db/db'
import { batchStudentTable, batchTable } from '@/server/db/schema/batch-schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { today } from '@/shared/common/date-utils.shared'
import { plusCycles } from '@/shared/common/payment-utils/payment-utils.shared'
import { withLog } from '@/shared/common/withLog.shared'

import { updateStudentPaymentReminderType } from './update-student-reminder-type/updateStudentPaymentReminderType'

const getNextRunningBatchDaf = async (log: pino.Logger, prevId?: UUID) => {
  return withLog(log, 'getNextBatcheDaf', () =>
    db.query.batchTable.findFirst({
      columns: {
        id: true,
        startDate: true,
        timezone: true,
        billingCycle: true,
        cycleCount: true,
        graceDays: true,
      },
      with: {
        teacher: {
          with: {
            user: {
              columns: {
                id: true,
              },
            },
          },
        },
        events: {
          columns: {
            id: true,
          },
        },
      },
      where: and(lte(batchTable.startDate, today()), eq(batchTable.over, false), prevId && gt(batchTable.id, prevId)),
      orderBy: [batchTable.id],
    }),
  )
}

type Batch = NonNullable<Awaited<ReturnType<typeof getNextRunningBatchDaf>>>

const getBatchStudentsDaf = async (log: pino.Logger, batchId: UUID) =>
  withLog(log, 'getBatchStudentsDafWithJoin', () =>
    db
      .select({
        id: batchStudentTable.id,
        firstJoinedAt: batchStudentTable.firstJoinedAt,
        paidTillCycle: batchStudentTable.paidTillCycle,
        lastReminderType: batchStudentTable.lastReminderType,
      })
      .from(batchStudentTable)
      .innerJoin(batchTable, eq(batchStudentTable.batchId, batchTable.id))
      .where(
        and(
          eq(batchStudentTable.batchId, batchId),
          isNull(batchStudentTable.leftAt),
          lt(batchStudentTable.paidTillCycle, batchTable.cycleCount),
        ),
      ),
  )

type BatchStudent = NonNullable<Awaited<ReturnType<typeof getBatchStudentsDaf>>>[number]

const updateBatchOverDaf = async (log: pino.Logger, batch: NonNullable<Batch>) =>
  withLog(log, 'updateBatchOverDaf', () => db.update(batchTable).set({ over: true }).where(eq(batchTable.id, batch.id)))

const processBatch = async (log: pino.Logger, batch: Batch) => {
  log.info(`Processing batch ${batch.id}`)
  const batchRunsUntil = plusCycles(
    DateTime.fromISO(batch.startDate, { zone: batch.timezone }),
    batch.billingCycle,
    batch.cycleCount,
  ).toISO()!
  const today = utc().setZone(batch.timezone).toISODate()!
  if (batchRunsUntil <= today) {
    log.info(`Batch ${batch.id}, which runs until ${batchRunsUntil}, is over by today, ${today}`)
    await updateBatchOverDaf(log, batch)
    return
  }
  const batchStudents = await getBatchStudentsDaf(log, batch.id)
  for (const batchStudent of batchStudents) {
    await processStudent(log.child({ batchStudentId: batchStudent.id }), batch, batchStudent)
  }
}

const processStudent = async (log: pino.Logger, batch: Batch, batchStudent: BatchStudent) => {
  await db.transaction(async (tx) => {
    await updateStudentPaymentReminderType(log, tx, batch, batchStudent)
  })
}

export const processStudentBilling = async (log: pino.Logger) => {
  let prevId: UUID | undefined = undefined
  while (true) {
    const batch = await getNextRunningBatchDaf(log, prevId)
    if (!batch) {
      log.info('Processing ended')
      break
    }
    await processBatch(log.child({ batchId: batch.id }), batch)
    prevId = batch.id
  }
}

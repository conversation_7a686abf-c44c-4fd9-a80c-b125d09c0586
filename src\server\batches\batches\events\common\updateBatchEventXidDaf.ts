import { UUID } from 'crypto'

import { eq } from 'drizzle-orm'
import { pino } from 'pino'

import { Transaction } from '@/server/db/db'
import { batchEventTable } from '@/server/db/schema'
import { utc } from '@/shared/common/date-utils-basic.shared'
import { withLog } from '@/shared/common/withLog.shared'

export const updateBatchEventXidDaf = async (log: pino.Logger, db: Transaction, eventId: UUID) =>
  withLog(log, 'updateBatchEventXidDaf', () =>
    db
      .update(batchEventTable)
      .set({
        eventXid: crypto.randomUUID().replaceAll('-', ''),
        updateRemarks: 'updatedGoogleEventXid',
        updatedAt: utc().toJSDate(),
      })
      .where(eq(batchEventTable.id, eventId)),
  )

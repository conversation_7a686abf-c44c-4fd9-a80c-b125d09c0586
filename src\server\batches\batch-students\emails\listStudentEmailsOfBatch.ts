import { UUID } from 'crypto'

import { aliasedTable, and, eq, isNull, ne } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { db } from '@/server/db/db'
import { batchStudentTable, batchTable, profileTable, userTable } from '@/server/db/schema'
import { withLog } from '@/shared/common/withLog.shared'

import { ensureCanUpdateBatch } from '../../batches/common/ensureCanUpdateBatch'

const findBatchStudentEmailsOfBatchDaf = (log: pino.Logger, batchId: UUID) =>
  withLog(log, 'findBatchStudentEmailsOfBatchDaf', () => {
    // Create alias for teacher profile table
    const teacherProfile = aliasedTable(profileTable, 'teacherProfile')

    return db
      .selectDistinct({
        email: userTable.email,
      })
      .from(batchStudentTable)
      .innerJoin(profileTable, eq(batchStudentTable.studentId, profileTable.id))
      .innerJoin(userTable, eq(profileTable.userId, userTable.id))
      .innerJoin(batchTable, eq(batchStudentTable.batchId, batchTable.id))
      .innerJoin(teacherProfile, eq(batchTable.teacherId, teacherProfile.id))
      .where(
        and(
          eq(batchStudentTable.batchId, batchId),
          isNull(batchStudentTable.leftAt), // student is still in the batch
          ne(batchStudentTable.lastReminderType, 'blocked'), // student is not blocked
          isNull(profileTable.suspendedAt), // student is not suspended
          isNull(userTable.suspendedAt), // user is not suspended
          eq(userTable.emailVerified, true), // user email is verified
          eq(userTable.mobileVerified, true), // user mobile is verified
          ne(userTable.id, teacherProfile.userId), // exclude teacher's email
        ),
      )
  })

export const listStudentEmailsOfBatch = async (c: Ctx, batchId: UUID) => {
  await ensureCanUpdateBatch(c, batchId)
  return findBatchStudentEmailsOfBatchDaf(c.log, batchId)
}

export type BatchStudentEmail = Awaited<ReturnType<typeof findBatchStudentEmailsOfBatchDaf>>[number]

import { ArchiveBoxXMarkIcon, KeyIcon, UserIcon } from '@heroicons/react/24/outline'

import { IMPERSONATING_ROLES } from '@/shared/profiles/role-utils.shared'
import { useSignedIn } from '@ui/users/auth/auth-token-store'

import { useCurrentProfile } from './current-profile-store'

export const useProfileSidebarLinks = () => {
  const signedIn = useSignedIn()
  const { currentProfile } = useCurrentProfile()

  const links = []

  if (signedIn) {
    links.push({ name: 'Create Token', href: '/users/me/create-token', icon: KeyIcon })
    links.push({ name: 'Invalidate Tokens', href: '/users/me/invalidate-tokens', icon: ArchiveBoxXMarkIcon })
  }

  if (currentProfile && IMPERSONATING_ROLES.includes(currentProfile.role)) {
    links.push({ name: 'Impersonate', href: '/profiles/impersonate', icon: UserIcon })
  }

  return links
}

import { afterAll, afterEach, beforeAll } from 'vitest'

import { mockServer } from '@/server/test/mockServer'
import '@/server/test/custom-matchers'

// Start server before all tests
beforeAll(() => {
  mockServer.listen({
    onUnhandledRequest(request) {
      // Ignore requests to our API from supertest
      if (request.url.startsWith('http://127.0.0.1')) {
        return
      }

      // Otherwise, print a warning for any unhandled request.
      console.log(`Unhandled ${request.method} ${request.url}`)
    },
  })
})

//  Close server after all tests
afterAll(() => {
  mockServer.close()
})

// Reset handlers after each test `important for test isolation`
afterEach(() => {
  mockServer.resetHandlers()
})

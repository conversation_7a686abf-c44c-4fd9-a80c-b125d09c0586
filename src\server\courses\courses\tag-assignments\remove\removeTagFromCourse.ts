import { UUID } from 'crypto'

import { and, eq } from 'drizzle-orm'
import pino from 'pino'

import { Ctx } from '@/server/common/auth-context-types.server'
import { ensureCanUpdateCourse } from '@/server/courses/courses/common/ensureCanUpdateCourse'
import { db } from '@/server/db/db'
import { courseTagAssignmentTable } from '@/server/db/schema/course-schema'
import { withLog } from '@/shared/common/withLog.shared'

const removeTagFromCourseDaf = (log: pino.Logger, courseId: UUID, tagId: UUID) =>
  withLog(log, 'removeTagFromCourseDaf', () =>
    db
      .delete(courseTagAssignmentTable)
      .where(and(eq(courseTagAssignmentTable.courseId, courseId), eq(courseTagAssignmentTable.tagId, tagId))),
  )

export const removeTagFromCourse = async (c: Ctx, courseId: UUID, tagId: UUID) => {
  await ensureCanUpdateCourse(c, courseId)
  await removeTagFromCourseDaf(c.log, courseId, tagId)
}

import path from 'path'

import devServer from '@hono/vite-dev-server'
import tailwindcss from '@tailwindcss/vite'
import react from '@vitejs/plugin-react'
import vike from 'vike/plugin'
import { defineConfig, loadEnv } from 'vite'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  return {
    resolve: {
      alias: {
        '@ui': path.resolve(__dirname, './src/pages'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    server: {
      host: '0.0.0.0',
    },
    plugins: [
      vike({}),
      devServer({
        entry: 'src/hono-entry.ts',
        exclude: [
          /^\/@.+$/,
          /.*\.(ts|tsx|vue)($|\?)/,
          /.*\.(s?css|less)($|\?)/,
          /^\/favicon\.ico$/,
          /.*\.(svg|png|jpg|jpeg)($|\?)/,
          /^\/(public|assets|static)\/.+/,
          /^\/node_modules\/.*/,
        ],
        injectClientScript: false,
      }),
      react({}),
      tailwindcss(),
      // mkcert(),
    ],
    build: {
      target: 'es2022',
    },
    define: {
      ...(mode === 'development' && {
        'process.env': JSON.stringify(env),
      }),
    },
  }
})
